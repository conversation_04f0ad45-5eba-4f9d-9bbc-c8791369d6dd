<?php

namespace Database\Seeders;

use App\Models\Legacy\Location;
use App\Models\SalesBaitConfiguration;
use App\Repositories\LocationRepository;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;

class SalesBaitConfigurationSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $industries = [SalesBaitConfiguration::INDUSTRY_SOLAR, SalesBaitConfiguration::INDUSTRY_ROOFING];
        $states = Location::query()->where(Location::TYPE, Location::TYPE_STATE)->get();
        $counties = Location::query()->where(Location::TYPE, Location::TYPE_COUNTY)->get();

        foreach($industries as $industry) {
            $data = [];

            SalesBaitConfiguration::query()->create([
                SalesBaitConfiguration::FIELD_TYPE => SalesBaitConfiguration::TYPE_NATIONAL,
                SalesBaitConfiguration::FIELD_ENABLED => true,
                SalesBaitConfiguration::FIELD_LOCATION_ID => null,
                SalesBaitConfiguration::FIELD_INDUSTRY => $industry
            ]);

            foreach($states as $state) {
                $data[] = [
                    SalesBaitConfiguration::FIELD_TYPE => SalesBaitConfiguration::TYPE_STATE,
                    SalesBaitConfiguration::FIELD_ENABLED => true,
                    SalesBaitConfiguration::FIELD_LOCATION_ID => $state->id,
                    SalesBaitConfiguration::FIELD_INDUSTRY => $industry
                ];

                if(count($data) >= 500) {
                    SalesBaitConfiguration::query()->insert($data);

                    $data = [];
                }
            }

            if(!empty($data)) {
                SalesBaitConfiguration::query()->insert($data);

                $data = [];
            }

            foreach($counties as $county) {
                $data[] = [
                    SalesBaitConfiguration::FIELD_TYPE => SalesBaitConfiguration::TYPE_COUNTY,
                    SalesBaitConfiguration::FIELD_ENABLED => true,
                    SalesBaitConfiguration::FIELD_LOCATION_ID => $county->id,
                    SalesBaitConfiguration::FIELD_INDUSTRY => $industry
                ];

                if(count($data) >= 500) {
                    SalesBaitConfiguration::query()->insert($data);

                    $data = [];
                }
            }

            if(!empty($data)) {
                SalesBaitConfiguration::query()->insert($data);

                $data = [];
            }
        }
    }
}
