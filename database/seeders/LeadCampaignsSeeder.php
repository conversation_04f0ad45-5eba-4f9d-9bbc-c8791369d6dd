<?php

namespace Database\Seeders;

use App\Models\Odin\Company;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use App\Models\Legacy\LeadCampaign;

class LeadCampaignsSeeder extends Seeder
{
    use WithoutModelEvents;

    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $company = Company::first();

        LeadCampaign::factory()->create([
            'company_id' => $company->legacy_id ?? Company::factory()->withLegacyCompany()->createQuietly()->legacy_id,
        ]);
    }
}
