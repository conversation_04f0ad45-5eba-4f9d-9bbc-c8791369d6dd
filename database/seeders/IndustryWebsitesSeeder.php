<?php

namespace Database\Seeders;

use App\Models\Odin\Industry;
use App\Models\Odin\IndustryWebsite;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class IndustryWebsitesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $industries = Industry::all()->pluck(Industry::FIELD_ID, Industry::FIELD_NAME)->toArray();

        DB::table(IndustryWebsite::TABLE)->insert([
            [
                IndustryWebsite::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryWebsite::FIELD_WEBSITE_ID => 1,
                IndustryWebsite::FIELD_SLUG => 'solarreviews'
            ],
            [
                IndustryWebsite::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryWebsite::FIELD_WEBSITE_ID => 2,
                IndustryWebsite::FIELD_SLUG => 'solar-estimate'
            ],
            [
                IndustryWebsite::FIELD_INDUSTRY_ID => $industries['Roofing'],
                IndustryWebsite::FIELD_WEBSITE_ID => 3,
                IndustryWebsite::FIELD_SLUG => 'roofing-calculator'
            ],
            [
                IndustryWebsite::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryWebsite::FIELD_WEBSITE_ID => 4,
                IndustryWebsite::FIELD_SLUG => 'solar-power-rocks'
            ],
            [
                IndustryWebsite::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryWebsite::FIELD_WEBSITE_ID => 5,
                IndustryWebsite::FIELD_SLUG => 'sun-number'
            ],
            [
                IndustryWebsite::FIELD_INDUSTRY_ID => $industries['Solar'],
                IndustryWebsite::FIELD_WEBSITE_ID => 6,
                IndustryWebsite::FIELD_SLUG => 'cut-my-bill'
            ],
        ]);
    }
}
