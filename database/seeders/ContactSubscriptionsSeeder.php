<?php

namespace Database\Seeders;

use App\Models\ContactSubscription;
use Illuminate\Database\Console\Seeds\WithoutModelEvents;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ContactSubscriptionsSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        DB::table('contact_subscriptions')->insert([
            [
                ContactSubscription::FIELD_ID => 1,
                ContactSubscription::FIELD_CONTACT_ID => 1,
                ContactSubscription::FIELD_CONTACT_TYPE => ContactSubscription::CONTACT_TYPE_CONTACT,
                ContactSubscription::FIELD_CONTACT_METHOD => ContactSubscription::CONTACT_METHOD_EMAIL,
                ContactSubscription::FIELD_NOTIFICATION_TYPE => ContactSubscription::NOTIFICATION_TYPE_SALESBAIT,
                ContactSubscription::FIELD_UNSUBSCRIBED => time(),
                ContactSubscription::FIELD_DELETED_AT => null
            ],
            [
                ContactSubscription::FIELD_ID => 2,
                ContactSubscription::FIELD_CONTACT_ID => 2,
                ContactSubscription::FIELD_CONTACT_TYPE => ContactSubscription::CONTACT_TYPE_USER,
                ContactSubscription::FIELD_CONTACT_METHOD => ContactSubscription::CONTACT_METHOD_EMAIL,
                ContactSubscription::FIELD_NOTIFICATION_TYPE => ContactSubscription::NOTIFICATION_TYPE_SALESBAIT,
                ContactSubscription::FIELD_UNSUBSCRIBED => 0,
                ContactSubscription::FIELD_DELETED_AT => null
            ],
            [
                ContactSubscription::FIELD_ID => 3,
                ContactSubscription::FIELD_CONTACT_ID => 3,
                ContactSubscription::FIELD_CONTACT_TYPE => ContactSubscription::CONTACT_TYPE_CONTACT,
                ContactSubscription::FIELD_CONTACT_METHOD => ContactSubscription::CONTACT_METHOD_SMS,
                ContactSubscription::FIELD_NOTIFICATION_TYPE => ContactSubscription::NOTIFICATION_TYPE_SALESBAIT,
                ContactSubscription::FIELD_UNSUBSCRIBED => 0,
                ContactSubscription::FIELD_DELETED_AT => null
            ],
            [
                ContactSubscription::FIELD_ID => 4,
                ContactSubscription::FIELD_CONTACT_ID => 4,
                ContactSubscription::FIELD_CONTACT_TYPE => ContactSubscription::CONTACT_TYPE_USER,
                ContactSubscription::FIELD_CONTACT_METHOD => ContactSubscription::CONTACT_METHOD_SMS,
                ContactSubscription::FIELD_NOTIFICATION_TYPE => ContactSubscription::NOTIFICATION_TYPE_SALESBAIT,
                ContactSubscription::FIELD_UNSUBSCRIBED => time(),
                ContactSubscription::FIELD_DELETED_AT => null
            ],
            [
                ContactSubscription::FIELD_ID => 5,
                ContactSubscription::FIELD_CONTACT_ID => 5,
                ContactSubscription::FIELD_CONTACT_TYPE => ContactSubscription::CONTACT_TYPE_CONTACT,
                ContactSubscription::FIELD_CONTACT_METHOD => ContactSubscription::CONTACT_METHOD_EMAIL,
                ContactSubscription::FIELD_NOTIFICATION_TYPE => ContactSubscription::NOTIFICATION_TYPE_SALESBAIT,
                ContactSubscription::FIELD_UNSUBSCRIBED => 0,
                ContactSubscription::FIELD_DELETED_AT => date('Y-m-d H:i:s')
            ],
        ]);
    }
}
