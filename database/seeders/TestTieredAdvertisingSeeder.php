<?php

namespace Database\Seeders;

use App\Enums\Advertising\AdvertisingPlatform;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryConfiguration;
use App\Models\TieredAdvertisingAccount;
use App\Models\TieredAdvertisingCampaign;
use App\Models\TieredAdvertisingConfiguration;
use Illuminate\Database\Seeder;

class TestTieredAdvertisingSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run(): void
    {
        TieredAdvertisingConfiguration::truncate();
        TieredAdvertisingAccount::truncate();
        TieredAdvertisingCampaign::truncate();


        $industryIds = $industryIds = Industry::query()
            ->join(IndustryConfiguration::TABLE, IndustryConfiguration::TABLE.'.'.IndustryConfiguration::FIELD_INDUSTRY_ID, '=', Industry::TABLE.'.'.Industry::FIELD_ID)
            ->where(IndustryConfiguration::TABLE.'.'.IndustryConfiguration::FIELD_FUTURE_CAMPAIGNS_ACTIVE, true)
            ->get([
                Industry::TABLE.'.'.Industry::FIELD_ID,
            ])->pluck(Industry::FIELD_ID);

        $bounds = [
            [
                TieredAdvertisingCampaign::FIELD_TIER => 1,
                TieredAdvertisingCampaign::FIELD_UPPER_BOUND => null,
                TieredAdvertisingCampaign::FIELD_TCPA_BID => 150,
                TieredAdvertisingCampaign::FIELD_LOWER_BOUND => 140,
            ],
            [
                TieredAdvertisingCampaign::FIELD_TIER => 2,
                TieredAdvertisingCampaign::FIELD_UPPER_BOUND => 140,
                TieredAdvertisingCampaign::FIELD_TCPA_BID => 130,
                TieredAdvertisingCampaign::FIELD_LOWER_BOUND => 110,
            ],
            [
                TieredAdvertisingCampaign::FIELD_TIER => 3,
                TieredAdvertisingCampaign::FIELD_UPPER_BOUND => 110,
                TieredAdvertisingCampaign::FIELD_TCPA_BID => 100,
                TieredAdvertisingCampaign::FIELD_LOWER_BOUND => 90,
            ],
            [
                TieredAdvertisingCampaign::FIELD_TIER => 4,
                TieredAdvertisingCampaign::FIELD_UPPER_BOUND => 90,
                TieredAdvertisingCampaign::FIELD_TCPA_BID => 70,
                TieredAdvertisingCampaign::FIELD_LOWER_BOUND => 60,
            ],
            [
                TieredAdvertisingCampaign::FIELD_TIER => 5,
                TieredAdvertisingCampaign::FIELD_UPPER_BOUND => 60,
                TieredAdvertisingCampaign::FIELD_TCPA_BID => 40,
                TieredAdvertisingCampaign::FIELD_LOWER_BOUND => 20,
            ],
        ];

        foreach (AdvertisingPlatform::cases() as $advertisingPlatform) {
            foreach ($industryIds as $industryId) {
                // Config create for platform and industry
                TieredAdvertisingConfiguration::query()->create([
                    TieredAdvertisingConfiguration::FIELD_PLATFORM => $advertisingPlatform->value,
                    TieredAdvertisingConfiguration::FIELD_INDUSTRY_ID => $industryId,
                    TieredAdvertisingConfiguration::FIELD_ENABLED => true,
                    TieredAdvertisingConfiguration::FIELD_UPDATE_FREQUENCY_MINUTES => 60,
                    TieredAdvertisingConfiguration::FIELD_LAST_LOCATION_UPDATE => null,
                    TieredAdvertisingConfiguration::FIELD_ROAS => 2.0,
                    TieredAdvertisingConfiguration::FIELD_CONFIGS => [],
                ]);

                // Account create
                TieredAdvertisingAccount::query()->create([
                    TieredAdvertisingAccount::FIELD_INDUSTRY_ID => $industryId,
                    TieredAdvertisingAccount::FIELD_PLATFORM => $advertisingPlatform->value,
                    TieredAdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID => $advertisingPlatform->value.'_industry_'.$industryId.'_acct_id',
                    TieredAdvertisingAccount::FIELD_NAME => $advertisingPlatform->value.' Industry '.$industryId.' Account',
                    TieredAdvertisingAccount::FIELD_DATA => [],
                ]);

                $acct = TieredAdvertisingAccount::query()
                    ->where(TieredAdvertisingAccount::FIELD_INDUSTRY_ID, $industryId)
                    ->where(TieredAdvertisingAccount::FIELD_PLATFORM, $advertisingPlatform->value)
                    ->get()
                    ->first();

                foreach ($bounds as $bound) {
                    TieredAdvertisingCampaign::query()->create([
                        TieredAdvertisingCampaign::FIELD_INDUSTRY_ID => $industryId,
                        TieredAdvertisingCampaign::FIELD_PLATFORM => $advertisingPlatform->value,
                        TieredAdvertisingCampaign::FIELD_TIER => $bound[TieredAdvertisingCampaign::FIELD_TIER],
                        TieredAdvertisingCampaign::FIELD_TIERED_ADVERTISING_ACCOUNT_ID => $acct->id,
                        TieredAdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID => $advertisingPlatform->value.'_industry_'.$industryId.'_tier_'.$bound[TieredAdvertisingCampaign::FIELD_TIER],
                        TieredAdvertisingCampaign::FIELD_NAME => $advertisingPlatform->name.' Industry '.$industryId.' Tier '.$bound[TieredAdvertisingCampaign::FIELD_TIER],
                        TieredAdvertisingCampaign::FIELD_TCPA_BID => $bound[TieredAdvertisingCampaign::FIELD_TCPA_BID],
                        TieredAdvertisingCampaign::FIELD_UPPER_BOUND => $bound[TieredAdvertisingCampaign::FIELD_UPPER_BOUND],
                        TieredAdvertisingCampaign::FIELD_LOWER_BOUND => $bound[TieredAdvertisingCampaign::FIELD_LOWER_BOUND],
                        TieredAdvertisingCampaign::FIELD_COVERED_POPULATION => 0,
                        TieredAdvertisingCampaign::FIELD_DATA => [],
                    ]);
                }
            }
        }
    }

}
