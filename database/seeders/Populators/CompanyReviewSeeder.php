<?php

namespace Database\Seeders\Populators;

use App\Models\Odin\Company;
use App\Models\Odin\CompanyReview;
use App\Models\Odin\CompanyReviewData;
use App\Models\Odin\CompanyReviewResponse;
use App\Models\Odin\Consumer;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Seeder;
use Faker;

class CompanyReviewSeeder extends Seeder
{
    /**
     * Run the database seeds.
     * <PERSON><PERSON> will attempt to use real Models to attach Reviews to
     * If no models are found, dummy ids will be inserted
     * @return void
     */
    public function run(): void
    {
        $faker = Faker\Factory::create();
        $companyIds = collect($this->getModelIdsOrDummyIds(Company::class));
        $consumersIds = $this->getModelIdsOrDummyIds(Consumer::class);
        $userIds = $this->getModelIdsOrDummyIds(User::class);
        print("Adding dummy Reviews for {$companyIds->count()} Companies...\n");

        $companyIds->each(function(int $companyId) use ($faker, $consumersIds, $userIds) {
             $reviewCount = $faker->numberBetween(0, 3);
             $companyReviews = CompanyReview::factory()
                 ->count($reviewCount)
                 ->create([
                     CompanyReview::FIELD_COMPANY_ID    => $companyId,
                     CompanyReview::FIELD_CONSUMER_ID   => $faker->randomElement($consumersIds),
                 ]);
             $companyReviews->each(function(CompanyReview $review) use ($userIds, $faker) {
                 CompanyReviewData::factory()->for($review, 'review')->create();
                 CompanyReviewResponse::factory()->for($review, 'review')->create([
                     CompanyReviewResponse::FIELD_USER_ID   => $faker->randomElement($userIds),
                 ]);
             });
        });
    }

    /**
     * @param string $modelClass
     * @param null|string $idColumn
     * @return array
     */
    protected function getModelIdsOrDummyIds(string $modelClass, ?string $idColumn = 'id'): array
    {
        /** @var $modelClass Model */
        return $modelClass::query()?->first()
            ? $modelClass::all()->pluck($idColumn)->toArray()
            : [ 1, 2, 3, 4, 5, 6, 7, 8, 9 ];
    }
}
