<?php

namespace Database\Seeders\Populators;

use App\Models\Odin\ConsumerProduct;
use Illuminate\Database\Seeder;

class ConsumerSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $consumerCount = 2000;
        print("Adding {$consumerCount} dummy Consumers and associated models...\n");

        ConsumerProduct::factory()->count($consumerCount)->create([
            ConsumerProduct::FIELD_STATUS => ConsumerProduct::STATUS_ALLOCATED
        ]);
    }
}
