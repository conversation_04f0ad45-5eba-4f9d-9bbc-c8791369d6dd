<?php

namespace Database\Seeders\Populators;

use App\Models\Action;
use App\Models\Call;
use App\Models\Odin\Company;
use App\Models\Phone;
use App\Models\Text;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Seeder;
use Faker;

class CompanyActivitiesSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     * @return void
     */
    public function run()
    {
        $faker = Faker\Factory::create();
        $companyIds = collect($this->getModelIdsOrDummyIds(Company::class));
        $userIds = $this->getModelIdsOrDummyIds(User::class);
        $phoneIds = $this->getModelIdsOrDummyIds(Phone::class);
        print("Adding dummy Activities for {$companyIds->count()} Companies...\n");

        $companyIds->each(function(int $companyId) use ($faker, $userIds, $phoneIds) {
            $actionCount = $faker->numberBetween(1, 2);
            $callCount = $faker->numberBetween(1, 2);
            $textCount = $faker->numberBetween(1, 2);

            Action::factory()
                ->count($actionCount)
                ->create([
                    Action::FIELD_FROM_USER_ID      => $faker->randomElement($userIds),
                    Action::FIELD_FOR_RELATION_TYPE => Action::RELATION_TYPE_COMPANY,
                    Action::FIELD_FOR_ID            => $companyId,
                ]);

            Call::factory()
                ->count($callCount)
                ->create([
                    Call::FIELD_PHONE_ID        => $faker->randomElement($phoneIds),
                    Call::FIELD_RELATION_TYPE   => Call::RELATION_COMPANY,
                    Call::FIELD_RELATION_ID     => $companyId,
                ]);

            Text::factory()
                ->count($textCount)
                ->create([
                    Text::FIELD_RELATION_TYPE   => Text::RELATION_COMPANY,
                    Text::FIELD_RELATION_ID     => $companyId,
                    Text::FIELD_PHONE_ID        => $faker->randomElement($phoneIds)
                ]);
        });


    }

    /**
     * @param string $modelClass
     * @param null|string $idColumn
     * @return array
     */
    protected function getModelIdsOrDummyIds(string $modelClass, ?string $idColumn = 'id'): array
    {
        /** @var $modelClass Model */
        return $modelClass::query()?->first()
            ? $modelClass::all()->pluck($idColumn)->toArray()
            : [ 1, 2, 3, 4, 5, 6, 7, 8, 9 ];
    }
}
