<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('consumers', function (Blueprint $table) {
            $table->unsignedBigInteger('cloned_from_id')->nullable();
        });

        Schema::table('consumer_products', function (Blueprint $table) {
            $table->unsignedBigInteger('cloned_from_id')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('consumers', function (Blueprint $table) {
            $table->dropColumn('cloned_from_id');
        });

        Schema::table('consumer_products', function (Blueprint $table) {
            $table->dropColumn('cloned_from_id');
        });
    }
};
