<?php

use App\Enums\PrivacyManagement\PrivacyRequestStatuses;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('privacy_requests', function (Blueprint $table) {
            $table->id();
            $table->uuid();
            $table->unsignedBigInteger('approved_by_id')->nullable();
            $table->json('payload');
            $table->json('scan_response')->nullable();
            $table->enum('status', PrivacyRequestStatuses::values());
            $table->string('source');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('privacy_requests');
    }
};
