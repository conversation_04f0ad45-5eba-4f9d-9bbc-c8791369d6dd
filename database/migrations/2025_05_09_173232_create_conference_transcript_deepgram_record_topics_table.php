<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('conference_transcript_deepgram_record_topics', static function (Blueprint $table) {
            $table->id();
            $table->foreignId('conference_transcript_deepgram_record_id')->constrained(indexName: 'ctdr_topics_ctdr_id_foreign')->cascadeOnDelete();
            $table->foreignId('conference_transcript_id')->constrained(indexName: 'ctdr_topics_ct_id_foreign')->cascadeOnDelete();
            $table->string('topic');
            $table->float('confidence_score');
            $table->text('text');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('conference_transcript_deepgram_record_topics');
    }
};
