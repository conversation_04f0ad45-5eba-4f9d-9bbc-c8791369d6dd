<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('recycled_leads', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('consumer_product_id');
            $table->foreign('consumer_product_id')->references('id')->on('consumer_products');
            $table->timestamp('contacted_at');
            $table->boolean('opted_out')->nullable();
            $table->json('data')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('recycled_leads');
    }
};
