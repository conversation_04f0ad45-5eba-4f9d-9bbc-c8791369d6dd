<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('lead_refund_item_refunds', function (Blueprint $table) {
            $table->id();

            $table->unsignedBigInteger('lead_refund_item_id');
            $table->unsignedBigInteger('lead_refund_id');
            $table->unsignedBigInteger('legacy_charge_id')->nullable();
            $table->string('external_charge_id')->nullable();
            $table->string('external_refund_id')->nullable();
            $table->string('status');
            $table->string('error_message')->nullable();

            $table->foreign('lead_refund_item_id')->references('id')->on('lead_refund_items')->noActionOnDelete();
            $table->foreign('lead_refund_id')->references('id')->on('lead_refunds')->noActionOnDelete();

            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('lead_refund_item_refunds');
    }
};
