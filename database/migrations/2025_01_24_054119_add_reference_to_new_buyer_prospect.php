<?php

use App\Models\Prospects\NewBuyerProspect;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('new_buyer_prospects', function (Blueprint $table) {
            $table->string('reference')->nullable()->after(NewBuyerProspect::FIELD_ID);
        });
    }
};
