<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('pricing_margins', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('industry_service_id');
            $table->float('exclusive_margin');
            $table->float('duo_margin');
            $table->float('trio_margin');
            $table->float('quad_margin');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('pricing_margins');
    }
};
