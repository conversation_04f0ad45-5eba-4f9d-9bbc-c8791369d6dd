<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('opportunity_notification_configs', function (Blueprint $table) {
            $table->dropColumn('frequency');
            $table->dropColumn('product_type');
            $table->dropColumn('content');
            $table->dropForeign(['email_template_id']);
            $table->dropColumn('email_template_id');
            $table->dropColumn('last_sent_at');
            $table->dropColumn('send_day');
            $table->dropColumn('activated_at');

            $table->string('attempt_on_days')->default('1,4');
            $table->unsignedTinyInteger('maximum_send_frequency')->default(3);
            $table->timestamp('expires_at');
            $table->boolean('active')->default(false);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('opportunity_notification_configs', function (Blueprint $table) {
            $table->string('frequency');
            $table->integer('product_type');
            $table->longText('content');
            $table->unsignedSmallInteger('email_template_id');
            $table->timestamp('last_sent_at');
            $table->string('send_day');
            $table->timestamp('activated_at');

            $table->dropColumn('attempt_on_days');
            $table->dropColumn('maximum_send_frequency');
            $table->dropColumn('expires_at');
            $table->dropColumn('active');
        });
    }
};
