<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('relationship_managers', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->dateTime('active_from')->useCurrent();
            $table->dateTime('active_to')->nullable();
            $table->timestamps();

            $table->foreign('user_id')->references('id')->on('users');
        });

        Schema::table('companies', function (Blueprint $table) {
            $table->unsignedBigInteger('relationship_manager_id')->nullable();

            $table->foreign('relationship_manager_id')->references('id')->on('relationship_managers');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('companies', function (Blueprint $table) {
            $table->dropForeign('companies_relationship_manager_id_foreign');
            $table->dropColumn('relationship_manager_id');
        });
        Schema::dropIfExists('relationship_managers');
    }
};
