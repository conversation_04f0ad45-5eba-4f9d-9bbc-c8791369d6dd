<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('marketing_campaign_consumers', function (Blueprint $table) {
            $table->unsignedBigInteger('cloned_consumer_product_id')->nullable();
            $table->foreign('cloned_consumer_product_id')->references('id')->on('consumer_products');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('marketing_campaign_consumers', function (Blueprint $table) {
            $table->dropForeign('marketing_campaign_consumers_cloned_consumer_product_id_foreign');
            $table->dropColumn('cloned_consumer_product_id');
        });
    }
};
