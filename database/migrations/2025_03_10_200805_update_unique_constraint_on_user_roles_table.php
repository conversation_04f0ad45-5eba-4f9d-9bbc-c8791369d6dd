<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_roles', function (Blueprint $table) {
            $table->dropUnique(['user_id', 'role']);

            $table->string('unique_user_role')->virtualAs("CONCAT(user_id, '#', role, '#', IF(deleted_at IS NULL, '-', deleted_at))");
            $table->unique('unique_user_role');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
