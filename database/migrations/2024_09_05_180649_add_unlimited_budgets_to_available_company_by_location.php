<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('available_company_by_locations', function (Blueprint $table) {
            $table->unsignedSmallInteger('unlimited_budgets')->default(0)->after('industry_slug');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('available_company_by_locations', function (Blueprint $table) {
            $table->dropColumn(['unlimited_budgets']);
        });
    }
};
