<?php

namespace Database\Factories;

use App\Models\Phone;
use App\Models\User;
use App\Models\UserPhone;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Support\Carbon;

class UserPhoneFactory extends Factory
{
    protected $model = UserPhone::class;

    public function definition(): array
    {
        return [
            'phone_id' => Phone::factory(),
            'user_id' => User::factory(),
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ];
    }
}
