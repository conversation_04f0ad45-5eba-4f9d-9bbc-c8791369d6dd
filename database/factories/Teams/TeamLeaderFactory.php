<?php

namespace Database\Factories\Teams;

use App\Models\Teams\TeamLeader;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<TeamLeader>
 */
class TeamLeaderFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            TeamLeader::FIELD_TITLE     => $this->faker->jobTitle(),
            TeamLeader::FIELD_USER_ID   => $this->faker->numberBetween(1, 19),
            TeamLeader::FIELD_TEAM_ID   => $this->faker->numberBetween(1, 19),
            TeamLeader::FIELD_REPORTS_TO => $this->faker->numberBetween(1, 19),
        ];
    }
}
