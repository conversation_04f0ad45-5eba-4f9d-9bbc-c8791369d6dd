<?php

namespace Database\Factories\Odin;

use App\Models\Odin\ProductCampaignSchedule;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends \Illuminate\Database\Eloquent\Factories\Factory<\App\Models\ProductCampaignBudget>
 */
class ProductCampaignScheduleFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition()
    {
        return [
            ProductCampaignSchedule::FIELD_SCHEDULE_ID => 1,
            ProductCampaignSchedule::FIELD_PRODUCT_CAMPAIGN_ID => 1
        ];
    }
}
