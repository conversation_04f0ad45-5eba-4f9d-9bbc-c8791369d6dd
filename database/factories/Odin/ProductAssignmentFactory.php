<?php

namespace Database\Factories\Odin;

use App\Abstracts\ResetUniqueFactory;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductRejection;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProductAssignmentFactory extends ResetUniqueFactory
{
    /** @inheritDoc */
    public function getData(bool $reset = false): array
    {
        $cost = [
            '19.95',
            '29.95',
            '39.95',
            '99.95',
        ];

        return [
            ProductAssignment::FIELD_COMPANY_ID => $this->faker->unique($reset)->randomNumber(6), //Specify ID at creation
            ProductAssignment::FIELD_CONSUMER_PRODUCT_ID => ConsumerProduct::factory(), //Specify ID at creation
            ProductAssignment::FIELD_COST => $this->faker->randomElement($cost),
            ProductAssignment::FIELD_CHARGEABLE => $this->faker->boolean(90),
            ProductAssignment::FIELD_DELIVERED => $this->faker->boolean(90),
            ProductAssignment::FIELD_EXCLUDE_BUDGET => $this->faker->boolean(20),
            ProductAssignment::FIELD_SALE_TYPE_ID => $this->faker->numberBetween(1, 4),
            ProductAssignment::FIELD_DELIVERED_AT => $this->faker->unique($reset)->unixTime(),
            ProductAssignment::FIELD_REJECTION_EXPIRY => Carbon::now()->addMonth()->timestamp,
            ProductAssignment::FIELD_PAYLOAD => [

            ]
        ];
    }

    /**
     * Configure the model factory.
     * @return Factory
     */
    public function configure(): Factory
    {
        return $this->afterCreating(function(ProductAssignment $assignment) {
            if ($assignment->{ProductAssignment::FIELD_REJECTION_EXPIRY}->year > 2000) {
                ProductRejection::factory()
                    ->count(1)
                    ->for($assignment)
                    ->create([
                        ProductRejection::FIELD_COMPANY_USER_ID => $assignment->{ProductAssignment::RELATION_COMPANY}
                                ?->users()
                                ->first()
                                ?->{CompanyUser::FIELD_ID}
                            ?? $this->faker->numberBetween(1, 19)
                    ]);
            }
        });
    }

    public function chargeableAndDelivered(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                ProductAssignment::FIELD_CHARGEABLE => true,
                ProductAssignment::FIELD_DELIVERED => true
            ];
        });
    }

    public function notRejected(): Factory
    {
        return $this->state(function (array $attributes) {
            return [
                ProductAssignment::FIELD_REJECTION_EXPIRY => Carbon::now()->subYears(200)->timestamp,
            ];
        });
    }
}
