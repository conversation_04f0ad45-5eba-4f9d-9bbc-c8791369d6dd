<?php

namespace Database\Factories\Prospects;

use App\Enums\Odin\StateAbbreviation;
use App\Enums\Prospects\ProspectSource;
use App\Enums\Prospects\ProspectStatus;
use App\Models\Prospects\NewBuyerProspect;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<NewBuyerProspect>
 */
class NewBuyerProspectFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            'reference' => fake()->uuid(),
            'external_reference' => fake()->uuid(),
            'user_id' => User::factory()->createQuietly(),
            'company_id' => fake()->randomNumber(), // TODO: Fix this due to company factory issues
            'status' => ProspectStatus::INITIAL,
            'industry_service_ids' => '',
            'source' => ProspectSource::SALESINTEL,
            'source_data' => '',
            'company_name' => fake()->company(),
            'company_website' => fake()->url(),
            'company_description' => fake()->sentence(),
            'company_phone' => fake()->phoneNumber(),
            'address_street' => fake()->streetAddress(),
            'address_city_key' => fake()->city(),
            'address_state_abbr' => fake()->randomElement(StateAbbreviation::cases()),
            'decision_maker_first_name' => fake()->firstName(),
            'decision_maker_last_name' => fake()->lastName(),
            'decision_maker_email' => fake()->email(),
            'decision_maker_phone' => fake()->phoneNumber(),
            'notes' => fake()->sentences(3, true),
            'ordinal_value' => fake()->randomNumber(3),
        ];
    }

    private function sourcedFrom(ProspectSource $source): Factory
    {
        return $this->state(fn (array $attributes) => ['source' => $source]);
    }

    public function sourcedFromAList(): Factory
    {
        return $this->sourcedFrom(ProspectSource::ALIST);
    }

    public function sourcedFromRegistration(): Factory
    {
        return $this->sourcedFrom(ProspectSource::REGISTRATION);
    }

    public function sourcedFromSalesIntel(): Factory
    {
        return $this->sourcedFrom(ProspectSource::SALESINTEL);
    }
}
