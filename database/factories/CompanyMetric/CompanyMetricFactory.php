<?php

namespace Database\Factories\CompanyMetric;

use App\Enums\CompanyMetrics\CompanyMetricRequestTypes;
use App\Enums\CompanyMetrics\CompanyMetricSources;
use App\Models\CompanyMetric;
use App\Models\Odin\Company;
use Carbon\Carbon;
use GuzzleHttp\Exception\GuzzleException;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyMetric>
 */
class CompanyMetricFactory extends Factory
{
    /**
     * The name of the factory's corresponding model.
     *
     * @var  string
     */
    protected $model = CompanyMetric::class;

    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     * @throws GuzzleException
     */
    public function definition(): array
    {
        $company = $this->faker->randomElement(Company::all());
        $source = $this->faker->randomElement(CompanyMetricSources::cases());

        $companyMetricResponse = CompanyMetricDummyResponseFactory::make($source);

        //get the latest complete month
        $currentDate = Carbon::now();
        $startDate = $currentDate->subMonth()->startOfMonth()->toDateTime();
        $endDate = $currentDate->subMonth()->endOfMonth()->toDateTime();

        return [
            CompanyMetric::FIELD_COMPANY_ID         => $company->{Company::FIELD_ID},
            CompanyMetric::FIELD_SOURCE             => $source,
            CompanyMetric::FIELD_REQUEST_TYPE       => $this->faker->randomElement(CompanyMetricRequestTypes::class),
            CompanyMetric::FIELD_REQUEST_URL        => $this->faker->url(),
            CompanyMetric::FIELD_REQUEST_RESPONSE   => $companyMetricResponse->getCompanyMetrics($company, $startDate, $endDate)->toArray(),
        ];
    }
}
