<?php

namespace Database\Factories\ConsumerReviews;

use App\Models\ConsumerReviews\CompanyRating;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<CompanyRating>
 */
class CompanyRatingFactory extends Factory
{
    /**
     * Define the model's default state.
     *
     * @return array<string, mixed>
     */
    public function definition(): array
    {
        return [
            CompanyRating::FIELD_COMPANY_ID           => fake()->randomNumber(),
            CompanyRating::FIELD_OVERALL_SCORE        => fake()->randomNumber(),
            CompanyRating::FIELD_NUM_REVIEWS          => fake()->randomNumber(),
            CompanyRating::FIELD_COMPANY_LOCATION_ID  => fake()->randomNumber(),
            CompanyRating::FIELD_CREATED_AT           => fake()->randomNumber(),
            CompanyRating::FIELD_DATA                 => [
                CompanyRating::FIELD_DATA_COUNT_BREAKDOWN => fake()->randomNumber(),
            ],
        ];
    }
}
