<?php

namespace Database\Factories\Conference;

use App\Models\Calendar\CalendarEvent;
use App\Models\Conference\Conference;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ConferenceFactory extends Factory
{
    public function definition(): array
    {
        return [
            'calendar_event_id' => CalendarEvent::factory(),
            'user_id' => User::factory(),
            'external_id' => $this->faker->randomNumber(),
            'start_time' => $this->faker->dateTime(),
            'end_time' => $this->faker->dateTime(),
            'expire_time' => $this->faker->dateTime(),
            'duration_in_seconds' => $this->faker->randomNumber(),
            'status' => 'active'
        ];
    }
}
