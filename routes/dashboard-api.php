<?php

use App\Http\Controllers\DashboardAPI\AuthenticationController;
use App\Http\Controllers\DashboardAPI\CampaignPriceController;
use App\Http\Controllers\DashboardAPI\V4\CampaignAlertController;
use App\Http\Controllers\DashboardAPI\V4\CompanyBillingControllerV4;
use App\Http\Controllers\DashboardAPI\ConsumerReviewsDashboardController;
use App\Http\Controllers\DashboardAPI\V4\CampaignPriceController as CampaignPriceControllerV4;
use App\Http\Controllers\DashboardAPI\CompanyController;
use App\Http\Controllers\DashboardAPI\V4\ProductAssignmentController;
use App\Http\Controllers\DashboardAPI\V4\StatisticsController;
use App\Http\Middleware\EnsureCompanyUserBelongsToCompany;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\DashboardAPI\ProfitabilityAssumptionController;
use App\Http\Controllers\DashboardAPI\ReferenceDataController;
use App\Http\Controllers\DashboardAPI\CompanyLocationController;
use App\Http\Controllers\DashboardAPI\CompanyUserController;
use App\Http\Controllers\DashboardAPI\NotificationSettingsController;
use App\Http\Controllers\DashboardAPI\CampaignController;
use App\Http\Controllers\DashboardAPI\ProductPricingController;
use App\Http\Controllers\DashboardAPI\ScheduleController;
use \App\Http\Controllers\DashboardAPI\V4\CompanyCampaignController;
use App\Http\Controllers\Watchdog\VideoUrlFromProductAssignmentController;

/*
|--------------------------------------------------------------------------
| Dashboard API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for the dashboard. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group.
|
| v3 routes are the initial routes used by Fixr MI Dashboard
|   - these routes all come through Admin2.0 first
|   - these routes can use the integration API for legacy-reliant tasks, e.g. all things campaign-related
|
| v4 routes are used initially for the Campaigns upgrade, followed by anything that still relies on legacy
|   - these routes should not rely on legacy integration
|   - all v4 data is Admin2.0 driven, the only interaction with legacy should be syncing back (if needed)
|
*/

Route::prefix('/v3')->name('dashboard-api.v3.')->group(function() {
    Route::post('/login', [AuthenticationController::class, 'login']);
    Route::post('/login-with-token', [AuthenticationController::class, 'loginWithToken']);
    Route::post('/reset-password', [AuthenticationController::class, 'resetPassword']);
    Route::post('/update-password', [AuthenticationController::class, 'updatePassword']);

    Route::middleware(['dashboard-auth'])->group(function() {
        Route::prefix('/users')->controller(\App\Http\Controllers\DashboardAPI\UserController::class)->group(function() {
            Route::get('/me', 'me');
            Route::get('/contract', 'contract');
        });

        Route::prefix('/companies')->name('companies.')->controller(\App\Http\Controllers\DashboardAPI\CompanyController::class)->group(function() {
            Route::prefix('/{companyId}')->group(function() {
                Route::get('/', 'get')->name('show');
                Route::get('/profile-progress', 'getProfileProgress');
                Route::patch('/industry-services', 'updateCompanyIndustryServices');

                Route::get('/company-contract-signed', 'getCompanyContractForCompany');
                Route::post('/company-contract-new', 'getNewContractForCompany');
                Route::post('/company-contract-agreed', 'agreeToContractForCompany');

                Route::post('/campaigns-zip-codes', 'App\Http\Controllers\DashboardAPI\CampaignController@getZipCodesForCampaigns');

                Route::prefix('/consumer-reviews')->controller(ConsumerReviewsDashboardController::class)->group(function() {
                    Route::get('/', 'getAllConsumerReviews');
                    Route::get('/recent', 'getRecentConsumerReviews');
                    Route::get('/rating', 'getConsumerReviewRating');
                    Route::post('/{review:uuid}/reply', 'postConsumerReviewReply');
                });

                Route::prefix('/profile')->controller(\App\Http\Controllers\DashboardAPI\CompanyProfileController::class)->group(function() {
                    Route::get('/logo', 'getCompanyLogo');
                    Route::post('/logo', 'updateCompanyLogo');
                    Route::patch('/update-basic-info', 'updateBasicInfo');
                    Route::get('/media-assets', 'getCompanyMediaAssets');
                    Route::post('/upload-media-assets', 'uploadCompanyMediaAssets');
                    Route::post('/youtube-asset', 'addCompanyYoutubeAsset');
                    Route::delete('/media-asset/{assetId}', 'deleteCompanyMediaAsset');

                    Route::get('/licenses', 'getCompanyLicenses');
                    Route::post('/licenses', 'createCompanyLicense');
                    Route::patch('/licenses/{licenseId}', 'updateCompanyLicense');
                    Route::delete('/licenses/{licenseId}', 'deleteCompanyLicense');
                });

                Route::prefix('/company-locations')->controller(CompanyLocationController::class)->group(function() {
                    Route::get('/', 'getCompanyLocations');
                    Route::post('/', 'createCompanyLocation');
                    Route::patch('/{locationId}', 'updateCompanyLocation');
                    Route::delete('/{locationId}', 'deleteCompanyLocation');
                    Route::patch('/make-primary/{locationId}', 'makeCompanyLocationPrimary');
                });

                Route::prefix('/users')->controller(CompanyUserController::class)->group(function() {
                    Route::get('/', 'getCompanyUsers');
                    Route::post('/', 'createCompanyUser');
                    Route::patch('/{companyUserId}', 'updateCompanyUser');
                    Route::delete('/{companyUserId}', 'deleteCompanyUser');
                });

                Route::prefix('/notification-settings')->controller(NotificationSettingsController::class)->group(function() {
                    Route::get('/', 'getNotificationSettings');
                    Route::put('/', 'updateNotificationSettings');
                });

                Route::prefix('/billing')->controller(\App\Http\Controllers\DashboardAPI\CompanyBillingController::class)->group(function() {
                    Route::prefix('/payment-methods')->group(function() {
                        Route::get('/', 'getPaymentMethods');
                        Route::put('/', 'addPaymentMethod');

                        Route::prefix("/{cardId}")->group(function () {
                            Route::delete('/', 'deletePaymentMethod');
                            Route::post('/make-primary', 'makePaymentMethodPrimary');
                        });
                    });

                    Route::prefix('/invoices')->group(function() {
                        Route::get('/', 'searchInvoices');
                        Route::get('/{invoiceId}/download', 'downloadInvoice');
                        Route::get('/{invoiceId}/transactions', 'getTransactionsForInvoice');
                        Route::post('/{invoiceId}/pay-now', 'payInvoiceNow');
                    });
                });

                Route::prefix('/{industry}')->group(function() {
                    // Industry scope
                     Route::prefix('/{service}')->group(function() {
                         // IndustryService scope
                         Route::prefix('/prices')->controller(ProductPricingController::class)->group(function() {
                             Route::get('/campaign-price-range', 'getPriceRangeForCampaign');
                             Route::post('/zipcode-price-range', 'getPriceRangeForZipCodes');
                         });

                         Route::prefix('/profitability-assumption')->controller(ProfitabilityAssumptionController::class)->group(function() {
                             Route::get('/', 'getProfitabilityAssumption');
                             Route::get('/global', 'getGlobalProfitabilityAssumptions');
                             Route::put('/update', 'updateProfitabilityAssumption');
                         });

                         Route::prefix('/products')->controller(\App\Http\Controllers\DashboardAPI\ProductController::class)->group(function() {
                             Route::get('/volume', 'getVolume');
                             Route::get('/missed-reasons', 'getMissedReasons');
                             Route::get('/location-statistics', 'getProductStatisticsByLocation');
                             Route::get('/{productType}/get', 'getConsumerProducts');

                             Route::prefix('/legacy/{legacyId}')->group(function() {
                                 Route::get('/check-rejection-quota', 'checkRejectionQuota');
                                 Route::patch('/reject-lead', 'rejectLead');
                                 Route::patch('/unreject-lead', 'unrejectLead');
                             });

                             Route::patch('appointments/{appointmentId}/reject', 'rejectAppointment');
                         });

                         Route::prefix('/campaigns')->name('campaigns.')->controller(CampaignController::class)->group(function() {
                             Route::get('/overview', 'getOverview');
                             Route::get('/', 'getCampaigns');
                             Route::post('/create', 'createCampaign');
                             Route::patch('/bulk-update', 'bulkUpdateCampaigns');

                             Route::prefix('/{campaignUuid}')->group(function() {
                                 Route::patch('/update', 'updateCampaign');
                                 Route::delete('/delete', 'deleteCampaign');
                                 Route::get('/', 'getCampaignDetail');

                                 Route::delete('/contact-delivery/{leadDeliveryMethodId}', 'deleteContactDelivery');

                                 Route::prefix('/prices')->name('prices.')->controller(CampaignPriceController::class)->group(function () {
                                     Route::get('/state', 'getStatePrices');
                                     Route::get('/county', 'getCountyPrices');
                                     Route::patch('/bid-state', 'bidStatePrice')->name('bid-state');
                                     Route::patch('/bid-county', 'bidCountyPrice')->name('bid-county');
                                 });

                             });
                         });
                     });
                });

                Route::prefix('/crm-deliveries')->controller(CampaignController::class)->group(function() {
                    Route::post('/create', 'createCrmDeliveryForCampaign');
                    Route::get('/delivery/{crmDeliveryId}', 'getCrmDeliveryDetail');
                    Route::patch('/delivery/{crmDeliveryId}', 'updateCrmDelivery');
                    Route::delete('/delivery/{campaignUuid}/{crmDeliveryId}', 'deleteCrmDelivery');
                });

                Route::prefix('/schedules')->controller(ScheduleController::class)->group(function() {
                    Route::get('/all', 'getAllCompanySchedules');
                    Route::post('/create-static-calendar', 'createStaticCalendar');
                    Route::patch('/update-schedule', 'updateSchedule');
                    Route::delete('/delete-schedule', 'deleteSchedule');
                    Route::get('/events', 'getCalendarEventsInDateRange');
                });
            });
        });

        Route::prefix('/reference')->controller(ReferenceDataController::class)->group(function() {
            Route::prefix('/locality-data')->group(function() {
                Route::get('/countries', 'getCountries');
                Route::get('/states', 'getStatesWithTotals');
                Route::get('/states-and-timezones', 'getStatesAndTimezones');
                Route::get('/state/{stateKey}', 'getStateDetail');
                Route::get('/state/{stateKey}/county/{countyId}', 'getCountyDetail');
                Route::get('/county-zip-codes', 'getCountyZipCodesByZipCodes');
                Route::get('/state-zip-codes/{stateKey}', 'getAllZipCodesInState');
                Route::get('/zip-codes-radius/{zipCode}/{radius}', 'getZipCodesByRadius');
                Route::post('/zip-codes-by-string', 'getZipCodesByZipCodeStrings');

            });

            Route::get('/timezones', 'getTimezones');

            Route::get('/industry-services', 'getServicesByIndustry');
        });

        Route::get('/{productAssignment}/watchdog', VideoUrlFromProductAssignmentController::class)
            ->name('playback.watchdog');
    });
});

Route::name('dashboard-api.v4.')->prefix('/v4')->group(function () {
    Route::name('products.')->prefix('/products')->middleware(['dashboard-auth'])->group(function () {
        Route::get('/configurations', [CompanyCampaignController::class, 'getProductConfigurations']);

        Route::prefix('/{productKey}')->group(function () {
            Route::name('companies.')->prefix('/companies')->controller(CompanyController::class)->group(function() {
                Route::prefix('/{companyId}')->group(function() {
                    Route::prefix('/{industry}')->group(function() {
                        Route::prefix('/{service}')->group(function() {
                            Route::prefix('/campaigns')->controller(CompanyCampaignController::class)->group(function() {
                                Route::get('/', 'getCampaigns');
                                Route::get('/get-zip-codes', 'getZipCodes');
                                Route::get('/module-configurations', 'getModuleConfigurations');
                                Route::get('/crm-configurations', 'getCRMConfigurations');
                                Route::patch('/pause', 'pauseCampaigns');
                                Route::patch('/unpause', 'unpauseCampaigns');
                                Route::post('/validate-targeted-zip-codes', 'validateTargetedZipCodes');
                                Route::get('/new-campaign-config', 'getNewCampaignConfiguration');
                                Route::get('/campaign-statistics', 'getCampaignStatistics');

                                Route::prefix('/prices')->controller(CampaignPriceControllerV4::class)->group(function () {
                                    Route::get('/state-floor', 'getStateFloorPrices');
                                    Route::get('/county-floor', 'getCountyFloorPrices');
                                    Route::post('/zip-code-price-range', 'getPriceRangeForZipCodes');

                                    Route::prefix('/{campaignReference}')->group(function () {
                                        Route::get('/state', 'getStatePrices');
                                        Route::get('/county', 'getCountyPrices');
                                    });
                                });

                                Route::prefix('/crm')->group(function() {
                                    Route::post('/execute-method', 'executeCrmMethod');
                                    Route::get('/import-options', 'getCrmImportOptions');
                                    Route::put('/save-template', 'saveCrmTemplate');
                                    Route::delete('/delete-template', 'deleteCrmTemplate');
                                });

                                Route::prefix('/{campaignReference}')->group(function() {
                                    Route::get('/', 'getCampaignDetail');
                                    Route::post('/save', 'saveCampaign');
                                    Route::delete('/delete', 'deleteCampaign');

                                    Route::prefix('/alerts')->controller(CampaignAlertController::class)->group(function () {
                                        Route::post('/', 'createAlert');
                                        Route::patch('/{alertId}', 'updateAlert');
                                        Route::delete('/{alertId}', 'deleteAlert');
                                        Route::patch('/{alertId}/toggle-active', 'toggleActive');
                                    });
                                });
                            });

                            Route::name('product-assignments.')->prefix('/product-assignments')->controller(ProductAssignmentController::class)->group(function() {
                                Route::get('/search', 'search')->name('search');
                                Route::patch('/reject', 'reject');
                                Route::patch('/unreject', 'unreject');
                                Route::get('/check-rejection-quota', 'rejectionWillExceedThreshold');
                            });

                            Route::prefix('/product-statistics')->controller(StatisticsController::class)->group(function () {
                                Route::get('/volume','getVolumeStatistics');
                                Route::get('/profitability', 'getProfitabilityStatistics');
                                Route::get('/purchased-breakdown', 'getPurchasedBreakdown');
                                Route::get('/location-statistics', 'getLocationStatistics');
                                Route::get('/default-analytics', 'getDefaultAnalyticsStatistics');
                            });

                            Route::prefix('billing')->controller(CompanyBillingControllerV4::class)->middleware(EnsureCompanyUserBelongsToCompany::class)->group(function () {
                                Route::get('/', 'getCompanyInvoices');
                                Route::patch('/pay', 'payInvoice');
                                Route::get('/summary', 'getBillingSummary');
                                Route::prefix('/payment-methods')->group(function() {
                                    Route::post('/', 'initialiseCustomerBilling');
                                    Route::get('/', 'getPaymentMethods');
                                    Route::prefix("/{cardId}")->group(function () {
                                        Route::delete('/', 'deletePaymentMethod');
                                        Route::post('/make-primary', 'makePaymentMethodPrimary');
                                    });
                                });
                            });
                        });
                    });
                });
            });
        });
    });
});
