<?php

use App\Enums\PermissionType;
use App\Http\Controllers\API\ActivityLogController;
use App\Http\Controllers\API\Advertising\AdvertisingAPIController;
use App\Http\Controllers\API\Affiliates\AffiliateUserController;
use App\Http\Controllers\API\Affiliates\Internal\CampaignController;
use App\Http\Controllers\API\Affiliates\Internal\CampaignLeadDetailsController;
use App\Http\Controllers\API\Affiliates\Internal\StatisticsController;
use App\Http\Controllers\API\Affiliates\PayoutStrategyController;
use App\Http\Controllers\API\Affiliates\Internal\AffiliateController;
use App\Http\Controllers\API\BudgetController;
use App\Http\Controllers\API\CompanyEmailController;
use App\Http\Controllers\API\CompanyMergeController;
use App\Http\Controllers\API\BundleManagement\v2\BundleInvoiceApiControllerV2;
use App\Http\Controllers\API\CompanyUserRelationship\CompanyUserRelationshipController;
use App\Http\Controllers\API\ReviewsAdminController;
use App\Http\Controllers\API\BundleManagement\BundleApiController;
use App\Http\Controllers\API\BundleManagement\BundleInvoiceApiController;
use App\Http\Controllers\API\BundleManagement\BundleInvoiceSearchController;
use App\Http\Controllers\API\BundleManagement\BundleSearchController;
use App\Http\Controllers\API\CallLogController;
use App\Http\Controllers\API\Communication\CommunicationApiController;
use App\Http\Controllers\API\CompaniesController;
use App\Http\Controllers\API\CompaniesServicingAreaController;
use App\Http\Controllers\API\CompanyActivityController;
use App\Http\Controllers\API\CompanyReviews\CompanyReviewsController;
use App\Http\Controllers\API\CompanySearchController;
use App\Http\Controllers\API\CompanyUsers\CompanyUsersController;
use App\Http\Controllers\API\CompanyZipCodeTargetingExceptionsController;
use App\Http\Controllers\API\ConsumerSearchController;
use App\Http\Controllers\API\ContractManagementController;
use App\Http\Controllers\API\CountyCoverageReportController;
use App\Http\Controllers\API\EmailTemplates\EmailTemplatesAPIController;
use App\Http\Controllers\API\EstimatedRevenuePerLeadByLocation\EstimatedRevenuePerLeadByLocationController;
use App\Http\Controllers\API\ExpertReviewsController;
use App\Http\Controllers\API\ExternalAuthController;
use App\Http\Controllers\API\ExternalSearch\GooglePlacesController;
use App\Http\Controllers\API\FloorPricing\FloorPriceSuggestionsController;
use App\Http\Controllers\API\FloorPricing\FloorPricingController;
use App\Http\Controllers\API\GlobalConfigurationsManagementController;
use App\Http\Controllers\API\HistoricalAvailableBudget\HistoricalAvailableBudgetController;
use App\Http\Controllers\API\IndustryManagement\CompaniesController as IndustryCompaniesController;
use App\Http\Controllers\API\IndustryManagement\ConfigurableFieldsController;
use App\Http\Controllers\API\IndustryManagement\IndustryManagementAPIController;
use App\Http\Controllers\API\IndustryManagement\ProductController;
use App\Http\Controllers\API\IndustryManagement\WebsiteController;
use App\Http\Controllers\API\LeadProcessing\Communication\CommunicationBaseController;
use App\Http\Controllers\API\LeadProcessing\ConsumerController\ConsumerProductApiController;
use App\Http\Controllers\API\LeadProcessing\LeadProcessingReportApiController;
use App\Http\Controllers\API\LeadProcessing\Management\ManagementBaseController;
use App\Http\Controllers\API\LeadProcessing\Notification\NotificationController;
use App\Http\Controllers\API\LeadProcessing\Processing\ProcessingBaseController;
use App\Http\Controllers\API\LeadsController;
use App\Http\Controllers\API\LeadsReportController;
use App\Http\Controllers\API\MissedRevenueByLocationController;
use App\Http\Controllers\API\NonPurchasingServiceAreaController;
use App\Http\Controllers\API\OpportunityNotifications\OpportunityNotificationsApiController;
use App\Http\Controllers\API\OpportunityNotifications\OpportunityNotificationsConfigApiController;
use App\Http\Controllers\API\OptInNameController;
use App\Http\Controllers\API\OutreachCadence\CadenceRoutineConfigurationController;
use App\Http\Controllers\API\OutreachCadence\CadenceTemplateController;
use App\Http\Controllers\API\OutreachCadence\CompanyCadenceRoutineController;
use App\Http\Controllers\API\PrivacyManagement\PrivacyManagementController;
use App\Http\Controllers\API\ProductProcessing\Processing\ProductProcessingAPIController;
use App\Http\Controllers\API\ProfitabilitySimulatorController;
use App\Http\Controllers\API\ReferenceLists\ReferenceListController;
use App\Http\Controllers\API\RolesPermissionsManagementController;
use App\Http\Controllers\API\Rulesets\RulesetsApiController;
use App\Http\Controllers\API\Sales\SalesManagementAPIController;
use App\Http\Controllers\API\SalesBaitManagement\SalesBaitManagementController;
use App\Http\Controllers\API\SearchController;
use App\Http\Controllers\API\TemplateManagementController;
use App\Http\Controllers\API\Territory\CustomerSuccessManagerController;
use App\Http\Controllers\API\TestLeadApiController;
use App\Http\Controllers\API\TinymceEditorController;
use App\Http\Controllers\API\UserPresetController;
use App\Http\Controllers\API\Users\Management\UserManagementAPIController;
use App\Http\Controllers\API\Users\UserBaseAPIController;
use App\Http\Controllers\API\Users\UserSettingsController;
use App\Http\Controllers\API\Workflows\TaskController;
use App\Http\Controllers\API\Workflows\TaskManagementController;
use App\Http\Controllers\Companies\CompanyDeleteController;
use App\Http\Controllers\CompanyCampaign\CompanyCampaignApiController;
use App\Http\Controllers\CompanyCampaignContactDeliveryLogController;
use App\Http\Controllers\CompanyCampaignCustomPricingLogController;
use App\Http\Controllers\CompanyCampaignDeliveryLogController;
use App\Http\Controllers\CompanyCampaignScheduleController;
use App\Http\Controllers\CompanyCampaignWizardController;
use App\Http\Controllers\CompanyConfigurationController;
use App\Http\Controllers\API\CompanyManagerAssignmentRequestController;
use App\Http\Controllers\API\Dashboard\InactiveCampaignsController;
use App\Http\Controllers\CompanyMetrics\CompanyMetricsController;
use App\Http\Controllers\CompanyQualityScoreConfigurationController;
use App\Http\Controllers\CompanyRoleHistoryController;
use App\Http\Controllers\CompanyUserController;
use App\Http\Controllers\ContactIdentificationController;
use App\Http\Controllers\ContractorProfileController;
use App\Http\Controllers\CRMModuleController;
use App\Http\Controllers\DashboardAPI\ReferenceDataController;
use App\Http\Controllers\DashboardAPI\V4\CampaignPriceController;
use App\Http\Controllers\FlowManagementProxyController;
use App\Http\Controllers\LeadRefundController;
use App\Http\Controllers\GoogleServiceController;
use App\Http\Controllers\LocationController;
use App\Http\Controllers\MarketingCampaign\MarketingCampaignConsumerController;
use App\Http\Controllers\MarketingCampaign\MarketingCampaignController;
use App\Http\Controllers\MarketingCampaign\MarketingCampaignLogsController;
use App\Http\Controllers\MarketingCampaign\MarketingDomainController;
use App\Http\Controllers\NeedLoveCompaniesController;
use App\Http\Controllers\NoteController;
use App\Http\Controllers\Odin\API\ConsumerFieldModuleVisibilityController;
use App\Http\Controllers\Odin\API\MailboxUserEmailController;
use App\Http\Controllers\Odin\ResourceAPI\CompanyQualityScoreRuleController;
use App\Http\Controllers\Odin\ResourceAPI\PhoneController;
use App\Http\Controllers\Prospects\ProspectingAPIController;
use App\Http\Controllers\SalesOverviewController;
use App\Http\Controllers\SiloManagementController;
use App\Http\Controllers\TagController;
use App\Http\Controllers\TeamManagementController;
use App\Http\Controllers\Watchdog\VideoUrlFromConsumerProductController;
use App\Http\EmailController;
use App\Http\Middleware\PiiDataLogger;
use Illuminate\Support\Facades\Route;
use App\Http\Controllers\Prospects\ProspectingGlobalConfigurationController;
use App\Http\Controllers\Prospects\ProspectingUserConfigurationController;
use App\Http\Controllers\Prospects\ProspectingUsersController;

/*
|--------------------------------------------------------------------------
| API Routes
|--------------------------------------------------------------------------
|
| Here is where you can register API routes for your application. These
| routes are loaded by the RouteServiceProvider within a group which
| is assigned the "api" middleware group. Enjoy building your API!
|
*/

Route::name('internal-api.v1.')->prefix('/v1')->group(function () {
    Route::prefix('/tags')->group(function () {
        Route::get('/list', [TagController::class, 'list']);
        Route::get('/all', [TagController::class, 'getAll']);
    });
    Route::prefix('/lead-processing/notifications')->controller(NotificationController::class)->group(function () {
        Route::post('/auth', 'authenticate');
        Route::get('/', 'getRecentNotificationsNextPaginated');
        Route::patch('/{id}', 'markAsRead');
        Route::post('/read-all', 'markAllNotificationsAsRead');
    });

    Route::prefix("/search")->controller(SearchController::class)->group(function () {
        Route::get("/consumer-products", "searchConsumerProducts");
        Route::get("/company-campaigns/{companyId}", "searchCompanyCampaigns");
        Route::get("/{query}", "search");
    });

    // TODO: Investigate permissions for this route
    Route::prefix('/lead-processing')->group(function () {
        Route::prefix('/leads')->controller(ProcessingBaseController::class)->group(function () {
            Route::get('/{id}/basic', 'getLeadBasicInfo')->where('id', '[0-9]+');
            Route::get('/{id}/{service}/verification', 'getLeadVerification')->where('id', '[0-9]+');
            Route::get('/{id}/related-activity', 'getLeadRelatedActivity')->where('id', '[0-9]+');
        });
    });

    Route::prefix('/emails/{emailTemplateType}')->controller(EmailController::class)->group(function () {
        Route::get('/email-content', 'getEmailContent');
        Route::post('/save-email-template', 'saveEmailTemplate');
        Route::post('/send','sendEmail')->middleware('protect_from_impersonation:throw_exception');
        Route::post('/preview', 'getEmailPreview');
    });

    Route::prefix("/missed-revenue")->controller(MissedRevenueByLocationController::class)->group(function () {
        Route::get("/", "getTopCountiesInIndustry");
    });

    Route::name('lead-processing.')->prefix('/lead-processing')->middleware('permission:lead-processing')->group(function () {
        Route::prefix('/management')->controller(ManagementBaseController::class)->middleware('permission:lead-processing-management')->group(function () {
            // Routes that pertain to the management of the lead processing screen.
            Route::get('/queues', 'getQueues');
            Route::post('/queues', 'createQueue');
            Route::get('/queues/{id}', 'getQueue');
            Route::put('/queues/{id}', 'updateQueue');
            Route::delete('/queues/{id}', 'deleteQueue');
            Route::get('/queues/{leadProcessingQueueConfiguration}/alerts', 'getAlerts');
            Route::post('/queues/{leadProcessingQueueConfiguration}/alerts', 'createAlert');
            Route::patch('/queues/{leadProcessingQueueConfiguration}/alerts/{alert}', 'updateAlert');
            Route::delete('/queues/{leadProcessingQueueConfiguration}/alerts/{alert}', 'deleteAlert');

            Route::get('/teams', 'getTeams');
            Route::post('/teams', 'createTeam');
            Route::get('/teams/{id}', 'getTeam');
            Route::put('/teams/{id}', 'updateTeam');
            Route::delete('/teams/{id}', 'deleteTeam');

            Route::get('/processors', 'getProcessors');
            Route::post('/processors', 'createProcessor');
            Route::get('/processors/{id}', 'getProcessor');
            Route::put('/processors/{id}', 'updateProcessor');
            Route::delete('/processors/{id}', 'deleteProcessor');

            Route::get('/statistics', 'getStatistics');

            Route::get('/admins/search', 'searchAdminUsers');

            Route::get('/industries', 'getIndustries');
            Route::get('/bounced-leads', 'getBouncedLeads');

            Route::get('/global-configs', 'getGlobalConfigs');
            Route::post('/global-configs', 'saveGlobalConfigs');
        });

        Route::prefix('/leads')->controller(ProcessingBaseController::class)->group(function () {
            // Routes that pertain to the lead processing screen.
            Route::get('/next-lead', 'getNextLead');
            Route::get('/queue', 'getQueue');
            Route::get('/processing-history', 'getHistory');

            Route::post('/cancel/{leadId}', 'cancel');
            Route::post('/mark-as-pending-review/{leadId}', 'markAsPendingReview');
            Route::post('/mark-as-under-review/{leadId}', 'markAsUnderReview');

            Route::patch('/update-basic-info/{leadId}', 'updateBasicInfo');
            Route::patch('/update-status-reason/{leadId}', 'updateStatusReason');

            Route::post('/heartbeat/{leadId}', 'heartbeat');
            Route::post('/approve/{leadId}', 'approve');

            Route::post('/send-sms/{leadId}', 'createOutboundSMS');

            Route::get('/time-configurations', 'getTimeConfigurations');

            Route::get('/companies-sold-to', 'getCompaniesSoldTo');
        });

        Route::prefix('/products')->controller(ProductProcessingAPIController::class)->group(function () {
            Route::get('/next-product', 'getNextProduct');
            Route::post('/{consumerProduct}/cancel', 'cancel');
            Route::post('/{consumerProduct}/remove-from-queue', 'removeFromQueue');
            Route::post('/{consumerProduct}/remove-from-affiliate-queue', 'removeFromAffiliateQueue');
            Route::post('/{consumerProduct}/mark-as-pending-review', 'markAsPendingReview');
            Route::post('/{consumerProduct}/mark-as-under-review', 'markAsUnderReview');
            Route::post('/{consumerProduct}/skip-in-aged-queue', 'skippedInAgedQueue');
            Route::get('/{consumerProduct}/aged-queue-skip-reasons', 'getAgedQueueSkipReasons');
            Route::post('/{consumerProduct}/approve', 'approveProduct');
            Route::post('/{consumerProduct}/heartbeat', 'heartbeat');
            Route::post('/refresh-aged-queue', 'refreshAgedQueue');
        });

        Route::prefix('/communication')->controller(CommunicationBaseController::class)->group(function () {
            Route::post('/token', 'token');

            Route::post('/outbound/sms/{leadId}', 'createOutboundSMS');

            Route::post('/outbound/call/{leadId}', 'createOutboundCall');
            Route::patch('/outbound/call', 'updateOutboundCall');

            Route::post('/inbound/call', 'createInboundCall');
            Route::patch('/inbound/call', 'updateInboundCall');

            Route::get('/details', 'lookupLead');

            Route::get('/history/sms/{phoneNumber}', 'getSMSHistoryForPhoneNumber');
            Route::get('/contact-url/{phoneNumber}', 'getContactUrlByPhoneNumber');
        });

        Route::name('consumer-products.')->prefix('/consumer-products')->controller(ConsumerProductApiController::class)->group(function () {
            Route::get('{consumerProductId}/other-consumer-products', 'getOtherConsumerProducts');
            Route::get('/{consumerProductId}/reviews', 'getReviewsFromConsumerProductId');
            Route::get('{consumerProductId}/basic-info', 'getConsumerProductBasicInfo');
            Route::patch('{consumerProductId}/update-basic-info', 'updateConsumerProductBasicInfo');
            Route::get('{consumerProductId}/contact-info', 'getConsumerProductContactInfo');
            Route::patch('{consumerProductId}/update-contact-info', 'updateConsumerProductContactInfo');
            Route::get('{consumerProductId}/utility-info', 'getConsumerProductUtilityInfo');
            Route::get('/{consumerProduct}/{service}/verification', 'getProductVerification');
            Route::get('/{consumerProduct}/date-info', 'getConsumerProductDateInfo');
            Route::middleware(PiiDataLogger::class)->get('/{consumerProduct}/lead-data', 'getProductLeadProcessingData');
            Route::get('/{leadId}/combined-lead-data-by-legacy-id', 'getConsumerProductCombinedDataByLegacyId');
            Route::get('/{leadId}/lead-data-by-legacy-id', 'getConsumerProductProcessingInfoByLegacyId');
            Route::patch('{consumerProductId}/update-utility-info', 'updateConsumerProductUtilityInfo');
            Route::get('{consumerProduct}/comments', 'getConsumerProductComments');
            Route::post('{consumerProduct}/comments', 'addComment');
            Route::patch('{consumerProduct}/comments', 'updateComment');
            Route::get('{consumerProduct}/comments/related-activity', 'getRelatedActivity');
            Route::get('{consumerProduct}/ping-post/logs', 'getPingPostLogs');
            Route::get('{consumerProduct}/ping-post/ping', 'pingPostPingOnly')
                ->middleware('permission:' . PermissionType::ADMIN->value);
            Route::get('{consumerProduct}/ping-post', 'pingPost')
                ->middleware('permission:' . PermissionType::ADMIN->value);
            Route::get('{consumerProduct}/appointments', 'getProductAppointments');
            Route::post('{consumerProduct}/appointments', 'createAppointment');
            Route::delete('{consumerProduct}/appointments/{appointmentId}', 'deleteAppointment');
            Route::get('/{consumerProduct}/assignments', 'getConsumerProductAssignments');
            Route::patch('/{consumerProduct}/assignments/{productAssignment}/sale-type', 'updateSaleType')
                ->middleware('permission:' . PermissionType::LEAD_ALLOCATION_AND_ADJUSTMENT->value);

            Route::get('/{consumerProduct}/future-campaigns/assignments', 'getProductProposedAssignments');
            Route::get('/{consumerProduct}/future-campaigns/get-price-for-campaigns', 'getPriceForCampaign');
            Route::post('/{consumerProduct}/future-campaigns/allocate', 'allocate')
                ->middleware('permission:' . PermissionType::LEAD_ALLOCATION_AND_ADJUSTMENT->value);

            Route::get('/{consumerProduct}/affiliate-info', 'getAffiliateInfo');
            Route::post('/{consumerProduct}/affiliate-manual-tracking', 'saveAffiliateManualTracking');

            Route::get('/unsold-products-in-county-by-zip-code', 'getUnsoldProductsInCountyByZipCode');
            Route::get('/undersold-products-in-county-by-zip-code', 'getUndersoldProductsInCountyByZipCode');

            Route::get('/{consumerProduct}/watchdog', VideoUrlFromConsumerProductController::class)
                ->name('playback.watchdog');

            Route::patch('{consumerProduct}/lock', 'lock');
        });
    });

    Route::prefix('/leads')->controller(LeadsController::class)->group(function () {
        Route::get('/{lead_id}/companies-sold-to', 'getCompaniesSoldTo');
        Route::get('/statuses', 'getLeadStatuses');
        Route::get('/categories', 'getLeadCategories');
        Route::get('/unsold-leads-in-county', 'getUnsoldLeadsInCounty');
        Route::get('/undersold-leads-in-county', 'getUndersoldLeadsInCounty');
        Route::get('/unsold-leads-in-county-by-zip-code', 'getUnsoldLeadsInCountyByZipCode');
        Route::get('/undersold-leads-in-county-by-zip-code', 'getUndersoldLeadsInCountyByZipCode');
    });

    Route::prefix('/reports-leads')->controller(LeadProcessingReportApiController::class)->group(function () {
        Route::get('/crm-bounce', 'getCrmBouncedLeads');
        Route::get('/oversold', 'getOverSoldLead');
        Route::get('/allocated', 'getAllocatedLead');
        Route::get('/delivered', 'getDeliveredLead');
        Route::get('/no-charged', 'getNoChargedLead');
    });

    Route::prefix('/phones')->controller(PhoneController::class)->group(function () {
        Route::get('available', 'getAvailablePhones');
    });

    //todo: potentially add phone permission middleware
    Route::name('communication.')->prefix('/communication')->controller(CommunicationApiController::class)->group(function () {
        Route::post('update-outbound-call', 'updateOutboundCall');
        Route::post('update-inbound-call', 'updateInboundCall');
        Route::patch('/update-relation', 'updateCallEntityRelation');
        Route::get('/lookup-caller', 'lookupCaller');
        Route::get('/voicemails', 'getVoicemails');
        Route::post('/voicemails/{id}/mark-heard/', 'markVoicemailHeard');
        Route::post('/create-outbound-sms', 'createOutboundSMS');
        Route::get('/call_recordings/lead/{id}', 'getCallRecordingsForLead');
        Route::get('/logs', 'getLogs')->name('get-logs');
        Route::get('/log-further-data', 'getLogFurtherData');
    });

    Route::name('users.')->prefix('/users')->controller(UserBaseAPIController::class)->group(function () {
        Route::get('/', 'getUsers')->name('get-users');
        Route::get('/all', 'listUsers');
        Route::get('/search', 'searchUsersByNameOrId');
        Route::get('/roles-permissions', 'getUserRolesAndPermissions')->name('get-user-roles-and-permissions');
        Route::get('/me', 'getLoggedUser')->name('get-logged-user');

        Route::post('user-activity/{id}/actions', 'saveUserAction');
        Route::get('{id}/actions', 'getUserActions');
        Route::prefix('/management')->controller(UserManagementAPIController::class)->middleware('permission:admin')->group(function () {
            Route::get('/available-numbers', 'getAvailableNumbers');

            Route::prefix('/users')->group(function () {
                Route::post('/', 'createUser');
                Route::put('/{user}', 'updateUser');
                Route::delete('/{user}', 'deleteUser');
            });
        });
    });

    //todo: add permission middleware
    Route::prefix('/task-management')->controller(TaskManagementController::class)->group(function () {
        Route::get('/event-categories', 'getEventCategories');
        Route::get('/events', 'getEvents');
        Route::post('/workflow-event', 'createWorkflowEvent');
        Route::get('/workflow-shortcodes', 'getWorkflowShortcodes');

        Route::get('/task-types', 'getTaskTypes');
        Route::post('/task-types', 'createTaskType');
        Route::delete('/task-types/{id}', 'deleteTaskType');
        Route::patch('/task-types/{id}', 'updateTaskType');
        Route::get('/modules', 'getModules');

        Route::post('/workflows', 'createWorkflow');
        Route::delete('/workflows', 'deleteWorkflow');
        Route::patch('/workflows/{workflowId}/rename', 'updateWorkflowName');
        Route::patch('/workflows/{workflow}/save-as-template', 'saveWorkflowAsTemplate');
        Route::get('/workflows/{eventId}', 'getWorkflows');
        Route::post('/workflows/{workflowId}/actions', 'createWorkflowAction');
        Route::patch('/workflows/{workflowId}/actions/{actionId}', 'updateWorkflowAction');
        Route::delete('/workflows/{workflowId}/actions/{actionId}', 'deleteWorkflowAction');

        Route::get('/task-categories', 'getTaskCategories');
        Route::post('/task-categories', 'createTaskCategory');
        Route::patch('/task-categories/{taskCategory}', 'updateTaskCategory');

        Route::get('/dynamic-priority-types', 'getDynamicPriorityTypes');

        Route::post('/create-test-event', 'createTestEvent');
    });

    Route::prefix('/tasks')->name('tasks.')->controller(TaskController::class)->group(function () {
        Route::post('/', 'createTaskForCompany');
        Route::patch('/{task}/update-manual-task-status', 'updateManualTask');
        Route::get('/next-task', 'getTask');
        Route::patch('{taskId}/running-workflow/{id}', 'updateRunningWorkflow');
        Route::patch('{task}/skip-task', 'skipTask');
        Route::patch('{task}/reschedule-task', 'rescheduleTask');
        Route::patch('{task}/task-subject', 'updateSubject');
        Route::get('{task}/task-notes', 'getTaskNotes');
        Route::post('{task}/task-notes', 'addTaskNotes');
        Route::patch('{task}/reassign-task', 'reassignTask');
        Route::patch('/{task}/priority', 'updatePriority');
        Route::get('teams', 'getTeams');
        Route::get('/all', 'getAllTasks')->name('get-all-tasks');
        Route::get('/today', 'getTodayTasks');
        Route::get('/overdue', 'getOverdueTasks');
        Route::get('/upcoming', 'getUpcomingTasks');
        Route::get('/completed', 'getCompletedTasks');
        Route::get('/muted', 'getMutedTasks');
        Route::get('/overview', 'getTaskOverview');
        Route::get('/minimal-counts', 'getMinimalTaskCounts');
        Route::post('{task}/actions', 'saveAction');
        Route::patch('/batch-complete', 'batchComplete');
        Route::patch('/batch-reschedule', 'batchReschedule');
        Route::delete('manual-tasks/{task}', 'deleteManualTask');
        Route::post('/mute-tasks', 'muteOrUnmuteTasksByAction')->defaults('mute', true);
        Route::post('/unmute-tasks', 'muteOrUnmuteTasksByAction')->defaults('mute', false);
    });

    //todo: permission
    Route::post('/tasks/company-search-tasks', [CompanySearchController::class, 'createTasksFromCompanySearch']);

    Route::get('/locations/states-with-counties', [LocationController::class, 'getStatesWithCounties']);

    Route::name('companies.')->prefix('/companies')->group(function () {
        Route::controller(CompaniesController::class)->group(function () {
            Route::post('/', 'createCompany');
            Route::get('/legacy-company-types', 'getLegacyCompanyTypes');
            Route::get('/action-categories', 'getCompanyActionCategories');
            Route::get('/company-statuses', 'getCompanyStatuses');
            Route::get('/company-admin-statuses', 'getCompanyAdminStatuses');
        });

        Route::prefix('/{company_id}')->controller(CompaniesController::class)->group(function () {

            Route::get('/check-for-duplicates', 'checkForDuplicates');;

            Route::controller(CompanyZipCodeTargetingExceptionsController::class)->group(function () {
                Route::get('/zip-code-targeting-exceptions', 'get');
                Route::patch('/zip-code-targeting-exceptions', 'update');
                Route::patch('/unrestricted-zip-code-targeting', 'toggleUnrestrictedZipCodeTargeting');
            });

            Route::prefix('/merge-tool')->controller(CompanyMergeController::class)->group(function() {
                Route::get('/past-merges', 'getPastMerges');
                Route::patch('/undo-merge', 'undoMerge');
                Route::patch('/new-merge', 'newMerge');
            });

            Route::get('/missed-products', [OpportunityNotificationsApiController::class, 'getCompanyMissedProducts']);
            Route::post('/import-emails', [CompanyEmailController::class, 'importEmails']);

            Route::get('/reset-crm-rejections', 'resetCrmRejections');
            Route::get('/campaigns-options', 'getCompanyCampaignsOptions');
            Route::get('/campaigns', 'getCampaignsForCompany')->name('get-campaigns-for-company');
            Route::delete('/campaigns/{campaignUuid}', 'deleteCampaign');
            Route::get('/actions', 'getCompanyActions')->name('get-company-actions');
            Route::patch('/actions/pin/{actionId}', 'toggleActionPin');
            Route::get('/invoices-summary', 'getCompanyInvoicesSummary');
            Route::get('/invoices', 'getCompanyInvoices');
            Route::get('/invoice/products/{invoiceId}', 'getAssociatedProducts');
            Route::get('/leads', 'getCompanyLeads');
            Route::get('/overview', 'getCompanyOverview');
            Route::get('/campaigns/budget-usage', 'getCampaignBudgetUsage');
            Route::get('/company-links', 'getCompanyLinks');
            Route::post('/new-link', 'createCompanyLink');
            Route::delete('/delete-link/{linkId}', 'deleteCompanyLink');
            Route::get('/revenue-overview', 'getRevenueOverview')->name('get-revenue-overview');
            Route::get('/revenue-graph-data', 'getRevenueGraph');
            Route::get('/leads-overview', 'getLeadsOverview');
            Route::get('/profile-data', 'getProfileData')->name('get-profile-data');
            Route::get('/revenue-insights', 'getRevenueInsights');
            Route::patch('/', 'updateCompanyDetails')->name('update-company-details');
            Route::patch('/basic-info', 'updateBasicInfo')->name('update-basic-info');
            Route::patch('/sales-status', 'updateSalesStatus');
            Route::patch('/consolidated-status', 'recalculateConsolidatedStatus');
            Route::patch('/addresses/{addressId}', 'updateCompanyAddresses');
            Route::patch('/configurable-fields', 'updateConfigurableFields');
            Route::post('/locations', 'createCompanyLocation');
            Route::patch('/locations/{locationId}', 'updateCompanyLocation');
            Route::delete('/locations/{locationId}', 'deleteCompanyLocation');
            Route::get('services', 'getCompanyServices');
            Route::get('/locations', 'getCompanyLocations');
            Route::get('/profile', 'getCompanyProfile')->name('get-company-profile');
            Route::get('/logo', 'getCompanyLogo');
            Route::post('/logo', 'saveCompanyLogo');
            Route::get('/media-assets', 'getCompanyMediaAssets');
            Route::post('/media-assets', 'saveCompanyMediaAssets');
            Route::post('/youtube-asset', 'saveCompanyYoutubeAsset');
            Route::delete('/media-assets/{assetId}', 'deleteCompanyMediaAsset');
            Route::get('/similar-companies', 'getOtherRegistrationsForCompany');
            Route::get('/best-revenue-scenario-logs', 'getBestRevenueScenarioLogsForCompany');
            Route::get('/investigate-allocation-failure/{consumerProductId}', 'investigateAllocationFailure');
            Route::get('/test-leads', 'getTestLeads');
            Route::post('/test-leads', 'createTestLead');
            Route::post('/update-chargeable-status/{leadId}', 'updateChargeableStatus');
            Route::get('/products', 'getProducts');
            Route::get('/shadow-token', 'getShadowToken');
            Route::get('/download-campaigns-zip-codes/{campaignId}', 'downloadCampaignZipcodes');
            Route::get('/company-contracts', 'getCompanyContracts');
            Route::get('/company-contracts/{contractId}/audit-logs', 'getCompanyContractAuditLogs');
            Route::get('/company-user-actions/{companyUserId}', 'getCompanyUserActions');
            Route::get('/contracts-approvals', 'getCompanyContractApprovals');
            Route::patch('/bypass-contract_signing', 'updateBypassContractSigning');

            Route::prefix('/opt-in-names')->controller(OptInNameController::class)->group(function () {
                Route::get('/', 'getCompanyOptInNames');
                Route::post('/', 'updateOptInNames');
                Route::patch('/delete-active-opt-in-name', 'deleteActiveOptInName');
                Route::patch('/{optInNameId}/set-active-opt-in-name', 'setActiveOptIn');
            });

            Route::prefix('/campaigns/{companyCampaign}/opt-in-names')->controller(OptInNameController::class)->group(function () {
                Route::patch('/delete-active-opt-in-name', 'deleteCampaignActiveOptInName');
                Route::patch('/{optInNameId}/set-active-opt-in-name', 'setCampaignActiveOptIn');
            });

            Route::prefix('/users')->controller(CompanyUserController::class)->group(function () {
                Route::get('/paginated', 'getPaginatedUsers');
                Route::get('/', 'getUsers');
                Route::delete('/{userId}', 'deleteCompanyUser');
                Route::post('/{userId}/reset-password', 'resetCompanyUserPassword');
                Route::patch('/{userId}/update-password', 'updateCompanyUserPassword');

                Route::post('/', 'createCompanyUser');
                Route::patch('/{userId}', 'updateCompanyUser');
            });

            Route::prefix('/contacts')->controller(CompanyUserController::class)->group(function() {
                Route::get('/', 'getCompanyContacts');
                Route::get('/{contactId}', 'getCompanyContact');
                Route::post('/', 'createCompanyContact');
                Route::patch('/{contactId}', 'updateCompanyContact');
                Route::patch('/pin/{contactId}', 'toggleContactPin');

            });

            Route::prefix('/activities')->controller(CompanyActivityController::class)->group(function () {
                Route::get('/', 'getActivities');
                Route::get('/overview', 'getActivitiesOverview');
                Route::post('/{activity}/conversation', 'addActivityConversation');
                Route::get('/{activity}/conversations', 'getActivityConversations');
                Route::get('/sms-history/{otherNumber}', 'getSmsHistoryForActivityFeed');
            });

            Route::prefix('/configurations')->controller(CompanyConfigurationController::class)->middleware('permission:' . PermissionType::COMPANY_CONFIGURE->value)->group(function () {
                Route::get('/', 'getConfiguration');
                Route::post('/', 'saveConfiguration');
            });

            Route::prefix('/metrics')->controller(CompanyMetricsController::class)->group(function () {
                Route::get('/ppc-spend-data', 'getCompanyPpcSpendData')->name('get-company-ppc-spend-data');
            });

            Route::prefix('/budget')->controller(BudgetController::class)->group(function () {
                Route::get('/', 'getCompanyBudgetUsage');
            });

            Route::prefix('/customer-success-managers')->controller(CustomerSuccessManagerController::class)->group(function () {
                Route::get('/', 'getCompanyCustomerSuccessManagers');
            });

            Route::patch('/update-am-pre-assignment', 'updateAmPreAssignment');
        });

        Route::name('history.')->prefix('/{company}/history')->group(function () {
            Route::get('/role', CompanyRoleHistoryController::class)->name('role');
        });

        Route::name('delete.')->prefix('/{company}/delete')->name('delete.')->group(function () {
            Route::delete('/dispatch', [CompanyDeleteController::class, 'dispatchDelete'])->name('dispatch');
            Route::get('/info', [CompanyDeleteController::class, 'info'])->name('info');
            Route::get('/validate', [CompanyDeleteController::class, 'validateDelete'])->name('validate-delete');
            Route::get('/impact', [CompanyDeleteController::class, 'impact'])->name('impact');
            Route::post('/cancel', [CompanyDeleteController::class, 'cancel'])->name('cancel');
        });

        Route::prefix('/search')->controller(CompanySearchController::class)->group(function () {
            Route::get('/company-names-id', 'searchCompaniesByNameOrId');
            Route::get('/init', 'getSearchOptions');
            Route::get('/company-names', 'getCompanyNames');
            Route::get('/admin-company', 'getAdminCompanies');
            Route::get('/campaign-statuses', 'getCampaignStatuses');
            Route::get('/payment-methods', 'getPaymentMethods');
        });

        Route::controller(GooglePlacesController::class)->group(function () {
            Route::prefix('/google-places-search')->group(function () {
                Route::get('/zip-code', 'getCompaniesByZipCode');
                Route::get('/county', 'getCompaniesByCounty');
            });
        });
    });

    Route::prefix('/sales-bait-management')->middleware('permission:admin')->controller(SalesBaitManagementController::class)->group(function () {
        Route::get('/configuration', 'configuration');
        Route::get('/configuration/{industry}/{stateId}', 'stateConfiguration');
        Route::patch('/configuration/{industry}', 'save');
        Route::get('/sales-baits', 'salesBaits');
        Route::get('/statistics', 'statistics');
        Route::get('/customstatistics', 'customStatistics');
        Route::get('/sales-bait-companies-for-lead/{leadId}', 'getCompaniesSentSalesBait');
        Route::get('/restricted-companies', 'getRestrictedCompanies');
        Route::post('/restrict-company', 'restrictCompany');
    });

    Route::name('sales-overview.')->prefix('sales-overview')
        ->controller(SalesOverviewController::class)
        ->middleware('permission:' . PermissionType::SALES_OVERVIEW_VIEW->value)
        ->group(function () {
            Route::get('/', 'getSalesOverview')->name('get');
            Route::get('/users/{user}/calls', 'getUserCalls');
            Route::get('/users/{user}/texts', 'getUserTexts');
            Route::get('/users/{user}/emails', 'getUserEmails');
            Route::get('/users/{user}/demos', 'getUserDemos');
            Route::get('/demos/filters', 'getDemoFilters');
            Route::post('/demos/filters', 'searchDemos');
            Route::post('/demos/export-to-csv', 'exportDemos');
            Route::get('/demos/users', 'searchUsers');
            Route::patch('/demos/associate-company', 'associateCompanyWithDemo');
        });

    Route::prefix('/company-users')->middleware('permission:company-users/view')->controller(CompanyUsersController::class)->group(function () {
        Route::get('/', 'getCompanyUsers');
        Route::get('/filter-options', 'getFilterOptions');
    });

    Route::prefix('/reference')->controller(ReferenceListController::class)->group(function () {
        Route::get('/industries', 'getIndustries');
        Route::get('/odin-industries', 'getOdinIndustries');
        Route::get('/states', 'getStates');
        Route::get('/states/{stateKey}/counties', 'getCountiesForState');
        Route::get('/counties', 'getCountiesByStates');
        Route::get('/states/{stateKey}/cities', 'getCitiesForState');
        Route::get('/industry-services', 'getIndustryServices');
        Route::get('/industries-services', 'getIndustriesServices');
        Route::get('/all-services-by-industry', 'allServicesByIndustry');
        Route::get('/global-types', 'getGlobalTypes');
        Route::get('/websites', 'getWebsites');
        Route::get('/countries', 'getCountries');
        Route::get('/timezones', 'getTimezones');
        Route::get('/sale-statuses', 'getSaleStatuses');
        Route::get('contract-keys', 'getContractKeys');
        Route::get('/email-template-types', 'getEmailTemplateTypes');
    });

    Route::prefix('/historical-available-budgets')->controller(HistoricalAvailableBudgetController::class)->middleware('permission:reports')->group(function () {
        Route::get('/', 'getBudgets');
        Route::get('/date-options', 'getDateOptions');
        Route::get('/company-details', 'getCompanyDetails');
    });

    Route::prefix('/profitability-simulator')->controller(ProfitabilitySimulatorController::class)->middleware('permission:reports')->group(function () {
        Route::post('/', 'getSimulatedData');
        Route::get('/campaign-data/{campaign}', 'getCampaignData');
    });

    Route::prefix('/estimated-revenue-per-lead')->controller(EstimatedRevenuePerLeadByLocationController::class)->middleware('permission:reports')->group(function () {
        Route::get('/', 'getEstimatedRevenuePerLead');
    });

    Route::name('leads-report.')->prefix('/leads-report')->controller(LeadsReportController::class)->middleware('permission:reports')->group(function () {
        Route::get('/options', 'getLeadsReportOptions');
        Route::post('/filter-option-updates', 'getFilterOptionUpdates');
        Route::get('/', 'getLeadsReport');
        Route::put('/save-preset', 'savePreset')->name('save-preset');
        Route::delete('/delete-preset', 'deletePreset')->name('delete-preset');
    });

    Route::name('county-coverage-report.')->prefix('/county-coverage-report')->controller(CountyCoverageReportController::class)->middleware('permission:reports')->group(function () {
        Route::get('/options', 'getReportOptions');
        Route::post('/filter-option-updates', 'getFilterOptionUpdates');
        Route::get('/', 'getReport');
        Route::put('/save-preset', 'savePreset')->name('save-preset');
        Route::delete('/delete-preset', 'deletePreset')->name('delete-preset');
        Route::get('/erpl-zip-codes/{industry}', 'downloadErplZipCodes');
    });

    Route::prefix('email-templates')->controller(EmailTemplatesAPIController::class)->middleware('permission:' . PermissionType::EMAIL_TEMPLATE->value)->group(function () {
        Route::get('/list', 'getUserEmailTemplates');
        Route::get('/all', 'getAllEmailTemplates');
        Route::get('/template/{templateId}', 'getEmailTemplate');
        Route::post('/preview', 'previewEmailTemplate');
        Route::post('/create', 'createEmailTemplate');
        Route::post('/duplicate', 'duplicateEmailTemplate');
        Route::patch('/update/{templateId}', 'updateEmailTemplate');
        Route::delete('/delete/{templateId}', 'deleteEmailTemplate');
        Route::get('/shortcodes', 'getShortcodes');
        Route::get('/options', 'getTemplateOptions');
        Route::post('/image', 'saveTemplateImage');
        Route::get('/filter-options', 'getFilterOptions');
        Route::post('/search', 'search');
        Route::post('retrieve', 'listEmailTemplates');
        Route::get('engine-options', 'getEngineOptions');
        Route::post('/send-test/{templateId}', 'sendTestEmail');

        Route::prefix('backgrounds')->group(function () {
            Route::get('/template/{templateId}', 'getEmailTemplateBackground');
            Route::get('/list', 'getUserEmailBackgroundTemplates');
            Route::get('/list-industry', 'getUserEmailBackgroundTemplatesByIndustry');
            Route::post('/preview', 'previewEmailBackgroundTemplate');
            Route::post('/create', 'createEmailBackgroundTemplate');
            Route::post('/duplicate', 'duplicateEmailBackgroundTemplate');
            Route::patch('/update/{templateId}', 'updateEmailTemplateBackground');
            Route::delete('/delete/{templateId}', 'deleteEmailBackgroundTemplate');
        });
    });

    Route::prefix('/roles-permissions-management')->controller(RolesPermissionsManagementController::class)->middleware('permission:permission-management/view')->group(function () {
        Route::get('/permissions', 'getPermissions');
        Route::post('/permissions', 'addPermission');
        Route::patch('/permissions/{permission}', 'updatePermission')->middleware('permission:permission-management/edit');
        Route::get('/roles', 'getRoles');
        Route::get('/role-options', 'getRoleOptions');
        Route::post('/roles', 'addRole')->middleware('permission:permission-management/edit');
        Route::patch('/roles/{role}', 'updateRole')->middleware('permission:permission-management/edit');
        Route::patch('/roles/{role}/sync-permissions', 'syncPermissions')->middleware('permission:permission-management/edit');
    });

    Route::name('non-purchasing-service-areas.')->prefix('/non-purchasing-service-areas')->controller(NonPurchasingServiceAreaController::class)->group(function () {
        Route::prefix('/companies')->group(function () {
            Route::prefix('/count')->group(function () {
                Route::get('/county', 'getCompanyCountInCounty');
            });
            Route::get('/zip-code', 'getCompaniesInCountyByZipCode')->name('get-companies-in-county-by-zip-code');
            Route::get('/county', 'getCompaniesInCounty');
        });
    });

    Route::prefix('/advertising')->controller(AdvertisingAPIController::class)->middleware('permission:advertising')->group(function () {
        Route::get('/campaigns/{platform}/{accountId}', 'getCampaigns');
        Route::get('/campaigns-paginated/{platform}/{accountId}', 'getCampaignsPaginated');
        Route::patch('campaigns/{platform}/{accountId}', 'updateCampaigns');
        Route::get('accounts/{platform}', 'getAccounts');
        Route::get('init-reference-lists', 'initReferenceLists');
        Route::get('reference-lists/{platform}', 'getPlatformReferenceLists');

        Route::prefix('/tiered-ads')->group(function () {
            Route::get('/options', 'getTieredAdsOptions');
            Route::get('/campaigns/{platform}/{industry}', 'getTieredAdsCampaigns');
            Route::post('/locations-update/{platform}/{industry}', 'triggerLocationsUpdate');
            Route::post('/{platform}/{industry}', 'updateTieredAdsCampaigns');
            Route::post('/assign-instance/{platform}/{industry}', 'assignTieredAdsInstance');
            Route::post('/delete-instance/{platform}/{industry}', 'deleteTieredAdsInstance');
            Route::get('/campaign-locations/{campaignId}', 'getTieredAdsCampaignLocations');
        });

        Route::middleware('permission:advertising-admin')->group(function () {
            Route::get('accounts-paginated/{platform}', 'getPaginatedAccounts');
            Route::post('accounts/{platform}', 'saveAccount');
            Route::delete('accounts/{platform}/{accountId}', 'deleteAccount');
            Route::get('token-auth-platforms', 'getTokenAuthPlatforms');
            Route::get('token-auth-endpoint/{platform}', 'getTokenAuthEndpoint');
            Route::get('/all-platforms', 'getAllPlatforms');
            Route::get('/advertisers', 'getAdvertisers');
            Route::get('/ad-cost-parameters', 'getAdCostParameters');
            Route::post('/ad-cost-backfill', 'adCostBackfill');
        });
    });

    Route::prefix('/user-settings/{user}')->controller(UserSettingsController::class)->group(function () {
        Route::get('/get-call-forwarding-data', 'getCallForwardingData');
        Route::patch('/update-call-forwarding-status', 'updateCallForwardingStatus');
        Route::patch('/update-call-forwarding-number', 'updateCallForwardingNumber');
        Route::get('/timezone', 'getTimezone');
        Route::post('/timezone', 'updateTimezone');

        Route::post('/filter-presets', 'storeUserFilterPresets');
        Route::get('/user-links', 'getUserLinks');
        Route::patch('/update-user-links', 'updateUserLinks');
        Route::get('/search-filters', 'getUserSearchFilters');
        Route::post('/search-filters', 'storeUserSearchFilters');

        Route::patch('/update-user-profile', 'updateUserProfile');
    });

    Route::middleware('permission:industry-management')->group(function () {
        Route::prefix('/websites')->controller(WebsiteController::class)->group(function () {
            Route::get('/', 'index');
            Route::post('/', 'createWebsite');
            Route::patch('/{website}', 'updateWebsite');
            Route::delete('/{website}', 'deleteWebsite');
            Route::get('/{website}/website-api-keys', 'getApiKeys');
            Route::post('/{website}/website-api-keys', 'CreateApiKey');
            Route::put('/{website}/website-api-keys/{websiteApiKey}', 'updateApiKey');
            Route::patch('/{website}/website-api-keys/{websiteApiKey}/enable', 'enableApiKey');
            Route::patch('/{website}/website-api-keys/{websiteApiKey}/disable', 'disableApiKey');
        });

        Route::prefix('/products')->controller(ProductController::class)->group(function () {
            Route::get('/', 'index');
            Route::post('/', 'createProduct');
            Route::patch('/{product}', 'updateProduct');
            Route::delete('/{product}', 'deleteProduct');
            Route::get('/{product}/services', 'getServices');
            Route::post('/{product}/services/{industryService}', 'addService');
            Route::delete('/{product}/services/{industryService}', 'deleteService');
        });

        Route::prefix('/industries')->controller(IndustryManagementAPIController::class)->group(function () {
            Route::get('/', 'getIndustries');
            Route::post('/', 'createIndustry');
            Route::patch('/default-lead-email-template', 'updateDefaultLeadEmailTemplate');
            Route::patch('/enable-future-campaigns', 'enableFutureCampaigns');
            Route::patch('/disable-future-campaigns', 'disableFutureCampaigns');
            Route::patch('/toggle-custom-floor-pricing', 'toggleCustomFloorPricingFlag');
            Route::patch('/update-reviews-flag', 'updateReviewsFlag');
            Route::patch('/{industry}', 'updateIndustry');
            Route::delete('/{industry}', 'deleteIndustry');

            Route::prefix('/{industry}/websites')->group(function () {
                Route::get('/', 'getIndustryWebsites');
                Route::post('/', 'createIndustryWebsite');
                Route::patch('/{industryWebsite}', 'updateIndustryWebsite');
                Route::get('/non-added', 'getNonAddedWebsites');
            });
            Route::delete('/website/{industryWebsite}', 'deleteIndustryWebsite');

            Route::prefix('/{industry}/services')->group(function () {
                Route::get('/', 'getIndustryServices');
                Route::post('/', 'createIndustryService');
                Route::patch('/{industryService}', 'updateIndustryService');
            });
            Route::delete('/service/{industryService}', 'deleteIndustryService');

            Route::prefix('/{industry}/companies')->group(function () {
                Route::get('/', 'getIndustryCompanies');
                Route::post('/', 'addIndustryCompany');
                Route::get('/non-added', 'getNonAddedCompanies');
            });
            Route::delete('/company/{industryCompany}', 'deleteIndustryCompany');

            Route::prefix('/{industry}/company-fields')->group(function () {
                Route::get('/', 'getIndustryCompanyFields');
                Route::post('/', 'createIndustryCompanyField');
                Route::patch('/{industryCompanyField}', 'updateIndustryCompanyField');
            });
            Route::delete('/company-field/{industryCompanyField}', 'deleteIndustryCompanyField');

            Route::prefix('/{industry}/consumer-fields')->group(function () {
                Route::get('/', 'getIndustryConsumerFields');
                Route::post('/', 'createIndustryConsumerField');
                Route::patch('/{industryConsumerField}', 'updateIndustryConsumerField');
            });
            Route::delete('/consumer-field/{industryConsumerField}', 'deleteIndustryConsumerField');

            Route::prefix('/{industry}/types')->group(function () {
                Route::get('/', 'getIndustryTypes');
                Route::post('/', 'createIndustryType');
                Route::patch('/{industryType}', 'updateIndustryType');
            });
            Route::delete('/type/{industryType}', 'deleteIndustryType');

            Route::prefix('/type/{industryType}/reviews')->group(function () {
                Route::get('/', 'getIndustryTypeReviews');
                Route::post('/', 'createIndustryTypeReview');
            });
            Route::delete('/review/{review}', 'deleteIndustryTypeReview');

            Route::prefix('/default-profitability')->group(function () {
                Route::get('/', 'getDefaultProfitabilityAssumptions');
                Route::put('/update-all', 'updateAllDefaultProfitabilityAssumptions');
                Route::put('/{industryServiceId}', 'updateDefaultProfitabilityAssumption');
                Route::delete('/{industryServiceId}', 'deleteDefaultProfitabilityAssumption');
            });

            Route::patch('/{industry}/delivery-email-template', 'updateDeliveryEmailTemplate');
        });

        Route::prefix('/configurable-fields')->controller(ConfigurableFieldsController::class)->group(function () {
            Route::get('/list', 'list');
            Route::post('/save', 'save');
            Route::get('/type-categories', 'getTypeCategories');
            Route::get('/field-types', 'getFieldTypes');
            Route::get('/company/categories', 'getCompanyFieldCategories');
            Route::get('/company/consumer-configurable-field-categories', 'getConsumerConfigurableFieldCategories');
            Route::post('/visibility', [ConsumerFieldModuleVisibilityController::class, 'saveMany']);
            Route::get('/visibility', [ConsumerFieldModuleVisibilityController::class, 'getFieldsVisibilityByFieldCategory']);
            Route::get('company-user/fields', 'getCompanyUserFields');
        });

        Route::prefix('/companies')->controller(IndustryCompaniesController::class)->group(function () {
            Route::prefix('/{company}')->group(function () {
                Route::get('/services', 'getServices');
                Route::post('/services/{industryService}', 'addService');
                Route::delete('/services/{industryService}', 'deleteService');
            });
        });
    });

    Route::prefix('/company-reviews')->controller(CompanyReviewsController::class)->middleware('permission:manage-company-reviews')->group(function () {
        Route::get('/', 'getCompanyReviews');
        Route::patch('/{id}', 'update');
        Route::get('/statuses', 'getStatuses');
    });

    Route::prefix('editors')->group(function () {
        Route::prefix('wysiwyg')->controller(TinymceEditorController::class)->group(function () {
            Route::post('upload-file', 'uploadFile');
        });
    });

    Route::prefix('/external-auth')->controller(ExternalAuthController::class)->middleware('permission:cms')->group(function () {
        Route::get('/get-token', 'getToken');
    });

    Route::prefix('/floor-pricing')
        ->controller(FloorPricingController::class)
        ->middleware(PermissionType::MINIMUM_PRICE_MANAGEMENT_VIEW->getPermissionString())
        ->group(function () {
            Route::get('/', 'getFloorPricing');
            Route::patch('/update-appointment-formulas', 'updateAppointmentFormulas');
            Route::patch('update-floor-pricing', 'UpdateFloorPricing');
            Route::post('/initialise-floor-pricing', 'initialiseFloorPricing');
            Route::post('/import-floor-pricing', 'importFloorPricing');
            Route::get('/exportable-industry-services', 'getExportableIndustryServices');
            Route::get('/default-pricing', 'getDefaultFloorPricing');
            Route::put('/save-default-pricing', 'saveDefaultFloorPricing');
            Route::patch('/repair-floor-pricing', 'repairFloorPricing');
            Route::get('/history', 'getFloorPriceHistory');
            Route::get('/industries/by-product', 'getIndustryServicesByProduct');
        });
    Route::prefix('/floor-price-suggestions')
        ->controller(FloorPriceSuggestionsController::class)
        ->middleware('permission:' . PermissionType::INDUSTRY_CONFIGURATION->value)
        ->group(function () {
            Route::get('/initial-data', 'getStateInitialData');
            Route::get('/industries/{industry}/services/{industryService}/states/{stateKey}', 'getFloorPriceSuggestions');
            Route::post('/industries/{industry}/services/{industryService}/apply-suggested-prices', 'applySuggestedPrices');
            Route::get('/industries/{industry}/services/{industryService}/price-margin', 'getPriceMargin');
            Route::patch('/price-margin/{pricingMargin}', 'updatePriceMargin');
            Route::get('/industries/{industry}/services/{industryService}/states/{stateKey}/statistics', 'getStatistics');
        });

    Route::prefix('/team')->group(function () {
        Route::prefix('/info')->controller(TeamManagementController::class)->group(function () {
            Route::get('/get-teams', 'getAllTeams');
            Route::get('/get-team-types', 'getTeamTypes');
            Route::get('/get-leader-teams', 'getLeaderTeams');
            Route::get('/sales-teams', 'getSalesTeams');
        });
        Route::prefix('/management')->controller(TeamManagementController::class)->group(function () {
            Route::post('/create-new-team', 'createNewTeam');
            Route::patch('/update-team', 'updateTeam');
            Route::delete('/delete-team/{teamId}', 'deleteTeam');
        });
    });

    Route::prefix('/bundles')->group(function () {
        Route::controller(BundleApiController::class)
            ->middleware('permission:manage-bundles|manage-bundle-invoices|view-bundles')
            ->group(function () {
                Route::get('/', 'index');
                Route::post('/', 'addBundle');
                Route::patch('/{bundle}', 'updateBundle');
                Route::delete('/{bundle}', 'deleteBundle');
            });


        Route::prefix('/search')
            ->controller(BundleSearchController::class)
            ->group(function () {
                Route::get('/', 'getBundles');
            });
    });

    Route::prefix('/companies-quality-score-rules')->group(function () {
        Route::middleware('permission:' . PermissionType::COMPANY_QUALITY_SCORE_MANAGEMENT->value)->group(function () {
            Route::controller(CompanyQualityScoreRuleController::class)
                ->group(function () {
                    Route::get('/', 'index');
                    Route::post('/', 'store');
                    Route::put('/{companyQualityScoreRule}', 'update');
                    Route::delete('/{companyQualityScoreRule}', 'delete');
                    Route::post('/test', 'testRule');
                    Route::post('/trigger/{companyQualityScoreRule}', 'trigger');
                });

            Route::prefix('/industry-configuration')
                ->controller(CompanyQualityScoreConfigurationController::class)
                ->group(function () {
                    Route::get('/', 'getConfigurations');
                    Route::put('/update-test-companies', 'updateTestCompanies');
                    Route::put('/update-rule-id', 'updateIndustryRule');
                    Route::get('/test-companies/{industryId}', 'getTestCompanies');
                });
        });
    });

    Route::prefix('/bundle-invoices')->group(function () {
        Route::controller(BundleInvoiceApiController::class)
            ->middleware(['permission:manage-bundles|manage-bundle-invoices|view-bundles'])
            ->group(function () {
                Route::get('/', 'index');
                Route::post('/', 'addBundleInvoice');
                Route::delete('/{bundleInvoice}', 'deleteBundleInvoice');
                Route::patch('/transition/{bundleInvoice}', 'executeStatusTransition');
                Route::get('/history/{bundleInvoice}', 'getBundleInvoiceHistory');
            });


        Route::prefix('/search')
            ->controller(BundleInvoiceSearchController::class)
            ->group(function () {
                Route::get('/', 'getInvoices');
            });
    });

    Route::prefix('/flow-management')->controller(FlowManagementProxyController::class)->group(function () {
        Route::get('/', 'getRevisionMetaData');
        Route::post('/create-flow', 'createNewFlow');
        Route::get('/consumer-fields/{serviceSlug}', 'getConsumerFields');
        Route::get('/{flowId}/is-valid', 'checkFlowIdIsValid');
        Route::patch('/{flowId}/update', 'updateFlow');
        Route::post('/{flowId}/create-revision', 'createNewRevision');
        Route::post('/{flowId}/{revisionUuid}/new-variant', 'createNewVariant');
        Route::post('/{flowId}/{revisionUuid}/publish', 'publishRevision');
        Route::post('/{flowId}/{revisionUuid}/set-production', 'setProductionRevision');
        Route::put('/{flowId}/{revisionUuid}/save', 'saveRevision');
        Route::post('/{flowId}/{revisionUuid}/copy-to', 'copyRevision');
        Route::get('/{flowId}/{revisionUuid}/preview', 'previewRevision');
        Route::patch('/{flowId}/rename-revision-group', 'renameRevisionGroup');
        Route::post('/upload-image/{flowId}', 'uploadImage');
        Route::get('/industry-services', 'getIndustryServices');

        Route::delete('/{flowId}/{revisionId}', 'deleteRevision');
        Route::patch('/{flowId}/{revisionId}/undelete', 'undeleteRevision');
        Route::delete('/{flowId}/{revisionId}/hard-delete', 'hardDeleteRevision');
        Route::delete('/{flowId}/{revisionGroupName}/delete-group', 'deleteRevisionGroup');
    });

    Route::prefix('/opportunity-notifications')->group(function () {
        Route::controller(OpportunityNotificationsApiController::class)
            ->middleware(['permission:opportunity-notifications'])
            ->group(function () {
                Route::get('/', 'index');
                Route::post('/test', 'testRuleset');
            });
    });

    Route::prefix('/opportunity-notifications-config')->group(function () {
        Route::controller(OpportunityNotificationsConfigApiController::class)
            ->middleware(['permission:opportunity-notifications'])
            ->group(function () {
                Route::get('/', 'index');
                Route::get('/config-types', 'getConfigTypes');
                Route::get('/options', 'searchOptions');
                Route::get('/email-template-options', 'getEmailTemplateOptions');
                Route::get('/email-template-preview', 'getEmailTemplatePreview');
                Route::get('/test-email-recipients', 'getTestEmailRecipients');
                Route::post('/send/{notificationConfig}', 'sendEmails');
                Route::post('/', 'addOpportunityNotificationConfig');
                Route::delete('/{notificationConfig}', 'deleteOpportunityNotificationConfig');
                Route::patch('/{notificationConfig}', 'updateOpportunityNotificationConfig');
                Route::post('/{filterPreset}/preview-company-count', 'getCompanyCountPreview');
            });
    });

    Route::prefix('/rulesets')->group(function () {
        Route::controller(RulesetsApiController::class)
            ->middleware(['permission:' . PermissionType::RULESET_MANAGEMENT->value])
            ->group(function () {
                Route::get('/', 'index');
                Route::post('/', 'createRuleset');
                Route::patch('/{ruleset}', 'updateRuleset');
                Route::delete('/{ruleset}', 'deleteRuleset');
                Route::get('/template', 'getTemplate');
            });
    });

    Route::prefix('/outreach-cadence')->middleware('permission:outreach-cadence')->group(function () {

        Route::prefix('/configuration')->group(function () {

            Route::prefix('/routines')->controller(CadenceRoutineConfigurationController::class)->group(function () {
                Route::get('/owned', 'getOwnedRoutineConfigurations');
                Route::get('/all-available', 'getAllAvailableRoutineConfigurations');
                Route::post('/create', 'createRoutineConfiguration');
                Route::patch('/update/{routineId}', 'updateRoutineConfiguration');
                Route::delete('/delete/{routineId}', 'deleteRoutineConfiguration');
                Route::post('/clone/', 'cloneRoutine');
                Route::post('/reorder-actions', 'reorderActions');
            });

            Route::prefix('/templates')->controller(CadenceTemplateController::class)->group(function () {
                Route::post('save-image', 'saveImage');
                Route::get('/user-email-header-footer', 'getUserHeaderFooter');
                Route::patch('/user-email-header', 'updateHeader');
                Route::patch('/user-email-footer', 'updateFooter');
                Route::get('/', 'getAllTemplates');
                Route::post('/preview-email', 'getEmailPreview');

                Route::prefix('/email')->group(function () {
                    Route::post('clone', 'cloneEmailTemplate');
                    Route::get('/', 'getEmailTemplates');
                    Route::post('/create', 'createEmailTemplate');
                    Route::patch('/update/{templateId}', 'updateEmailTemplate');
                    Route::delete('/delete/{templateId}', 'deleteEmailTemplate');
                });

                Route::prefix('/sms')->group(function () {
                    Route::post('clone', 'cloneSmsTemplate');
                    Route::get('/', 'getSmsTemplates');
                    Route::post('/create', 'createSmsTemplate');
                    Route::patch('/update/{templateId}', 'updateSmsTemplate');
                    Route::delete('/delete/{templateId}', 'deleteSmsTemplate');
                });

                Route::prefix('/task')->group(function () {
                    Route::post('clone', 'cloneTaskTemplate');
                    Route::get('/', 'getTaskTemplates');
                    Route::post('/create', 'createTaskTemplate');
                    Route::patch('/update/{templateId}', 'updateTaskTemplate');
                    Route::delete('/delete/{templateId}', 'deleteTaskTemplate');
                });

            });

        });

        Route::prefix('/company-routines')->controller(CompanyCadenceRoutineController::class)->group(function () {
            Route::get('/', 'getCompanyRoutines');
            Route::get('/find-for-company/{companyId}', 'findCompanyRoutines');
            Route::get('/contacts-for-routine/{routineId}/', 'getRoutineContacts');
            Route::patch('/contacts-for-routine/{routineId}/', 'updateRoutineContacts');
            Route::patch('/skip-group/{groupId}', 'skipGroup');
            Route::patch('/un-skip-group/{groupId}', 'unSkipGroup');
            Route::post('/mass-assign', 'massAssign');
            Route::post('/mass-terminate', 'massTerminate');
            Route::post('/mass-assign-v2', 'massAssignV2');
            Route::post('/mass-terminate-v2', 'massTerminateV2');
            Route::delete('/{routineId}', 'terminateRoutine');
        });
    });

    Route::name('companies-servicing-area.')->prefix('/companies-servicing-area')->controller(CompaniesServicingAreaController::class)->group(function () {
        Route::get('/filter-options', 'getFilterOptions')->name('get-filter-options');
        Route::put('/save-preset', 'savePreset')->name('save-preset');
        Route::delete('/delete-preset', 'deletePreset')->name('delete-preset');
    });

    Route::name('company-search.')->prefix('/company-search')->controller(CompanySearchController::class)->group(function () {
        Route::get('/filter-options', 'getFilterOptions')->name('get-filter-options');
        Route::post('/filter-option-updates', 'getFilterOptionUpdates');
        Route::post('/search', 'search')->name('search');
        Route::put('/save-preset', 'savePreset')->name('save-preset');
        Route::delete('/delete-preset', 'deletePreset')->name('delete-preset');
    });

    Route::prefix('/user-presets')->controller(UserPresetController::class)->group(function () {
        Route::get('/filters', 'getFilterPresets');
    });

    Route::name('consumer-search.')->prefix('/consumer-search')->controller(ConsumerSearchController::class)->group(function() {
        Route::post('/filter-option-updates', 'getFilterOptionUpdates');
        Route::get('/filter-options', 'getFilterOptions')->name('get-filter-options');
        Route::post('/search', 'search')->name('search');
        Route::put('/save-preset', 'savePreset');
        Route::delete('/delete-preset', 'deletePreset');
        Route::get('/check-lead-processor', 'checkLeadProcessor');
        Route::post('/aggregates', 'getConsumerSearchAggregates');
    });

    Route::prefix('/silo-management')->controller(SiloManagementController::class)->group(function () {
        Route::get('/location-types', 'getSiloLocationTypes');
        Route::get('/shortcodes', 'getSiloShortcodes');
        Route::post('/create-silo-with-locations', 'createNewSiloWithLocations');
        Route::get('/all-silos', 'getAllSilos');
        Route::patch('/update-silo', 'updateSilo');
        Route::patch('/update-location-page', 'updateLocationSiloPage');
    });

    Route::prefix('/global-configurations-management')->controller(GlobalConfigurationsManagementController::class)
        ->middleware(PermissionType::GLOBAL_CONFIGURATIONS_VIEW->getPermissionString())
        ->group(function () {
            Route::get('/configurations', 'getConfigurations');
            Route::post('/configurations', 'addConfiguration')->middleware(PermissionType::GLOBAL_CONFIGURATIONS_EDIT->getPermissionString());
            Route::patch('/configurations/{configuration}', 'updateConfiguration')->middleware(PermissionType::GLOBAL_CONFIGURATIONS_EDIT->getPermissionString());
            Route::delete('/configurations/{configurationId}', 'deleteConfiguration')->middleware(PermissionType::GLOBAL_CONFIGURATIONS_EDIT->getPermissionString());
        });

    Route::get('/export-call-logs', [CallLogController::class, 'export']);

    Route::prefix('/test-leads')->controller(TestLeadApiController::class)->middleware('permission:test-leads')->group(function () {
        Route::get('/', 'getTestLeads');
        Route::get('/filters', 'getTestLeadTableFilters');
        Route::get('/{testLeadId}', 'getTestLeadCommunications');
        Route::get('/{testLeadId}/export-communications', 'exportTestLeadCommunications');
        Route::post('/phone_fraud_score', 'getPhoneFraudScore');
    });

    Route::prefix('/expert-reviews')->middleware('permission:' . PermissionType::EXPERT_REVIEWS_VIEW->value)->controller(ExpertReviewsController::class)->group(function () {
        Route::get('/{companyId}', 'getExpertReviewsForCompany');
        Route::post('/{companyId}', 'createExpertReview');
        Route::patch('/{companyId}/{reviewId}', 'updateExpertReview');
        Route::delete('/{companyId}/{reviewId}', 'deleteExpertReview')->middleware('permission:' . PermissionType::EXPERT_REVIEWS_DELETE->value);
        Route::get('/{companyId}/history', 'getExpertReviewHistoryForCompany');
    });

    Route::get('/user-google-services', [GoogleServiceController::class, 'listUserServiceIntegrations'])
        ->middleware('protect_from_impersonation:throw_exception');


    Route::prefix('/mailbox')
        ->middleware('protect_from_impersonation:throw_exception')
        ->controller(MailboxUserEmailController::class)
        ->group(function () {
            Route::get('/list', 'listEmails');
            Route::get('/labels', 'getLabels');
            Route::get('/{userEmailUuid}/detail', 'getEmailDetailData');
            Route::patch('/modify', 'modifyEmails');
            Route::delete('/delete', 'deleteEmails');
            Route::post('/send', 'sendEmail');
            Route::post('/{emailUuidToForward}/forward', 'forwardEmail');
            Route::post('/{emailUuidToReply}/reply', 'replyEmail');
            Route::get('/get-permission', 'generateUrlForPermissionToMailbox');
            Route::post('/sync-mailbox', 'syncMailbox');
            Route::get('/user-signature', 'getUserEmailSignature');
        });

    Route::prefix('/identified-contacts')->controller(ContactIdentificationController::class)->group(function () {
        Route::post('/identify-and-nominate', 'identifyAndNominateContact');
        Route::patch('/{identifiedContact}/nominate', 'nominateContact');
    });


    Route::prefix('/notes')->controller(NoteController::class)->group(function () {
        Route::get('/', 'getNotes');
        Route::post('/', 'createNote');
    });


    Route::prefix('/contract-management')->middleware(PermissionType::CONTRACT_MANAGEMENT_VIEW->getPermissionString())->controller(ContractManagementController::class)->group(function() {
        Route::get('/', 'getContracts');
        Route::get('/contracts/{contract}/files-url', 'getContractFilesAsUrl');
        Route::patch('/contracts/{contract}/update', 'updateContract');
        Route::delete('/contracts/{contract}', 'deleteContract');
        Route::post('/contracts/{contract}/activate', 'activateContract')->middleware(PermissionType::CONTRACT_MANAGEMENT_EDIT->getPermissionString());
        Route::post('/contracts/{contract}/company-users/{companyUser}/send-signature-request', 'sendSignatureRequest')->middleware(PermissionType::CONTRACT_MANAGEMENT_SEND->getPermissionString());
        Route::post('/contracts/company-users/{companyUser}/upload', 'uploadCompanyContract')->middleware(PermissionType::CONTRACT_MANAGEMENT_UPLOAD->getPermissionString());
    });

    // These routes enable the Dashboard Campaign Wizard to work in Admin2.0
    // They may not be optimized, some of this data is probably already available somewhere in A2. To Be Continued....
    Route::prefix('/campaign-wizard')->name('campaign-wizard.')->middleware(['register-admin-actioning-user'])->controller(CompanyCampaignWizardController::class)->group(function() {
        Route::get('/industry-service-products', 'getIndustryServiceProducts');
        Route::get('/new-wizard-configuration', 'getNewWizardConfiguration');
        Route::get('/status-change-config', 'getCampaignStatusChangeConfig');
        Route::get('/location-statistics', 'getBiddingLocationStatistics');
        Route::get('/replacer-field-documentation', 'getReplacerFieldDocumentation');
        Route::get('/custom-pricing-bid-modification-options', 'getCustomPricingBidModificationOptions');

        Route::name('locality-store.')->prefix('/locality-store')->controller(ReferenceDataController::class)->group(function () {
            Route::get('/countries', 'getCountries');
            Route::get('/states', 'getStatesWithTotals')->name('states');
            Route::get('/state/{stateKey}', 'getStateDetail');
            Route::get('/state/{stateKey}/county/{countyId}', 'getCountyDetail');
            Route::get('/county-zip-codes', 'getCountyZipCodesByZipCodes');
            Route::get('/state-zip-codes/{stateKey}', 'getAllZipCodesInState');
            Route::get('/zip-codes-radius/{zipCode}/{radius}', 'getZipCodesByRadius');
            Route::post('/zip-codes-by-string', 'getZipCodesByZipCodeStrings');
        });

        Route::prefix('/prices')->group(function () {
            Route::post('/zip-code-price-range', 'getPriceRangeForZipCodes');
        });

        Route::prefix('/{companyId}')->name('company.')->group(function () {
            Route::get('/products/configurations', 'getProductConfigurations');
            Route::post('/validate-targeted-zip-codes', 'validateTargetedZipCodes');
            Route::get('/crm-configurations', 'getCrmConfigurations')->name('get-crm-configurations');

            Route::prefix('/bidding')->controller(CampaignPriceController::class)->group(function () {
                Route::get('/state-floor/{productKey}/{service}', 'getStateFloorPrices');
                Route::get('/county-floor/{productKey}/{service}', 'getCountyFloorPrices');
                Route::get('/{campaignReference}/state', 'getStatePrices');
                Route::get('/{campaignReference}/county', 'getCountyPrices');
                Route::get('/{campaignReference}/custom-state-floor', 'getCustomStateFloorPrices');
                Route::get('/{campaignReference}/custom-county-floor', 'getCustomCountyFloorPrices');
                Route::put('/{campaignReference}/save-custom-floor', 'saveCustomFloorPrices');
            });

            Route::prefix('/delivery')->group(function () {
                Route::prefix('/company-users')->group(function () {
                    Route::get('/', 'getCompanyUsers');
                    Route::patch('/update', 'updateCompanyUser');
                    Route::post('/create', 'createCompanyUser');
                    Route::delete('/delete', 'deleteCompanyUser');
                });
            });

            Route::name('crm.')->prefix('/crm')->group(function () {
                Route::post('/execute-method', 'executeCrmMethod');
                Route::get('/import-options', 'getCrmImportOptions');
                Route::put('/save-template', 'saveCrmTemplate')->name('save-template');
                Route::delete('/delete-template', 'deleteCrmTemplate');
            });

            Route::prefix('delivery-modules')->controller('')->group(function () {
                Route::prefix('/crm-modules')->controller(CRMModuleController::class)->group(function () {
                    Route::post('/{crmModuleId}/integrations/test', 'testCrmIntegration');
                });
            });

            Route::prefix('/campaign')->name('campaign.')->group(function () {
                Route::get('/list', 'getCampaignSummaryList')->name('get-campaign-summary-list');
                Route::post('/new', 'saveNewCampaign');
                Route::patch('/update', 'updateCampaign')->name('update');
                Route::patch('/pause', 'pauseCampaign');
                Route::patch('/unpause', 'unpauseCampaign');
                Route::delete('/delete', 'deleteCampaign');
                Route::get('/zip-codes', 'downloadCampaignZipCodes');
                Route::get('/{campaignReference}/custom-floor-prices', 'getCustomCampaignFloorPrices');
                Route::get('/{campaignReference}', 'getCompanyCampaignForEditing');
                Route::patch('/{campaignReference}/bidding-status/toggle', 'toggleCampaignBiddingStatus');
                Route::patch('/{campaignReference}/ad-automation-status/toggle', 'toggleAdAutomationExclusion');
            });

            Route::prefix('/schedules')->controller(CompanyCampaignScheduleController::class)->group(function() {
                Route::get('/all', 'getAllCompanySchedules');
                Route::post('/create-static-calendar', 'createStaticCalendar');
                Route::patch('/update-schedule', 'updateSchedule');
                Route::delete('/delete-schedule', 'deleteSchedule');
                Route::get('/events', 'getCalendarEventsInDateRange');
            });

        });

        Route::prefix('/campaigns')->controller(CompanyCampaignApiController::class)->group(function () {
            Route::prefix('/{companyCampaign:reference}')->group(function () {
                Route::get('/filter-options', 'getFilterOptions');
                Route::prefix('/filters')->group(function () {
                    Route::get('/', 'getFilters');
                    Route::post('/', 'createFilter');
                    Route::patch('/{companyCampaignFilter}', 'updateFilter');
                    Route::delete('/{companyCampaignFilter}', 'deleteFilter');
                });
            });
        });
    });

    Route::prefix('/privacy-management')
        ->middleware(['permission:'. PermissionType::PRIVACY_MANAGEMENT_VIEW->value])
        ->controller(PrivacyManagementController::class)
        ->group(function () {
            Route::get('/', 'getPrivacyRequests');
            Route::get('/{requestId}', 'getPrivacyRequest');
            Route::post('/', 'createNewPrivacyRequest');
            Route::post('/scan/{privacyRequest}', 'scanSystem');
            Route::post('/redact/{privacyRequest}', 'redactPpi');
            Route::post('/redact/{privacyRequest}/finish', 'finishRedactPpi');
            Route::post('/update-privacy-request/{privacyRequest}', 'updatePrivacyRequest');
        });

    Route::name('crm-delivery-logs.')->prefix('/crm-delivery-logs')
        ->middleware([PermissionType::COMPANY_CAMPAIGN_DELIVERY_LOGS_VIEW->getPermissionString()])
        ->controller(CompanyCampaignDeliveryLogController::class)
        ->group(function () {
            Route::get('/', 'listDeliveryLogs')->name('list');
            Route::get('/export', 'exportDeliveryLogs')->name('export');
        });

    Route::name('contact-delivery-logs.')->prefix('/contact-delivery-logs')
        ->middleware([PermissionType::COMPANY_CAMPAIGN_DELIVERY_LOGS_VIEW->getPermissionString()])
        ->controller(CompanyCampaignContactDeliveryLogController::class)
        ->group(function () {
            Route::get('/', 'listContactDeliveryLogs')->name('list');
        });

    Route::prefix('activity-logs')
        ->middleware([PermissionType::ACTIVITY_LOGS_CAMPAIGNS_VIEW->getPermissionString()])
        ->controller(ActivityLogController::class)
        ->group(function () {
            Route::get('/', 'getActivityLogs');
            Route::post('/', 'createNewLog');
        });

    Route::prefix('/companies/contractor-profile')
        ->controller(ContractorProfileController::class)
        ->group(function () {
            Route::patch('/toggle-display', 'toggleDisplay');
            Route::get('/business-hours', 'getBusinessHours');
            Route::patch('/business-hours', 'updateBusinessHours');
            Route::get('/brands-and-services', 'getBrandsAndServices');
            Route::patch('/brands-and-services', 'updateBrandsAndServices');
            Route::get('/credentials', 'getCredentials');
            Route::patch('/credentials', 'updateCredentials');
            Route::get('/content', 'getContent');
            Route::patch('/content', 'updateContent');
        });

    Route::prefix('/lead-refunds')
        ->middleware([PermissionType::COMPANY_CAMPAIGN_DELIVERY_LOGS_VIEW->getPermissionString()])
        ->group(function () {
            Route::get('/', [LeadRefundController::class, 'listRefunds']);
            Route::post('/', [LeadRefundController::class, 'create']);
            Route::get('/refund-data', [LeadRefundController::class, 'getLeadRefundData']);

            Route::prefix('/approvals')
                ->group(function () {
                    Route::get('/{approval}', [LeadRefundController::class, 'showApproval']);
                    Route::post('/{approval}/review', [LeadRefundController::class, 'reviewApproval']);
                    Route::post('/{approval}/comments', [LeadRefundController::class, 'addCommentsToApproval']);
                });
        });

    Route::prefix('/marketing-campaigns')
        ->controller(MarketingCampaignController::class)
        ->group(function () {
            Route::get('/filter-options', [MarketingCampaignController::class, 'getFilterOptions']);
            Route::get('/estimate', [MarketingCampaignController::class, 'getEstimate']);
            Route::get('/', 'listMarketingCampaigns');
            Route::get('/options', 'getMarketingCampaignOptions');
            Route::get('/callback-types', 'getMarketingCampaignCallbackTypes');
            Route::get('/types', 'getMarketingCampaignTypes');
            Route::get('/shortcodes', 'getMarketingCampaignShortcodes');
            Route::post('/', 'createMarketingCampaign');
            Route::post('/send-test-email', 'sendTestMarketingEmail');
            Route::post('/send-test-sms', 'sendTestMarketingSMS');
            Route::patch('/', 'updateMarketingCampaignTargets');
            Route::prefix('/domains')->group(function () {
                Route::get('/', [MarketingDomainController::class, 'index']);
                Route::post('/sync', [MarketingDomainController::class, 'syncDomains']);
            });
            Route::prefix('/consumers')->controller(MarketingCampaignConsumerController::class)
                ->group(function () {
                    Route::get('/', 'listMarketingConsumers');
            });
            Route::prefix('/logs')->controller(MarketingCampaignLogsController::class)
                ->group(function () {
                    Route::get('/', 'listMarketingLogs');
                    Route::get('/{logId}', 'getMarketingLog');
                });
            Route::prefix('/{marketingCampaign}')->group(function () {
                Route::get('/', 'getMarketingCampaign');
                Route::patch('/', 'updateMarketingCampaign');
                Route::delete('/', 'deleteMarketingCampaign');
                Route::post('/update-metrics', 'updateMarketingCampaignMetrics');
                Route::post('/toggle','toggleMarketingCampaignStatus');
            });
    });

    Route::prefix('/company-campaign-custom-pricing-logs')
        ->middleware([PermissionType::COMPANY_CAMPAIGN_CUSTOM_PRICING_LOGS_VIEW->getPermissionString()])
        ->controller(CompanyCampaignCustomPricingLogController::class)
        ->group(function () {
            Route::get('/state', 'listCompanyCampaignCustomStatePricingLogs');
            Route::get('/county', 'listCompanyCampaignCustomCountyPricingLogs');
        });

    Route::prefix('/affiliates-portal')
        ->name('affiliates-portal.')
        ->middleware([PermissionType::AFFILIATES->getPermissionString()])
        ->group(function() {
            Route::get('statistics', StatisticsController::class)->name('statistics');
            Route::prefix('affiliates')->name('affiliates.')->group(function () {
                Route::get('/', [AffiliateController::class, 'index'])->name('index');
                Route::post('/', [AffiliateController::class, 'store'])->name('store');
                Route::prefix('{affiliate:uuid}')->group(function () {
                    Route::get('/', [AffiliateController::class, 'show'])->name('show');
                    Route::patch('/', [AffiliateController::class, 'update'])->name('update');
                    Route::delete('/', [AffiliateController::class, 'destroy'])->name('destroy');
                    Route::get('/strategy-events', [PayoutStrategyController::class, 'getStrategyTimeline']);
                    Route::get('lead-details', [AffiliateController::class, 'getLeadDetails']);
                    Route::get('shadow-url', [AffiliateController::class, 'getShadowUrl'])->name('shadow-url');
                    Route::prefix('campaigns')->group(function () {
                        Route::get('/', [CampaignController::class, 'index'])->name('campaigns');
                        Route::prefix('{campaign}')->group(function () {
                            Route::get("/lead-details", [CampaignLeadDetailsController::class, 'index'])->name('lead-details');
                        });
                    });
                    Route::prefix('users')->name('users.')->group(function () {
                        Route::get('/', [AffiliateUserController::class, 'index'])->name('index');
                        Route::post('/', [AffiliateUserController::class, 'store'])->name('store');
                        Route::prefix('/{user}')->group(function () {
                            Route::patch('/', [AffiliateUserController::class, 'update'])->name('update');
                            Route::delete('/', [AffiliateUserController::class, 'destroy'])->name('destroy');
                        });
                    });
                });
                Route::prefix('/payment-strategy')->group(function () {
                    Route::get('/types', [PayoutStrategyController::class, 'types']);
                });
            });
        });
    Route::prefix('/template-management')
        ->middleware([PermissionType::TEMPLATE_MANAGEMENT->getPermissionString()])
        ->controller(TemplateManagementController::class)
        ->group(function () {
            Route::get('/template-selectors/get-initial-data', 'getTemplateSelectorData');
            Route::get('/template-selectors/{templateRelation}', 'getTemplateSelectors');
            Route::post('/template-selectors/{templateRelation}', 'saveTemplateSelector');
            Route::delete('/template-selectors/{templateRelation}/{templateSelector}', 'deleteTemplateSelector');
            Route::get('/{templateType}', 'getTemplates');
            Route::post('/{templateType}', 'createTemplate');
            Route::patch('/{templateType}/{template}', 'updateTemplate');
        });

    Route::prefix('/reviews')->controller(ReviewsAdminController::class)->group(function () {
        Route::get('/', 'getReviews');
        Route::prefix('/{review:uuid}')->group(function () {
            Route::get('/details', 'getReviewDetails');
            Route::post('/approve', 'approveReview');
            Route::post('/decline', 'declineReview');
            Route::post('/pending', 'setPendingReview');
            Route::post('/reply', 'postReviewReply');
        });
    });

    Route::prefix('company-manager')->group(function() {
        Route::get('/', [CompanyUserRelationshipController::class, 'list']);
        Route::get('/roles', [CompanyUserRelationshipController::class, 'listCompanyUserRelationshipRoles']);
        Route::prefix('{id}')->group(function () {
            Route::get('/', [CompanyUserRelationshipController::class, 'getCompanyUserRelationship']);
            Route::post('/', [CompanyUserRelationshipController::class, 'updateCompanyUserRelationship']);
        });
    });

    Route::prefix('/sales')->group(function () {
        Route::prefix('/management')->controller(SalesManagementAPIController::class)->group(function () {
            Route::get('/account-assignment-participants', 'getAccountAssignmentParticipants');
            Route::patch('/account-assignment-participants', 'updateAccountAssignmentParticipants');
        });
    });
});


Route::prefix('v2')->name('internal-api.v2.')->group(function () {
    Route::prefix('bundle-invoices')
        ->controller(BundleInvoiceApiControllerV2::class)
        ->middleware(['permission:manage-bundles|manage-bundle-invoices|view-bundles'])
        ->group(function () {
            Route::patch('/transition/{bundleInvoice}', 'executeStatusTransition');
        });


    Route::get('/need-love-companies', [NeedLoveCompaniesController::class, 'getNeedLoveCompanies']);

    Route::name('prospecting.')->prefix('/prospecting')->controller(ProspectingAPIController::class)->middleware('permission:prospecting')->group(function (){
        Route::get('/get-next-available-existing-company', 'getNextAvailableExistingCompany')->name('get-next-available-company');
        Route::get('/get-prospect/{newBuyerProspect:external_reference}', 'getProspect');
        Route::get('/get-next-available-prospect', 'getNextAvailableProspect')->name('get-next-available-prospect')->middleware('permission:work-sourced-prospects');
        Route::get('/get-next-available-registration', 'getNextAvailableRegistration');
        Route::get('/state-options', 'getStateOptions');
        Route::get('/city-options/{stateAbbr}', 'getCityOptions');
        Route::patch('/update-prospect/{prospect:external_reference}', 'updateProspect');
        Route::post('/convert-to-company/{prospect:external_reference}', 'convertToCompany');
        Route::post('/archive-prospect/{prospect:external_reference}', 'archiveProspect');
        Route::post('/release-back-to-queue/{prospect:external_reference}', 'releaseBackToQueue');
        Route::post('/release-company-back-to-queue/{company:id}', 'releaseCompanyBackToQueue')->name('release-company-back-to-queue');
        Route::get('/get-my-prospects', 'getMyProspects');
        Route::get('/get-my-converted-companies', 'getMyConvertedCompanies')->name('get-my-converted-companies');
        Route::post('/flag-as-duplicate', 'flagAsDuplicate');
        Route::get('/get-closer-for-demo', 'getCloserForDemo');
        Route::get('/scan-missed-products', 'scanMissedProducts')->name('scan-missed-products');
        Route::get('/get-verified-company-count', 'getVerifiedCompanyCount');
        Route::get('/get-most-recent-registrations', 'getMostRecentRegistrations');

        Route::prefix('/{companyId}')->group(function() {
            Route::post('/send-missed-products-notification', 'sendMissedProductsNotification');
            Route::get('/available-product-previews', 'getAvailableMissedProductPreviews');
            Route::post('/deliver-missed-products', 'deliverMissedProducts');
            Route::get('/available-email-recipients', 'getAvailableEmailRecipients');
        });

        Route::name('configuration.')->prefix('/configuration')->middleware('permission:update-prospecting-configurations')->group(function () {
            Route::get('/global', [ProspectingGlobalConfigurationController::class, 'index'])->name('global.index');
            Route::patch('/global', [ProspectingGlobalConfigurationController::class, 'update'])->name('global.update');

            Route::get('/user', [ProspectingUserConfigurationController::class, 'index'])->name('user.index');
            Route::post('/user', [ProspectingUserConfigurationController::class, 'store'])->name('user.store');
            Route::patch('/user/{roleConfiguration}', [ProspectingUserConfigurationController::class, 'update'])->name('user.update');

            Route::get('/users', [ProspectingUsersController::class, 'index'])->name('users.index');
        });
    });

    Route::name('company-manager-assignment-requests.')->prefix('/company-manager-assignment-requests')->controller(CompanyManagerAssignmentRequestController::class)->group(function () {
        Route::middleware('permission:action-company-manager-assignment-requests')->group(function () {
            Route::get('/get-pending-requests', 'getPendingRequests')->name('get-pending-requests');
            Route::get('/get-request-history', 'getRequestHistory')->name('get-request-history');
            Route::patch('/{request}/approve', 'approve')->name('approve');
            Route::patch('/{request}/deny', 'deny')->name('deny');
        });
        Route::get('/get-my-requests', 'getMyRequests')->name('get-my-requests');
        Route::post('/request-assignment', 'requestAssignment')->name('request-assignment');
    });

    Route::get('/campaigns/inactive', InactiveCampaignsController::class)
        ->name('campaigns.inactive');

    require __DIR__.'/metrics.php';
    require __DIR__.'/flow-engines.php';
});

require __DIR__ . '/internal-api-billing.php';
