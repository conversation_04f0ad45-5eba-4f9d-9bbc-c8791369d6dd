import {defineStore} from "pinia";
import {computed, ref} from "vue";
import ApiService from "../../vue/components/Billing/services/api";
import {DateTime} from 'luxon'
import useInvoiceHelper from "../../composables/useInvoiceHelper";
import {useInvoiceModalStore} from "./invoice-modal.store.js";

export const useInvoiceCollectionsStore = defineStore('invoice-collections-store',
    () => {
        const invoiceStore = useInvoiceModalStore()
        const saving = ref(false)

        const RECOVERY_STATUSES = {
            RECOVERED: {
                id: 'recovered',
                name: 'Recovered'
            },
            FAILED: {
                id: 'failed',
                name: 'Failed'
            },
        }

        const recoveryStatusOptions = Object.values(RECOVERY_STATUSES);

        const updateInvoiceCollectionsData = function (invoiceId, {
            amountRecovered,
            recoveryStatus,
        }) {
            saving.value = true
            try {
                invoiceStore.api.updateInvoiceCollectionsData(invoiceId, {
                    amountRecovered,
                    recoveryStatus,
                })
            } catch (err) {
                console.error(err)
            }

            invoiceStore.retrieveInvoiceData(invoiceId)

            saving.value = false
        }

        const shouldShowInvoiceCollections = computed(() => {
            return invoiceStore?.invoiceTotals?.outstanding > 0 || invoiceStore?.status?.id === 'collection'
        })

        const shouldShowInvoiceWriteOffs = computed(() => {
            return invoiceStore?.status?.id === 'written_off'
        })

        const canUpdateInvoiceCollections = computed(() => {
            return true
        })

        return {
            saving,
            recoveryStatusOptions,
            shouldShowInvoiceCollections,
            canUpdateInvoiceCollections,
            shouldShowInvoiceWriteOffs,
            updateInvoiceCollectionsData
        }
    });
