<template>
    <div v-if="grouping > 0"
         class="px-5 grid uppercase text-xs font-bold rounded grid-cols-7 gap-5 pb-3 items-center"
         :class="[darkMode ? 'text-slate-400' : 'text-slate-500']"
    >
        <p class="table-header-text">LOCATION</p>
        <p class="table-header-text">CONSUMER COUNT</p>
        <p class="table-header-text">AVG REVENUE/CONSUMER</p>
        <p class="table-header-text">AVG DAILY AVAILABLE BUDGET</p>
        <p class="table-header-text">POTENTIAL REVENUE</p>
        <div class="flex items-center gap-3">
            <p class="table-header-text">SELECT ALL</p>
            <custom-checkbox :dark-mode="darkMode" @update:modelValue="handleCheckboxUpdate" v-if="leads.length" :input-disabled="processing" />
        </div>
        <div>
            <custom-button
                :dark-mode="darkMode"
                @click="contactLeads"
                :disabled="disableBatchCreate"
            >
                Create Batch
            </custom-button>
        </div>
    </div>
    <div v-else
        class="px-5 grid uppercase text-xs font-bold rounded grid-cols-8 gap-5 pb-3 items-center"
        :class="[darkMode ? 'text-slate-400' : 'text-slate-500']"
    >
        <p class="table-header-text">ID / TYPE / DATE</p>
        <p class="table-header-text">STATUS</p>
        <p class="table-header-text">CONSUMER</p>
        <p class="table-header-text">ADDRESS</p>
        <p class="table-header-text">DETAILS</p>
        <p class="table-header-text">CONTACTED ON (MST)</p>
        <div class="flex items-center gap-3">
            <p class="table-header-text">SELECT ALL</p>
            <custom-checkbox :dark-mode="darkMode" @update:modelValue="handleCheckboxUpdate" v-if="leads.length" :input-disabled="processing" />
        </div>
        <div>
            <custom-button
                :dark-mode="darkMode"
                @click="contactLeads"
                :disabled="disableBatchCreate"
            >
                Create Batch
            </custom-button>
        </div>
    </div>
</template>

<script>
import CustomCheckbox from "../../Shared/SlideWizard/components/CustomCheckbox.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";

export default {
    name: "RecyclableLeadSearchTableHeader",
    components: {CustomButton, CustomCheckbox},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        leads: {
            type: Array,
            default: []
        },
        processing: {
            type: Boolean,
            default: false
        },
        grouping: {
            type: Number,
            default: 0,
        },
    },
    emits: ['select-all', 'contact-leads'],
    computed: {
        disableBatchCreate() {
           return this.processing || this.leads.filter(lead => lead.selected).length <= 0;
        }
    },
    methods: {
        handleCheckboxUpdate(result) {
            this.$emit('select-all', result);
        },
        contactLeads() {
            this.$emit('contact-leads');
        }
    }
}
</script>
