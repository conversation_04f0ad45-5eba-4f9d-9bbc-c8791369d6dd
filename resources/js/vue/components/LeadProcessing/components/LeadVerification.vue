<template>
    <div class="row-span-3 border rounded-lg"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <div class="flex items-center justify-between px-5 pt-5">
            <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Lead Verification</h5>
            <div class="flex items-center">
                <h5 class="text-sm font-medium mr-2" :class="{'text-grey-475' : !darkMode, 'text-grey-120' : darkMode}">
                    Confidence:
                </h5>
                <div class="text-sm">
                    {{ Math.round(whitepagesData.confidence) }}%
                </div>
            </div>
        </div>
        <Tab
            :dark-mode="darkMode"
            :tabs="tabs"
            @selected="switchTab"
            :tabs-classes="'w-full'"
            :tab-type="'Normal'"
            :default-tab-index="0"
        />

        <whitepages-verification v-if="selectedTab === 'Whitepages'" :data="whitepagesData" :dark-mode="darkMode"/>
        <ip-quality-score-verification v-else-if="selectedTab === 'IP Quality Score'" :lead-id="leadId" :api="api" :dark-mode="darkMode"/>
    </div>
</template>

<script>
    import WhitepagesVerification from "./WhitepagesVerification.vue";
    import IpQualityScoreVerification from "../../Shared/modules/Consumer/IpQualityScoreVerification.vue";
    import {ApiService} from "../services/api/api";
    import Tab from "../../Shared/components/Tab.vue";

    export default {
        name: "LeadVerification",
        components: {
            IpQualityScoreVerification,
            WhitepagesVerification,
            Tab
        },
        props: {
            api: ApiService,
            whitepagesData: Object,
            leadId: Number,
            darkMode: {
                type: Boolean,
                default: false,
            }
        },
        data: function() {
            return {
                showIpQualityScoreVerification: false,
                selectedTab: 'Whitepages',

                tabs: [
                    { name: 'Whitepages', current: true },
                    { name: 'IP Quality Score', current: false  },
                ]
            };
        },
        methods: {
            switchTab(tab) {
                this.selectedTab = tab;
            },
        }
    };
</script>
