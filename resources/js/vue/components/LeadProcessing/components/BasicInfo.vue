<template>
    <div>

        <div class="grid grid-cols-2 gap-4 h-full">

            <!-- Date Added -->
            <div id="date-added" class="row-span-2 border rounded-lg"
                 :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
                <div class="p-5">
                    <h5 class="text-primary-500 text-sm uppercase font-semibold pb-4 leading-tight">Date Added</h5>
                    <div class="flex items-center">
                        <svg class="mr-2 flex-shrink-0" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M3.5 0C3.26794 0 3.04538 0.0921872 2.88128 0.256282C2.71719 0.420376 2.625 0.642936 2.625 0.875V1.75H1.75C1.28587 1.75 0.840752 1.93437 0.512563 2.26256C0.184374 2.59075 0 3.03587 0 3.5V12.25C0 12.7141 0.184374 13.1592 0.512563 13.4874C0.840752 13.8156 1.28587 14 1.75 14H12.25C12.7141 14 13.1592 13.8156 13.4874 13.4874C13.8156 13.1592 14 12.7141 14 12.25V3.5C14 3.03587 13.8156 2.59075 13.4874 2.26256C13.1592 1.93437 12.7141 1.75 12.25 1.75H11.375V0.875C11.375 0.642936 11.2828 0.420376 11.1187 0.256282C10.9546 0.0921872 10.7321 0 10.5 0C10.2679 0 10.0454 0.0921872 9.88128 0.256282C9.71719 0.420376 9.625 0.642936 9.625 0.875V1.75H4.375V0.875C4.375 0.642936 4.28281 0.420376 4.11872 0.256282C3.95462 0.0921872 3.73206 0 3.5 0V0ZM3.5 4.375C3.26794 4.375 3.04538 4.46719 2.88128 4.63128C2.71719 4.79538 2.625 5.01794 2.625 5.25C2.625 5.48206 2.71719 5.70462 2.88128 5.86872C3.04538 6.03281 3.26794 6.125 3.5 6.125H10.5C10.7321 6.125 10.9546 6.03281 11.1187 5.86872C11.2828 5.70462 11.375 5.48206 11.375 5.25C11.375 5.01794 11.2828 4.79538 11.1187 4.63128C10.9546 4.46719 10.7321 4.375 10.5 4.375H3.5Z" fill="#0081FF"/>
                        </svg>
                        <p class="pb-0">{{ lead.date_added }}</p>
                    </div>
                </div>
            </div>

            <!-- Utility -->
            <div id="utility" class="row-span-3 border rounded-lg"
                 :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}" v-if="lead.utility !== null && lead.electric_cost != null && lead.electric_cost > 0">
                <div class="p-5">
                    <h5 class="text-primary-500 text-sm uppercase font-semibold pb-4 leading-tight">Utility</h5>
                    <div class="flex items-center mb-3">
                        <svg class="mr-2 flex-shrink-0" width="14" height="18" viewBox="0 0 14 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M8.30045 0.0460013C8.50345 0.109843 8.6808 0.236782 8.80669 0.408357C8.93258 0.579932 9.00046 0.787194 9.00045 1V6H13.0004C13.1834 5.99992 13.3628 6.05001 13.5192 6.14483C13.6757 6.23964 13.8031 6.37554 13.8876 6.53774C13.9722 6.69995 14.0106 6.88223 13.9988 7.06477C13.9869 7.2473 13.9253 7.42309 13.8204 7.573L6.82044 17.573C6.69862 17.7475 6.52428 17.8787 6.32278 17.9473C6.12129 18.0159 5.90317 18.0184 5.70014 17.9545C5.49711 17.8906 5.31978 17.7635 5.19394 17.5919C5.06809 17.4202 5.00031 17.2129 5.00044 17V12H1.00045C0.817527 12.0001 0.63809 11.95 0.481663 11.8552C0.325235 11.7604 0.19781 11.6245 0.113255 11.4623C0.0286992 11.3001 -0.00974701 11.1178 0.00209983 10.9352C0.0139467 10.7527 0.0756328 10.5769 0.180445 10.427L7.18045 0.427001C7.30246 0.252788 7.47683 0.122014 7.67825 0.0536774C7.87966 -0.0146589 8.09761 -0.0169975 8.30045 0.0470013V0.0460013Z" fill="#0081FF"/>
                        </svg>
                        <p class="pb-0 ">{{ lead.utility.name }} ({{ lead.utility.abbr }})</p>
                    </div>
                    <div class="flex items-center">
                        <svg @click="editField('electric_cost')" class="cursor-pointer mr-2 flex-shrink-0" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M7 14C10.866 14 14 10.866 14 7C14 3.13401 10.866 0 7 0C3.13401 0 0 3.13401 0 7C0 10.866 3.13401 14 7 14ZM6.75803 10.7183V11.5294H7.34161V10.7188C7.81225 10.6943 8.22266 10.6083 8.57284 10.4608C8.99511 10.2815 9.31774 10.0302 9.54074 9.70703C9.76611 9.38148 9.87998 8.99695 9.88235 8.55345C9.87998 8.25149 9.82423 7.98373 9.71511 7.75018C9.60835 7.51664 9.45771 7.31376 9.26318 7.14154C9.06865 6.96933 8.83854 6.82425 8.57284 6.7063C8.30714 6.58834 8.01535 6.49398 7.69746 6.42321L7.34161 6.33859V4.57542C7.58218 4.60644 7.78145 4.67978 7.93943 4.79545C8.15531 4.95351 8.27749 5.17526 8.30596 5.46071H9.78272C9.7756 5.029 9.65936 4.64919 9.43399 4.32128C9.20862 3.99337 8.8931 3.73741 8.48744 3.5534C8.15285 3.40074 7.77091 3.31141 7.34161 3.2854V2.47059H6.75803V3.28851C6.35157 3.31804 5.98149 3.40633 5.64779 3.5534C5.23026 3.73741 4.90169 3.99337 4.66209 4.32128C4.42486 4.64919 4.30743 5.03254 4.3098 5.47132C4.30743 6.00683 4.48417 6.43264 4.84001 6.74876C5.19586 7.06487 5.681 7.29724 6.29542 7.44586L6.75803 7.56138V9.4259C6.60101 9.40738 6.45511 9.37253 6.32033 9.32132C6.11157 9.23876 5.94432 9.11726 5.81859 8.95685C5.69523 8.79407 5.62525 8.59119 5.60864 8.34821H4.11765C4.12951 8.86956 4.25643 9.30717 4.4984 9.66103C4.74275 10.0125 5.08199 10.2779 5.51612 10.4572C5.87793 10.6058 6.2919 10.6928 6.75803 10.7183ZM7.34161 9.42228C7.47799 9.40397 7.60254 9.37268 7.71525 9.3284C7.90978 9.25291 8.06042 9.14793 8.16718 9.01347C8.27393 8.879 8.32731 8.72448 8.32731 8.54991C8.32731 8.38713 8.27867 8.25031 8.18141 8.13943C8.08652 8.02855 7.94655 7.93419 7.76151 7.85634C7.63994 7.80453 7.49997 7.75585 7.34161 7.71031V9.42228ZM6.75803 6.19354V4.57796C6.63379 4.59531 6.5223 4.62416 6.42353 4.66452C6.25035 4.73294 6.1175 4.82848 6.02498 4.95115C5.93483 5.07382 5.88976 5.21301 5.88976 5.3687C5.88502 5.49845 5.9123 5.61169 5.9716 5.70841C6.03329 5.80513 6.1175 5.88888 6.22426 5.95965C6.33101 6.02806 6.45437 6.08822 6.59434 6.14012C6.64753 6.15895 6.70209 6.17675 6.75803 6.19354Z" fill="#0081FF"/>
                        </svg>
                        <edit-field :dark-mode="darkMode" @update-field="updateField" @cancel-edit="cancelEdit" v-if="editing.electric_cost" :value="lead.electric_cost" :field-name="'electric_cost'"></edit-field>
                        <p v-else class="pb-0 " :class="{'font-bold text-red-500': lead.electric_cost <= 50}">${{ lead.electric_cost }}</p>
                    </div>
                </div>
            </div>

            <!-- Contact -->
            <div id="contact" class="row-span-5 border rounded-lg"
                 :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
                <div class="p-5">
                    <h5 class="text-primary-500 text-sm uppercase font-semibold pb-4 leading-tight">Contact</h5>
                    <div class="flex items-center truncate pb-3">
                        <svg @click="editField('name')" class="cursor-pointer mr-2 flex-shrink-0" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M14 7C14 8.85652 13.2625 10.637 11.9497 11.9497C10.637 13.2625 8.85652 14 7 14C5.14348 14 3.36301 13.2625 2.05025 11.9497C0.737498 10.637 0 8.85652 0 7C0 5.14348 0.737498 3.36301 2.05025 2.05025C3.36301 0.737498 5.14348 0 7 0C8.85652 0 10.637 0.737498 11.9497 2.05025C13.2625 3.36301 14 5.14348 14 7V7ZM8.75 4.375C8.75 4.83913 8.56563 5.28425 8.23744 5.61244C7.90925 5.94063 7.46413 6.125 7 6.125C6.53587 6.125 6.09075 5.94063 5.76256 5.61244C5.43437 5.28425 5.25 4.83913 5.25 4.375C5.25 3.91087 5.43437 3.46575 5.76256 3.13756C6.09075 2.80937 6.53587 2.625 7 2.625C7.46413 2.625 7.90925 2.80937 8.23744 3.13756C8.56563 3.46575 8.75 3.91087 8.75 4.375V4.375ZM7 7.875C6.16228 7.87483 5.34212 8.11518 4.63699 8.56748C3.93186 9.01978 3.37141 9.66501 3.02225 10.4265C3.51459 10.9993 4.12499 11.4588 4.81158 11.7735C5.49817 12.0883 6.2447 12.2508 7 12.25C7.7553 12.2508 8.50183 12.0883 9.18842 11.7735C9.87501 11.4588 10.4854 10.9993 10.9777 10.4265C10.6286 9.66501 10.0681 9.01978 9.36301 8.56748C8.65788 8.11518 7.83772 7.87483 7 7.875V7.875Z" fill="#0081FF"/>
                        </svg>
                        <edit-field :dark-mode="darkMode" @update-field="updateField" @cancel-edit="cancelEdit" v-if="editing.name" :value="lead.name" :field-name="'name'"></edit-field>
                        <p v-else class="pb-0 truncate">{{ lead.name }}</p>
                    </div>
                    <div class="flex items-center truncate pb-3">
                        <svg @click="editField('email')" class="cursor-pointer mr-2 flex-shrink-0" width="14" height="11" viewBox="0 0 14 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M0.00262451 1.6485L7 5.14675L13.9974 1.6485C13.9715 1.20264 13.7761 0.783552 13.4512 0.477073C13.1264 0.170595 12.6966 -8.18866e-05 12.25 2.9473e-08H1.75C1.30339 -8.18866e-05 0.873641 0.170595 0.54878 0.477073C0.223919 0.783552 0.0285281 1.20264 0.00262451 1.6485V1.6485Z" fill="#0081FF"/>
                            <path d="M14 3.60324L7 7.10324L0 3.60324V8.74999C0 9.21412 0.184374 9.65924 0.512563 9.98743C0.840752 10.3156 1.28587 10.5 1.75 10.5H12.25C12.7141 10.5 13.1592 10.3156 13.4874 9.98743C13.8156 9.65924 14 9.21412 14 8.74999V3.60324Z" fill="#0081FF"/>
                        </svg>
                        <edit-field :dark-mode="darkMode" @update-field="updateField" @cancel-edit="cancelEdit" v-if="editing.email" :value="lead.email" :field-name="'email'"></edit-field>
                        <p v-else class="pb-0 truncate">{{ lead.email }}</p>
                    </div>
                    <div class="flex items-center justify-between pb-3">
                        <div class="flex items-center">
                            <svg class="mr-2 flex-shrink-0" width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M7.61958 0.256582C7.45524 0.0922926 7.23238 0 7 0C6.76762 0 6.54476 0.0922926 6.38042 0.256582L0.246001 6.391C0.0863671 6.55629 -0.00196356 6.77765 3.31282e-05 7.00743C0.00202981 7.2372 0.0941942 7.457 0.256676 7.61948C0.419158 7.78196 0.638956 7.87413 0.868731 7.87612C1.09851 7.87812 1.31987 7.78979 1.48515 7.63016L1.74192 7.37339V13.145C1.74192 13.3774 1.83425 13.6003 1.9986 13.7647C2.16295 13.929 2.38585 14.0213 2.61827 14.0213H4.37096C4.60338 14.0213 4.82628 13.929 4.99063 13.7647C5.15498 13.6003 5.24731 13.3774 5.24731 13.145V11.3923C5.24731 11.1599 5.33964 10.937 5.50398 10.7726C5.66833 10.6083 5.89123 10.516 6.12365 10.516H7.87635C8.10877 10.516 8.33167 10.6083 8.49602 10.7726C8.66036 10.937 8.75269 11.1599 8.75269 11.3923V13.145C8.75269 13.3774 8.84502 13.6003 9.00937 13.7647C9.17371 13.929 9.39662 14.0213 9.62904 14.0213H11.3817C11.6142 14.0213 11.8371 13.929 12.0014 13.7647C12.1657 13.6003 12.2581 13.3774 12.2581 13.145V7.37339L12.5148 7.63016C12.6801 7.78979 12.9015 7.87812 13.1313 7.87612C13.361 7.87413 13.5808 7.78196 13.7433 7.61948C13.9058 7.457 13.998 7.2372 14 7.00743C14.002 6.77765 13.9136 6.55629 13.754 6.391L7.61958 0.256582V0.256582Z" fill="#0081FF"/>
                            </svg>
                            <p class="pb-0 break-words">{{ lead.address.street }}, {{ lead.address.city }}, {{ lead.address.state_abbr }} {{ lead.address.zip_code }}</p>
                        </div>
                        <div v-if="!editing.own_property">
                          <div @click="editField('own_property')" v-if="darkMode" class="cursor-pointer px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap" :class="{'text-blue-550' : lead.own_property === true, 'text-red-350' : lead.own_property !== true}">
                            <svg v-if="lead.own_property === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#0081FF"/>
                            </svg>
                            <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                              <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <p v-if="lead.own_property === true" class="text-sm">Property Owner</p>
                            <p v-else class="text-sm">Not Property Owner</p>
                          </div>
                          <div @click="editField('own_property')" v-if="!darkMode" class="cursor-pointer px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap" :class="{'text-blue-550 bg-cyan-125' : lead.own_property === true, 'text-red-350 bg-red-75' : lead.own_property !== true}">
                            <svg v-if="lead.own_property === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#0081FF"/>
                            </svg>
                            <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                              <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                              <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <p v-if="lead.own_property === true" class="text-sm">Property Owner</p>
                            <p v-else class="text-sm">Not Property Owner</p>
                          </div>
                        </div>
                        <edit-field :dark-mode="darkMode" v-else type='select' :options="propertyOptions" @update-field="updateField" @cancel-edit="cancelEdit" :value="lead.own_property" :field-name="'own_property'"></edit-field>
                    </div>
                    <div class="flex items-center justify-between">
                        <div class="flex items-center">
                            <svg @click="editField('phone')" class="cursor-pointer mr-2 flex-shrink-0" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M13.7428 10.9665L10.5482 8.06198C10.3972 7.92473 10.1988 7.85153 9.99482 7.85784C9.79086 7.86414 9.59732 7.94946 9.45508 8.09577L7.57448 10.0298C7.12182 9.94335 6.21177 9.65965 5.27501 8.72525C4.33825 7.78771 4.05455 6.87532 3.97046 6.4258L5.90293 4.54443C6.04942 4.40229 6.13487 4.20873 6.14117 4.00471C6.14748 3.80068 6.07416 3.60221 5.93672 3.45129L3.03291 0.257523C2.89542 0.106131 2.70432 0.0142999 2.50021 0.00153385C2.2961 -0.0112322 2.09505 0.056072 1.93976 0.189152L0.234412 1.65165C0.0985433 1.78801 0.0174476 1.9695 0.00650858 2.16168C-0.00527953 2.35815 -0.23004 7.01206 3.3787 10.6223C6.52691 13.7697 10.4704 14 11.5565 14C11.7153 14 11.8127 13.9953 11.8386 13.9937C12.0308 13.983 12.2122 13.9015 12.3479 13.765L13.8096 12.0589C13.9432 11.9041 14.011 11.7032 13.9985 11.4991C13.9861 11.295 13.8943 11.1039 13.7428 10.9665Z" fill="#0081FF"/>
                            </svg>

                            <div v-if="canCallConsumer" class="flex items-center">
                                <div v-if="!lead.can_call" class="text-red-500">
                                    Recently Contacted
                                </div>
                                <edit-field :dark-mode="darkMode" @update-field="updateField" @cancel-edit="cancelEdit" v-if="editing.phone" :value="lead.phone" :field-name="'phone'"></edit-field>
                                <p v-else class="pb-0 text-lg">{{ lead.phone }}</p>

                                <div v-if="!editing.phone" class="inline-flex items-center ml-2">
                                    <button @click="call" class="inline-flex items-center px-3 py-1 mx-2 bg-primary-500 hover:bg-blue-500 transition duration-200 rounded text-xs font-medium text-white">
                                        <svg class="mr-1 w-3" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M11.7796 9.3999L9.04134 6.91027C8.91191 6.79263 8.74182 6.72988 8.56699 6.73529C8.39216 6.74069 8.22628 6.81382 8.10435 6.93923L6.49241 8.59697C6.10441 8.52287 5.32438 8.2797 4.52144 7.47879C3.7185 6.67518 3.47533 5.89313 3.40325 5.50783L5.05965 3.89523C5.18522 3.77339 5.25846 3.60748 5.26386 3.4326C5.26927 3.25773 5.20642 3.08761 5.08862 2.95825L2.59964 0.220734C2.48179 0.0909691 2.31799 0.0122571 2.14304 0.00131473C1.96808 -0.00962763 1.79575 0.0480617 1.66265 0.162131L0.200925 1.4157C0.0844657 1.53258 0.0149551 1.68814 0.00557879 1.85287C-0.00452532 2.02127 -0.197177 6.01034 2.89603 9.10486C5.59449 11.8026 8.97465 12 9.90558 12C10.0416 12 10.1252 11.996 10.1474 11.9946C10.3121 11.9854 10.4676 11.9156 10.5839 11.7986L11.8368 10.3362C11.9513 10.2035 12.0094 10.0313 11.9987 9.8564C11.988 9.68146 11.9094 9.51763 11.7796 9.3999V9.3999Z" fill="white"/>
                                        </svg>
                                        Call
                                    </button>
                                    <button @click="sms"  class="inline-flex items-center px-3 py-1 border border-primary-500 hover:bg-cyan-125 transition duration-200 rounded text-xs font-medium text-blue-550">
                                        <svg class="mr-1 w-3" viewBox="0 0 12 11" fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <path d="M0 2C0 1.46957 0.229869 0.960859 0.63904 0.585786C1.04821 0.210714 1.60316 0 2.18182 0H9.81818C10.3968 0 10.9518 0.210714 11.361 0.585786C11.7701 0.960859 12 1.46957 12 2V6C12 6.53043 11.7701 7.03914 11.361 7.41421C10.9518 7.78929 10.3968 8 9.81818 8H7.63636L4.36364 11V8H2.18182C1.60316 8 1.04821 7.78929 0.63904 7.41421C0.229869 7.03914 0 6.53043 0 6V2Z" fill="#0081FF"/>
                                        </svg>
                                        Text
                                    </button>
                                </div>
                            </div>
                            <div v-else>
                                REDACTED: Too Recently Contacted
                            </div>
                        </div>
                        <div v-if="darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap" :class="{'text-green-550' : lead.phone_verification_status === true, 'text-red-350' : lead.phone_verification_status !== true}">
                            <svg v-if="lead.phone_verification_status === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                            </svg>
                            <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <p v-if="lead.phone_verification_status === true" class="text-sm">Phone Verified</p>
                            <p v-else class="text-sm">Phone Unverified</p>
                        </div>
                        <div v-if="!darkMode" class="px-4 inline-flex items-center rounded-full py-1 font-medium whitespace-no-wrap" :class="{'text-green-550 bg-green-150' : lead.phone_verification_status === true, 'text-red-350 bg-red-75' : lead.phone_verification_status !== true}">
                            <svg v-if="lead.phone_verification_status === true" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#00AE07"/>
                            </svg>
                            <svg v-else class="mr-1 flex-shrink-0" width="10" height="10" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            </svg>
                            <p v-if="lead.phone_verification_status === true" class="text-sm">Phone Verified</p>
                            <p v-else class="text-sm">Phone Unverified</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Lead Info -->
            <div id="lead-info" class="row-span-4 border rounded-lg"
                 :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
                <div class="p-5">
                    <h5 class="text-primary-500 text-sm uppercase font-semibold pb-4 leading-tight">Lead Info</h5>
                    <div class="flex items-center pb-3">
                        <svg @click="editField('companies_requested')" class="cursor-pointer mr-2 flex-shrink-0" width="14" height="12" viewBox="0 0 14 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9.33333 2.33333C9.33333 2.95217 9.0875 3.54566 8.64992 3.98325C8.21233 4.42083 7.61884 4.66667 7 4.66667C6.38116 4.66667 5.78767 4.42083 5.35008 3.98325C4.9125 3.54566 4.66667 2.95217 4.66667 2.33333C4.66667 1.71449 4.9125 1.121 5.35008 0.683418C5.78767 0.245833 6.38116 0 7 0C7.61884 0 8.21233 0.245833 8.64992 0.683418C9.0875 1.121 9.33333 1.71449 9.33333 2.33333V2.33333ZM13.2222 3.88889C13.2222 4.30145 13.0583 4.69711 12.7666 4.98883C12.4749 5.28056 12.0792 5.44444 11.6667 5.44444C11.2541 5.44444 10.8584 5.28056 10.5667 4.98883C10.275 4.69711 10.1111 4.30145 10.1111 3.88889C10.1111 3.47633 10.275 3.08067 10.5667 2.78894C10.8584 2.49722 11.2541 2.33333 11.6667 2.33333C12.0792 2.33333 12.4749 2.49722 12.7666 2.78894C13.0583 3.08067 13.2222 3.47633 13.2222 3.88889V3.88889ZM10.1111 9.33333C10.1111 8.50822 9.78333 7.71689 9.19989 7.13345C8.61644 6.55 7.82512 6.22222 7 6.22222C6.17488 6.22222 5.38356 6.55 4.80011 7.13345C4.21667 7.71689 3.88889 8.50822 3.88889 9.33333V11.6667H10.1111V9.33333ZM3.88889 3.88889C3.88889 4.30145 3.725 4.69711 3.43328 4.98883C3.14155 5.28056 2.74589 5.44444 2.33333 5.44444C1.92077 5.44444 1.52511 5.28056 1.23339 4.98883C0.941666 4.69711 0.777778 4.30145 0.777778 3.88889C0.777778 3.47633 0.941666 3.08067 1.23339 2.78894C1.52511 2.49722 1.92077 2.33333 2.33333 2.33333C2.74589 2.33333 3.14155 2.49722 3.43328 2.78894C3.725 3.08067 3.88889 3.47633 3.88889 3.88889V3.88889ZM11.6667 11.6667V9.33333C11.6678 8.54255 11.467 7.76458 11.0833 7.07311C11.4282 6.98487 11.7886 6.97655 12.1371 7.04878C12.4856 7.12102 12.8131 7.2719 13.0944 7.48993C13.3758 7.70796 13.6036 7.98736 13.7605 8.30683C13.9175 8.6263 13.9994 8.9774 14 9.33333V11.6667H11.6667ZM2.91667 7.07311C2.53303 7.76459 2.33224 8.54256 2.33333 9.33333V11.6667H2.06671e-07V9.33333C-0.000149697 8.97714 0.0812493 8.62564 0.237959 8.30578C0.394668 7.98591 0.622529 7.70617 0.904076 7.48799C1.18562 7.26981 1.51339 7.11898 1.86224 7.04707C2.2111 6.97516 2.57179 6.98406 2.91667 7.07311V7.07311Z" fill="#0081FF"/>
                        </svg>
                        <edit-field :dark-mode="darkMode" type='select' :options="companiesRequestedOptions" @update-field="updateField" @cancel-edit="cancelEdit" v-if="editing.companies_requested" :value="lead.companies_requested" :field-name="'companies_requested'"></edit-field>
                        <p v-else-if="lead.companies_requested === 0" class="text-red-500 pb-0 ">0 Companies Requested</p>
                        <p v-else class="pb-0 ">{{ lead.companies_requested }} {{ lead.companies_requested > 1 ? 'Companies' : 'Company' }} Requested</p>
                    </div>
                    <div class="flex items-center pb-3">
                        <svg @click="editField('appointments_requested')" class="cursor-pointer mr-2 flex-shrink-0" width="14" height="13" viewBox="0 0 14 13" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12.6 0H1.4C1.0287 0 0.672601 0.1475 0.41005 0.41005C0.1475 0.672601 0 1.0287 0 1.4V11.2C0 11.5713 0.1475 11.9274 0.41005 12.19C0.672601 12.4525 1.0287 12.6 1.4 12.6H12.6C12.9713 12.6 13.3274 12.4525 13.5899 12.19C13.8525 11.9274 14 11.5713 14 11.2V1.4C14 1.0287 13.8525 0.672601 13.5899 0.41005C13.3274 0.1475 12.9713 0 12.6 0ZM6.3 9.8H2.1V8.4H6.3V9.8ZM11.9 7H2.1V5.6H11.9V7ZM11.9 4.2H2.1V2.8H11.9V4.2Z" fill="#0081FF"/>
                        </svg>
                        <edit-field :dark-mode="darkMode" @update-field="updateField" @cancel-edit="cancelEdit" v-if="editing.appointments_requested" :value="lead.appointments_requested" :field-name="'appointments_requested'"></edit-field>
                        <p v-else class="pb-0 ">{{ lead.appointments_requested ? lead.appointments_requested : "No Appointments Requested" }}</p>
                    </div>
                    <div class="flex items-center pb-3">
                        <svg @click="editField('best_time_to_contact')" class="cursor-pointer mr-2 flex-shrink-0" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M7 14C8.85652 14 10.637 13.2625 11.9497 11.9497C13.2625 10.637 14 8.85652 14 7C14 5.14348 13.2625 3.36301 11.9497 2.05025C10.637 0.737498 8.85652 0 7 0C5.14348 0 3.36301 0.737498 2.05025 2.05025C0.737498 3.36301 0 5.14348 0 7C0 8.85652 0.737498 10.637 2.05025 11.9497C3.36301 13.2625 5.14348 14 7 14V14ZM7.875 3.5C7.875 3.26794 7.78281 3.04538 7.61872 2.88128C7.45462 2.71719 7.23206 2.625 7 2.625C6.76794 2.625 6.54538 2.71719 6.38128 2.88128C6.21719 3.04538 6.125 3.26794 6.125 3.5V7C6.12505 7.23205 6.21727 7.45457 6.38138 7.61862L8.85588 10.094C8.93717 10.1753 9.03368 10.2398 9.1399 10.2838C9.24612 10.3278 9.35997 10.3504 9.47494 10.3504C9.58991 10.3504 9.70375 10.3278 9.80997 10.2838C9.91619 10.2398 10.0127 10.1753 10.094 10.094C10.1753 10.0127 10.2398 9.91619 10.2838 9.80997C10.3278 9.70375 10.3504 9.58991 10.3504 9.47494C10.3504 9.35997 10.3278 9.24612 10.2838 9.1399C10.2398 9.03368 10.1753 8.93717 10.094 8.85588L7.875 6.63775V3.5Z" fill="#0081FF"/>
                        </svg>
                        <edit-field :dark-mode="darkMode" @update-field="updateField" @cancel-edit="cancelEdit" v-if="editing.best_time_to_contact" :value="lead.best_time_to_contact" :field-name="'best_time_to_contact'"></edit-field>
                        <p v-else class="pb-0 ">{{ lead.best_time_to_contact ? lead.best_time_to_contact : "Not Specified" }}</p>
                    </div>
                    <div class="flex items-center pb-3">
                        <svg class="mr-2 flex-shrink-0" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M7 0C3.1402 0 0 3.1402 0 7C0 10.8598 3.1402 14 7 14C10.8598 14 14 10.8598 14 7C14 3.1402 10.8598 0 7 0ZM12.5517 6.3H10.6169C10.5305 4.76787 10.1019 3.27458 9.3625 1.9299C10.2194 2.33034 10.9612 2.94098 11.5188 3.70504C12.0764 4.4691 12.4317 5.36176 12.5517 6.3V6.3ZM7.371 1.4189C8.0955 2.3737 9.0699 4.0649 9.2099 6.3H4.921C5.0183 4.4828 5.6168 2.7804 6.6367 1.4182C6.7571 1.4112 6.8775 1.4 7 1.4C7.1253 1.4 7.2478 1.4112 7.371 1.4189ZM4.6816 1.9089C3.9928 3.2326 3.5952 4.7334 3.521 6.3H1.4483C1.56924 5.35349 1.92964 4.45347 2.49542 3.68509C3.06121 2.91671 3.81366 2.30537 4.6816 1.9089V1.9089ZM1.4483 7.7H3.5301C3.6253 9.3653 3.9956 10.8346 4.6193 12.061C3.76683 11.6592 3.02941 11.0489 2.47517 10.2867C1.92093 9.52446 1.56776 8.63483 1.4483 7.7V7.7ZM6.615 12.5811C5.6343 11.3925 5.0554 9.7272 4.9287 7.7H9.2078C9.0622 9.6411 8.4259 11.3372 7.3857 12.5804C7.2583 12.5888 7.1309 12.6 7 12.6C6.8698 12.6 6.7431 12.5888 6.615 12.5811ZM9.4227 12.0407C10.0912 10.7849 10.4993 9.31 10.6064 7.7H12.551C12.4329 8.62699 12.0848 9.50972 11.5384 10.2678C10.992 11.026 10.2647 11.6354 9.4227 12.0407V12.0407Z" fill="#0081FF"/>
                        </svg>
                        <p class="pb-0 ">{{ lead.origin ? lead.origin : "Unknown" }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import EditField from "./EditField.vue";
import DispatchesGlobalEventsMixin from "../../../mixins/dispatches-global-events-mixin";
import {CommunicationRelationTypes} from "../../Communications/enums/communication";
import {ROLES, useRolesPermissions} from "../../../../stores/roles-permissions.store.js";

export default {
  name: "BasicInfo",
  components: {
    EditField
  },
  props: {
        lead: Object,
        darkMode: {
            type: Boolean,
            default: false,
        },
        canEdit: {
            type: Boolean,
            default: true
        }
    },
    data() {
      return {
        editing: {
          name: false,
          email: false,
          phone: false,
          best_time_to_contact: false,
          own_property: false,
          appointments_requested: false,
          electric_cost: false,
          companies_requested: false,
        },
        temporaryField: '',
        propertyOptions: [
          {id: true, value: "Yes"},
          {id: false, value: "No"}
        ],
        companiesRequestedOptions: [
          {id: 1, value: 1},
          {id: 2, value: 2},
          {id: 3, value: 3},
          {id: 4, value: 4},
        ],
          permissionStore: useRolesPermissions(),
      }
    },
    computed: {
        canCallConsumer() {
            return this.lead.can_call || this.permissionStore.hasRole(ROLES.LEADS_AUDITOR)
        },
    },
    mixins: [DispatchesGlobalEventsMixin],
    methods: {
      updateField(fieldName, fieldValue) {
        this.editing[fieldName] = false;
        this.$emit('update-basic-field', fieldName, fieldValue);
      },
      editField(fieldName) {
          if(!this.canEdit)
              return;

        this.editing[fieldName] = true;
      },
      cancelEdit(fieldName) {
        this.editing[fieldName] = false;
      },
        call() {
            this.dispatchGlobalEvent('call', {phone: this.lead.phone, name: this.lead.name, id: this.lead.id, relId: this.lead.consumer_product_id, relType: CommunicationRelationTypes.LEAD});
        },
        sms() {
            this.dispatchGlobalEvent('sms', {phone: this.lead.phone, name: this.lead.name, id: this.lead.id, relId: this.lead.consumer_product_id, relType: CommunicationRelationTypes.LEAD});
        }
    }
};
</script>
