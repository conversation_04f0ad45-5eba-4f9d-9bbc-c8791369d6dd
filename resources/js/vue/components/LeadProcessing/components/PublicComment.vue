<template>
    <div id="public-comment">
        <div class="p-5 border rounded-lg" :class="{'bg-light-module border-light-border text-gray-700': !darkMode, 'bg-dark-module border-dark-border text-gray-300': darkMode}">
            <div>
                <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Public Comments</h5>
            </div>
            <!--    TODO: remove this component completely some time after release -->
            <div class="mt-5 px-10 italic text-sm">
                Public comments can now be added by selecting the 'Send to Company' option when adding free text comments from Lead Search, from the Comments & Activity module above, or from the 'Select a reason...' dialog pop up when actioning a lead.
            </div>
        </div>
    </div>
</template>

<script>
    export default {
        name: "PublicComment",
        props: {
            modelValue: {
                type: String,
                default: ''
            },
            darkMode: {
                type: Boolean,
                default: false,
            }
        },
    };
</script>
