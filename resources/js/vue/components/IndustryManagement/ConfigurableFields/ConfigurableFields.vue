<template>
    <div>
        <alerts-container v-if="alertActive" :alert-type="alertType" :text="alertText" :dark-mode="darkMode"/>
        <div class="flex items-center gap-5 p-5">
            <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Selected Categories</h5>
            <div class="flex items-center gap-3">
                <div class="flex flex-col justify-center min-w-[9rem]">
                    <dropdown :dark-mode="darkMode" :options="dropdownOptions.type" :placeholder="'Choose a type'" v-model="type"></dropdown>
                </div>
                <div v-if="type?.length > 0" class="flex flex-col justify-center min-w-[9rem]">
                    <dropdown :dark-mode="darkMode" :options="dropdownOptions.category" :placeholder="'Choose a Scope'" v-model="category"></dropdown>
                </div>
                <div v-if="category?.length > 0 && category !== 'global'" class="flex flex-col justify-center min-w-[9rem]">
                    <dropdown :dark-mode="darkMode" :options="dropdownOptions.categoryIds" :placeholder="'choose a category'" v-model="categoryId"></dropdown>
                </div>
                <div class="flex flex-col justify-end">
                    <button class="transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5 whitespace-nowrap"
                            @click="getFields">
                        Select
                    </button>
                </div>
                <button
                    v-if="type === 'consumer' && category && categoryId"
                    class="transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5 whitespace-nowrap"
                    @click="() => isConsumerFieldsModuleVisibilityModalVisible = true"
                >
                    Field Visibility
                </button>
            </div>
        </div>
        <div class="relative">
            <div>
                <div v-if="loading || saving" class="flex flex-col h-[28rem] items-center justify-center w-full">
                    <loading-spinner :label="saving ? 'Saving' : 'Loading'" />
                </div>
                <div v-else-if="fields?.length > 0">
                    <div id="fieldList">
                        <div class="grid gap-5 w-full text-slate-500 font-semibold uppercase text-xs px-5 pb-3"
                             :class="`grid-cols-${ Math.min(12, numberOfColumns) }`">
                            <div class="flex flex-col" v-for="col in activeColumnOrder">
                                {{ unslugify(col, '_') }}
                            </div>
                        </div>
                        <div class="h-[28rem] overflow-y-auto pb-32 border-y pb-3 divide-y" :class="{'bg-light-background divide-light-border border-light-border': !darkMode, 'bg-dark-background divide-dark-border border-dark-border': darkMode}">
                            <field v-for="(field, idx) in fields" class="px-5"
                                   :key="idx"
                                   v-model="fields[idx]"
                                   :current-index="idx+1"
                                   :active-column-order="activeColumnOrder"
                                   :number-of-columns="numberOfColumns"
                                   :field-types="fieldTypes"
                                   :company-field-categories="companyFieldCategories"
                                   :consumer-configurable-field-categories="consumerConfigurableFieldCategories"
                                   :dark-mode="darkMode"
                                   @delete="showFieldDeletionConfirmationModal (idx)" />
                        </div>
                    </div>
                </div>
                <div v-else-if="currentFieldsLabel.length > 0" class="flex flex-col h-[28rem] items-center justify-center text-2xl w-full">
                    No {{ currentFieldsLabel }} fields
                </div>
                <div v-else-if="fields?.length <= 0" class="h-[28rem] border-t border-b" :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-40 border-dark-border': darkMode}">

                </div>
                <FieldDeletionConfirmationModal v-if="isFieldDeletionConfirmationModalVisible" :dark-mode="darkMode"
                                                @choice="handleFieldDeletionConfirmation"
                                                :selected-field-name="fields[selectedFieldIndex].name">
                </FieldDeletionConfirmationModal>
            </div>
            <div class="absolute bottom-0 inset-x-0 p-5 border-t" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
                <div class="flex justify-between">
                    <div v-if="currentFieldsLabel" class="text-orange-500">
                        Currently viewing {{ currentFieldsLabel }} fields
                    </div>
                    <div v-if="this.fieldCols.length > 0" class="flex flex-row items-end justify-end gap-3">
                        <custom-button
                                @click="addField">
                            + New Field
                        </custom-button>
                        <custom-button
                                @click="saveFields">
                            Save
                        </custom-button>
                    </div>
                </div>
            </div>
            <ConsumerFieldsModuleVisibilityModal
                v-if="isConsumerFieldsModuleVisibilityModalVisible && category && categoryId"
                :fields="fields"
                :dark-mode="darkMode"
                @close="() => isConsumerFieldsModuleVisibilityModalVisible = false"
                :category="category"
                :categoryName="dropdownOptions.category.find(c => c.id === category).name"
                :categoryId="categoryId"
                :categoryIdName="dropdownOptions.categoryIds.find(c => c.id === categoryId).name"
            />
        </div>
    </div>
</template>

<script>
    import ApiService from './services/api';
    import SharedApiService from "../../Shared/services/api";
    import AlertsMixin from '../../../mixins/alerts-mixin';
    import AlertsContainer from "../../Shared/components/AlertsContainer.vue";
    import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
    import Field from "./components/Field.vue";
    import { unslugify } from "../../Shared/services/strings";
    import { determineInputType } from "./services/determineInputType";
    import Dropdown from "../../Shared/components/Dropdown.vue";
    import FieldDeletionConfirmationModal from './components/FieldDeletionConfirmationModal.vue'
    import * as ConsumerConfigurableFieldCategory from "./services/consumerConfigurableFieldCategory";
    import CustomButton from "../../Shared/components/CustomButton.vue";
    import ConsumerFieldsModuleVisibilityModal
        from "../IndustrySetup/components/ConsumerFieldsModuleVisibilityModal.vue";

    export default {
        name: "ConfigurableFields",
        props: {
            darkMode: {
                type: Boolean,
                default: false
            }
        },
        components: {
            ConsumerFieldsModuleVisibilityModal,
            CustomButton,
            Dropdown,
            Field,
            LoadingSpinner,
            AlertsContainer,
            FieldDeletionConfirmationModal
        },
        mixins: [
            AlertsMixin
        ],
        created: function() {
            this.api.getTypeCategories().then(res => {
                if(res.data.data.status === true) {
                    this.typeCategories = res.data.data.type_categories;
                    this.dropdownOptions.type = Object.keys(this.typeCategories).map(key => ({ id: key, name: unslugify(key, '_') }));
                }
                else {
                    this.showAlert('error', 'Problem loading type categories');
                }
            }).catch(() => {
                this.showAlert('error', 'Error loading type categories');
            });

            this.api.getFieldTypes().then(res => {
                if(res.data.data.status === true) {
                    this.fieldTypes = res.data.data.field_types;
                }
                else {
                    this.showAlert('error', 'Problem loading field types');
                }
            }).catch(() => {
                this.showAlert('error', 'Error loading field types');
            });

            this.getCompanyFieldCategory();
            this.getConsumerConfigurableFieldCategories();
        },
        data: function() {
            return {
                isConsumerFieldsModuleVisibilityModalVisible: false,
                isFieldDeletionConfirmationModalVisible: false,
                selectedFieldIndex: null,
                api: ApiService.make(),
                sharedApi: SharedApiService.make(),
                category: '',
                type: '',
                typeCategories: {},
                categoryId: '',
                categoryIds: {},
                fields: [],
                fieldCols: [],
                activeColumnOrder: [],
                fieldTypes: {},
                currentFieldsLabel: '',
                currentFieldsCategory: '',
                currentFieldsCategoryId: null,
                currentFieldsType: '',
                unslugify: unslugify,
                determineInputType: determineInputType,
                loading: false,
                saving: false,
                companyFieldCategories: [],
                consumerConfigurableFieldCategories: [],
                numberOfColumns: 7,
                dropdownOptions: {
                    type: [],
                    category: [],
                    categoryIds: [],
                },
            };
        },
        computed: {
            defaultConsumerConfigurableFieldCategoryId(){
                return ConsumerConfigurableFieldCategory.getDefaultCategoryId(this.consumerConfigurableFieldCategories)
            }
        },
        methods: {
            getFields() {
                if(!this.type
                || !this.category
                || (this.category !== 'global' && !this.categoryId)) {
                    this.showAlert('warning', 'Missing search parameters');
                    return;
                }

                this.loading = true;
                this.fields = {};
                this.setCurrentFields();

                this.api.list(this.type, this.category, this.currentFieldsCategoryId).then(res => {
                    if(res.data.data.status === true) {
                        this.fields = res.data.data.fields;
                        this.fieldCols = res.data.data.field_columns;
                        this.createColumnOrder();
                    }
                    else {
                        this.showAlert('error', 'Problem loading fields');
                    }
                }).catch(() => {
                    this.showAlert('error', 'Error loading fields');
                }).finally(() => {
                    this.loading = false;
                })
            },
            createColumnOrder() {
                const columnArray = ['no.'],
                    primaryKeys = [ 'name', 'key', 'type', 'category', 'category_id', 'payload', 'show_on_dashboard', 'show_on_profile', 'send_to_company', 'hidden' ],
                    hideKeys = [ 'id', 'created_at', 'modified_at' ];
                columnArray.push(...primaryKeys.filter(key => this.fieldCols.includes(key), ...primaryKeys.filter(key => ![ ...primaryKeys, ...hideKeys ].includes(key))));
                this.activeColumnOrder = columnArray.filter(v=>v);
                this.numberOfColumns = this.activeColumnOrder.length + 1;
            },
            addField() {
                let newField = {};
                let inputType;
                this.fieldCols.forEach((col) => {
                    inputType = this.determineInputType(col);

                    if(inputType === 'json') {
                        newField[col] = {};
                    }
                    else if(inputType === 'id') {
                        if(['industry_id', 'industry_service_id'].includes(col)) {
                            newField[col] = this.currentFieldsCategoryId;
                        }
                        else {
                            newField[col] = 0;
                        }
                    }
                    else if(inputType === 'boolean') {
                        newField[col] = false;
                    }
                    else if(inputType === 'fieldCategoryId') {
                        // Consumer configurable field category id
                        newField[col] = this.defaultConsumerConfigurableFieldCategoryId;
                    }
                    else {
                        newField[col] = '';
                    }
                });

                this.fields.push(newField);

                // Scroll div bottom to show new fields added
                const fieldsDiv = document.getElementById("fieldList");
                setTimeout(() => fieldsDiv.scrollTop = fieldsDiv.scrollHeight, 100)
            },

            removeFieldByIdx(idx){
                this.fields.splice(idx, 1);
            },

            handleFieldDeletionConfirmation(choice){
                if(choice) this.removeFieldByIdx(this.selectedFieldIndex)

                this.isFieldDeletionConfirmationModalVisible = !this.isFieldDeletionConfirmationModalVisible
            },

            showFieldDeletionConfirmationModal (idx) {
                this.selectedFieldIndex = idx
                this.isFieldDeletionConfirmationModalVisible = !this.isFieldDeletionConfirmationModalVisible
            },

            saveFields() {
                this.saving = true;
                this.api.save(this.fields, this.currentFieldsType, this.currentFieldsCategory, this.currentFieldsCategoryId).then(res => {
                    if(res.data.data.status === true) {
                        this.showAlert('success', 'Fields saved');
                        this.getFields();
                    }
                    else {
                        this.showAlert('error', 'Problem saving fields');
                    }
                }).catch(err => {
                    let errorMsg = 'Error saving fields';
                    if(err.response.status === 422) {
                        const errMsgs = new Set();
                        Object.values(err.response.data.errors || {}).forEach((messages) => {
                            errMsgs.add(messages[0]);
                        });

                        errorMsg = Array.from(errMsgs).join('. ');
                    }

                    this.showAlert('error', errorMsg);
                }).finally(() => {
                    this.saving = false;
                });
            },
            getCategoryIds() {
                if(this.category === 'industry') {
                    this.sharedApi.getOdinIndustries().then(res => {
                        if(res.data.data.status === true) {
                            this.categoryIds = res.data.data.industries;
                        }
                        else {
                            this.showAlert('error', 'Problem loading industries');
                        }
                    }).catch(() => {
                        this.showAlert('error', 'Error loading industries');
                    });
                }
                else if(this.category === 'service') {
                    this.sharedApi.getIndustryServices().then(res => {
                        if(res.data.data.status === true) {
                            this.categoryIds = res.data.data.industry_services;
                        }
                        else {
                            this.showAlert('error', 'Problem loading services');
                        }
                    }).catch(() => {
                        this.showAlert('error', 'Error loading services');
                    });
                }
                else if(this.category === 'global_type') {
                    this.sharedApi.getGlobalTypes().then(res => {
                        if(res.data.data.status === true) {
                            this.categoryIds = res.data.data.global_types;
                        }
                        else {
                            this.showAlert('error', 'Problem loading global types');
                        }
                    }).catch(() => {
                        this.showAlert('error', 'Error loading global types');
                    });
                }
            },
            setCurrentFields() {
                this.currentFieldsCategory = this.category;
                if(this.categoryId) {
                    let index = this.categoryIds.findIndex(cat => cat.id === this.categoryId);
                    this.currentFieldsCategoryId = this.categoryIds[index].num_id;
                }
                this.currentFieldsType = this.type;
                let typeLabel;
                if(this.currentFieldsCategory === 'global') {
                    typeLabel = this.currentFieldsCategory.slice(0, 1).toUpperCase() + this.currentFieldsCategory.slice(1);
                }
                else {
                    const selectedCid = this.categoryIds.find((cid) => {
                        return cid.num_id === this.currentFieldsCategoryId;
                    });

                    typeLabel = selectedCid.name + (selectedCid.industry ? " (" + selectedCid.industry + ") " : " ") + this.unslugify(this.currentFieldsCategory);
                }

                this.currentFieldsLabel = typeLabel + " " + this.unslugify(this.currentFieldsType, '_')
            },

            getCompanyFieldCategory()
            {
                this.api.getCompanyFieldCategories()
                    .then(resp => this.companyFieldCategories = resp.data.data.categories)
                    .catch(() => this.showAlert('error', 'Problem loading company configurable field categories'));
            },

            getConsumerConfigurableFieldCategories() {
                this.api.getConsumerConfigurableFieldCategories()
                    .then(resp => this.consumerConfigurableFieldCategories = resp.data.data.categories)
                    .catch(() => this.showAlert('error', 'Problem loading consumer configurable field categories'));
            }
        },
        watch: {
            category(newCategory) {
                if(newCategory !== 'global') {
                    this.categoryId = null;
                    this.getCategoryIds();
                }
            },
            type(newType) {
                this.category = '';
                this.dropdownOptions.category = this.typeCategories[newType].map(key => ({ id: key, name: unslugify(key, '_') }));
            },
            categoryIds(newIds) {
                if (!newIds) return;
                this.dropdownOptions.categoryIds = Object.values(newIds).map(category => ({ id: category.id, name: category.name }));
            }
        }
    }
</script>
