<template>
    <div>
        <div class="flex items-center justify-between p-5">
            <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Industry - Service List</h5>
            <button
                class="transition duration-200 bg-primary-500 hover:bg-blue-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5" @click="openModal">
                Add New Service
            </button>
        </div>
        <div class="grid grid-cols-11 gap-x-3 mb-2 px-5" v-if="!loading">
            <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs col-span-2">Industry</p>
            <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs col-span-2">Name</p>
            <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs col-span-2">Slug</p>
            <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs col-span-1">Campaign Filter Enabled</p>
            <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs col-span-1">Show On Website</p>
            <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs col-span-1">Show On Registration</p>
            <p class="text-slate-500 font-semibold tracking-wide uppercase text-xs col-span-1">Show On Dashboard</p>
        </div>
        <div class="border-t border-b h-[22rem] overflow-y-auto" v-if="!loading"
             :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-40 border-dark-border': darkMode}">
            <div>
                <div class="grid grid-cols-11 gap-x-3 border-b px-5 py-3 items-center"
                     v-for="industryService in industryServices" :key="industryService.id"
                     :class="{'text-slate-900 hover:bg-light-module border-light-border': !darkMode, 'text-slate-100 hover:bg-dark-module border-dark-border': darkMode}">
                    <p class="text-sm col-span-2 truncate">
                        {{ industryService.industry }}
                    </p>
                    <p class="text-sm col-span-2 truncate">
                        {{ industryService.name }}
                    </p>
                    <p class="text-sm col-span-2 truncate">
                        {{ industryService.slug }}
                    </p>
                    <div class="col-span-1 px-3 inline-flex items-center rounded-full py-1 whitespace-no-wrap"
                         :class="[{'text-blue-550' : industryService.campaign_filter_enabled, 'text-red-350' : !industryService.campaign_filter_enabled}, {'bg-cyan-150': industryService.campaign_filter_enabled && !darkMode}, {'bg-red-100': !industryService.campaign_filter_enabled && !darkMode}]">
                        <svg v-if="industryService.campaign_filter_enabled" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none"
                             xmlns="http://www.w3.org/2000/svg">
                            <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#0081FF"/>
                        </svg>
                        <svg v-else class="mr-1 flex-shrink-0" width="10" height="9" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <p class="font-semibold">{{ industryService.campaign_filter_enabled ? 'Yes' : 'No' }}</p>
                    </div>
                    <div class="col-span-1 px-3 inline-flex items-center rounded-full py-1 whitespace-no-wrap"
                         :class="[{'text-blue-550' : industryService.show_on_website, 'text-red-350' : !industryService.show_on_website}, {'bg-cyan-150': industryService.show_on_website && !darkMode}, {'bg-red-100': !industryService.show_on_website && !darkMode}]">
                        <svg v-if="industryService.show_on_website" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#0081FF"/>
                        </svg>
                        <svg v-else class="mr-1 flex-shrink-0" width="10" height="9" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <p class="font-semibold">{{ industryService.show_on_website ? 'Yes' : 'No' }}</p>
                    </div>
                    <div class="col-span-1 px-3 inline-flex items-center rounded-full py-1 whitespace-no-wrap"
                         :class="[{'text-blue-550' : industryService.show_on_registration, 'text-red-350' : !industryService.show_on_registration}, {'bg-cyan-150': industryService.show_on_registration && !darkMode}, {'bg-red-100': !industryService.show_on_registration && !darkMode}]">
                        <svg v-if="industryService.show_on_registration" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#0081FF"/>
                        </svg>
                        <svg v-else class="mr-1 flex-shrink-0" width="10" height="9" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <p class="font-semibold">{{ industryService.show_on_registration ? 'Yes' : 'No' }}</p>
                    </div>
                    <div class="col-span-1 px-3 inline-flex items-center rounded-full py-1 whitespace-no-wrap"
                         :class="[{'text-blue-550' : industryService.show_on_dashboard, 'text-red-350' : !industryService.show_on_dashboard}, {'bg-cyan-150': industryService.show_on_dashboard && !darkMode}, {'bg-red-100': !industryService.show_on_dashboard && !darkMode}]">
                        <svg v-if="industryService.show_on_dashboard" class="mr-1 flex-shrink-0" width="11" height="9" viewBox="0 0 11 9" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3.91794 5.86773L1.49074 3.32401L0 4.89347L3.92005 9L11 1.56724L9.50715 0L3.91794 5.86773Z" fill="#0081FF"/>
                        </svg>
                        <svg v-else class="mr-1 flex-shrink-0" width="10" height="9" viewBox="0 0 10 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M9 1L1 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                            <path d="M1 1L9 9" stroke="#E25555" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                        </svg>
                        <p class="font-semibold">{{ industryService.show_on_dashboard ? 'Yes' : 'No' }}</p>
                    </div>
                    <ActionsHandle :dark-mode="darkMode" @edit="editIndustryService(industryService)" @delete="deleteIndustryService(industryService)"/>
                </div>
            </div>
        </div>
        <div class="h-32 flex items-center justify-center" v-if="loading">
            <loading-spinner></loading-spinner>
        </div>
        <div class="p-3"></div>
        <modal :small="true" v-if="showModal" @close="closeModal" :dark-mode="darkMode" @confirm="saveIndustryService" :close-text="'Cancel'" :confirm-text="confirmText">
            <template v-slot:header>
                <h4>{{ industryService.id ? 'Update' : 'Create' }} Industry Service</h4>
            </template>
            <template v-slot:content>
                <div class="grid gap-3">
                    <div class="grid grid-cols-4">
                        <p class="uppercase font-semibold text-xs w-30"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Name
                        </p>
                        <input class="col-span-3 border rounded px-3 focus:outline-none focus:border focus:border-primary-500 pr-5 py-2"
                               placeholder="Service Name"
                               v-model="industryService.name"
                               :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}"
                               @change="generateSlug" />
                    </div>
                    <div class="grid grid-cols-4">
                        <p class="uppercase font-semibold text-xs w-30"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Slug
                        </p>
                        <input class="col-span-3 border rounded px-3 focus:outline-none focus:border focus:border-primary-500 pr-5 py-2"
                               placeholder="Service Slug"
                               v-model="industryService.slug"
                               :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}">
                    </div>
                    <div class="grid grid-cols-4">
                        <p class="uppercase font-semibold text-xs w-5"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Show On Website
                        </p>
                        <toggle-switch class="pr-10 py-2"
                                       :dark-mode="darkMode"
                                       v-model="industryService.show_on_website"
                        />
                    </div>
                    <div class="grid grid-cols-4">
                        <p class="uppercase font-semibold text-xs w-5"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Show On Registration
                        </p>
                        <toggle-switch class="pr-10 py-2"
                                :dark-mode="darkMode"
                                v-model="industryService.show_on_registration"
                               />
                    </div>
                    <div class="grid grid-cols-4">
                        <p class="uppercase font-semibold text-xs w-5"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Show On Dashboard
                        </p>
                        <toggle-switch class="pr-10 py-2"
                                       :dark-mode="darkMode"
                                       v-model="industryService.show_on_dashboard"
                        />
                    </div>
                    <div class="grid grid-cols-4">
                        <p class="uppercase font-semibold text-xs w-5"
                           :class="{'text-grey-600': !darkMode, 'text-blue-400 ': darkMode}">
                            Campaign Filter Enabled
                        </p>
                        <toggle-switch class="pr-10 py-2"
                                       :dark-mode="darkMode"
                                       v-model="industryService.campaign_filter_enabled"
                        />
                    </div>
                </div>
            </template>
        </modal>
        <alerts-container :dark-mode="darkMode" :alert-type="'error'" :text="error" v-if="error"></alerts-container>
    </div>

</template>

<script>
import Modal from "../../../Shared/components/Modal.vue";
import ApiService from "../services/api";
import ActionsHandle from "../../../Shared/components/ActionsHandle.vue";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import AlertsContainer from "../../../Shared/components/AlertsContainer.vue";
import {DateTime} from "luxon";
import ToggleSwitch from "../../../Shared/components/ToggleSwitch.vue";

export default {
    name: "IndustryServices",
    components: {ToggleSwitch, Modal, ActionsHandle, LoadingSpinner, AlertsContainer},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        industry: {
            type: Number,
            default: 0
        }
    },
    data () {
        return {
            api: ApiService.make(),
            showModal: false,
            industryServices: [],
            industryService: {name: null, slug: null, show_on_website: false, show_on_registration: false, show_on_dashboard: false, campaign_filter_enabled: false},
            saving: false,
            loading: false,
            error: null
        }
    },
    created() {
        this.getIndustryServices();
    },
    computed: {
        confirmText: function () {
            if (this.saving) return 'Saving...';
            if (this.industryService.id) return 'Update';

            return 'Create';
        }
    },
    methods: {
        openModal() {
            this.showModal = true;
        },
        closeModal () {
            this.showModal = false;
            this.industryService = {name: null, slug: null, show_on_website: false, show_on_registration: false, show_on_dashboard: false, campaign_filter_enabled: false};
        },
        getIndustryServices() {
            if(!this.industry) return;
            if(!this.loading) this.loading = true;
            this.error = null;

            this.api.getIndustryServices(this.industry).then(resp => this.industryServices = resp.data.data.services)
                .catch(e => this.error = e.response.data.message).finally(() => this.loading = false);
        },
        saveIndustryService() {
            if(this.saving) return;
            this.saving = true;
            this.error = null;

            if (this.industryService.id) {
                this.updateIndustryService();
                return;
            }
            this.createIndustryService();
        },
        getParamsToMakeRequest() {
            return {
                name                    : this.industryService.name,
                slug                    : this.industryService.slug,
                show_on_website         : this.industryService.show_on_website,
                industry                : this.industry,
                show_on_registration    : this.industryService.show_on_registration,
                show_on_dashboard       : this.industryService.show_on_dashboard,
                campaign_filter_enabled : this.industryService.campaign_filter_enabled,
            }
        },
        createIndustryService() {
            this.api.createIndustryServices(this.industry, this.getParamsToMakeRequest()).then(() => this.refreshList(true))
                .catch(e => this.error = e.response.data.message).finally(() => this.saving = false);
        },
        editIndustryService(industryService) {
            this.industryService = {...industryService};
            this.openModal();
        },
        updateIndustryService() {
            this.api.updateIndustryService(this.industryService.id, this.industry, this.getParamsToMakeRequest()).then(() => this.refreshList(true))
                .catch(e => this.error = e.response.data.message).finally(() => this.saving = false);
        },
        deleteIndustryService(industryService) {
            this.error = null;
            this.api.deleteIndustryService(industryService.id).then(() => this.refreshList())
                .catch(e => this.error = e.response.data.message);
        },
        formatDate(date) {
            return date ? DateTime.fromISO(date).toLocaleString(DateTime.DATETIME_SHORT) : null
        },
        refreshList(closeModal = false) {
            this.getIndustryServices();
            if(closeModal) this.closeModal();
        },
        generateSlug() {
            if (!this.industryService.slug) {
                this.industryService.slug = `${this.industryService.name}`.trim().toLowerCase().split(/[\s_-]+/g).join('-');
            }
        },
    }
}
</script>
