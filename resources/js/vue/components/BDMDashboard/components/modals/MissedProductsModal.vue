<script setup>
import Modal from "../../../Shared/components/Modal.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import { computed, onMounted, ref } from "vue";
import BaseTable from "../../../Shared/components/BaseTable.vue";
import CustomCheckbox from "../../../Shared/SlideWizard/components/CustomCheckbox.vue";
import MultiSelect from "../../../Shared/components/MultiSelect.vue";

const props = defineProps({
    darkMode: false,
    company: {},
    notificationConfig: {},
    parentLoading: false,
    apiService: null,
});

const emit = defineEmits(['close-modal', 'flash-alert']);

const loading = ref(false);
const productPreviews = ref(null);
const selectedProducts = ref({});
const maxSelectedProducts = ref(10);

const companyUsers = ref([]);
const selectedCompanyUserIds = ref([]);

onMounted(() => void fetchEmailRecipients());

const emailOptions = computed(() => {
    return companyUsers.value.map(user => ({ id: user.id, name: `${user.email}${user.is_decision_maker ? ' (Decision Maker)' : ''}` }));
})

const flashAlert = (...args) => {
    emit('flash-alert', ...args);
}

const closeModal = () => {
    emit('close-modal');
}

const companyInsideMinimumSendThreshold = () => (props.company.can_send_at ?? 0) > Date.now();

const companyExceedsMissedProductThreshold = () => props.company.missed_product_count >= props.notificationConfig.lead_threshold;

const companyHasReceivedNotification = () => props.company.last_sent_at > 0;

const reNotificationTime = () => {
    const hours = (props.company.can_send_at - Date.now()) / 1000 / 3600;

    return hours > 48
        ? `${Math.round(hours / 24)} days`
        : `${hours.toFixed(1)} hours`;
}

const sendMissedProductsNotification = async () => {
    loading.value = true;
    props.apiService.sendMissedProductsNotification(props.company.id, selectedCompanyUserIds.value).then(res => {
        if(res.data.status) {
            flashAlert('success', 'Email dispatched successfully.');
            emit('close-modal');
        }
        else {
            flashAlert('error', res.data.message ?? 'An unknown error occurred');
        }
    }).catch((e) => {
        flashAlert('error', e);
    }).finally(() => {
        loading.value = false;
    });
}

const fetchEmailRecipients = async () => {
    loading.value = true;
    props.apiService.getAvailableEmailRecipients(props.company.id).then(res => {
        if(res.data.status) {
            companyUsers.value = res.data.company_users;
        }
        else {
            flashAlert('error', res.data.message ?? 'An unknown error occurred');
        }
    }).catch((e) => {
        flashAlert('error', e);
    }).finally(() => {
        loading.value = false;
    });
}

const fetchAvailableMissedProducts = () => {
    loading.value = true;
    props.apiService.getAvailableMissedProductPreviews(props.company.id).then(res => {
        if(res.data.missed_products?.length) {
            productPreviews.value = res.data.missed_products ?? productPreviews.value;
            maxSelectedProducts.value = res.data.max_selectable ?? maxSelectedProducts.value;
            if (productPreviews.value?.length)
                preselectProducts();
        }
        else {
            flashAlert('error', res.data.message ?? 'An unknown error occurred');
        }
    }).catch((e) => {
        flashAlert('error', e);
    }).finally(() => {
        loading.value = false;
    });
}

const preselectProducts = () => {
    productPreviews.value.forEach((product, index) => {
        selectedProducts.value[product.id] = index < maxSelectedProducts.value;
    });
}

const selectedProductCount = computed(() => Object.values(selectedProducts.value).reduce((output, value) => output + (value ? 1 : 0), 0))

const deliverProducts = async () => {
    const consumerProductIds = Object.entries(selectedProducts.value).reduce((output, [id, selected]) => {
        return selected ? [...output, id] : output;
    }, []);

    if (consumerProductIds.length > maxSelectedProducts.value)
        return;

    loading.value = true;
    props.apiService.deliverMissedProducts(props.company.id, consumerProductIds).then(res => {
        if(res.data.status) {
            flashAlert('success', `${res.data.delivered?.length ?? 0} products delivered via email.`);
            props.company.last_sent_at = Date.now();
            closeModal();
        }
        else
            flashAlert('error', res.data.message ?? 'An unknown error occurred');
    }).catch((e) => {
        flashAlert('error', e);
    }).finally(() => {
        loading.value = false;
    });
}

const handleEmailSelection = (newValue) => {
    selectedCompanyUserIds.value = newValue;
}
</script>

<template>
    <Modal
        :dark-mode="darkMode"
        :hide-confirm="true"
        @close="closeModal"
        close-text="Close"
        :small="true"
        :inputs-disabled="parentLoading"
    >
        <template v-slot:header>
            <div class="flex items-center">
                <svg  class="w-5 mr-2" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75" />
                </svg>
                <h5 class="text-md">Missed Products</h5>
            </div>
        </template>
        <template v-slot:content>
            <div class="relative min-h-[16rem]"
                 :class="[ darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']"
            >
                <LoadingSpinner v-if="parentLoading || loading" />
                <div v-else>
                    <p class="mb-2">This company missed {{ company.missed_product_count }} products in the last {{ notificationConfig.days_to_query ?? 7 }} days.</p>
                    <p v-if="companyHasReceivedNotification()">
                        This company last received a missed products email: {{ (new Date(company.last_sent_at).toLocaleString()) }}.
                    </p>
                    <div class="grid grid-cols-2 items-center gap-x-3 gap-y-5 mt-6">
                        <div>
                            <CustomButton
                                @click="sendMissedProductsNotification()"
                                :disabled="companyInsideMinimumSendThreshold() || !companyExceedsMissedProductThreshold() || !selectedCompanyUserIds.length"
                            >
                                Dispatch Email
                            </CustomButton>
                        </div>
                        <div>
                            <p v-if="companyInsideMinimumSendThreshold()"
                                class="whitespace-pre-line w-full"
                            >
                                This company has received a missed product notification within the minimum allowed frequency.
                                They can be notified again in {{ reNotificationTime() }}.
                            </p>
                            <div v-else-if="!companyExceedsMissedProductThreshold()">
                                <p>This company has not had enough missed leads for a notification at this time.</p>
                            </div>
                            <div v-else class="w-full">
                                <div v-if="companyUsers.length">
                                    <MultiSelect
                                        :dark-mode="darkMode"
                                        :options="emailOptions"
                                        :selected-ids="selectedCompanyUserIds"
                                        :show-search-box="false"
                                        text-place-holder="Select Recipients"
                                        @input="handleEmailSelection"
                                    />
                                </div>
                                <div v-else>
                                    <p class="italic">This company has no valid email recipients</p>
                                </div>
                            </div>
                        </div>
                        <div class="w-full">
                            <CustomButton
                                :disabled="!companyHasReceivedNotification()"
                                @click="fetchAvailableMissedProducts()"
                            >
                                Show available missed products
                            </CustomButton>
                        </div>
                        <p class="w-full">
                            Fetch a list of available missed products to deliver to company
                        </p>
                    </div>
                    <!--  TODO: also check if company has completed a demo?  -->
                    <div v-if="companyHasReceivedNotification() && productPreviews?.length"
                        class="my-4"
                    >
                        <div class="grid grid-cols-2 gap-x-3 items-center my-4">
                            <div class="w-full">
                                <CustomButton
                                    @click="deliverProducts()"
                                    :disabled="selectedProductCount > maxSelectedProducts || !selectedProductCount || !selectedCompanyUserIds?.length"
                                >
                                    Deliver products
                                </CustomButton>
                            </div>
                            <div class="w-full">
                                <div v-if="companyUsers.length">
                                    <MultiSelect
                                        :dark-mode="darkMode"
                                        :options="emailOptions"
                                        :selected-ids="selectedCompanyUserIds"
                                        :show-search-box="false"
                                        text-place-holder="Select Recipients"
                                        @input="handleEmailSelection"
                                    />
                                </div>
                            </div>
                        </div>
                        <div class="my-3 text-center w-full">
                            <p :class="[selectedProductCount > maxSelectedProducts ? 'text-red-600' : '']">
                                {{ selectedProductCount }}/{{ Math.min(productPreviews.length, maxSelectedProducts) }} products selected
                            </p>
                        </div>
                        <div class="mt-6">
                            <div v-if="!loading && !productPreviews?.length">
                                No products found for this company.
                            </div>
                            <div v-else>
                                <BaseTable :dark-mode="darkMode" :loading="loading">
                                    <template #head>
                                        <tr>
                                            <th>Selected</th>
                                            <th>Date</th>
                                            <th>Industry</th>
                                            <th>Legs Sold</th>
                                            <th>Zip Code</th>
                                        </tr>
                                    </template>
                                    <template #body>
                                        <tr v-for="product in productPreviews">
                                            <td>
                                                <CustomCheckbox
                                                    :dark-mode="darkMode"
                                                    v-model="selectedProducts[product.id]"
                                                />
                                            </td>
                                            <td>{{ $filters.dateFromTimestamp(product.created_at) }}</td>
                                            <td>{{ product.industry }}</td>
                                            <td>{{ product.contacts_requested - product.assignments_remaining }} / {{ product.contacts_requested }}</td>
                                            <td>{{ product.zip_code }}</td>
                                        </tr>
                                    </template>
                                </BaseTable>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </Modal>
</template>