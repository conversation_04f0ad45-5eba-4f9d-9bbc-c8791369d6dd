<template>
    <div class="border rounded-lg"
         :class="{'bg-light-module border-light-border text-slate-900': !darkMode, 'bg-dark-module border-dark-border text-slate-100': darkMode}">
        <div class="p-5">
            <div class="flex items-center justify-between pb-3">
                <div class="flex items-center">
                    <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight mr-6">Campaigns</h5>
                    <div class="flex items-center" v-if="selectedCampaignModel === 'legacy' && campaignStore?.campaigns?.length"
                         :class="[darkMode ? 'text-slate-100' : 'text-slate-900']"
                    >
                        <svg class="w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor">
                            <path fill-rule="evenodd" d="M10.5 3.798v5.02a3 3 0 0 1-.879 2.121l-2.377 2.377a9.845 9.845 0 0 1 5.091 1.013 8.315 8.315 0 0 0 5.713.636l.285-.071-3.954-3.955a3 3 0 0 1-.879-2.121v-5.02a23.614 23.614 0 0 0-3 0Zm4.5.138a.75.75 0 0 0 .093-1.495A24.837 24.837 0 0 0 12 2.25a25.048 25.048 0 0 0-3.093.191A.75.75 0 0 0 9 3.936v4.882a1.5 1.5 0 0 1-.44 1.06l-6.293 6.294c-1.62 1.621-.903 4.475 1.471 4.88 2.686.46 5.447.698 8.262.698 2.816 0 5.576-.239 8.262-.697 2.373-.406 3.092-3.26 1.47-4.881L15.44 9.879A1.5 1.5 0 0 1 15 8.818V3.936Z" clip-rule="evenodd" />
                        </svg>
                        <div class="ml-2 italic">- This Company has active Future Campaigns</div>
                    </div>
                </div>
                <alerts-container :text="alertText" :alert-type="alertType" v-if="alertActive" :dark-mode="darkMode"/>
            </div>
            <div v-if="showFilters" class="flex justify-between">
                <div class="flex items-center gap-x-3">
                    <p class="text-grey-300 uppercase text-xs mr-2">Filters</p>
                    <svg width="16" height="10" viewBox="0 0 16 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M3 4H13V6H3V4ZM0 0H16V2H0V0ZM6 8H10V10H6V8Z" fill="#ABB0B5"/>
                    </svg>
                    <Dropdown
                        v-model="selectedCampaignModel"
                        :options="campaignModelOptions"
                        :dark-mode="darkMode"
                    />
                    <div v-if="selectedCampaignModel === 'legacy'">
                        <Dropdown
                            v-model="selectedCampaignStatus"
                            @update:modelValue="getCampaigns"
                            :dark-mode="darkMode"
                            :options="filterOptions"
                            :selected="campaignStatus"
                        />
                    </div>
                    <div v-else class="flex items-center gap-x-3">
                        <div class="w-[16rem]">
                            <Dropdown
                                v-model="selectedFutureCampaignStatus"
                                @update:modelValue="refreshCampaigns(null, true)"
                                :dark-mode="darkMode"
                                :options="futureCampaignStatusOptions"
                                :selected="null"
                            />
                        </div>
                        <div class="w-[12rem]">
                            <Dropdown
                                v-model="selectedFutureIndustry"
                                @update:modelValue="refreshCampaigns(null, true)"
                                :dark-mode="darkMode"
                                :options="futureIndustryOptions"
                                :selected="null"
                            />
                        </div>
                        <div class="w-48">
                            <Dropdown
                                v-model="selectedFutureProduct"
                                @update:modelValue="refreshCampaigns(null, true)"
                                :dark-mode="darkMode" class="w-48"
                                :options="futureProductOptions"
                                :selected="null"
                            />
                        </div>
                    </div>
                </div>
                <div v-if="selectedCampaignModel === 'future'" class="flex items-center gap-2">
                    <div v-if="!canCreateFutureCampaigns"
                        class="w-[20rem] text-xs rounded py-1 px-3 border"
                        :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
                    >
                        <p class="text-center">The current User does not have the required permission to create Campaigns.</p>
                    </div>
                    <CustomButton
                        v-if="selectedFutureProduct !== 'Appointment'"
                        :dark-mode="darkMode"
                        @click="createFutureCampaign()"
                        :disabled="!canCreateFutureCampaigns"
                        >
                        Create New Campaign
                    </CustomButton>
                </div>
                <div v-else class="flex items-center gap-2">
                    <a class="transition duration-200 text-sm font-semibold focus:outline-none py-2 rounded-md px-5 mr-3"
                       :href="getLegacyAdminCompanyCampaignsUrl(legacyCompanyId)" target="_blank"
                       :class="{'bg-grey-475 hover:bg-blue-800 text-white': !darkMode, 'bg-blue-400 hover:bg-blue-500 text-white': darkMode}">
                        Create New Campaign
                    </a>
                    <div class="flex flex-row gap-2">
                        <Dropdown v-if="campaigns.length > 0" v-model="selectedDropdownCampaigns" :dark-mode="darkMode" class="w-48" :options="campaigns" :selected="campaigns" placeholder="select a campaign"></Dropdown>
                        <a v-if="campaigns.length > 0"
                           @click.prevent="downloadZipcodes"
                           class="transition duration-200 bg-primary-500 hover-bg-500 text-white text-sm font-semibold focus:outline-none py-2 rounded-md px-5"
                           :class="{
                                        'bg-grey-475 hover:bg-blue-800 text-white cursor-pointer': !darkMode,
                                        'bg-blue-400 hover:bg-blue-500 text-white cursor-pointer': darkMode,
                                   }"
                        >
                            Download Zipcodes
                        </a>
                    </div>
                </div>
            </div>
            <div v-if="selectedCampaignModel === 'future' && selectedFutureProduct === 'Appointment'" class="flex mt-2 py-4 gap-x-2 items-center">
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="size-6">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 9v3.75m9-.75a9 9 0 1 1-18 0 9 9 0 0 1 18 0Zm-9 3.75h.008v.008H12v-.008Z" />
                </svg>
                <p>
                    Appointments are no longer being allocated. You can view Appointment campaigns, but no changes can be saved.
                </p>
            </div>
            <div v-if="!canEditFutureCampaigns"
                 class="w-[20rem] text-xs rounded py-1 px-3 border mt-2 ml-2"
                 :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
            >
                <p class="text-xs text-center">
                    The current User does not have the required permissions to make any changes to Campaigns.
                </p>
            </div>
        </div>
        <div>
            <div class="border-t border-b relative"
                 :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}">
                <div>
                    <LoadingSpinner :dark-mode="darkMode" v-if="loading || initialLoad" />
                    <!--         LEGACY CAMPAIGNS           -->
                    <div v-else-if="selectedCampaignModel === 'legacy'"
                         class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-3 m-4"
                    >
                        <lead-campaign-card
                            v-for="campaign in campaigns"
                            :campaign="campaign"
                            :company-id="companyId"
                            :dark-mode="darkMode"
                            :has-edit-rights="hasEditRights"
                            @edit="(uuid) => editCampaign(uuid)"
                            @delete="openDeleteModal(campaign)"
                        />
                    </div>
                    <!--         FUTURE CAMPAIGNS           -->
                    <div v-else-if="selectedCampaignModel === 'future'">
                        <div v-if="!campaignStore.campaigns?.length" class="p-5">
                            No Campaigns found.
                        </div>
                        <div v-else class="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-3 m-4">
                            <company-campaign-card
                                v-for="campaign in campaignStore.campaigns"
                                :campaign="campaign"
                                :unrestricted-zip-code-targeting="unrestrictedZipCodeTargeting"
                                :dark-mode="darkMode"
                                :opt-in-names="campaignStore.companyOptInNames"
                                :company-id="companyId"
                                @refresh-campaign="refreshCampaigns"
                            >
                                <template v-slot:action-handle>
                                    <ActionsHandle
                                        v-if="hasEditRights"
                                        :dark-mode="darkMode"
                                        :custom-actions="getFutureCampaignActions(campaign)"
                                        :no-custom-action="false"
                                        :no-delete-button="selectedFutureProduct === 'Appointment' || !canDeleteFutureCampaigns"
                                        :edit-label="canEditFutureCampaigns ? 'Edit' : 'View'"
                                        @edit="editFutureCampaign(campaign.reference)"
                                        @delete="openDeleteModal(campaign)"
                                        @pause="pauseFutureCampaign(campaign.reference)"
                                        @unpause="unpauseFutureCampaign(campaign.reference)"
                                        @toggle-bidding-status="toggleBiddingStatus(campaign.reference)"
                                        @zip-codes="downloadCampaignZipCodes(campaign.reference, campaign.name)"
                                        @edit-floor-prices="toggleCustomFloorPrices(true, campaign.reference)"
                                        @view-activity-logs="viewActivityLogs(campaign.id)"
                                        @view-alert-logs="viewAlertLogs(campaign.id)"
                                        @configure="toggleCampaignConfigurationModal(campaign.reference)"
                                    />
                                </template>
                            </company-campaign-card>
                        </div>
                    </div>
                    <div v-if="selectedCampaignModel === 'legacy'" class="flex justify-center my-4">
                        <LoadingSpinner :dark-mode="darkMode" v-if="infiniteLoader && !loading"/>
                        <div v-else>
                            <button
                                @click="loadMore(selectedCampaignModel === 'future'? campaignStore.paginationData : paginationData)"
                                v-if="!reachedEnd && !loading"
                                class="text-sm rounded-md inline-flex items-center px-5 py-2 h-9 font-semibold transition duration-200 bg-primary-500 hover:bg-primary-600 text-white">
                                Load More
                            </button>
                        </div>
                    </div>
                </div>
            </div>
            <div v-if="selectedCampaignModel !== 'legacy'" class="p-3 flex items-center justify-end gap-2">
                <div>
                    <span class="text-sm text-slate-500">Results Per Page</span>
                </div>
                <div>
                    <Dropdown
                        :dark-mode="darkMode"
                        placement="top"
                        :options="perPageOptions"
                        v-model="campaignStore.paginationData.per_page"
                        @update:model-value="refreshCampaigns(null, true)"
                    />
                </div>
                <Pagination
                    :dark-mode="darkMode"
                    :pagination-data="campaignStore.paginationData ?? {}"
                    :show-pagination="true"
                    @change-page="campaignStore.handlePaginationEvent($event.link, getFutureCampaignFilters())"
                />
            </div>
        </div>

        <CampaignWizard
            v-if="showCampaignWizard"
            :dark-mode="darkMode"
            :company-id="companyId"
            :editing-campaign-reference="editingFutureCampaign"
            :future-industry-services="futureIndustryServices"
            :readonly="readonlyCampaign"
            :custom-budget-types="customBudgetTypes"
            @close:wizard="closeCampaignWizard(false)"
            @refresh-and-close:wizard="closeCampaignWizard(true)"
        />

        <CustomCampaignFloorPriceModal
            v-if="showCustomFloorPriceModal"
            :dark-mode="darkMode"
            :campaign-reference="editingFloorPricesReference"
            @cancel:modal="toggleCustomFloorPrices(false)"
        />

        <!--    DELETE MODAL    -->
        <modal
            :small="true"
            :dark-mode="darkMode"
            v-if="showDeleteModal"
            @close="closeDeleteModal()"
            @confirm="deleteCampaign()"
            :close-text="'Cancel'"
            :confirm-text="'Delete'"
            :disable-confirm="deleteInProgress"
        >
            <template v-slot:header>
                Confirm Delete?
            </template>
            <template v-slot:content>
                <p v-if="deleteError === null">Are you sure you wish to delete this Lead Campaign - <strong>{{ deleteCampaignName }}</strong>?</p>
                <alert v-else :text="deleteError" :alert-type="'error'" :dark-mode="darkMode"></alert>
            </template>
        </modal>

        <!--    PAUSE CAMPAIGN MODAL    -->
        <Modal
            v-if="showPauseCampaignModal"
            :dark-mode="darkMode"
            @close="() => showPauseCampaignModal = false"
            @confirm="confirmPauseCampaign"
            confirm-text="Pause"
            :disable-confirm="loading || !(selectedPauseType >= 0)"
            :small="true"
        >
            <template v-slot:header>
                Pause Campaign
            </template>
            <template v-slot:content>
                <div class="mb-4">
                    <p class="text-sm pb-1">Pause Type</p>
                    <Dropdown
                        :options="pauseCampaignOptions"
                        v-model="selectedPauseType"
                        :dark-mode="darkMode"
                    />
                </div>
                <div v-show="parseInt(selectedPauseType) === 1" class="mb-4 grid grid-cols-2 gap-x-3">
                    <div>
                        <p class="text-sm pb-1">Reactivate At</p>
                        <DatePicker
                            :enable-time-picker="true"
                            :dark="darkMode"
                            model-type="yyyy-MM-dd HH:mm:ss"
                            :inline="false"
                            :is24="false"
                            :min-date="minDate"
                            :maxData="maxDate"
                            v-model="reactivateAt"
                            @cancel="() => reactivateAt = null"
                        ></DatePicker>
                    </div>
                    <div>
                        <p class="text-sm pb-1">Reactivation Timezone</p>
                        <Dropdown
                            :options="timezoneOptions"
                            v-model="selectedTimezone"
                            :dark-mode="darkMode"
                        />
                    </div>
                </div>
                <div class="mb-4 grid grid-cols-2 gap-x-3">
                    <div>
                        <p class="text-sm pb-1">Pause Reason</p>
                        <Dropdown
                            :options="pauseReasonOptions"
                            v-model="selectedReason"
                            :dark-mode="darkMode"
                        />
                    </div>
                    <div v-if="selectedReason === 'other'">
                        <p class="text-sm pb-1">Pause details</p>
                        <CharLimitTextArea
                            :dark-mode="darkMode"
                            v-model="otherReason"
                            placeholder="Reason for pausing campaign..."
                            textarea-class="w-full"
                        />
                    </div>
                </div>
            </template>
        </Modal>

        <modal
            :dark-mode="darkMode"
            :no-buttons="true"
            v-if="showActivityLogModal"
            @close="showActivityLogModal = false"
        >
            <template v-slot:header>
                <p class="text-lg text-primary-500">Campaign Activity Logs</p>
            </template>
            <template v-slot:content>
                <activity-logs :subject-type="campaignActivityLogRelationClass" :subject-id="logSubjectId" :dark-mode="darkMode"/>
            </template>
        </modal>

        <modal
            :dark-mode="darkMode"
            :no-buttons="true"
            v-if="showAlertLogModal"
            @close="showAlertLogModal = false"
        >
            <template v-slot:header>
                <p class="text-lg text-primary-500">Campaign Alert Logs</p>
            </template>
            <template v-slot:content>
                <activity-logs :subject-type="campaignActivityLogRelationClass" :subject-id="logSubjectId" :dark-mode="darkMode" :events="['alert_sent']"/>
            </template>
        </modal>

        <CompanyCampaignConfigurationModal
            :dark-mode="darkMode"
            @close="toggleCampaignConfigurationModal"
            v-if="showCampaignConfigurationModal"
            :campaign-reference="campaignReference"
        />
    </div>
</template>

<script>
import Dropdown from "../Shared/components/Dropdown.vue";
import ActionsHandle from "../Shared/components/ActionsHandle.vue";
import Pagination from "../Shared/components/Pagination.vue";
import ApiService from "./services/api";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import LegacyAdminMixin from "../Shared/mixins/legacy-admin-mixin";
import Modal from "../Shared/components/Modal.vue";
import Alert from "../Shared/components/Alert.vue";
import AlertsContainer from "../Shared/components/AlertsContainer.vue";
import alertsMixin from "../../mixins/alerts-mixin";
import WizardSlideContainer from "../Shared/SlideWizard/components/WizardSlideContainer.vue";
import CampaignWizard from "../Campaigns/Wizard/CampaignWizard.vue";
import CustomButton from "../Shared/components/CustomButton.vue";
import { useFutureCampaignStore } from "../Campaigns/Wizard/stores/future-campaigns.js";
import CharLimitTextArea from "../Shared/components/CharLimitTextArea.vue";
import DatePicker from "@vuepic/vue-datepicker";
import Tooltip from "../Shared/components/Tooltip.vue";
import CustomCampaignFloorPriceModal from "./components/CustomCampaignFloorPriceModal.vue";
import { PERMISSIONS, useRolesPermissions } from "../../../stores/roles-permissions.store.js";
import ActivityLogs from "../Shared/components/ActivityLogs/ActivityLogs.vue";
import Badge from "../Shared/components/Badge.vue";
import LeadCampaignCard from "./LeadCampaignCard.vue";
import CompanyCampaignCard from "./CompanyCampaignCard.vue";
import CompanyCampaignConfigurationModal from "./components/CompanyCampaignConfigurationModal.vue";
import { useCompanyStore } from "../../../stores/company/company.store.js";
import { CampaignStatus } from "../Campaigns/Wizard/stores/product-configuration.js";


export default {
    name: "Campaigns",
    mixins: [LegacyAdminMixin, alertsMixin],
    components: {
        CompanyCampaignConfigurationModal,
        CompanyCampaignCard,
        LeadCampaignCard,
        Badge,
        ActivityLogs,
        CustomCampaignFloorPriceModal,
        Tooltip,
        CharLimitTextArea,
        CustomButton,
        CampaignWizard,
        WizardSlideContainer,
        Pagination,
        ActionsHandle,
        Dropdown,
        LoadingSpinner,
        Modal,
        Alert,
        AlertsContainer,
        DatePicker
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            default: null
        },
        legacyCompanyId: {
            Type: Number,
            default: null,
        },
        unrestrictedZipCodeTargeting: {
            type: Boolean,
            default: false
        },
        showFilters: {
            type: Boolean,
            default: false
        },
        hasEditRights: {
            type: Boolean,
            default: false,
        },
        campaignActivityLogRelationClass: {
            type: String,
            default: null,
        }
    },
    data() {
        return {
            apiService: ApiService.make(),
            campaigns: [],
            paginationData: null,
            perPageOptions: [{ id: 10, name: '10' }, { id: 20, name: '20' }, { id: 50, name: '50' }],
            loading: false,
            infiniteLoader: false,
            reachedEnd: false,
            initialLoad: true,
            filterOptions: [
                {id: -1, name: 'All'},
                {id: 1, name: 'Active'},
                {id: 0, name: 'Inactive'}
            ],
            campaignStatus: null,
            selectedCampaignStatus: null,
            showDeleteModal: false,
            deleteError: null,
            deleteInProgress: false,
            deleteCampaignUuid: null,
            deleteCampaignName: '',
            selectedDropdownCampaigns: null,
            alertActive: false,
            alertType: '',
            alertText: '',

            campaignStore: useFutureCampaignStore(),
            permissionsStore: useRolesPermissions(),
            companyStore: useCompanyStore(),

            campaignModelOptions: ([{ name: 'Legacy Campaigns', id: 'legacy' }, { name: 'Future Campaigns', id: 'future' }]),
            selectedCampaignModel: 'future',
            showCampaignWizard: false,
            editingFutureCampaign: null,

            futureCampaignStatusConfig: null,
            pauseCampaignOptions: [],
            pauseReasonOptions: [],
            showPauseCampaignModal: false,
            selectedPauseType: null,
            selectedReason: null,
            reactivateAt: null,
            selectedTimezone: 0,
            otherReason: '',
            statusChangeCampaignReference: null,

            futureIndustryServices: [],
            selectedFutureIndustry: 0,
            selectedFutureCampaignStatus: -1,
            selectedFutureProduct: 'Lead',
            customFloorPricingIndustryIds: [],
            customBudgetTypes: {},
            showCustomFloorPriceModal: false,
            editingFloorPricesReference: null,
            showCampaignConfigurationModal: false,
            campaignReference: null,

            showActivityLogModal: false,
            showAlertLogModal: false,
            logSubjectId: null,

            editingIndustryName: null,
            timezoneList: {},
            timezoneDST: false,
            defaultTime: '08:00:00',
            minDate: null,
            maxDate: null,
        }
    },
    created: function () {
        this.initialize();
    },
    computed: {
        futureIndustryOptions() {
            return [
                { id: 0, name: 'All Industries' },
                ...Object.values(this.futureIndustryServices).map(serviceArray => ({ id: serviceArray[0].industry_name, name: serviceArray[0].industry_name }))
            ];
        },
        futureProductOptions() {
            const options = Object.values(this.futureIndustryServices).reduce((output, serviceGroup) => {
                serviceGroup.forEach(service => {
                    service.products.forEach(product => {
                        if (!output.find(productOption => productOption.id === product.name)) {
                            output.push({ id: product.name, name: product.name });
                        }
                    });
                });
                return output;
            }, [
                { id: 0, name: 'All Products' },
                { id: 'Lead', name: 'Lead' },
            ]);
            this.selectedFutureProduct = this.selectedFutureProduct ?? options.find(option => /lead/i.test(option.name))?.id ?? null;

            return options;
        },
        futureCampaignStatusOptions() {
            return [
                { id: -1, name: 'All Statuses' },
                { id: 2, name: 'Active - currently active' },
                { id: 1, name: 'Paused - temporarily paused' },
                { id: 0, name: 'Off - permanently paused' },
            ];
        },
        canCreateFutureCampaigns() { return this.permissionsStore.hasPermission(PERMISSIONS.PERMISSION_CAMPAIGNS_CREATE); },
        canEditFutureCampaigns() { return this.permissionsStore.hasPermission(PERMISSIONS.PERMISSION_CAMPAIGNS_UPDATE); },
        canDeleteFutureCampaigns() { return this.permissionsStore.hasPermission(PERMISSIONS.PERMISSION_CAMPAIGNS_DELETE); },
        canViewActivityLogs() { return this.permissionsStore.hasPermission(PERMISSIONS.PERMISSION_ACTIVITY_LOGS_CAMPAIGNS_VIEW); },
        canEditBiddingStatus() { return this.permissionsStore.hasPermission(PERMISSIONS.PERMISSION_CAMPAIGN_ENABLE_DISABLE_BIDDING) },
        readonlyCampaign() {
            return (this.editingFutureCampaign && !this.canEditFutureCampaigns) || (!this.editingFutureCampaign && !this.canCreateFutureCampaigns)
        },
        timezoneOptions() {
            return [
                ...Object.entries(this.timezoneList).map(([name, offset]) => ({ name: `${name} ${this.timezoneDST ? 'DST' : 'Time'} (UTC ${offset})`, id: offset })),
                { name: 'UTC', id: 0 }
            ];
        },
    },
    methods: {
        async initialize() {
            await Promise.all([
                this.campaignStore.initialize(this.companyId, this.getFutureCampaignFilters()),
                this.getCampaigns(),
                this.getFutureIndustries(),
                this.getTimezoneData(),
            ]);
            this.setDefaultDates();

            this.getFutureCampaignPauseOptions();

            this.initialLoad = false;
        },
        setDefaultDates() {
            const tomorrowDate = (new Date(Date.now() + (1000*3600*24)).toISOString()).match(/^[\d-]+/);
            const defaultDate = `${tomorrowDate} ${this.defaultTime}`;
            this.maxDate = (new Date(Date.now() + (1000*3600*24*30*3)).toISOString());
            this.minDate = defaultDate;
            this.reactivateAt = defaultDate;
            this.selectedTimezone = this.timezoneOptions.find(timezone => timezone.id === this.companyStore.company?.timezone_offset)?.id
                ?? this.timezoneOptions.find(timezone => /^mountain/.test(timezone.name))?.id
                ?? 0;
        },
        async getCampaigns() {
            if(this.companyId) {
                this.loading = true;
                return new Promise(res => {
                    this.apiService.getCampaigns(this.companyId, this.selectedCampaignStatus).then(resp => {
                        let { data, ...paginationData } = resp.data.data.campaigns;
                        this.campaigns = data;
                        this.paginationData = paginationData;
                    }).catch(e => console.log(e))
                        .finally(() => {
                            this.loading = false;
                            res(true);
                        });
                });
            }
        },
        async loadMore(data) {
            this.infiniteLoader = true;
            if (data.current_page + 1 <= data.last_page) {
                const page = parseInt(data.current_page) + 1;
                const url = data.path + "?page=" + page;
                let params = {};

                if (this.selectedCampaignModel === 'future') {
                    params = this.getFutureCampaignFilters();
                } else {
                    params = {
                        status: this.selectedCampaignStatus
                    };
                }

                await axios.get(url, {
                    params: params
                }).then(resp => {
                    let {data, ...paginationData} = resp.data.data.campaigns;

                    if (this.selectedCampaignModel === 'future') {
                        this.campaignStore.campaigns.push(...data);
                        this.campaignStore.paginationData = paginationData;
                    } else {
                        this.campaigns.push(...data);
                        this.paginationData = paginationData;
                    }
                }).catch(e => console.log(e)).finally(() => {
                    this.infiniteLoader = false
                });
            } else {
                this.infiniteLoader = false
                this.reachedEnd = true
            }
        },
        async handlePaginationEvent(newPageUrl) {
            this.loading = true;
            await axios.get(newPageUrl.link, {
                params: {
                    status: this.selectedCampaignStatus
                }
            }).then(resp => {
                let {data, ...paginationData} = resp.data.data.campaigns;
                this.campaigns = data;
                this.paginationData = paginationData;
            }).catch(e => console.log(e)).finally(() => this.loading = false);
        },
        editCampaign(campaignUuid) {
            const url = this.getLegacyAdminCompanyCampaignsUrl(this.legacyCompanyId, campaignUuid);
            window.open(url, '_blank').focus();
        },
        async deleteCampaign() {
            this.deleteInProgress = true;
            this.loading = true;
            const resp = this.selectedCampaignModel === 'future'
                ? await this.campaignStore.apiService.deleteCampaign(this.deleteCampaignUuid).catch(e => e)
                : await this.apiService.deleteCampaign(this.companyId, this.deleteCampaignUuid).catch(e => e);
            if (resp.data?.data?.status) {
                this.closeDeleteModal();
                this.refreshCampaigns();
            }
            else {
                this.deleteError = resp?.response?.data?.message ?? resp.message ?? `An error has occurred.`;
                this.loading = false;
            }

            this.deleteInProgress = false;
        },
        openDeleteModal(campaign) {
            this.deleteCampaignUuid = campaign.reference;
            this.deleteCampaignName = campaign.name;
            this.deleteError = null;
            this.showDeleteModal = true;
        },
        closeDeleteModal() {
            this.showDeleteModal = false;
            this.deleteCampaignUuid = null;
            this.deleteCampaignName = '';
        },
        downloadZipcodes() {
            if (!this.selectedDropdownCampaigns) {
                this.activateAlert('error', 'No campaign selected. Please select a campaign to download zip codes.');
                console.error('No campaign selected for download');
                return;
            }

            const campaignId = this.selectedDropdownCampaigns;
            const selectedCampaign = this.campaigns.find(campaign => campaign.id === campaignId);
            const campaignUuid = selectedCampaign.uuid;

            this.apiService.downloadZipcodes(this.companyId, campaignUuid)
                .then(response => {
                    if (response.data?.data?.status) {
                        const csvFileName = selectedCampaign.name.toLowerCase().replace(/\s+/g, '_') + '.csv';
                        const csvContent = response.data.data.csvContent;

                        const blob = new Blob([csvContent], {type: 'text/csv'});
                        const url = window.URL.createObjectURL(blob);
                        const link = document.createElement('a');
                        link.href = url;
                        link.setAttribute('download', csvFileName);
                        document.body.appendChild(link);
                        link.click();
                        document.body.removeChild(link);

                        this.selectedDropdownCampaigns = null;
                    }
                })
                .catch(error => {
                    this.activateAlert('error', 'Error downloading zip codes. Please try again.');
                    console.error('Error downloading zip codes: ', error);
                })
        },
        activateAlert(type, text) {
            this.alertActive = true;
            this.alertType = type;
            this.alertText = text;

            setTimeout(() => {
                this.alertActive = false;
                this.alertType = '';
                this.alertText = '';
            },2000)
        },

        // Future Campaigns
        getFutureCampaignFilters() {
            return {
                status: this.selectedFutureCampaignStatus ?? null,
                industry: this.selectedFutureIndustry ?? null,
                product: this.selectedFutureProduct ?? null,
            };
        },
        async refreshCampaigns(pageLink = null, resetPage = false) {
            this.loading = true;
            const filters = this.getFutureCampaignFilters();
            const { status, message } = pageLink
                ? await this.campaignStore.handlePaginationEvent(pageLink, filters)
                : await this.campaignStore.getCampaignList(filters, resetPage ? 1 : null);

            if (!status)
                this.showAlert('error', message ?? "There was an error fetching Campaigns");

            this.loading = false;
        },
        editFutureCampaign(campaignReference) {
            this.editingFutureCampaign = campaignReference;
            this.showCampaignWizard = true;
        },
        createFutureCampaign() {
            this.editingFutureCampaign = null;
            this.showCampaignWizard = true;
        },
        closeCampaignWizard(refreshCampaignList = false) {
            if (refreshCampaignList) {
                this.loading = true;
                this.campaignStore.getCampaignList(this.getFutureCampaignFilters()).then(() => this.loading = false);
            }

            this.showCampaignWizard = false;
        },
        async pauseFutureCampaign(campaignReference) {
            this.showPauseCampaignModal = true;
            this.statusChangeCampaignReference = campaignReference;
        },
        confirmPauseCampaign() {
            if (this.validatePauseReason()) {
                this.loading = true;
                this.campaignStore.apiService.pauseCampaign({
                    reference: this.statusChangeCampaignReference,
                    status: this.selectedPauseType,
                    reason: this.selectedReason === 'other' ? this.otherReason : this.selectedReason,
                    reactivate_at: this.selectedPauseType === CampaignStatus.PausedTemporarily
                        ? {
                            timestamp: this.reactivateAt,
                            offset: this.selectedTimezone,
                        }
                        : null,
                }).then(resp => {
                    if (resp.data?.data?.status) {
                        this.refreshCampaigns();
                        this.showAlert('success', 'The Campaign has been paused.');
                        this.clearPauseModal();
                    }
                    else {
                        this.loading = false;
                    }
                }).catch(resp => {
                    this.showAlert('error', resp?.response?.data?.message ?? resp.message ?? `An error has occurred pausing the Campaign.`);
                    this.loading = false;
                });
            }
        },

        unpauseFutureCampaign(campaignReference) {
            this.statusChangeCampaignReference = campaignReference;

            this.loading = true;
            this.campaignStore.apiService.unpauseCampaign({
                reference: campaignReference
            }).then(resp => {
                if (resp.data?.data?.status) {
                    this.refreshCampaigns();
                    this.showAlert('success', 'The Campaign has been reactivated.');
                }
                else {
                    this.loading = false;
                }
            }).catch(resp => {
                this.showAlert('error', resp?.response?.data?.message ?? resp.message ?? `An error has occurred reactivating the Campaign.`);
                this.loading = false;
            });
        },
        toggleBiddingStatus(campaignReference) {
            this.loading = true;

            this.campaignStore.apiService.toggleCampaignBiddingStatus(campaignReference)
                .then(async resp => {
                    await this.refreshCampaigns();
                    this.showAlert('success', `Bidding status updated`);
                }).catch(e => {
                this.showAlert('error', e?.response?.data?.message ?? e.message ?? `An error has occurred reactivating the Campaign.`);
            }).finally(() => this.loading = false);
        },
        viewActivityLogs(campaignId) {
            this.showActivityLogModal = true;
            this.logSubjectId = campaignId;
        },
        viewAlertLogs(campaignId) {
            this.showAlertLogModal = true;
            this.logSubjectId = campaignId;
        },
        validatePauseReason() {
            const errors = [];
            if (!(this.selectedPauseType >= 0)) errors.push('Please select a Pause Status from the menu.');
            if (!this.selectedReason) errors.push('Please select a reason for pausing.');
            if (this.selectedReason === 'other' && !this.otherReason.trim()) errors.push('Please provide details on the reason for pausing t his campaign.');
            if (parseInt(this.selectedPauseType) === 1 && !this.reactivateAt) errors.push('Please choose a reactivation date.');
            if (errors.length) {
                this.showAlert('error', errors.join("\n"));
                return false;
            }

            return true;
        },
        clearPauseModal() {
            this.selectedPauseType = this.pauseCampaignOptions[0]?.id ?? null;
            this.selectedReason = this.pauseReasonOptions[0]?.id ?? null;
            this.showPauseCampaignModal = false;
            this.statusChangeCampaignReference = null;
        },
        getFutureCampaignActions(campaign) {
            const output = [];
            if (this.selectedFutureProduct !== 'Appointment' && this.canEditFutureCampaigns) {
                if (campaign.active)
                    output.push({ event: 'pause', name: 'Pause' });
                else
                    output.push({ event: 'unpause', name: 'Unpause' });
            }

            output.push({ event: 'configure', name: 'Configure'});

            if (this.canEditBiddingStatus) {
                output.push({ event: 'toggle-bidding-status', name: campaign.bidding_disabled ? 'Enable Bidding' : 'Disable Bidding'});
            }

            output.push({ event: 'zip-codes', name: 'Download Zip Codes' });

            let targetServiceId = null;
            for (const industry in this.futureIndustryServices) {
                for (const service of this.futureIndustryServices[industry]) {
                    if (service.name === campaign.service && service.industry_name === campaign.industry) {
                        targetServiceId = service.industry_id;
                        break;
                    }
                }
                if (targetServiceId) break;
            }

            if (targetServiceId && this.customFloorPricingIndustryIds?.includes(targetServiceId)) {
                const label = this.canEditFutureCampaigns ? 'Edit Custom Floor Prices' : 'View Custom Floor Pricing';
                output.push({ event: 'edit-floor-prices', name: label });
            }

            if (this.canViewActivityLogs) {
                output.push({ event: 'view-activity-logs', name: 'View Logs'});
                output.push({ event: 'view-alert-logs', name: 'View Alert Logs'});
            }

            return output;
        },
        async getFutureCampaignPage(paginationEvent) {
            this.loading = true;
            const { status, message } = await this.campaignStore.handlePaginationEvent(paginationEvent.link, { status: this.selectedCampaignStatus });
            if (!status)
                this.showAlert('error', message ?? 'An error occurred fetching the campaigns.');

            this.loading = false;
        },
        toggleCustomFloorPrices(show = false, campaignReference) {
            if (campaignReference) {
                this.editingFloorPricesReference = campaignReference;

                this.showCustomFloorPriceModal = true;
            }
            else {
                this.showCustomFloorPriceModal = !!show;
            }
        },
        async downloadCampaignZipCodes(campaignReference, campaignName) {
            this.loading = true;

            this.campaignStore.apiService.downloadZipCodes(campaignReference).then(resp => {
                if (resp.data?.data?.status) {
                    const blob = new Blob([ resp.data.data.zip_codes ], { type: 'text/csv' });
                    const link = document.createElement('a');
                    link.href = window.URL.createObjectURL(blob);
                    if (!campaignName) {
                        campaignName = 'campaign_zips';
                    }
                    const csvFileName = campaignName.toLowerCase().replace(/\s+/g, '_') + '.csv';

                    link.setAttribute('download', csvFileName);
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                }
            }).catch(e => {
                this.showAlert('error', e);
            }).finally(() => {
                this.loading = false;
            });
        },
        async getFutureIndustries() {
            this.campaignStore.apiService.getIndustryServiceProductOptions(this.companyId).then(resp => {
                if (resp.data?.data?.status) {
                    this.futureIndustryServices = resp.data.data.industry_services;
                    this.customFloorPricingIndustryIds = resp.data.data.floor_price_industry_ids ?? [];
                    this.customBudgetTypes = resp.data.data.custom_budget_types ?? {};
                }
            }).catch(e => {
                this.showAlert('error', e);
            }).finally(() => {
                this.loading = false;
            });
        },
        getFutureCampaignPauseOptions() {
            this.pauseCampaignOptions = (this.campaignStore.statusConfiguration?.statuses ?? []).reduce((output, value, index) => {
                return (/active/i.test(value))
                    ? output
                    : [...output, { name: value, id: index }];
            }, []);
            this.pauseReasonOptions = Object.entries(this.campaignStore.statusConfiguration?.reasons ?? []).map(([key, value]) => ({ name: value, id: key }));
            this.selectedPauseType = this.pauseCampaignOptions?.find(option => /temp/i.test(option.name))?.id ?? null;
            this.selectedReason = this.pauseReasonOptions?.[0]?.id ?? null;
        },
        toggleCampaignConfigurationModal(campaignReference = null) {
            if(!campaignReference) {
                this.campaignReference = null;
                this.showCampaignConfigurationModal = false;

                return;
            }

            this.campaignReference = campaignReference;
            this.showCampaignConfigurationModal = true;
        },
        async getTimezoneData() {
            const resp = await this.campaignStore.apiService.getTimezones().catch(e => e);
            if (resp.data?.data?.status) {
                this.timezoneList = resp.data.data.timezones;
                this.timezoneDST = resp.data.data.dst;
            }
            else {
                this.showAlert('error', resp.data?.data?.message ?? resp.response?.message);
            }
        }
    },
    watch: {
        companyId(newVal, oldVal) {
            if (newVal !== oldVal) {
                this.getCampaigns()
            }
        },
    }
}
</script>

<style scoped>

</style>
