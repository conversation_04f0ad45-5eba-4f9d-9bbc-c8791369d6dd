import axios from 'axios';

export default class ApiService {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    static make() {
        return new ApiService('internal-api', 'companies', 1);
    }

    getGooglePlacesCompaniesByZipCode(zipCode, industry) {
        return this.axios().get(`google-places-search/zip-code`, {
            params: {
                zip_code: zipCode,
                industry: industry
            }
        });
    }

    getGooglePlacesCompaniesByCounty(countyKey, stateKey, industry) {
        return this.axios().get(`google-places-search/county`, {
            params: {
                county_key: countyKey,
                state_key: stateKey,
                industry: industry
            }
        });
    }

    getCampaigns(companyId, status, perPage) {
        return this.axios().get(`/${companyId}/campaigns`, {
            params: {
                status: status,
                per_page: perPage
            }
        });
    }

    getAllCampaignsForSelect(companyId, {
        status = undefined,
        product = undefined,
        source = null,
        withDeleted = null,
    } = {}) {
        return this.axios().get(`/${companyId}/campaigns-options`, {
            params: {
                status,
                product,
                source,
                withDeleted
            }
        });
    }

    getRevenueOverview(companyId) {
        return this.axios().get(`/${companyId}/revenue-overview`);
    }

    getRevenueGraph(companyId, period, duration) {
        return this.axios().get(`/${companyId}/revenue-graph-data`, {
            params: {period, duration}
        })
    }

    getLeadsOverview(companyId, startTimestamp) {
        return this.axios().get(`/${companyId}/leads-overview`, {
            params: {
                start_timestamp: startTimestamp
            }
        });
    }

    getCompanyProfileData(companyId) {
        return this.axios().get(`/${companyId}/profile-data`);
    }

    getLegacyCompanyTypes() {
        return this.axios().get('/legacy-company-types')
    }

    updateCompanyDetails(companyId, companyDetails) {
        return this.axios().patch(`/${companyId}`, companyDetails);
    }

    updateCompanyConfigurableFields(companyId, data) {
        return this.axios().patch(`/${companyId}/configurable-fields`, data);
    }

    deleteCampaign(companyId, campaignUuid) {
        return this.axios().delete(`/${companyId}/campaigns/${campaignUuid}`);
    }

    getServices(companyId) {
        return this.axios().get(`/${companyId}/services`);
    }

    getCompanyActivityFeed(companyId, params = {}) {
        if (!(params instanceof Object)) {
            throw new TypeError(`"params" must be an object.`);
        }

        return this.axios().get(`/${companyId}/activities`, {
            params: params
        });
    }

    getCompanyActivityFeedOverview(companyId, params = {}) {
        return this.axios().get(`/${companyId}/activities/overview`, {
            params
        });
    }

    getActivityConversations(companyId, activityId) {
        return this.axios().get(`/${companyId}/activities/${activityId}/conversations`);
    }

    saveActivityConversation(companyId, activityId, payload) {
        return this.axios().post(`/${companyId}/activities/${activityId}/conversation`, payload);
    }

    getSmsHistory(externalPhoneNumber, companyId) {
        return this.axios().get(`/${companyId}/activities/sms-history/${externalPhoneNumber}`);
    }

    getBestRevenueScenarioLogs(companyId, page = 1) {
        return this.axios().get(`/${companyId}/best-revenue-scenario-logs`, {
            params: {
                page: page
            }
        });
    }

    investigateAllocationFailure(companyId, consumerProductId, campaignId = null) {
        return this.axios().get(`/${companyId}/investigate-allocation-failure/${consumerProductId}`, {
            params: {
                campaign_id: campaignId
            }
        });
    }

    getTestLeads(companyId, params = {}) {
        return this.axios().get(`/${companyId}/test-leads`, {
            params
        })
    }

    createTestLead(companyId, campaignId, source) {
        return this.axios().post(`/${companyId}/test-leads`, {
            campaign_id: campaignId,
            source,
        })
    }

    downloadZipcodes(companyId, campaignId) {
        return this.axios().get(`/${companyId}/download-campaigns-zip-codes/${campaignId}`);
    }

    updateChargeableStatus(companyId, leadId) {
        return this.axios().post(`/${companyId}/update-chargeable-status/${leadId}`);
    }

    getCompanyConsumerReviews(companyId, params = {}){
        return this.axios().get(`/${companyId}/consumer-reviews`, {
            params
        });
    }

    getReviewDetails(companyId, reviewId, params = {}){
        return this.axios().get(`/${companyId}/consumer-reviews/${reviewId}/details`, {
            params
        });
    }

    postReviewReply(companyId, reviewId, reply){
        return this.axios().post(`/${companyId}/consumer-reviews/${reviewId}/reply`, {
            reply: reply,
        });
    }

    approveCompanyConsumerReview(companyId, reviewId){
        return this.axios().post(`/${companyId}/consumer-reviews/${reviewId}/approve`);
    }

    declineCompanyConsumerReview(companyId, reviewId){
        return this.axios().post(`/${companyId}/consumer-reviews/${reviewId}/decline`);
    }

    setPendingConsumerReview(companyId, reviewId){
        return this.axios().post(`/${companyId}/consumer-reviews/${reviewId}/pending`);
    }

    getPastMergesForCompany(companyId) {
        return this.axios().get(`/${companyId}/merge-tool/past-merges`);
    }

    undoMerge(companyId, mergeRecordId, dryRun) {
        return this.axios().patch(`/${companyId}/merge-tool/undo-merge`, {
            company_merge_record_id: mergeRecordId,
            dry_run: dryRun ? 1 : 0,
        });
    }

    mergeCompany(toCompanyId, fromCompanyId, dryRun) {
        return this.axios().patch(`/${toCompanyId}/merge-tool/new-merge`, {
            source_company_id: fromCompanyId,
            dry_run: dryRun ? 1 : 0,
        });
    }

    toggleDisplayProfile(companyId){
        return this.axios().patch('/contractor-profile/toggle-display', {company_id: companyId})
    }

    getContractorBusinessHours(companyId){
        return this.axios().get('/contractor-profile/business-hours?company_id=' + companyId)
    }

    updateContractorBusinessHours(companyId, timezone, times){
        return this.axios().patch('/contractor-profile/business-hours', {
            company_id: companyId,
            timezone,
            times,
        })
    }

    getBrandsAndServices(companyId){
        return this.axios().get('/contractor-profile/brands-and-services?company_id=' + companyId)
    }

    updateBrandsAndServices(companyId, brands, services){
        return this.axios().patch('/contractor-profile/brands-and-services', {
            company_id: companyId,
            brands_sold: brands,
            services: services,
        })
    }

    getCredentials(companyId){
        return this.axios().get('contractor-profile/credentials?company_id=' + companyId)
    }

    updateCredentials(companyId, licenses, certifications){
        return this.axios().patch('contractor-profile/credentials', {
            company_id: companyId,
            licenses,
            certifications,
        })
    }

    getContractorProfileContent(companyId){
        return this.axios().get('contractor-profile/content?company_id=' + companyId)
    }

    updateContractorProfileContent(companyId, expertRating, introduction, customersLike, customersDislike){
        return this.axios().patch('contractor-profile/content', {
            company_id: companyId,
            expert_rating: expertRating,
            introduction: introduction,
            customers_like: customersLike,
            customers_dislike: customersDislike,
        })
    }

    importEmails(companyId) {
        return this.axios().post(`/${companyId}/import-emails`);
    }

    getCompanyMissedProducts(companyId, {page = 1, perPage = 25, ...filters}) {
        return this.axios().get(`/${companyId}/missed-products`, {
            params: {
                page,
                per_page: perPage,
                ...filters
            }
        });
    }
}
