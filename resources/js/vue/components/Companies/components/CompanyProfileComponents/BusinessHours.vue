<template>
    <div class="m-6">

        <div class="my-6">
            <label>Timezone</label>
            <div class="w-72">
                <dropdown
                    :dark-mode="darkMode"
                    :options="timezoneOptions"
                    v-model="timezone"
                    placeholder="Choose Timezone"
                    v-on:change="caneSave=true"
                ></dropdown>
            </div>
        </div>

        <table>
            <tr>
                <th></th>
                <th>Open</th>
                <th>Close</th>
            </tr>
            <tr v-for="day in days">
                <td>{{ day[0].toUpperCase() + day.slice(1) }}</td>
                <td>
                    <CustomInput :dark-mode="darkMode" v-model="times[day]['open']" v-on:change="caneSave=true" type="time"></CustomInput>
                </td>
                <td>
                    <CustomInput :dark-mode="darkMode" v-model="times[day]['close']" v-on:change="caneSave=true" type="time"></CustomInput>
                </td>
            </tr>
        </table>

        <custom-button :dark-mode="darkMode" :disabled="!caneSave || timezone === null" class="my-6" v-on:click="save">Save</custom-button>

    </div>
</template>

<script>

import ApiService from "../../services/api";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import CustomInput from "../../../Shared/components/CustomInput.vue";

export default {
    name: 'BusinessHours',
    components: {
        CustomInput,
        CustomButton,
        Dropdown
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        hasEditRights: {
            type: Boolean,
            default: false,
        },
        companyId: {
            type: Number,
            default: null
        },
    },
    data() {
        return {
            api: ApiService.make(),
            days: ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday'],
            timezone: null,
            timezoneOptions: ['EST', 'CST', 'MST', 'PST', 'AKST', 'YST', 'AHST'],
            times: {
                sunday: {open: null, close: null},
                monday: {open: null, close: null},
                tuesday: {open: null, close: null},
                wednesday: {open: null, close: null},
                thursday: {open: null, close: null},
                friday: {open: null, close: null},
                saturday: {open: null, close: null},
            },
            caneSave: false,
        }
    },
    created() {
        if (this.companyId) {
            this.getBusinessHours()
        }
    },
    methods: {
        getBusinessHours(){
            this.api.getContractorBusinessHours(this.companyId)
                .then(resp => {
                    this.timezone = resp.data.data.timezone
                    this.times = resp.data.data.times
                })
        },
        save(){
            if(this.caneSave && this.timezone !== null)
                this.api.updateContractorBusinessHours(this.companyId, this.timezone, this.times).then(() => this.caneSave = false)
        }
    }
}
</script>
