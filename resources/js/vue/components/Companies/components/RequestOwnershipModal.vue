<script setup>
import Modal from "../../Shared/components/Modal.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import {onMounted, ref} from "vue";
import OwnershipApiService from "../../SalesManagement/services/api/ownership-api.js";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import ActionsHandle from "../../Shared/components/ActionsHandle.vue";
import BaseTable from "../../Shared/components/BaseTable.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import Badge from "../../Shared/components/Badge.vue";


const props= defineProps({
    darkMode: false,
    companyId: Number,
    companyName: ''
})

const ownershipRoleOptions = ref([
    {id: 'account-manager', name: 'Account Manager'},
    {id: 'business-development-manager', name: 'Business Development Manager'},
    {id: 'customer-success-manager', name: 'Customer Success Manager'},
    {id: 'onboarding-manager', name: 'Onboarding Manager'},
    {id: 'sales-development-representative', name: 'Sales Development Representative'},
])

const selectedRole = ref(ownershipRoleOptions.value[0].id)
const emit         = defineEmits(['close'])
function closeModal() {
    selectedRole.value = null
    emit('close')
}
const loading = ref(false)
const myOwnershipRequests = ref(null)

const ownershipApiService = OwnershipApiService.make();

onMounted(() => {
    getMyOwnershipRequests()
})
const getMyOwnershipRequests = () => {
    loading.value = true
    ownershipApiService.getMyOwnershipRequests()
        .then(res => {
            myOwnershipRequests.value = res.data.data
        })
        .catch(error => {
            console.error('Error fetching posts:', error)
        })
        .finally(() => {
            loading.value = false
        })
}

function requestOwnership(companyId, role) {
    if (role instanceof Object) {
        role = role.id
    }

    ownershipApiService.requestOwnershipAssignment(companyId, role)
        .then(response => {
            console.log('Success:', response.data)
        })
        .catch(error => {
            console.error('Error:', error.response?.data || error.message)
        })
        .finally(
            closeModal()
        )
}

</script>

<template>
    <Modal container-classes="p-0" no-buttons :dark-mode="darkMode" @close="closeModal">
        <template #header>
            <p class="font-semibold">Request Ownership for {{props.companyName}}</p>
        </template>
        <template #content>
            <div class="p-6" v-if="loading">
                <LoadingSpinner></LoadingSpinner>
            </div>
            <div v-else>
                <div class="p-6">
                    <p class="text-sm font-semibold mb-2">Select the Role you wish to request ownership as:</p>
                    <div class="grid lg:grid-cols-3 gap-3">
                        <Dropdown class="lg:col-span-2" :dark-mode="darkMode" :options="ownershipRoleOptions" v-model="selectedRole"></Dropdown>
                        <CustomButton :dark-mode="darkMode" @click="requestOwnership(companyId, selectedRole)">Request Ownership</CustomButton>
                    </div>

                </div>
                <div v-if="myOwnershipRequests">
                    <p class="font-semibold inline-flex items-center gap-3 px-6 pb-3">Pending Requests</p>
                    <BaseTable body-height="h-64" :dark-mode="darkMode" :loading="loading">
                        <template #head>
                            <tr>
                                <th>Company</th>
                                <th>Role Requested</th>
                                <th>Status</th>
                            </tr>
                        </template>
                        <template #body>
                            <tr v-if="!myOwnershipRequests.requests.length > 0">
                                No pending requests
                            </tr>
                            <tr v-else
                                v-for="ownershipRequest in myOwnershipRequests.requests" :key="ownershipRequest.id">
                                <td><a target="_blank"
                                       :class="[props.companyName === ownershipRequest.company.name ? 'text-orange-500' : 'text-primary-500']" :href="ownershipRequest.company.profile_link">
                                    {{ownershipRequest.company.name}}
                                </a>
                                </td>
                                <td class="capitalize">{{ownershipRequest.role.replaceAll('-', ' ')}}</td>
                                <td>
                                    <Badge class="capitalize" v-if="ownershipRequest.status === 'denied'" color="red">{{ownershipRequest.status}}</Badge>
                                    <Badge class="capitalize" v-else-if="ownershipRequest.status === 'pending'" color="primary">{{ownershipRequest.status}}</Badge>
                                    <Badge class="capitalize" v-else-if="ownershipRequest.status === 'approved'" color="green">{{ownershipRequest.status}}</Badge>
                                </td>
                            </tr>
                        </template>
                    </BaseTable>
                </div>
            </div>
        </template>
    </Modal>
</template>

<style scoped>

</style>
