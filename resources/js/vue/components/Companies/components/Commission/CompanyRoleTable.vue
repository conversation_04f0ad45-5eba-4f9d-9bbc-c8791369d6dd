<template>
    <div>
        <SimpleTable
            v-if="canView"
            title="Company Managers"
            :dark-mode="darkMode"
            :data="data"
            :headers="headers"
            :loading="loading"
            v-model="filter"
            :table-filters="availableFilters"
            :pagination-data="paginationData"
            @search="handleSearch"
            @reset="handleReset"
            row-classes="gap-5 grid items-center py-3 rounded px-5 text-sm"
        >
            <template #row.col.active="{item, value}">
                <badge :dark-mode="darkMode" :color="value ? 'green' : 'blue'" class="flex items-center gap-1">
                    {{value ? 'Active' : 'Inactive'}}
                    <simple-icon
                        :icon="simpleIcon.icons.INFORMATION_CIRCLE"
                        :dark-mode="darkMode"
                        :color="!value ? simpleIcon.colors.BLUE : simpleIcon.colors.GREEN"
                        :tooltip="!value
                        ? `Unassigned ${$filters.dateFromTimestamp(item.deleted_at, 'usWithTime', 'America/Denver')}`
                        : `Assigned ${$filters.dateFromTimestamp(item.created_at, 'usWithTime', 'America/Denver')}`"
                    />
                </badge>
            </template>
            <template #row.col.commissionable="{item, value}">
                {{value ? $filters.dateFromTimestamp(value, 'usWithTime', 'America/Denver') : 'N/A'}}
            </template>
            <template #row.col.deleted_at="{item, value}">
                {{value ? $filters.dateFromTimestamp(value, 'usWithTime', 'America/Denver') : 'N/A'}}
            </template>
            <template #row.col.actions="{item}">
                <EditCompanyUserRelationshipButton
                    :company-user-relation-id="item.id"
                    :dark-mode="darkMode"
                    @save="handleReset"
                />
            </template>
        </SimpleTable>
    </div>
</template>
<script>
import SimpleTable from "../../../Shared/components/SimpleTable/SimpleTable.vue";
import {
    SimpleTableFilterTypesEnum
} from "../../../Shared/components/SimpleTable/enum/simpleTableFilterLocationsEnum.js";
import {
    SimpleTableHiddenFilterTypesEnum
} from "../../../Shared/components/SimpleTable/enum/simpleTableFilterHiddenTypes.js";
import {PERMISSIONS, useRolesPermissions} from "../../../../../stores/roles-permissions.store.js";
import ApiService from "./service/api.js";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon.js";
import EditCompanyUserRelationshipButton from "./EditCompanyUserRelationshipButton.vue";
import Badge from "../../../Shared/components/Badge.vue";
const simpleIcon = useSimpleIcon();
const DEFAULT_TABLE_FILTER = {
    page: 1,
    perPage: 100,
}
export default {
    name: "CompanyRoleTable",
    components: {Badge, EditCompanyUserRelationshipButton, SimpleIcon, SimpleTable},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        scope: {
            type: Object,
            default: {},
        },
    },
    data() {
        return {
            simpleIcon,
            headers: [
                {title: 'Name', field: 'name'},
                {title: 'Role', field: 'role', cols: 2},
                {title: 'Active', field: 'active'},
                {title: 'Commissionable From', field: 'commissionable', cols: 2},
                {title: 'Commissionable To', field: 'deleted_at', cols: 2},
                {title: 'Actions', field: 'actions'},
            ],
            data: [],
            availableFilters: [
                {
                    location: SimpleTableFilterTypesEnum.VISIBLE,
                    field: 'name',
                    title: "User Name"
                },
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    type: SimpleTableHiddenFilterTypesEnum.SINGLE_OPTION,
                    field: 'active',
                    title: "Active",
                    options: [
                        {id: 'false', name: 'False'},
                        {id: 'true', name: 'True'},
                    ]
                },
                {
                    location: SimpleTableFilterTypesEnum.HIDDEN,
                    type: SimpleTableHiddenFilterTypesEnum.MULTIPLE_OPTIONS,
                    field: 'roles',
                    title: "Role",
                    options: []
                },
            ],
            filter: {},
            loading: false,
            paginationData: {},
            rolePermissionStore: useRolesPermissions(),
            api: ApiService.make(),
            DEFAULT_TABLE_FILTER,
            cur: null,
        }
    },
    computed: {
        canView() {
            return this.rolePermissionStore.hasPermission(PERMISSIONS.COMPANY_COMMISSION_VIEW);
        }
    },
    created() {
        this.getFilterOptions();
        this.handleSearch();
    },
    methods: {
        async getFilterOptions() {
            const roles = await this.api.listRoles();
            const roleFilterIndex = this.availableFilters.findIndex(filter => filter.field === 'roles')

            this.availableFilters[roleFilterIndex].options = roles.data.data;
        },
        async handleSearch() {
            this.filter = {...this.filter, ...DEFAULT_TABLE_FILTER, ...this.scope};
            await this.list();
        },
        async handleReset() {
            this.filter = {...DEFAULT_TABLE_FILTER, ...this.scope};
            await this.list();
        },
        async list() {
            this.loading = true;
            this.api.list(this.filter)
                .then(resp => {
                    const {data, links, meta} = resp.data
                    this.data = data
                    this.paginationData = {links, ...meta}
                })
                .catch(error => {
                    //todo: show alert on failure
                })
                .finally(() => this.loading = false)
        },
        editManager(curId) {
            this.cur = curId
        }
    }
}
</script>
