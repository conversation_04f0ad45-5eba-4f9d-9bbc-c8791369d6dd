<template>
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-4 pb-10">
        <div v-for="role in roles">
            <company-role-history :role="role" :company-id="companyId" :dark-mode="darkMode"/>
        </div>
    </div>
</template>

<script setup>
import CompanyRoleHistory from "./components/CompanyRoleHistory.vue";

const roles = [
    'account-manager',
    'business-development-manager',
    'customer-success-manager',
    'onboarding-manager',
    'sales-development-representative',
];

const props = defineProps({
    darkMode: false,
    companyId: {
        required: true,
    }
})
</script>
