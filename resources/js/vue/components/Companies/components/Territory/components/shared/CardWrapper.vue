<template>
    <div
        :class="[darkMode ? darkStyle : lightStyle, baseStyle]"
    >
        <div v-if="title" class="flex pl-3 py-3 gap-2 border-b justify-between" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
            <h5 class="text-xs uppercase text-primary-500 font-bold leading-tight">{{ title }}</h5>
            <slot name="header-actions"></slot>
        </div>
        <loading-spinner v-if="loading" :dark-mode="darkMode"/>
        <slot v-else>
            <div class="flex justify-center items-center text-slate-500 h-20">
                No Data
            </div>
        </slot>
    </div>
</template>
<script>
import LoadingSpinner from "../../../../../Shared/components/LoadingSpinner.vue";

export default {
    name: "CardWrapper",
    components: {LoadingSpinner},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: null,
        },
        style: {
            type: String,
            default: "default"
        },
        loading: {
            type: Boolean,
            default: null,
        }
    },
    computed: {
        lightStyle() {
            const styles = {
                default: "bg-light-module border-light-border",
            }
            return styles[this.style] ?? styles.default
        },
        darkStyle() {
            const styles = {
                default: "bg-dark-module border-dark-border",
            }
            return styles[this.style] ?? styles.default
        },
        baseStyle() {
            const styles = {
                default: "border rounded-md overflow-hidden",
            }
            return styles[this.style] ?? styles.default
        }
    }

}
</script>
