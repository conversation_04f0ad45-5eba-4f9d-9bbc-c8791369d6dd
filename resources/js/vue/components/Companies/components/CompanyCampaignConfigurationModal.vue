<template>
    <modal :no-buttons="true" :dark-mode="darkMode" @close="$emit('close')">
        <template v-slot:header>
            <p class="text-lg text-primary-500">Campaign Configurations</p>
        </template>
        <template v-slot:content>
            <alerts-container v-if="alertActive" :alert-type="alertType" :dark-mode="darkMode" :text="alertText"/>
            <div class="flex gap-5" v-if="canToggleAdExclusion">
                <p class="font-semibold">Exclude from Ad Automation:</p>
                <toggle-switch v-model="excludedFromAutomation" :disabled="saving" @change="updateStatus"/>
            </div>
            <div class="my-7">
                <CampaignFilters :campaign="campaign" :dark-mode="darkMode" @show-alert="reportAlert"/>
            </div>
        </template>
    </modal>
</template>

<script>
import Modal from "../../Shared/components/Modal.vue";
import {useFutureCampaignStore} from "../../Campaigns/Wizard/stores/future-campaigns.js";
import ToggleSwitch from "../../Shared/components/ToggleSwitch.vue";
import {PERMISSIONS, useRolesPermissions} from "../../../../stores/roles-permissions.store.js";
import AlertsMixin from "../../../mixins/alerts-mixin.js";
import AlertsContainer from "../../LeadProcessing/components/AlertsContainer.vue";
import CampaignFilters from "./CampaignFilters.vue";

export default {
    name: "CompanyCampaignConfigurationModal",
    components: {CampaignFilters, AlertsContainer, ToggleSwitch, Modal},
    mixins: [AlertsMixin],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        campaignReference: {
            type: String,
            required: true
        }
    },
    emits: ['close'],
    data() {
        return {
            campaignStore: useFutureCampaignStore(),
            permissionStore: useRolesPermissions(),
            campaign: {},
            excludedFromAutomation: false,
            saving: false
        }
    },
    beforeMount() {
        this.campaign = this.campaignStore.campaigns.find(campaign => campaign.reference === this.campaignReference) ?? {};
        this.excludedFromAutomation = this.campaign.excluded_from_ad_automation;
    },
    computed: {
        canToggleAdExclusion() {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_CAMPAIGN__EXCLUDE_FROM_AD_AUTOMATION);
        }
    },
    methods: {
        updateStatus(status) {
            this.saving = true;
            this.campaignStore.apiService.toggleAdAutomationStatus(this.campaignReference, {status: status})
                .then(resp => {
                    if (resp.data.data.status) {
                        this.campaign.excluded_from_ad_automation = status;
                    }
                })
                .catch(() => {
                    this.showAlert('error', 'Something went wrong. Please try again');
                    this.excludedFromAutomation = this.campaign.excluded_from_ad_automation;
                })
                .finally(() => this.saving = false);
        },
        reportAlert(message, type='error') {
            this.showAlert(type, message);
        }
    }
}
</script>
