<template>
    <modal :dark-mode="darkMode" @close="closeModal" :confirm-text="confirmText" :close-text="'Cancel'" @confirm="saveAction" :z-index-hundred="zIndexHundred">
        <template v-slot:header>
            <h4 class="text-xl font-medium">{{ action ? 'Edit' : 'Add' }} Action<span v-if="companyName"><span class="text-slate-500"> for </span>{{companyName}}</span></h4>
        </template>
        <template v-slot:content>
            <div class="grid grid-cols-3 gap-4">
                <div>
                    <alerts-container
                        class="mb-4"
                        v-if="alertActive"
                        :alert-type="alertType"
                        :text="alertText"
                        :dark-mode="darkMode">
                    </alerts-container>
                    <div class="mb-4">
                        <p class="capitalize font-semibold text-sm" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Category</p>
                        <div class="mt-2">
                            <Dropdown
                                :placeholder="categories.placeholder"
                                :dark-mode="darkMode"
                                :options="categories.options"
                                v-model="categoryId"
                            />
                        </div>
                    </div>
                    <div class="mb-4">
                        <p class="capitalize font-semibold text-sm" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Display Date (optional)</p>
                        <div class="mt-2">
                            <custom-input v-model="displayDate" :dark-mode="darkMode" class="cursor-pointer"
                                          placeholder="Enter subject" type="date"
                            />
                        </div>
                    </div>
                    <div class="mb-4">
                        <p class="capitalize font-semibold text-sm">Company Contact</p>
                        <div class="mt-2 flex items-end gap-2">
                            <Dropdown class="flex-grow" :dark-mode="darkMode" :placeholder="'Company'" placement="top" :options="companyContacts" v-model="companyContactId" />
                            <div v-if="companyContactId > 0" @click="companyContactId = 0" class="cursor-pointer border text-primary-500 rounded-md h-9 w-9 inline-flex items-center justify-center" :class="[darkMode ? 'bg-dark-background hover:bg-dark-module border-dark-border' : 'bg-primary-50 hover:bg-primary-200 border-primary-200']">
                                <svg class="fill-current w-4" viewBox="0 0 18 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                                    <path d="M17.817 9.186C17.5814 8.03435 17.1209 6.94048 16.462 5.967C15.8146 5.00928 14.9897 4.18442 14.032 3.537C13.0584 2.87833 11.9646 2.41789 10.813 2.182C10.2081 2.05933 9.59221 1.99901 8.975 2.002V0L5 3L8.975 6V4.002C9.459 4 9.943 4.046 10.41 4.142C11.305 4.32541 12.1552 4.68321 12.912 5.195C13.6584 5.69824 14.3008 6.34063 14.804 7.087C15.5853 8.24223 16.002 9.60536 16 11C15.9998 11.9359 15.8128 12.8623 15.45 13.725C15.2735 14.1405 15.0579 14.5383 14.806 14.913C14.5531 15.2854 14.2659 15.6332 13.948 15.952C12.98 16.9182 11.7511 17.5809 10.412 17.859C9.48073 18.047 8.52127 18.047 7.59 17.859C6.69456 17.6754 5.84404 17.3173 5.087 16.805C4.34148 16.3022 3.6998 15.6605 3.197 14.915C2.41656 13.7585 1.9997 12.3952 2 11H0C0.00106417 12.7937 0.536886 14.5463 1.539 16.034C2.18685 16.9901 3.01086 17.8142 3.967 18.462C5.45262 19.4675 7.20611 20.0033 9 20C9.60927 19.9999 10.217 19.9386 10.814 19.817C11.9647 19.5794 13.0579 19.1191 14.032 18.462C14.5103 18.1397 14.956 17.7717 15.363 17.363C15.7705 16.9544 16.1388 16.5084 16.463 16.031C17.4676 14.5458 18.0031 12.7931 18 11C17.9999 10.3907 17.9386 9.78301 17.817 9.186Z"/>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div class="mb-4" v-if="action == null">
                        <p class="capitalize font-semibold text-sm">Update Sales Status</p>
                        <div class="mt-2">
                            <Dropdown :dark-mode="darkMode" :placeholder="'Select'" placement="top" :options="salesStatusOptions" v-model="updateSalesStatus" />
                        </div>
                    </div>
                    <div v-if="taggableUsers.length > 0">
                        <div class="mb-4">
                            <p class="capitalize font-semibold text-sm mb-2" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Tag Staff</p>
                            <div class="max-w-[308px]">
                                <multi-select
                                    text-place-holder="Tag Staff"
                                    :options="taggableUsers"
                                    :dark-mode="darkMode"
                                    :show-search-box="true"
                                    :selected-ids="tags"
                                    placement="top"
                                />
                            </div>
                        </div>
                        <div v-if="tags.length >= 1">
                            <p class="capitalize font-semibold text-sm mb-2" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Notify By Email</p>
                            <div class="inline-flex items-center gap-2 cursor-pointer">
                                <input
                                    id="email-tagged-users"
                                    name="emailTaggedUsers"
                                    type="checkbox"
                                    v-model="tag_by_email"
                                    class="rounded-sm cursor-pointer w-4 h-4 border focus:outline-none outline-none focus:ring-0 checked:bg-primary-500 border-primary-500"
                                    :class="[darkMode ? 'bg-dark-background' : 'bg-light-background']"
                                >
                                <label for="email-tagged-users"
                                       class="block text-xs font-medium"
                                       :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                    Newly tagged staff will receive a notification and an email.
                                </label>

                            </div>

                        </div>
                    </div>
                </div>
                <div class="col-span-2">
                    <div class="mb-4">
                        <p class="capitalize font-semibold text-sm mb-2" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Subject</p>
                        <div>
                            <custom-input v-model="subject" :dark-mode="darkMode"
                                          placeholder="Enter subject" type="text"/>
                        </div>
                    </div>
                    <p class="capitalize font-semibold text-sm mb-2" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Notes</p>
                    <wysiwyg-editor
                        v-model="note"
                        :dark-mode="darkMode"
                        auto-width="100%"
                        editor-height="450"
                    >
                    </wysiwyg-editor>
                </div>
            </div>

        </template>
    </modal>
</template>

<script>
    import MarkdownEditorMixin from "../mixins/markdown-editor-mixin";
    import MarkdownEditor from "../components/MarkdownEditor.vue";
    import Modal from "../components/Modal.vue";
    import ApiService from "../../Tasks/services/api";
    import Dropdown from "../components/Dropdown.vue";
    import SharedApiService from "../services/api";
    import WysiwygEditor from "./WysiwygEditor.vue";
    import AlertsContainer from "./AlertsContainer.vue";
    import AlertsMixin from "../../../mixins/alerts-mixin";
    import CustomInput from "./CustomInput.vue";
    import MultiSelect from "./MultiSelect.vue";
    import CustomCheckbox from "../SlideWizard/components/CustomCheckbox.vue";

    export default {
        name: "ActionNoteModal",
        components: {
            CustomCheckbox,
            CustomInput,
            MultiSelect,
            AlertsContainer,
            WysiwygEditor,
            MarkdownEditor,
            MarkdownEditorMixin,
            Modal,
            Dropdown
        },
        mixins: [
            MarkdownEditorMixin, AlertsMixin
        ],
        props: {
            darkMode: {
                type: Boolean,
                default: false
            },
            action: {
                type: Object,
                default: () => {}
            },
            companyId: {
                type: Number,
                default: 0
            },
            taskId: {
                type: Number,
                default: 0
            },
            categoryDefault: {
                type: String,
                default: null
            },
            zIndexHundred: {
                type: Boolean,
                default: false
            },
            companyName: {
                type: String,
                default: ''
            }
        },
        data: function() {
            return {
                sharedApi: SharedApiService.make(),
                taskApi: ApiService.make(),
                crmOptions: [
                    {id: 'company', name: 'Company'},
                    {id: 'company_contact', name: 'Company Contact'}
                ],
                categories: {
                    options: [],
                    placeholder: 'Select Category',
                },
                companyContacts: [],
                taggableUsers: [],

                actionId: this.action?.id || 0,
                subject: this.action?.subject || '',
                displayDate: this.action?.display_date || this.toDateInputValue(new Date()),
                categoryId: this.action?.action_category_id || 0,
                note: this.action?.message || '',
                tags: this.action?.tags || [],
                tag_by_email: this.action?.tag_by_email || false,
                companyContactId: this.action?.target_type === 'company_contact' ? this.action?.target_id || 0 : 0,
                updateSalesStatus: -1,

                confirmText: 'Save',
                saving: false,
                salesStatusOptions: [],

                errorMessages: {
                    getCategories: 'Error fetching Action Categories',
                    saveAction: 'An unknown error occurred while saving an Action',
                    getTaggableUsers: 'An unknown error occurred while fetching users',
                },
            };
        },
        async created() {
            this.getSaleStatusTypes()
            await this.getCompanyContacts();
            await this.getActionCategories();
            await this.getTaggableUsers();
            const item = this.categoryDefault ? this.categories.options.find(item => item.name === this.categoryDefault) : null;
            this.categoryId = item ? item.id : this.categoryId;
        },
        computed: {
            targetType() {
                if(this.companyContactId === 0) {
                    return 'company';
                }
                else {
                    return 'company_contact';
                }
            },
        },
        methods: {
            getSaleStatusTypes() {
                this.sharedApi.getSaleStatusTypes().then(resp => {
                    const statuses = resp.data.data.statuses;
                    this.salesStatusOptions = [{id: -1, name: 'No'}, ...Object.keys(statuses).map((status) => ({id: statuses[status], name: status}))];
                });
            },
            async getTaggableUsers() {
                return this.sharedApi.getUsers().then(response => {
                    if (response.data.data.status) {
                        response.data.data.users.forEach((user) => {
                            this.taggableUsers.push({
                                id: user.id,
                                name: user.name
                            });
                        });
                    } else {
                        this.showAlert('error', this.errorMessages.getTaggableUsers);
                    }
                }).catch(err => {
                    this.showAlert('error', err.response?.data?.message || this.errorMessages.getTaggableUsers);
                });
            },
            async getActionCategories() {
                return this.sharedApi.getActionCategories().then(response => {
                    if (response.data.data.status) {
                        this.categories.options = response.data.data.categories;
                    } else {
                        this.showAlert('error', this.errorMessages.getCategories);
                    }
                }).catch(err => {
                    this.showAlert('error', err.response?.data?.message || this.errorMessages.getCategories);
                });
            },
            async getCompanyContacts() {
                this.sharedApi.getCompanyContacts(this.companyId).then(resp => this.companyContacts = resp.data.data.contacts);
            },
            saveAction() {
                if(this.saving)
                    return;

                this.saving = true;
                this.confirmText = 'Saving...';

                this.taskApi.saveAction(
                    this.actionId,
                    this.taskId,
                    this.targetType === 'company' ? this.companyId : this.companyContactId,
                    this.targetType,
                    this.subject,
                    this.note,
                    this.categoryId,
                    this.displayDate,
                    this.tags,
                    this.tags.length > 0 ? this.tag_by_email : false,
                    this.updateSalesStatus
                ).then(() => {
                    this.reloadActions();
                    this.closeModal();
                }).catch(err => {
                    this.showAlert('error', err.response?.data?.message || this.errorMessages.saveAction);
                }).finally(() => {
                    this.confirmText = 'Save';
                    this.saving = false;
                });
            },
            closeModal() {
                if (window.tinymce?.editors.length) {
                    window.tinymce?.editors.forEach(editor => window.tinymce.remove(editor));
                }
                this.$emit('close');
            },
            reloadActions() {
                this.$emit('reload-actions');
            },
            toDateInputValue(dateObject) {
                const local = new Date(dateObject);
                local.setMinutes(dateObject.getMinutes() - dateObject.getTimezoneOffset());
                return local.toJSON().slice(0,10);
            }
        }
    }
</script>
