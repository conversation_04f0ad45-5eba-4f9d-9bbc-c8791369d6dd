<template>
    <div class="w-full relative">
        <div @click="!disabled ? (dropdownActive = true) : (dropdownActive = false)"
            :class="[
            dropdownActive ? (darkMode ? 'bg-dark-module border-primary-500 shadow-lg shadow-primary-500/10' : 'bg-light-module border-primary-500 shadow-lg shadow-primary-500/10') : (darkMode ? 'border-dark-border' : 'border-light-border'),
            darkMode ? ('hover:bg-dark-module text-slate-200 bg-dark-background') : 'hover:bg-light-module text-slate-700 bg-light-background',
            disabled ? (darkMode ? 'bg-dark-module' : 'bg-light-module') + ' opacity-75 cursor-not-allowed pointer-events-none' : ''
            ]"
            class="z-30 cursor-pointer capitalize text-sm font-medium transition duration-100 w-full border flex justify-between items-center rounded py-2 px-3 h-9">
            <p class="leading-5 truncate w-11/12">{{  payloadName ? payloadName : placeholder }}</p>
            <svg
                :class="{
                    'text-grey-550': disabled,
                    'text-blue-550': !disabled,
                    'rotate-180': dropdownActive
                }"
                class="fill-current w-6 transform transition-all duration-200"
                width="13" height="8" viewBox="0 0 13 8" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M12.2071 0.792893C12.5976 1.18342 12.5976 1.81658 12.2071 2.20711L7.20711 7.20711C6.81658 7.59763 6.18342 7.59763 5.79289 7.20711L0.792893 2.20711C0.402368 1.81658 0.402369 1.18342 0.792893 0.792893C1.18342 0.402368 1.81658 0.402368 2.20711 0.792893L6.5 5.08579L10.7929 0.792893C11.1834 0.402369 11.8166 0.402369 12.2071 0.792893Z"/>
            </svg>
        </div>
        <div v-if="dropdownActive" class="absolute flex items-center justify-center inset-0 z-20">
            <input v-model="searchQuery" placeholder="Search..." class="border-none text-sm font-medium m-0 px-2.5 focus:border-transparent focus:ring-0 h-[95%] w-[99%] appearance-none" :class="[darkMode ? 'bg-dark-background' : 'bg-light-background']">
        </div>
        <div v-if="dropdownActive" class="absolute left-0 divide-y divide-inherit z-50 overflow-y-auto rounded w-full border shadow-module cursor-pointer"
             :class="[darkMode ? 'border-dark-border bg-dark-background' : 'border-light-border bg-light-background', placementIsTop ? 'bottom-0 mb-10' : 'top-0 mt-10', maxHeight]">

            <p
                v-for="option in filteredOptions"
                :class="{'hover:bg-light-module': !darkMode, 'hover:bg-dark-module': darkMode}"
                class="py-3 px-3 capitalize text-sm font-medium transition duration-200 relative z-10"
                @click="() => selectOption(option)"
                :key="option.id ? option.id : option">
                <slot name="option" v-bind="{option}">
                    {{ option.name ? option.name : option }}
                </slot>
            </p>
        </div>
        <div v-if="dropdownActive" @click="toggleDropdown" class="fixed inset-0 z-10"></div>
    </div>

</template>

<script>
import CustomInput from "./CustomInput.vue";

export default {
    name: "Dropdown",
    components: {CustomInput},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        options: {
            type: Array,
            default: () => []
        },
        placeholder: {
            type: String,
            default: 'All'
        },
        modelValue: {
            default: null,
        },
        disabled: {
            type: Boolean,
            default: false
        },
        maxHeight: {
            type: String,
            default: 'max-h-40'
        },
        placement: {
            type: String,
            required: false
        },
        type: {
            type: String,
            default: 'text',
        },
        name: {
            type: String,
            default: ''
        }
    },
    data() {
        return {
            dropdownActive: false,
            searchQuery: ''
        }
    },
    computed: {
        payload: {
            get() {
                return this.modelValue
            },
            set(value) {
                value = this.type === 'number'
                    ? parseInt(value)
                    : value;

                this.$emit('update:modelValue', value);
                this.$emit('input', value);
            }
        },
        payloadName() {
            const item = this.options.find(item => item === this.payload || item.id === this.payload);

            return item ? (item.name ? item.name : item) : this.payload;
        },
        placementIsTop() {
            return this.placement === 'top';
        },
        filteredOptions() {
            if (!this.searchQuery) {
                return this.options;
            }

            const query = this.searchQuery.toLowerCase();

            return this.options.filter(option =>
                option.name.toLowerCase().includes(query)
            );
        },
    },
    emits: [
        'update:modelValue',
        'change',
        'input'
    ],
    methods: {
        toggleDropdown() {
            if(this.disabled) {
                this.dropdownActive = false;
            }
            else {
                this.dropdownActive = ! this.dropdownActive
            }
        },

        selectOption(option) {
            const id = option.id;
            if((
                    !id
                    && id !== 0
                    && option !== this.payload
                )
                || id !== this.payload?.id) {
                this.$emit('change', option, this.payload);
            }
            this.payload = id || (id === 0 ? id : option);
            this.dropdownActive = ! this.dropdownActive
        }
    }
}
</script>

<style scoped>

</style>
