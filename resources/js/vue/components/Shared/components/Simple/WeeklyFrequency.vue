<template>
    <div class="flex gap-2 items-center">
        <span> on every </span>
        <dropdown
            base-class="flex-fit"
            :options="dayOptions"
            v-model="modelValue.week_day"
            :dark-mode="darkMode"
        />
    </div>
</template>
<script>
import Dropdown from "../Dropdown.vue";

export default {
    name: "WeeklyFrequency",
    components: {Dropdown},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: Object,
            default: {
                week_day: "monday"
            }
        }
    },
    data() {
        return {
            dayOptions: [
                {id: 'monday', name: 'Monday'},
                {id: 'tuesday', name: 'Tuesday'},
                {id: 'wednesday', name: 'Wednesday'},
                {id: 'thursday', name: 'Thursday'},
                {id: 'friday', name: 'Friday'},
                {id: 'saturday', name: 'Saturday'},
                {id: 'sunday', name: 'Sunday'},
            ]
        }
    }
}
</script>
