<template>
    <div class="absolute" v-if="show">
        <transition name="modal">
            <div class="fixed inset-0 flex items-center justify-center bg-opacity-75 bg-dark-background z-100" >
                <div class="absolute shadow rounded-lg w-full mx-4 md:mx-8"
                     :class="[darkMode ? 'bg-dark-module border border-dark-border text-slate-100' : 'bg-light-module border border-light-border text-slate-900', width]">
                    <div class="flex items-center justify-between p-5 border-b"
                         :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
                        <div class="flex items-center">
                            <h3 class="font-semibold text-lg mr-1"
                                :class="[darkMode ? 'text-slate-400' : 'text-slate-500']">Configure Filters for</h3>
                            <h3 class="font-semibold text-lg">{{ filterComponent?.name }}</h3>
                        </div>
                        <div class="flex space-x-2">
                            <CustomButton color="slate-light" :dark-mode="darkMode" @click.stop="$emit('close')">
                                Cancel
                            </CustomButton>
                            <CustomButton :disabled="disableApplyFilters" :dark-mode="darkMode" @click.stop="apply">
                                Apply Filters
                            </CustomButton>
                        </div>
                    </div>
                    <div class="overflow-y-auto px-5 pt-5 pb-32 max-h-[25rem] mt-2">
                        <component :dark-mode="darkMode"
                                   :is="filterComponent?.id"></component>
                    </div>
                </div>
            </div>
        </transition>
    </div>
</template>

<script>
import CustomButton from "../components/CustomButton.vue"
import Dropdown from "../../Shared/components/Dropdown.vue";
import ToggleSwitch from "./ToggleSwitch.vue";
import CompanyServicingAreaFilters from "./FilterConfigs/CompanyServicingAreaFilters.vue";
import ContactsFilters from "./FilterConfigs/ContactsFilters.vue";

/**
 * @typedef filterComponent
 * @property {string} id
 * @property {string} name
 */

export default {
    emits: ['apply', 'close'],
    name: "WithoutUserPresetFilterConfigModal",
    components: {
        ContactsFilters,
        ToggleSwitch,
        Dropdown,
        CustomButton,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        show: {
            type: Boolean,
            default: false,
        },
        disableApplyFilters: {
            type: Boolean,
            default: false,
        },
        filterComponent: {
            type: Object,
            required: true
        },
        width: {
            type: String,
            default: 'max-w-screen-xl'
        }
    },
    methods: {
        /**
         * Runs the apply event.
         */
        apply() {
            this.$emit('apply')
            this.$emit('close')
        }
    }
}

</script>
