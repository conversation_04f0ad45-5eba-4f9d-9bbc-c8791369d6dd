<template>
    <div class="absolute">
        <transition name="modal">
            <div class="fixed inset-0 flex items-center justify-center bg-opacity-75 bg-dark-background z-100">
                <div class="absolute shadow rounded-lg w-full max-w-screen-xl mx-4 md:mx-8"
                     :class="[darkMode ? 'bg-dark-module border border-dark-border text-slate-100' : 'bg-light-module border border-light-border text-slate-900']">
                    <div class="flex items-center justify-between p-5 border-b"
                         :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
                        <div class="flex items-center">
                            <h3 class="font-semibold text-lg mr-1"
                                :class="[darkMode ? 'text-slate-400' : 'text-slate-500']">Configure Filters for</h3>
                            <h3 class="font-semibold text-lg">{{ filterComponent.name }}</h3>
                        </div>
                        <div class="flex space-x-2">
                            <CustomButton color="slate-light" :dark-mode="darkMode" @click.stop="$emit('close')" :disabled="loadingSave">
                                Cancel
                            </CustomButton>
                            <CustomButton :dark-mode="darkMode" @click.stop="apply" :disabled="loadingSave" >
<!--                                TODO - Add loading property to custom button component -->
                                <div class="relative">
                                    <span :class="loadingSave ? 'invisible' : ''">Apply Filters</span>
                                    <div v-if="loadingSave" role="status" class="absolute -translate-x-1/2 -translate-y-1/2 top-2/4 left-1/2">
                                        <svg aria-hidden="true"  class="w-6 h-6 animate-spin fill-blue-600" viewBox="0 0 100 101" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M100 50.5908C100 78.2051 77.6142 100.591 50 100.591C22.3858 100.591 0 78.2051 0 50.5908C0 22.9766 22.3858 0.59082 50 0.59082C77.6142 0.59082 100 22.9766 100 50.5908ZM9.08144 50.5908C9.08144 73.1895 27.4013 91.5094 50 91.5094C72.5987 91.5094 90.9186 73.1895 90.9186 50.5908C90.9186 27.9921 72.5987 9.67226 50 9.67226C27.4013 9.67226 9.08144 27.9921 9.08144 50.5908Z" fill="currentColor"/><path d="M93.9676 39.0409C96.393 38.4038 97.8624 35.9116 97.0079 33.5539C95.2932 28.8227 92.871 24.3692 89.8167 20.348C85.8452 15.1192 80.8826 10.7238 75.2124 7.41289C69.5422 4.10194 63.2754 1.94025 56.7698 1.05124C51.7666 0.367541 46.6976 0.446843 41.7345 1.27873C39.2613 1.69328 37.813 4.19778 38.4501 6.62326C39.0873 9.04874 41.5694 10.4717 44.0505 10.1071C47.8511 9.54855 51.7191 9.52689 55.5402 10.0491C60.8642 10.7766 65.9928 12.5457 70.6331 15.2552C75.2735 17.9648 79.3347 21.5619 82.5849 25.841C84.9175 28.9121 86.7997 32.2913 88.1811 35.8758C89.083 38.2158 91.5421 39.6781 93.9676 39.0409Z" fill="currentFill"/></svg>
                                    </div>
                                </div>
                            </CustomButton>
                        </div>
                    </div>
                    <div class="grid grid-cols-2 gap-8 p-5 border-b relative"
                         :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
                        <div class="absolute inset-0 opacity-90 z-50 flex items-center justify-center"
                             :class="[darkMode ? 'bg-dark-module' : 'bg-light-module']">
                            <!--                            <p class="font-medium" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Filter Preset feature coming soon...</p>-->
                        </div>
                        <div class="flex items-center relative z-0">
                            <h4 class="font-medium mr-2 w-32">Filter preset:</h4>
                            <Dropdown :options="filterComponent.userPresetList" :selected="filterComponent.userPreset"
                                      v-model="filterComponent.userPreset" :dark-mode="darkMode" :disabled="loadingSave">

                            </Dropdown>
                        </div>
                        <div v-if="initialPreset === filterComponent.userPreset" class="flex items-center gap-x-8">
                            <p :class="[(this.initialPresetList !== this.filterComponent.userPresetList) ? 'text-slate-500' : 'text-primary-500 hover:text-primary-600']"
                               class="font-medium text-sm cursor-pointer">Save</p>
                            <p :class="[(this.initialPresetList !== this.filterComponent.userPresetList) ? 'text-slate-500' : 'text-primary-500 hover:text-primary-600']"
                               class="font-medium text-sm cursor-pointer">Save As...</p>
                            <p class="text-rose-500 hover:text-rose-600 font-medium text-sm cursor-pointer">Delete</p>
                        </div>
                        <div v-else class="flex items-center gap-x-8">
                            <p class="text-primary-500 hover:text-primary-600 font-medium text-sm cursor-pointer">Save
                                Preset As...</p>
                        </div>
                    </div>
                    <div class="overflow-y-auto px-5 pt-5 pb-32 max-h-[25rem] mt-2">
                        <component :dark-mode="darkMode" :is="filterComponent.id" :filters="filter" @update:filters="filter = $event"></component>
                    </div>
                </div>
            </div>
        </transition>
    </div>
</template>

<script>
import CustomButton from "../components/CustomButton.vue"
import Dropdown from "../../Shared/components/Dropdown.vue";
import ToggleSwitch from "./ToggleSwitch.vue";
import CompanyServicingAreaFilters from "./FilterConfigs/CompanyServicingAreaFilters.vue";
import {UserSettingsApiFactory} from "../../../../services/api/user_settings/factory.js";
import UnsoldLeadsFilters from "./FilterConfigs/UnsoldLeadsFilters.vue";

export default {
    emits: ['apply', 'close', 'update:filter'],
    name: "FilterConfigModal",
    components: {
        CompanyServicingAreaFilters,
        UnsoldLeadsFilters,
        ToggleSwitch,
        Dropdown,
        CustomButton,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        filterComponent: {
            type: Object,
        },
        user: {
            type: Object,
            required: true
        },
        filter: {
            type: Object,
            required: false
        }
    },
    data() {
        return {
            api: UserSettingsApiFactory.makeApiService('api'),
            initialPreset: null,
            initialPresetList: null,
            loadingSave: false,
        }
    },
    mounted() {
        this.initialPreset = this.filterComponent.userPreset;
        this.initialPresetList = this.filterComponent.userPresetList;
    },
    methods: {
        /**
         * Runs the apply event.
         */
        async apply() {
            this.loadingSave = true
            try {
                await this.api.storeUserFilterPresets(this.user.id, this.filter)
                this.$emit('apply')
                this.$emit('close')
            } catch(error) {
               console.error(error)
            } finally {
                this.loadingSave = false
            }
        }
    },
    watch: {
        filter: {
            handler(newValue) {
                this.$emit('update:filter', newValue);
            },
            deep: true
        }
    }
}

</script>
