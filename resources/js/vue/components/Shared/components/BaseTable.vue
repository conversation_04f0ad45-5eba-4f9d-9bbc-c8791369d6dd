<script setup>
import {computed, onMounted, ref} from "vue";
import LoadingSpinner from "./LoadingSpinner.vue";
const props = defineProps({
    darkMode: {
        type: Boolean,
        default: false
    },
    bodyHeight: {
        type: String,
        default: 'h-80'
    },
    loading: {
        type: Boolean,
        default: false
    }
})

const themeBackgroundClasses = computed(() => {
    if(props.darkMode === true) {
        return 'dark'
    }
    else {
        return 'light'
    }
})
</script>

<template>
    <table class="base-table">
        <thead :class="[themeBackgroundClasses]">
            <slot name="head"></slot>
        </thead>
        <tbody v-if="loading" :class="[bodyHeight, themeBackgroundClasses]">
            <LoadingSpinner />
        </tbody>
        <tbody v-else :class="[bodyHeight, themeBackgroundClasses]">
            <slot name="body"></slot>
        </tbody>
    </table>
</template>

<style>
    .base-table {
        @apply w-full mb-5;
    }
    .base-table thead {
        @apply block;
    }
    .base-table thead tr {
        @apply table table-fixed;
        width: calc(100% - 1em);
    }
    .base-table thead tr th {
        @apply px-5 pb-2 text-left text-xs font-semibold uppercase;
    }
    .base-table thead.dark tr {
        @apply text-slate-50;
    }
    .base-table thead.light tr {
        @apply text-slate-900;
    }
    .base-table tbody {
        @apply block overflow-y-auto border-y divide-y;
    }
    .base-table tbody.dark {
        @apply border-dark-border bg-dark-background;
    }
    .base-table tbody.light {
        @apply border-light-border bg-light-background;
    }
    .base-table tbody tr {
        @apply table table-fixed relative transition-all duration-100 w-full min-h-16;
    }
    .base-table tbody.dark tr {
        @apply bg-dark-background border-dark-border border-opacity-50 text-slate-50;
    }
    .base-table tbody.light tr  {
        @apply bg-light-background border-light-border border-opacity-50 text-slate-900;
    }
    .base-table tbody.dark tr:hover {
        @apply bg-dark-module;
    }
    .base-table tbody.light tr:hover  {
        @apply bg-light-module;
    }
    .base-table tbody tr td {
        @apply px-5 text-left;
    }
</style>
