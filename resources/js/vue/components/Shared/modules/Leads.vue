<template>
    <div class="col-span-3 border rounded-lg"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <alerts-container :text="alertText" :alert-type="alertType" v-if="alertActive" :dark-mode="darkMode"/>
        <div class="p-5">
            <div class="flex items-center justify-between pb-3">
                <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Leads</h5>
            </div>
            <div v-if="showFilters && !loading">
                <div class="grid grid-cols-13 gap-2 mb-2">
                    <div class="flex items-center mr-3">
                        <p class="text-grey-300 uppercase text-xs mr-2">Filters</p>
                        <svg width="16" height="10" viewBox="0 0 16 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3 4H13V6H3V4ZM0 0H16V2H0V0ZM6 8H10V10H6V8Z" fill="#ABB0B5"/>
                        </svg>
                    </div>
                    <Dropdown v-model="selectedCampaign" :options="campaigns" :dark-mode="darkMode" class="col-span-3" placeholder="Campaigns">
                        <template #option="{option}">
                            <div class="flex gap-1 items-center">
                                <div>
                                    {{ option?.name }}
                                </div>
                                <company-campaign-status-badge
                                    :status="option.payload.status"
                                    :dark-mode="darkMode"
                                />
                            </div>
                        </template>
                    </Dropdown>
                    <Dropdown v-model="selectedLeadStatus" :options="leadStatuses" :dark-mode="darkMode" class="col-span-3" placeholder="Status"></Dropdown>
                    <Dropdown v-model="selectedService" :options="services" :dark-mode="darkMode" class="col-span-3" placeholder="Service"></Dropdown>
                    <Dropdown v-model="selectedProduct" :options="products" :dark-mode="darkMode" class="col-span-3" placeholder="All Products" @change="handleProductChange"></Dropdown>
                    <Datepicker :class="{'w-1/3': date}" :dark="darkMode" v-model="date" range class="col-span-3"></Datepicker>
                    <Dropdown v-if="leadRefundsStore.canViewRefund()" v-model="selectedLeadRefunded" :options="leadRefundedOptions" :dark-mode="darkMode" class="col-span-3" placeholder="Lead refund status"></Dropdown>
                </div>
                <div class="flex items-center">
                    <div class="relative mr-2 min-w-[18rem]">
                        <input v-model="searchLeadId" class="w-full border rounded pl-8  focus:outline-none focus:border focus:border-primary-500 pr-4 py-2 h-9" placeholder="Consumer Product or Lead ID" type="text"
                               :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}"/>
                        <div class="absolute top-0 left-0 w-8 h-full flex justify-center items-center">
                            <svg class="inset-y-0 fill-current text-grey-400" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M5.66667 2C4.69421 2 3.76158 2.38631 3.07394 3.07394C2.38631 3.76158 2 4.69421 2 5.66667C2 6.14818 2.09484 6.62498 2.27911 7.06984C2.46338 7.5147 2.73346 7.91891 3.07394 8.25939C3.41442 8.59987 3.81863 8.86996 4.26349 9.05423C4.70835 9.23849 5.18515 9.33333 5.66667 9.33333C6.14818 9.33333 6.62498 9.23849 7.06984 9.05423C7.5147 8.86996 7.91891 8.59987 8.25939 8.25939C8.59987 7.91891 8.86996 7.5147 9.05423 7.06984C9.23849 6.62498 9.33333 6.14818 9.33333 5.66667C9.33333 4.69421 8.94703 3.76158 8.25939 3.07394C7.57176 2.38631 6.63913 2 5.66667 2ZM1.65973 1.65973C2.72243 0.597022 4.16377 0 5.66667 0C7.16956 0 8.6109 0.597022 9.6736 1.65973C10.7363 2.72243 11.3333 4.16377 11.3333 5.66667C11.3333 6.41082 11.1868 7.14769 10.902 7.83521C10.7458 8.21219 10.5498 8.57029 10.3178 8.90361L13.7071 12.2929C14.0976 12.6834 14.0976 13.3166 13.7071 13.7071C13.3166 14.0976 12.6834 14.0976 12.2929 13.7071L8.90361 10.3178C8.57029 10.5498 8.21219 10.7458 7.83521 10.902C7.14769 11.1868 6.41082 11.3333 5.66667 11.3333C4.92251 11.3333 4.18564 11.1868 3.49813 10.902C2.81062 10.6172 2.18593 10.1998 1.65973 9.6736C1.13353 9.14741 0.716126 8.52272 0.431349 7.83521C0.146573 7.1477 0 6.41082 0 5.66667C0 4.16377 0.597022 2.72243 1.65973 1.65973Z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="relative mr-2 min-w-[18rem]">
                        <input v-model="searchName" class="w-full border rounded pl-8  focus:outline-none focus:border focus:border-primary-500 pr-4 py-2 h-9" placeholder="Name or Email" type="text"
                               :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}"/>
                        <div class="absolute top-0 left-0 w-8 h-full flex justify-center items-center">
                            <svg class="inset-y-0 fill-current text-grey-400" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M5.66667 2C4.69421 2 3.76158 2.38631 3.07394 3.07394C2.38631 3.76158 2 4.69421 2 5.66667C2 6.14818 2.09484 6.62498 2.27911 7.06984C2.46338 7.5147 2.73346 7.91891 3.07394 8.25939C3.41442 8.59987 3.81863 8.86996 4.26349 9.05423C4.70835 9.23849 5.18515 9.33333 5.66667 9.33333C6.14818 9.33333 6.62498 9.23849 7.06984 9.05423C7.5147 8.86996 7.91891 8.59987 8.25939 8.25939C8.59987 7.91891 8.86996 7.5147 9.05423 7.06984C9.23849 6.62498 9.33333 6.14818 9.33333 5.66667C9.33333 4.69421 8.94703 3.76158 8.25939 3.07394C7.57176 2.38631 6.63913 2 5.66667 2ZM1.65973 1.65973C2.72243 0.597022 4.16377 0 5.66667 0C7.16956 0 8.6109 0.597022 9.6736 1.65973C10.7363 2.72243 11.3333 4.16377 11.3333 5.66667C11.3333 6.41082 11.1868 7.14769 10.902 7.83521C10.7458 8.21219 10.5498 8.57029 10.3178 8.90361L13.7071 12.2929C14.0976 12.6834 14.0976 13.3166 13.7071 13.7071C13.3166 14.0976 12.6834 14.0976 12.2929 13.7071L8.90361 10.3178C8.57029 10.5498 8.21219 10.7458 7.83521 10.902C7.14769 11.1868 6.41082 11.3333 5.66667 11.3333C4.92251 11.3333 4.18564 11.1868 3.49813 10.902C2.81062 10.6172 2.18593 10.1998 1.65973 9.6736C1.13353 9.14741 0.716126 8.52272 0.431349 7.83521C0.146573 7.1477 0 6.41082 0 5.66667C0 4.16377 0.597022 2.72243 1.65973 1.65973Z"/>
                            </svg>
                        </div>
                    </div>
                    <div class="relative mr-2">
                        <input v-model="searchState" class="w-full border rounded pl-8  focus:outline-none focus:border focus:border-primary-500 pr-4 py-2 h-9" placeholder="State or Zipcode" type="text"
                               :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}"/>
                        <div class="absolute top-0 left-0 w-8 h-full flex justify-center items-center">
                            <svg class="inset-y-0 fill-current text-grey-400" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M5.66667 2C4.69421 2 3.76158 2.38631 3.07394 3.07394C2.38631 3.76158 2 4.69421 2 5.66667C2 6.14818 2.09484 6.62498 2.27911 7.06984C2.46338 7.5147 2.73346 7.91891 3.07394 8.25939C3.41442 8.59987 3.81863 8.86996 4.26349 9.05423C4.70835 9.23849 5.18515 9.33333 5.66667 9.33333C6.14818 9.33333 6.62498 9.23849 7.06984 9.05423C7.5147 8.86996 7.91891 8.59987 8.25939 8.25939C8.59987 7.91891 8.86996 7.5147 9.05423 7.06984C9.23849 6.62498 9.33333 6.14818 9.33333 5.66667C9.33333 4.69421 8.94703 3.76158 8.25939 3.07394C7.57176 2.38631 6.63913 2 5.66667 2ZM1.65973 1.65973C2.72243 0.597022 4.16377 0 5.66667 0C7.16956 0 8.6109 0.597022 9.6736 1.65973C10.7363 2.72243 11.3333 4.16377 11.3333 5.66667C11.3333 6.41082 11.1868 7.14769 10.902 7.83521C10.7458 8.21219 10.5498 8.57029 10.3178 8.90361L13.7071 12.2929C14.0976 12.6834 14.0976 13.3166 13.7071 13.7071C13.3166 14.0976 12.6834 14.0976 12.2929 13.7071L8.90361 10.3178C8.57029 10.5498 8.21219 10.7458 7.83521 10.902C7.14769 11.1868 6.41082 11.3333 5.66667 11.3333C4.92251 11.3333 4.18564 11.1868 3.49813 10.902C2.81062 10.6172 2.18593 10.1998 1.65973 9.6736C1.13353 9.14741 0.716126 8.52272 0.431349 7.83521C0.146573 7.1477 0 6.41082 0 5.66667C0 4.16377 0.597022 2.72243 1.65973 1.65973Z"/>
                            </svg>
                        </div>
                    </div>
                    <!-- todo impliment searching invoice                   -->
<!--                    <div class="relative">-->
<!--                        <input v-model="searchInvoice" class="w-full border rounded pl-8  focus:outline-none focus:border focus:border-primary-500 pr-4 py-2 h-9" placeholder="Invoice No." type="text"-->
<!--                               :class="{'border-grey-200 bg-grey-50': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}"/>-->
<!--                        <div class="absolute top-0 left-0 w-8 h-full flex justify-center items-center">-->
<!--                            <svg class="inset-y-0 fill-current text-grey-400" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">-->
<!--                                <path fill-rule="evenodd" clip-rule="evenodd" d="M5.66667 2C4.69421 2 3.76158 2.38631 3.07394 3.07394C2.38631 3.76158 2 4.69421 2 5.66667C2 6.14818 2.09484 6.62498 2.27911 7.06984C2.46338 7.5147 2.73346 7.91891 3.07394 8.25939C3.41442 8.59987 3.81863 8.86996 4.26349 9.05423C4.70835 9.23849 5.18515 9.33333 5.66667 9.33333C6.14818 9.33333 6.62498 9.23849 7.06984 9.05423C7.5147 8.86996 7.91891 8.59987 8.25939 8.25939C8.59987 7.91891 8.86996 7.5147 9.05423 7.06984C9.23849 6.62498 9.33333 6.14818 9.33333 5.66667C9.33333 4.69421 8.94703 3.76158 8.25939 3.07394C7.57176 2.38631 6.63913 2 5.66667 2ZM1.65973 1.65973C2.72243 0.597022 4.16377 0 5.66667 0C7.16956 0 8.6109 0.597022 9.6736 1.65973C10.7363 2.72243 11.3333 4.16377 11.3333 5.66667C11.3333 6.41082 11.1868 7.14769 10.902 7.83521C10.7458 8.21219 10.5498 8.57029 10.3178 8.90361L13.7071 12.2929C14.0976 12.6834 14.0976 13.3166 13.7071 13.7071C13.3166 14.0976 12.6834 14.0976 12.2929 13.7071L8.90361 10.3178C8.57029 10.5498 8.21219 10.7458 7.83521 10.902C7.14769 11.1868 6.41082 11.3333 5.66667 11.3333C4.92251 11.3333 4.18564 11.1868 3.49813 10.902C2.81062 10.6172 2.18593 10.1998 1.65973 9.6736C1.13353 9.14741 0.716126 8.52272 0.431349 7.83521C0.146573 7.1477 0 6.41082 0 5.66667C0 4.16377 0.597022 2.72243 1.65973 1.65973Z"/>-->
<!--                            </svg>-->
<!--                        </div>-->
<!--                    </div>-->
                    <button @click="getLeads" class="inline-flex items-center px-5 h-9 py-1 mx-2 bg-primary-500 hover:bg-blue-500 transition duration-200 rounded-lg text-xs font-medium text-white">
                        <svg class="fill-current text-white" width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd" d="M5.66667 2C4.69421 2 3.76158 2.38631 3.07394 3.07394C2.38631 3.76158 2 4.69421 2 5.66667C2 6.14818 2.09484 6.62498 2.27911 7.06984C2.46338 7.5147 2.73346 7.91891 3.07394 8.25939C3.41442 8.59987 3.81863 8.86996 4.26349 9.05423C4.70835 9.23849 5.18515 9.33333 5.66667 9.33333C6.14818 9.33333 6.62498 9.23849 7.06984 9.05423C7.5147 8.86996 7.91891 8.59987 8.25939 8.25939C8.59987 7.91891 8.86996 7.5147 9.05423 7.06984C9.23849 6.62498 9.33333 6.14818 9.33333 5.66667C9.33333 4.69421 8.94703 3.76158 8.25939 3.07394C7.57176 2.38631 6.63913 2 5.66667 2ZM1.65973 1.65973C2.72243 0.597022 4.16377 0 5.66667 0C7.16956 0 8.6109 0.597022 9.6736 1.65973C10.7363 2.72243 11.3333 4.16377 11.3333 5.66667C11.3333 6.41082 11.1868 7.14769 10.902 7.83521C10.7458 8.21219 10.5498 8.57029 10.3178 8.90361L13.7071 12.2929C14.0976 12.6834 14.0976 13.3166 13.7071 13.7071C13.3166 14.0976 12.6834 14.0976 12.2929 13.7071L8.90361 10.3178C8.57029 10.5498 8.21219 10.7458 7.83521 10.902C7.14769 11.1868 6.41082 11.3333 5.66667 11.3333C4.92251 11.3333 4.18564 11.1868 3.49813 10.902C2.81062 10.6172 2.18593 10.1998 1.65973 9.6736C1.13353 9.14741 0.716126 8.52272 0.431349 7.83521C0.146573 7.1477 0 6.41082 0 5.66667C0 4.16377 0.597022 2.72243 1.65973 1.65973Z"/>
                        </svg>
                    </button>
                    <button @click="resetSearch" class="transition duration-200 text-sm font-semibold focus:outline-none py-2 rounded-md px-5 mr-3"
                            :class="{'bg-grey-250 hover:bg-light-background text-white': !darkMode, 'bg-grey-500 hover:bg-grey-600 text-white': darkMode}">
                        Reset
                    </button>
                </div>
            </div>
        </div>
        <div v-if="!loading">
            <div class="grid gap-x-3 my-2 px-5" :class="leadRefundsStore.canViewRefund() ? 'grid-cols-15' : 'grid-cols-14'">
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-1">Campaign</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-1">Consumer Product ID</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-1">Industry : Service</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-1">Date Sent</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">Chargeable</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-1">Delivered</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">Contact</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-3">Address</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-1">Cost</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-1">Invoice ID</p>
                <p v-if="leadRefundsStore.canViewRefund()" class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-1">Refund</p>
            </div>
            <div class="border-t border-b h-80 overflow-y-auto divide-y"
                 :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}">
                <div
                    v-for="lead in leads"
                    class="grid gap-x-3 p-5 items-center"
                    :class="[
                        darkMode
                        ? 'text-slate-100 hover:bg-dark-module border-dark-border'
                        : 'text-slate-900 hover:bg-light-module border-light-border',
                        leadRefundsStore.canViewRefund() ? 'grid-cols-15' : 'grid-cols-14'
                    ]"
                >
                    <div class="flex flex-col gap-1">
                        <div>
                            <Badge v-if="lead.is_test_lead" color="amber" :dark-mode="darkMode">
                                <svg class="mr-1 fill-current" width="15" height="15" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"> <path d="M12 17.75C12.4142 17.75 12.75 17.4142 12.75 17V11C12.75 10.5858 12.4142 10.25 12 10.25C11.5858 10.25 11.25 10.5858 11.25 11V17C11.25 17.4142 11.5858 17.75 12 17.75Z" fill="#1C274C"></path> <path d="M12 7C12.5523 7 13 7.44772 13 8C13 8.55228 12.5523 9 12 9C11.4477 9 11 8.55228 11 8C11 7.44772 11.4477 7 12 7Z" fill="#1C274C"></path> <path fill-rule="evenodd" clip-rule="evenodd" d="M1.25 12C1.25 6.06294 6.06294 1.25 12 1.25C17.9371 1.25 22.75 6.06294 22.75 12C22.75 17.9371 17.9371 22.75 12 22.75C6.06294 22.75 1.25 17.9371 1.25 12ZM12 2.75C6.89137 2.75 2.75 6.89137 2.75 12C2.75 17.1086 6.89137 21.25 12 21.25C17.1086 21.25 21.25 17.1086 21.25 12C21.25 6.89137 17.1086 2.75 12 2.75Z" fill="#1C274C"></path> </g></svg>
                                Test Lead
                            </Badge>
                        </div>
                        <p class="text-sm col-span-1">{{ lead.campaign }}</p>
                    </div>
                    <a class="text-sm text-blue-550 col-span-1" :href="'/consumer-product/?consumer_product_id=' + lead.consumer_product_id" target="_blank">{{ lead.consumer_product_id }}</a>
                    <p class="text-sm col-span-1">{{ lead.industry }} : {{ lead.service }}</p>
                    <p class="text-sm col-span-1">{{ lead.delivered_at }}</p>
                    <p class="text-sm col-span-2">{{ lead.chargeable }}
                        <button
                            class="ml-2 text-xs rounded-full inline-flex items-center px-3 py-1 font-semibold transition duration-200 bg-rose-500 hover:bg-rose-600 text-white"
                            v-if="canNoCharge(lead) && lead.chargeable === 'Yes'"
                            @click="showNoChargeLeadModal(lead)"
                        >
                            No Charge
                        </button>
                    </p>
                    <p class="text-sm col-span-1" :class="{'text-blue-550 hover:cursor-pointer': lead.delivery_logs?.length > 0}" @click="showDeliveryLogsModal(lead.delivery_logs)" >{{ lead.delivered }}</p>
                    <p class="text-sm col-span-2">{{ lead.contact }}</p>
                    <p class="text-sm col-span-3">{{ lead.address }}</p>
                    <p class="text-sm col-span-1">{{ lead.cost }}</p>

                    <p v-if="lead.billing_version === 'v2' && lead.invoice_id" @click="showInvoiceModal = true; viewingInvoiceId = lead.invoice_id" class="text-sm col-span-1 text-blue-550 hover:cursor-pointer">{{ lead.invoice_id }}</p>
                    <a v-else-if="lead.invoice_id" class="col-span-1 text-sm text-blue-550 hover:cursor-pointer" :href="getLegacyAdminInvoiceUrl(lead.invoice_id)" target="_blank">{{ lead.invoice_id }}</a>
                    <p v-else class="text-sm col-span-1">{{ lead.rejection ? 'Rejected' : lead.cancellation ? 'Cancelled' : 'N/A'  }}</p>

                    <div class="flex flex-col gap-1 items-center" v-if="leadRefundsStore.canViewRefund()">
                        <div class="cursor-pointer">
                            <lead-refund-item-refund-status-badge
                                v-if="lead.refund_status"
                                :status="lead.refund_status"
                                @click="openLeadRefundReview(true, lead.refund_lead_id)">
                            </lead-refund-item-refund-status-badge>
                        </div>
                        <custom-checkbox v-if="isRefunding && lead.is_refundable" v-model="refundItemProductIds[lead.product_assignment_id]" />
                    </div>
                </div>
            </div>
            <div class="p-3 flex gap-1 items-center">
                <Pagination
                    class="flex-1"
                    :dark-mode="darkMode"
                    :pagination-data="paginationData"
                    :show-pagination="true"
                    @change-page="handlePaginationEvent"
                    :show-results-per-page="true"
                    :show-page-selector="true"
                    per-page-label="Results Per Page"
                ></Pagination>
                <custom-button v-if="leadRefundsStore.canRequestRefund()" @click="handleRefundClick">
                    {{refundingItemIds.length > 0 ? `Refund ${refundingItemIds.length} items` : !isRefunding ? 'Initiate a refund request' : 'Check the leads you want to refund'}}
                </custom-button>
                <custom-button v-if="isRefunding" @click="handleCancelRefund">
                    Cancel refund
                </custom-button>
            </div>
            <modal v-if="deliveryLogsModal" @close="deliveryLogsModal = false" :hide-confirm="true" :dark-mode="darkMode">
                <template v-slot:content>
                    <div v-for="log in deliveryLogsInModal">
                        <p><strong>CRM Name:</strong> {{log.crm_name}}</p>
                        <p><strong>Delivery Status:</strong> {{log.delivery_status == "1"}}</p>
                        <p><strong>Delivery Data:</strong> {{log.delivery_data}}</p>
                        <p><strong>Error Log:</strong> {{log.error_log}}</p>
                        <hr>
                    </div>
                </template>
            </modal>
            <modal v-if="noChargeConfirmModal" @confirm="noChargeLead" :no-min-height="true" :confirm-text="'Confirm'" @close="closeNoChargeLeadModal">
                <template v-slot:content>
                    <p class="font-semibold">Confirm no charge Lead: {{ this.leadToNoCharge?.id }}?</p>
                </template>
            </modal>
        </div>
        <div v-if="loading">
            <loading-spinner :dark-mode="darkMode"></loading-spinner>
        </div>
        <RefundLeadRequestModal
            v-if="showRefundLeadRequestModal"
            :product-assignment-ids-to-refund="refundingItemIds"
            :dark-mode="darkMode"
            @close="handleCancelRefund"
        />
        <review-refund-lead-request
            v-if="showReviewModal"
            :lead-refund-request-id="selectedLeadRefundId"
            @close="openLeadRefundReview(false)"
            disable-review-buttons
        />
        <view-create-invoice-modal
            :company-id="companyId"
            v-if="showInvoiceModal"
            @close="showInvoiceModal  = false; viewingInvoiceId = null"
            :dark-mode="darkMode"
            :invoice-id="viewingInvoiceId"
            :readonly="true"
        />
    </div>
</template>

<script>
import Dropdown from "../components/Dropdown.vue";
import ActionsHandle from "../components/ActionsHandle.vue";
import Pagination from "../components/Pagination.vue";
import SharedApiService from "../services/api";
import LoadingSpinner from "../components/LoadingSpinner.vue";
import LegacyAdminMixin from "../mixins/legacy-admin-mixin";
import CompanyApiService from "../../Companies/services/api";
import Modal from "../../Shared/components/Modal.vue";
import AlertsContainer from "../components/AlertsContainer.vue";
import HasAlertsMixin, {AlertTypes} from "../mixins/has-alerts-mixin";
import Badge from "../components/Badge.vue";
import {PERMISSIONS, useRolesPermissions} from "../../../../stores/roles-permissions.store";
import CustomButton from "../components/CustomButton.vue";
import CustomCheckbox from "../SlideWizard/components/CustomCheckbox.vue";
import RefundLeadRequestModal from "../components/LeadRefunds/RefundLeadRequestModal.vue";
import ReviewRefundLeadRequest from "../../LeadRefunds/ReviewRefundLeadRequestModal.vue";
import LeadRefundStatusBadge from "../../LeadRefunds/components/LeadRefundStatusBadge.vue";
import useLeadRefundsHelper from "../../../../composables/leadRefundsHelper.js";
import {useLeadRefundsStore} from "../../../../stores/lead-refunds-store.js";
import LeadRefundItemRefundStatusBadge from "../../LeadRefunds/components/LeadRefundItemRefundStatusBadge.vue";
import useCompanyCampaign from "../../../../composables/useCompanyCampaign.js";
import CompanyCampaignStatusBadge from "../components/CompanyCampaign/CompanyCampaignStatusBadge.vue";
import ViewCreateInvoiceModal from "../../Billing/ViewCreateInvoiceModal.vue";

export default {
    name: "Leads",
    components: {
        ViewCreateInvoiceModal,
        CompanyCampaignStatusBadge,
        LeadRefundItemRefundStatusBadge,
        LeadRefundStatusBadge,
        ReviewRefundLeadRequest,
        RefundLeadRequestModal,
        CustomCheckbox,
        CustomButton, Badge, Pagination, ActionsHandle, Dropdown, LoadingSpinner, Modal, AlertsContainer},
    mixins: [LegacyAdminMixin, HasAlertsMixin],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            default: null
        },
        showFilters: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            leads: [],
            campaigns: [],
            leadStatuses: [],
            services: [],
            products: [],
            leadRefundedOptions: useLeadRefundsHelper().getLeadStatusOptions(),
            selectedCampaign: null,
            selectedLeadStatus: null,
            selectedService: null,
            selectedProduct: null,
            selectedLeadRefunded: null,
            searchLeadId: null,
            searchName: null,
            searchState: null,
            paginationData: null,
            apiService: null,
            companyApiService: null,
            loading: false,
            searchParams: {},
            date: null,
            deliveryLogsModal: false,
            deliveryLogsInModal: null,
            noChargeConfirmModal: false,
            leadToNoCharge: null,
            permissionStore: useRolesPermissions(),
            refundItemProductIds: {},
            isRefunding: false,
            showRefundLeadRequestModal: false,
            showReviewModal: false,
            selectedLeadRefundId: null,
            leadRefundsStore: useLeadRefundsStore(),
            showInvoiceModal: false,
            viewingInvoiceId: null
        }
    },
    computed: {
        refundingItemIds(){
            return Object.entries(this.refundItemProductIds).filter(([key, value]) => value).map(([key]) => key)
        },
    },
    created() {
        this.apiService = SharedApiService.make();
        this.companyApiService = CompanyApiService.make();

        if(this.companyId) {
            this.getLeads();
            this.getCampaigns();
            this.getStatuses();
            this.getServices();
            this.getProducts();
            this.selectedProduct = 'all';
        }
    },
    methods: {
        openLeadRefundReview(show, leadRefundId = null){
            console.log(leadRefundId)
            this.showReviewModal = show
            this.selectedLeadRefundId = leadRefundId;
        },
        handleRefundClick(){
            if (!this.isRefunding) {
                this.isRefunding = true
            } else if (this.refundingItemIds.length > 0){
                this.showRefundLeadRequestModal = true
            }
        },
        handleCancelRefund(){
            this.refundItemProductIds = {}
            this.isRefunding = false
            this.showRefundLeadRequestModal = false
            this.getLeads()
        },
        getLeads () {
            this.loading = true;
            this.setSearchParams();
            this.apiService.getCompanyLeads(this.companyId, this.searchParams).then(resp => {
                let {data, ...paginationData} = resp.data.data.leads;
                this.leads = data;
                this.paginationData = paginationData;
            }).catch(e => console.log(e)).finally(() => this.loading = false);
        },
        async handlePaginationEvent(newPageUrl) {
            this.loading = true;
            this.setSearchParams();
            await axios.get(newPageUrl.link, {
                params: this.searchParams
            }).then(resp => {
                let {data, ...paginationData} = resp.data.data.leads;
                this.leads = data;
                this.paginationData = paginationData;
            }).catch(e => console.log(e)).finally(() => this.loading = false);
        },
        getCampaigns() {
            this.companyApiService.getAllCampaignsForSelect(
                this.companyId,
                {source: 'all', withDeleted: true}
            ).then(resp => {
                this.campaigns = resp.data.data.campaigns
            })
        },
        getStatuses() {
            this.apiService.getLeadStatuses().then(resp => {
                this.leadStatuses = resp.data.data.statuses;
            })
        },
        getServices() {
            this.companyApiService.getServices(this.companyId).then(resp => {
                this.services = resp.data.data.services
                    .map(service => ({id: service.id, name: `${service.industry}: ${service.name}`}))
                    .sort((a, b) => a.name.localeCompare(b.name, undefined, {sensitivity: 'base'}));
            })
        },
        getProducts() {
            this.apiService.getProducts(this.companyId).then(resp => {
                this.products = [{ id: 'all', name: 'All Products' }, ...resp.data.data.products];
            })
        },
        resetSearch() {
            this.searchParams = {};
            this.selectedCampaign = null;
            this.selectedLeadStatus = null;
            this.selectedService = null;
            this.searchLeadId = null;
            this.searchName = null;
            this.searchState = null;
            this.date = null;
            this.selectedProduct = null;
            this.selectedLeadRefunded = null;
        },
        setSearchParams() {
            const selectedCampaignData = this.campaigns.find(e => e.id === this.selectedCampaign)

            this.searchParams = {
                'campaign_id': selectedCampaignData?.id,
                'status': this.selectedLeadStatus,
                'service': this.selectedService,
                'lead_id': this.searchLeadId,
                'name': this.searchName,
                'state': this.searchState,
                'date_from': (this.date) ? new Date(this.date[0]).getTime() / 1000 : null,
                'date_to': (this.date) ? new Date(this.date[1]).getTime() / 1000 : null,
                'product': (this.selectedProduct === 'all') ? null : this.selectedProduct,
                'lead_refund_status': this.selectedLeadRefunded
            }
        },
        isLeadInRejectionWindow(lead) {
            const rejectionExpiry = new Date(lead.rejection_expiry);
            const currentTime = new Date();

            return currentTime < rejectionExpiry;
        },
        canNoCharge(lead) {
            return this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_COMPANIES_LEADS_UPDATE_CHARGEABLE_STATUS) && (lead.can_no_charge ?? false);
        },
        async noChargeLead() {
            if (!this.leadToNoCharge)
                return;

            const leadId = this.leadToNoCharge.id;

          try {
              await this.companyApiService.updateChargeableStatus(this.companyId, leadId).then(resp => {
                  const message = resp.data.data.message;
                  if (resp.data.data.status === false){
                      this.showAlert(AlertTypes.error, message);
                  }else{
                      this.showAlert(AlertTypes.success, message);
                  }
              })
              this.getLeads();
              this.closeNoChargeLeadModal();
          } catch (error) {
              if (error.response && error.response.status === 403) {
                  this.showAlert(AlertTypes.error, 'You do not have permission to update the chargeable status.');
              }else {
                  console.error('Error updating chargeable status: ', error);
                  this.showAlert(AlertTypes.error, 'An error occurred while updating chargeable status.');
              }

              this.closeNoChargeLeadModal();
          }
        },
        handleProductChange(newProduct) {
            if (newProduct === 'all') {
                this.selectedProduct = null;
            }
        },
        showDeliveryLogsModal(deliveryLogs)
        {
            if(deliveryLogs) {
                this.deliveryLogsModal = true;
                this.deliveryLogsInModal = deliveryLogs;
            }
        },
        showNoChargeLeadModal(lead) {
            this.leadToNoCharge = lead;
            this.noChargeConfirmModal = true;
        },
        closeNoChargeLeadModal() {
            this.leadToNoCharge = null;
            this.noChargeConfirmModal = false;
        }
    }
}
</script>

<style scoped>

</style>
