<template>
    <div class="border rounded-lg"  :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
        <div class="px-5 pt-5">
            <div class="flex flex-wrap items-center justify-between">
                <h5 class="text-primary-500 text-sm uppercase font-bold leading-tight">Leads Overview</h5>
                <div class="flex items-center space-x-4">
                    <p @click="selectTab(tab.name)" v-for="tab in tabs" class="uppercase cursor-pointer text-sm font-bold" :class="selectedTab === tab.name ? 'text-rose-600' : 'text-slate-500'">{{ tab.name }}</p>
                </div>
            </div>
        </div>

        <div v-if="!loading" class="grid 2xl:grid-cols-2 gap-4 p-5">
            <div :class="darkMode ? 'text-slate-100' : 'text-slate-900'">
                <p class="font-bold mb-8 text-xl">{{ totalLeads }} Total Leads</p>
                <p class="font-normal"><span class="text-rose-600 font-bold mb-4">{{rejectionPercentage + '%'}}</span> of the company's total leads have been <strong class="text-rose-600">manually rejected </strong> <span v-if="selectedTab !== 'All Time'">in the last {{ selectedTab }}</span><span v-else>{{ selectedTab }}</span>.</p>
                <div class="mb-4 mt-10">
                    <div class="flex items-center mr-8 mb-2">
                        <div class="flex-shrink-0 w-3 h-3 mr-1 rounded-full bg-rose-600"
                        ></div>
                        <p class="text-sm font-medium" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Manually Rejected Leads</p>
                    </div>
                    <div class="flex items-center mr-8">
                        <div class="flex-shrink-0 w-3 h-3 text-sm mr-1 rounded-full bg-slate-300"
                        ></div>
                        <p class="text-sm font-medium" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Total Leads</p>
                    </div>
                </div>
                <p class="2xl:hidden font-semibold" :class="darkMode ? 'text-slate-400' : 'text-slate-500'"><span class="text-rose-500">{{rejectedLeads}}</span> Manually rejected leads</p>
            </div>
            <div class="2xl:block flex items-center justify-center xl:mt-8">
                <div class="text-center">
                    <div class="ring-chart" :class="[darkMode ? 'ring-chart-dark' : 'ring-chart-light']" :style="['--ring-percent:' + rejectionPercentage +';' , '--rejected-leads:' + rejectedLeads +';'] ">
                        <svg viewBox="-50 -50 100 100">
                            <circle cx="0" cy="0" r="50%"/>
                        </svg>
                        <p class="inside-text text-center" :class="darkMode ? 'text-slate-400' : 'text-slate-500'">Manually rejected<br>leads</p>
                    </div>
                </div>
            </div>
            <div class="col-span-2 w-full" v-if="permissionStore.hasPermission(PERMISSIONS.PERMISSION_COMPANY_OVERVIEW_LEAD_CHANNEL_BREAKDOWN)">
                <p class="text-gray-700 font-semibold text-xl my-3">Lead Channels</p>
                <div class="w-full grid grid-cols-5 items-center gap-3">
                    <p class="text-gray-700 text-right col-span-2">Direct from calculators</p>
                    <div class="p-2 h-8 col-span-2"
                         :class="{'bg-green-500' : !darkMode, 'border-r-2 border-green-500 bg-dark-background': darkMode}"
                         :style="{width: calculatePercentage(getCountByChannel(channelMapping.DIRECT_FROM_CALCULATORS)) + '%'}">
                    </div>
                    <p class="font-bold text-green-500">
                        {{ getCountByChannel(channelMapping.DIRECT_FROM_CALCULATORS) }} ({{ calculatePercentage(getCountByChannel(channelMapping.DIRECT_FROM_CALCULATORS)) }}%)
                    </p>
                    <p class="text-gray-700 text-right col-span-2">Affiliate leads</p>
                    <div class="p-2 h-8 col-span-2"
                         :class="{'bg-cyan-500' : !darkMode, 'border-r-2 border-cyan-500 bg-dark-background': darkMode}"
                         :style="{width: calculatePercentage(getCountByChannel(channelMapping.AFFILIATE_LEADS)) + '%'}">
                    </div>
                    <p class="font-bold text-cyan-500">
                        {{ getCountByChannel(channelMapping.AFFILIATE_LEADS) }} ({{ calculatePercentage(getCountByChannel(channelMapping.AFFILIATE_LEADS)) }}%)
                    </p>
                    <p class="text-gray-700 text-right col-span-2">Reworked aged leads</p>
                    <div class="p-2 h-8 col-span-2"
                         :class="{'bg-yellow-500' : !darkMode, 'border-r-2 border-yellow-500 bg-dark-background': darkMode}"
                         :style="{width: calculatePercentage(getCountByChannel(channelMapping.REWORKED_AGED_LEADS)) + '%'}">
                    </div>
                    <p class="font-bold text-yellow-500">
                        {{ getCountByChannel(channelMapping.REWORKED_AGED_LEADS) }} ({{ calculatePercentage(getCountByChannel(channelMapping.REWORKED_AGED_LEADS)) }}%)
                    </p>
                    <p class="text-gray-700 text-right col-span-2">Upsold legs</p>
                    <div class="p-2 h-8 col-span-2"
                         :class="{'bg-orange-500' : !darkMode, 'border-r-2 border-orange-500 bg-dark-background': darkMode}"
                         :style="{width: calculatePercentage(getCountByChannel(channelMapping.UNSOLD_LEGS)) + '%'}">
                    </div>
                    <p class="font-bold text-orange-500">
                        {{ getCountByChannel(channelMapping.UNSOLD_LEGS) }} ({{ calculatePercentage(getCountByChannel(channelMapping.UNSOLD_LEGS)) }}%)
                    </p>
                    <p class="text-gray-700 text-right col-span-2">Revalidated from email campaigns</p>
                    <div class="p-2 h-8 col-span-2"
                         :class="{'bg-amber-900' : !darkMode, 'border-r-2 border-amber-900 bg-dark-background': darkMode}"
                         :style="{width: calculatePercentage(getCountByChannel(channelMapping.REVALIDATED_FROM_EMAIL_CAMPAIGN)) + '%'}">
                    </div>
                    <p class="font-bold text-amber-900">
                        {{ getCountByChannel(channelMapping.REVALIDATED_FROM_EMAIL_CAMPAIGN) }} ({{ calculatePercentage(getCountByChannel(channelMapping.REVALIDATED_FROM_EMAIL_CAMPAIGN)) }}%)
                    </p>
                </div>
            </div>
        </div>
        <div v-else class="flex items-center justify-center" style="min-height: 260px;">
            <loading-spinner></loading-spinner>
        </div>
    </div>
</template>

<script>
import ApiService from "../../Companies/services/api";
import LoadingSpinner from "../components/LoadingSpinner.vue";
import dayjs from "dayjs";
import {PERMISSIONS, useRolesPermissions} from "../../../../stores/roles-permissions.store.js";

const CHANNEL_MAPPING = {
    DIRECT_FROM_CALCULATORS: 1,
    AFFILIATE_LEADS: 2,
    REWORKED_AGED_LEADS: 3,
    UNSOLD_LEGS: 4,
    REVALIDATED_FROM_EMAIL_CAMPAIGN: 5
}

export default {
    name: "LeadsOverview",
    components: { LoadingSpinner },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        companyId: {
            type: Number,
            default: null
        }
    },
    data() {
        return {
            tabs : [
                {name: '30 Days', current: true},
                {name: '3 Months', current: false},
                {name: 'All Time', current: false},
            ],
            api: ApiService.make(),
            selectedTab: '30 Days',
            rejectedLeads: 0,
            totalLeads: 0,
            loading: false,
            countByChannel: {},
            channelMapping: CHANNEL_MAPPING,
            permissionStore: useRolesPermissions()
        }
    },
    created() {
        if (this.companyId) this.getLeadsOverview();
    },
    computed: {
        PERMISSIONS() {
            return PERMISSIONS;
        },
        rejectionPercentage: function () {
            if (!this.rejectedLeads || !this.totalLeads) return 0;

            return Number((this.rejectedLeads / this.totalLeads) * 100).toFixed(2);
        },
        startTimestamp: function () {
            if (this.selectedTab === 'All Time') return undefined;
            if (this.selectedTab === '30 Days') return dayjs().subtract(30, 'day').startOf('day').unix();
            if (this.selectedTab === '3 Months') return dayjs().subtract(3, 'month').startOf('day').unix();
        }
    },
    methods: {
        getLeadsOverview() {
            this.loading = true;

            this.api.getLeadsOverview(this.companyId, this.startTimestamp)
                .then(resp => {
                    this.rejectedLeads = resp.data.data.total_rejection;
                    this.totalLeads = resp.data.data.total_leads;
                    this.countByChannel = resp.data.data.count_by_channel;
                })
                .finally(() => this.loading = false);
        },
        selectTab(selected) {
            this.selectedTab = selected;
            this.getLeadsOverview();
        },
        sendToTab(value) {
            this.$emit('send-to-tab', value)
        },
        calculatePercentage(leads) {
            if (leads === 0 || this.totalLeads === 0) {
                return 0
            }

            return Math.round(leads / this.totalLeads * 100);
        },
        getCountByChannel(channel) {
            return this.countByChannel[channel]?.total ?? 0;
        }
    }
}
</script>

<style scoped>
.ring-chart {
    --ring-size: 190;
    --ring-stroke-width: 15;
    --ring-percent: 0;
    --ring-color: #e11d48;
    --rejected-leads: 0;

    width: calc(1px * var(--ring-size));
    height: calc(1px * var(--ring-size));
    @apply relative inline-flex items-center justify-center;
}

.ring-chart::before {
    @apply box-border w-full h-full absolute top-0 left-0 z-0;
    content: '';
    border-radius: 50%;
}

.ring-chart-light::before {
    border: calc(1px * var(--ring-stroke-width)) solid #F5F6F6;
}
.ring-chart-dark::before {
    border: calc(1px * var(--ring-stroke-width)) solid #0F1A24;
}

.ring-chart::after {
    counter-reset: rejected-leads var(--rejected-leads);
    content: counter(rejected-leads);
    font: bold 26px sans-serif;
    @apply text-rose-600 mb-4;
}
.inside-text {
    top: 100px;
    @apply absolute font-bold text-base;
}
.ring-chart svg {
    @apply w-full h-full overflow-hidden absolute top-0 left-0;
    border-radius: 50%;
    transform: rotate(-90deg);
}

.ring-chart circle {
    fill: none;
    stroke-width: calc(1px * (var(--ring-stroke-width) * 2) / (var(--ring-size) / 100));
    stroke: var(--ring-color);
    stroke-dasharray: 314;
    stroke-dashoffset: calc(314px * ((100 - var(--ring-percent)) / 100));
}
</style>
