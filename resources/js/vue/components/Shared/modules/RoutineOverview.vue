<template>
    <div>
        <div class="border rounded-lg"
             :class="[darkMode ? 'bg-dark-module border-dark-border text-white' : 'bg-light-module border-light-border text-slate-900']">
            <div class="px-5 pt-5">
                <div class="flex items-center justify-between h-6">
                    <h5 class="text-primary-500 text-sm uppercase font-semibold leading-tight">Cadence Routines</h5>
                    <ActionsHandle
                                   v-if="totalRoutineCount > 0 && selectedTabName === 'Ongoing' && !loading"
                                   :dark-mode="darkMode"
                                   :custom-actions="customActions"
                                   :no-custom-action="false"
                                   :no-edit-button="true"
                                   :no-delete-button="true"
                                   width="w-56"
                                   @terminate-routine="confirmTerminate = true"
                                   @configure-contacts="configureContacts()"
                    >
                    </ActionsHandle>
                </div>
            </div>
            <div v-if="loading"
                 class="h-100 flex items-center justify-center"
                 :class="[darkMode ? 'bg-dark-module' : 'bg-light-module']">
                <loading-spinner></loading-spinner>
            </div>
            <div v-if="!loading">
                <tab :tabs="tabs" :dark-mode="darkMode" :tabs-classes="'w-full'" tab-type="Normal" @selected="tabSelected" :default-tab-index="tabIndex"></tab>
            </div>
            <div class="h-[25rem] overflow-y-auto" v-if="!loading">
                <div v-if="selectedTabName === 'Ongoing'" class="grid">
                    <div v-if="routines.ongoing.length < 1">
                        <div class="h-[25rem] flex flex-col columns-2 justify-center items-center text-slate-500 font-semibold">
                            No Routine found.
                            <custom-button @click="loadAssignCadenceModal">Assign Cadence</custom-button>
                        </div>
                    </div>
                    <div v-for="routine in routines.ongoing">
                        <p @click="expandRoutine(routine)" class="cursor-pointer flex items-center px-5 py-2 font-semibold text-sm sticky top-0 border-b z-30 relative" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border', expandedRoutines === routine.id ? '' : 'opacity-75 hover:opacity-100']">
                            <svg class="mr-2 transform transition duration-300" :class="[expandedRoutines === routine.id ? 'rotate-90' : '']" width="6" height="10" viewBox="0 0 6 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M0.292787 9.69471C0.105316 9.50718 0 9.25288 0 8.98771C0 8.72255 0.105316 8.46824 0.292787 8.28071L3.58579 4.98771L0.292787 1.69471C0.110629 1.50611 0.00983372 1.25351 0.0121121 0.991311C0.0143906 0.729114 0.11956 0.478302 0.304968 0.292894C0.490376 0.107485 0.741189 0.00231622 1.00339 3.78025e-05C1.26558 -0.00224062 1.51818 0.0985542 1.70679 0.280712L5.70679 4.28071C5.89426 4.46824 5.99957 4.72255 5.99957 4.98771C5.99957 5.25288 5.89426 5.50718 5.70679 5.69471L1.70679 9.69471C1.51926 9.88218 1.26495 9.9875 0.999786 9.9875C0.734622 9.9875 0.480314 9.88218 0.292787 9.69471Z" fill="#0081FF"/>
                            </svg>
                            {{new Date(routine.created_at).toDateString()}}
                        </p>
                        <div v-if="expandedRoutines === routine.id">
                            <DetailsGroup
                                v-if="queuedActionGroups(routine).length > 0"
                                name="Queued For Execution"
                                :dark-mode="darkMode"
                                :actions="queuedActionGroups(routine)"
                                @skip-action-group="skipActionGroup"
                                @un-skip-action-group="unSkipActionGroup"/>
                            <DetailsGroup
                                v-if="futureActionGroups(routine).length > 0"
                                name="Pending Queue"
                                :dark-mode="darkMode"
                                :actions="futureActionGroups(routine)"
                                @skip-action-group="skipActionGroup"
                                @un-skip-action-group="unSkipActionGroup"/>
                            <DetailsGroup
                                v-if="concludedActionGroups(routine).length > 0"
                                name="Concluded" :dark-mode="darkMode"
                                :actions="concludedActionGroups(routine)"/>
                            <DetailsGroup
                                v-if="skippedActionGroups(routine).length > 0"
                                name="Skipped"
                                :dark-mode="darkMode"
                                :actions="skippedActionGroups(routine)"/>
                        </div>
                    </div>
                </div>
                <div v-if="selectedTabName === 'Concluded'" class="grid">
                    <div v-if="routines.concluded.length < 1">
                        <div class="h-[25rem] flex flex-col columns-2 justify-center items-center text-slate-500 font-semibold">
                            No Routines found.
                        </div>
                    </div>
                    <div v-for="routine in routines.concluded">
                        <p @click="expandRoutine(routine)" class="cursor-pointer flex items-center px-5 py-2 font-semibold text-sm sticky top-0 border-b z-30 relative" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border', expandedRoutines === routine.id ? '' : 'opacity-75 hover:opacity-100']">
                            <svg class="mr-2 transform transition duration-300" :class="[expandedRoutines === routine.id ? 'rotate-90' : '']" width="6" height="10" viewBox="0 0 6 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M0.292787 9.69471C0.105316 9.50718 0 9.25288 0 8.98771C0 8.72255 0.105316 8.46824 0.292787 8.28071L3.58579 4.98771L0.292787 1.69471C0.110629 1.50611 0.00983372 1.25351 0.0121121 0.991311C0.0143906 0.729114 0.11956 0.478302 0.304968 0.292894C0.490376 0.107485 0.741189 0.00231622 1.00339 3.78025e-05C1.26558 -0.00224062 1.51818 0.0985542 1.70679 0.280712L5.70679 4.28071C5.89426 4.46824 5.99957 4.72255 5.99957 4.98771C5.99957 5.25288 5.89426 5.50718 5.70679 5.69471L1.70679 9.69471C1.51926 9.88218 1.26495 9.9875 0.999786 9.9875C0.734622 9.9875 0.480314 9.88218 0.292787 9.69471Z" fill="#0081FF"/>
                            </svg>
                            {{new Date(routine.created_at).toDateString()}}
                        </p>
                        <div v-if="expandedRoutines === routine.id">
                            <DetailsGroup
                                v-if="queuedActionGroups(routine).length > 0"
                                name="Queued For Execution"
                                :dark-mode="darkMode"
                                :actions="queuedActionGroups(routine)"
                                @skip-action-group="skipActionGroup"
                                @un-skip-action-group="unSkipActionGroup"/>
                            <DetailsGroup
                                v-if="futureActionGroups(routine).length > 0"
                                name="Pending Queue"
                                :dark-mode="darkMode"
                                :actions="futureActionGroups(routine)"
                                @skip-action-group="skipActionGroup"
                                @un-skip-action-group="unSkipActionGroup"/>
                            <DetailsGroup
                                v-if="concludedActionGroups(routine).length > 0"
                                name="Concluded" :dark-mode="darkMode"
                                :actions="concludedActionGroups(routine)"/>
                            <DetailsGroup
                                v-if="skippedActionGroups(routine).length > 0"
                                name="Skipped"
                                :dark-mode="darkMode"
                                :actions="skippedActionGroups(routine)"/>
                        </div>
                    </div>
                </div>
                <div v-if="selectedTabName === 'Terminated'" class="grid">
                    <div v-if="routines.terminated.length < 1">
                        <div class="h-[25rem] flex flex-col columns-2 justify-center items-center text-slate-500 font-semibold">
                            No Routines found.
                        </div>
                    </div>
                    <div v-for="routine in routines.terminated">
                        <p @click="expandRoutine(routine)" class="cursor-pointer flex items-center px-5 py-2 font-semibold text-sm sticky top-0 border-b z-30 relative" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border', expandedRoutines === routine.id ? '' : 'opacity-75 hover:opacity-100']">
                            <svg class="mr-2 transform transition duration-300" :class="[expandedRoutines === routine.id ? 'rotate-90' : '']" width="6" height="10" viewBox="0 0 6 10" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd" d="M0.292787 9.69471C0.105316 9.50718 0 9.25288 0 8.98771C0 8.72255 0.105316 8.46824 0.292787 8.28071L3.58579 4.98771L0.292787 1.69471C0.110629 1.50611 0.00983372 1.25351 0.0121121 0.991311C0.0143906 0.729114 0.11956 0.478302 0.304968 0.292894C0.490376 0.107485 0.741189 0.00231622 1.00339 3.78025e-05C1.26558 -0.00224062 1.51818 0.0985542 1.70679 0.280712L5.70679 4.28071C5.89426 4.46824 5.99957 4.72255 5.99957 4.98771C5.99957 5.25288 5.89426 5.50718 5.70679 5.69471L1.70679 9.69471C1.51926 9.88218 1.26495 9.9875 0.999786 9.9875C0.734622 9.9875 0.480314 9.88218 0.292787 9.69471Z" fill="#0081FF"/>
                            </svg>
                            {{new Date(routine.created_at).toDateString()}}
                        </p>
                        <div v-if="expandedRoutines === routine.id">
                            <DetailsGroup
                                v-if="queuedActionGroups(routine).length > 0"
                                name="Queued For Execution"
                                :dark-mode="darkMode"
                                :actions="queuedActionGroups(routine)"
                                @skip-action-group="skipActionGroup"
                                @un-skip-action-group="unSkipActionGroup"/>
                            <DetailsGroup
                                v-if="futureActionGroups(routine).length > 0"
                                name="Pending Queue"
                                :dark-mode="darkMode"
                                :actions="futureActionGroups(routine)"
                                @skip-action-group="skipActionGroup"
                                @un-skip-action-group="unSkipActionGroup"/>
                            <DetailsGroup
                                v-if="concludedActionGroups(routine).length > 0"
                                name="Concluded" :dark-mode="darkMode"
                                :actions="concludedActionGroups(routine)"/>
                            <DetailsGroup
                                v-if="skippedActionGroups(routine).length > 0"
                                name="Skipped"
                                :dark-mode="darkMode"
                                :actions="skippedActionGroups(routine)"/>
                        </div>
                    </div>
                </div>

            </div>
            <div class="p-3"></div>
        </div>


        <Modal :dark-mode="darkMode" v-if="assignCadence" :small="true" @confirm="assignCadenceToCompany()"
               @close="assignCadence = false" close-text="Cancel" confirm-text="Assign Cadence">
            <template v-slot:header>
                <p class="font-semibold">Assign a Cadence Routine</p>
            </template>
            <template v-slot:content>
                <p class="mb-2 uppercase font-semibold text-xs" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Filter Routines</p>
                <div class="flex gap-3 mb-3">
                    <custom-input class="flex-grow" :dark-mode="darkMode" v-model="filter" placeholder="Routine Name Or Author"></custom-input>
                    <div class="inline-flex items-center justify-start w-40 gap-2">
                        <toggle-switch @click="" v-model="shared" :dark-mode="darkMode"></toggle-switch>
                        <svg v-if="shared" class="fill-current text-slate-500" width="18" height="15" viewBox="0 0 18 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M12 3C12 3.79565 11.6839 4.55871 11.1213 5.12132C10.5587 5.68393 9.79565 6 9 6C8.20435 6 7.44129 5.68393 6.87868 5.12132C6.31607 4.55871 6 3.79565 6 3C6 2.20435 6.31607 1.44129 6.87868 0.87868C7.44129 0.316071 8.20435 0 9 0C9.79565 0 10.5587 0.316071 11.1213 0.87868C11.6839 1.44129 12 2.20435 12 3ZM17 5C17 5.53043 16.7893 6.03914 16.4142 6.41421C16.0391 6.78929 15.5304 7 15 7C14.4696 7 13.9609 6.78929 13.5858 6.41421C13.2107 6.03914 13 5.53043 13 5C13 4.46957 13.2107 3.96086 13.5858 3.58579C13.9609 3.21071 14.4696 3 15 3C15.5304 3 16.0391 3.21071 16.4142 3.58579C16.7893 3.96086 17 4.46957 17 5ZM13 12C13 10.9391 12.5786 9.92172 11.8284 9.17157C11.0783 8.42143 10.0609 8 9 8C7.93913 8 6.92172 8.42143 6.17157 9.17157C5.42143 9.92172 5 10.9391 5 12V15H13V12ZM5 5C5 5.53043 4.78929 6.03914 4.41421 6.41421C4.03914 6.78929 3.53043 7 3 7C2.46957 7 1.96086 6.78929 1.58579 6.41421C1.21071 6.03914 1 5.53043 1 5C1 4.46957 1.21071 3.96086 1.58579 3.58579C1.96086 3.21071 2.46957 3 3 3C3.53043 3 4.03914 3.21071 4.41421 3.58579C4.78929 3.96086 5 4.46957 5 5ZM15 15V12C15.0014 10.9833 14.7433 9.98303 14.25 9.094C14.6933 8.98054 15.1568 8.96984 15.6049 9.06272C16.053 9.1556 16.474 9.34959 16.8357 9.62991C17.1974 9.91023 17.4903 10.2695 17.6921 10.6802C17.8939 11.091 17.9992 11.5424 18 12V15H15ZM3.75 9.094C3.25675 9.98305 2.9986 10.9833 3 12V15H2.6572e-07V12C-0.000192468 11.542 0.104463 11.0901 0.305947 10.6789C0.507431 10.2676 0.800394 9.90793 1.16238 9.62742C1.52437 9.3469 1.94578 9.15298 2.39431 9.06052C2.84284 8.96806 3.30658 8.97951 3.75 9.094Z"/>
                        </svg>
                        <svg v-else width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M3.375 3.375C3.375 5.23575 4.88925 6.75 6.75 6.75C8.61075 6.75 10.125 5.23575 10.125 3.375C10.125 1.51425 8.61075 0 6.75 0C4.88925 0 3.375 1.51425 3.375 3.375ZM12.75 14.25H13.5V13.5C13.5 10.6058 11.1442 8.25 8.25 8.25H5.25C2.355 8.25 0 10.6058 0 13.5V14.25H12.75Z" fill="#64748B"/>
                        </svg>

                        <p class="text-sm font-semibold text-slate-500">{{shared ? 'All' : 'Mine Only'}}</p>
                    </div>
                </div>
                <br/>
                <p class="mb-2 uppercase font-semibold text-xs" :class="[darkMode ? 'text-slate-100' : 'text-slate-900']">Select Routine</p>
                <Dropdown
                    v-model="selectedAssignCadenceRoutineId"
                    :dark-mode="darkMode"
                    :options="filteredRoutines"
                    placeholder="Select Routine"
                ></Dropdown>
                <span class="text-xs">{{filteredRoutines.length}} results</span>
            </template>
        </Modal>
        <Modal :dark-mode="darkMode" v-if="confirmTerminate" :small="true" @confirm="terminateRoutine"
               @close="confirmTerminate = false" close-text="Cancel" confirm-text="Terminate">
            <template v-slot:header>
                <p class="font-semibold">Are you sure you want to terminate this routine?</p>
                <p v-if="configuringContacts" class="font-semibold">Configure Routine Contacts</p>
            </template>
            <template v-slot:content></template>
        </Modal>
        <Modal :dark-mode="darkMode" container-classes="p-0" v-if="configuringContacts" :small="true"
               @confirm="updateContacts" @close="configuringContacts = false; this.contactsLoading = true;"
               button-wrapper-classes="pb-5 pr-5">
            <template v-slot:header>
                <p class="font-semibold">Configure Routine Contacts</p>
            </template>
            <template v-slot:content>
                <div v-if="contactsLoading"
                     class="border-t border-b h-100 overflow-y-auto divide-y flex items-center justify-center"
                     :class="[darkMode ? 'bg-dark-background border-dark-border divide-dark-border' : 'bg-light-background  border-light-border divide-light-border']">
                    <loading-spinner></loading-spinner>
                </div>
                <div v-if="!contactsLoading" class="border-b h-[24rem] overflow-y-auto divide-y pb-32"
                     :class="[darkMode ? 'bg-dark-background border-dark-border divide-dark-border text-white' : 'bg-light-background border-light-border divide-light-border text-slate-900']">
                    <div class="grid grid-cols-2 gap-3 px-6 py-4 items-center" v-for="contact in contacts"
                         :key="contact">
                        <p>{{ contact.first_name + ' ' + contact.last_name }}</p>
                        <Dropdown @input="addContactUpdate(contact)" placeholder="Select Configuration"
                                  v-model="contact.cadence_routine_exclusion_status" :options="contactConfigurations"
                                  :dark-mode="darkMode"></Dropdown>
                    </div>
                </div>
            </template>
        </Modal>
    </div>
</template>

<script>
import DetailsGroup from "../../OutreachCadence/components/OngoingRoutines/components/DetailsGroup.vue";
import ActionsHandle from "../../Shared/components/ActionsHandle.vue";
import Modal from "../../Shared/components/Modal.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import ApiService from "../../OutreachCadence/services/api";
import {isArray} from "lodash";
import LoadingSpinner from "../../LeadProcessing/components/LoadingSpinner.vue";
import Tab from "../components/Tab.vue";
import CustomButton from "../components/CustomButton.vue";
import ToggleSwitch from "../components/ToggleSwitch.vue";
import CustomInput from "../components/CustomInput.vue";

export default {
    name: "RoutineOverview",
    components: {
        CustomInput,
        ToggleSwitch,
        CustomButton, Tab, LoadingSpinner, Dropdown, Modal, ActionsHandle, DetailsGroup},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        companyId: {
            type: Number,
            default: null,
        },
        currentUserId: {
            type: Number,
            default: null,
        },
    },
    mounted() {
        this.loading = true
        this.getRoutines()
    },
    data() {
        return {
            apiService: ApiService.make(),
            routines: {ongoing: [], concluded: [], terminated: []},
            contacts: [],
            contactConfigurations: [
                {id: 'allow', name: 'Allow'},
                {id: 'exclude_this', name: 'Exclude from current'},
                {id: 'exclude_all', name: 'Exclude from all'},
            ],
            customActions: [
                {event: 'configure-contacts', name: 'Configure Contacts'},
                {event: 'terminate-routine', name: 'Terminate Routine'},
            ],
            confirmTerminate: false,
            configuringContacts: false,
            contactUpdates: [],
            contactsLoading: true,
            tabs: [
                {name: 'Ongoing', current: true},
                {name: 'Concluded', current: false},
                {name: 'Terminated', current: false}
            ],
            selectedTabName: 'Ongoing',
            tabIndex: 0,
            expandedRoutines: null,
            assignCadence: false,
            shared: true,
            filter: '',
            selectedAssignCadenceRoutineId: null,
            assignableRoutines: [],
            loading: true,
        }
    },
    computed: {
        queuedActionGroups() {
            return routine => {
                return routine.scheduled_groups.filter(group => ['pending', 'queued'].includes(group.status))
            }
        },
        futureActionGroups() {
            return routine => {
                return routine.scheduled_groups.filter(group => group.status === 'not_started')
            }
        },
        concludedActionGroups() {
            return routine => {
                return routine.scheduled_groups.filter(group => group.status === 'concluded')
            }
        },
        skippedActionGroups() {
            return routine => {
                return routine.scheduled_groups.filter(group => group.status === 'skipped')
            }
        },
        totalRoutineCount() {
            let count = 0
            Object.keys(this.routines).forEach(key => {
                count += this.routines[key].length
            })
            return count
        },
        filteredRoutines() {
            let routines = this.assignableRoutines
            if(!this.shared)
                routines = routines.filter(routine => routine.user_id == this.currentUserId)
            if(this.filter.length > 0)
                routines = routines.filter(routine => {
                    return routine.name.toLowerCase().indexOf(this.filter.toLowerCase()) >= 0 ||
                        routine.user.name.toLowerCase().indexOf(this.filter.toLowerCase()) >= 0
                })
            if(this.selectedAssignCadenceRoutineId && !routines.find(routine => routine.id === this.selectedAssignCadenceRoutineId))
                routines.unshift(this.assignableRoutines.find(routine => routine.id === this.selectedAssignCadenceRoutineId))
            return routines
        }
    },
    methods: {
        expandRoutine(routine) {
            if(this.expandedRoutines === routine.id) {
                this.expandedRoutines = null
            }
            else {
                this.expandedRoutines = routine.id
            }
        },
        expandRoutineOnTabChange(routine) {
            this.expandedRoutines = routine.id
        },
        getRoutines() {
            this.loading = true
            this.apiService.getCompanyRoutinesForCompany(this.companyId).then((resp) => {
                if (!isArray(resp.data.data)) {
                    this.routines = resp.data.data
                    Object.keys(this.routines).forEach(key => {
                        if(!isArray(this.routines[key])) {
                            this.routines[key] = Object.values(this.routines[key])
                        }
                    })
                    if(this.routines.ongoing.length > 0) {
                        this.expandRoutineOnTabChange(this.routines[this.selectedTabName.toLowerCase()][0])
                    }
                }
            }).finally(resp => this.loading = false)
        },
        terminateRoutine() {
            const ongoingRoutineId = this.routines.ongoing.length > 0 ? this.routines.ongoing.find(routine => true).id : null
            if (ongoingRoutineId) {
                this.apiService.terminateRoutine(ongoingRoutineId).then(() => {
                    this.confirmTerminate = false
                    this.getRoutines()
                })
            }
        },
        skipActionGroup(actionGroup) {
            this.apiService.skipActionGroup(actionGroup.id).then(() => actionGroup.skip = 1)
        },
        unSkipActionGroup(actionGroup) {
            this.apiService.unSkipActionGroup(actionGroup.id).then(() => actionGroup.skip = 0)
        },
        configureContacts() {
            this.configuringContacts = true;
            this.apiService.getCadenceContacts(this.expandedRoutines).then(resp => {
                this.contacts = resp.data.data
                this.contactsLoading = false
            })
        },
        addContactUpdate(contact) {
            this.contactUpdates = this.contactUpdates.filter(item => item.userId !== contact.id)
            this.contactUpdates.push({userId: contact.id, newStatus: contact.cadence_routine_exclusion_status})
        },
        updateContacts() {
            this.contactsLoading = true
            if (this.contactUpdates.length > 0) {
                this.apiService.updateContacts(this.expandedRoutines, this.contactUpdates).then(() => {
                    this.contactUpdates = []
                    this.configuringContacts = false
                    this.contactsLoading = true
                })
            } else {
                this.contactUpdates = []
                this.configuringContacts = false
            }
        },
        tabSelected(tabName) {
            this.selectedTabName = tabName;
            if(this.routines[this.selectedTabName.toLowerCase()].length > 0) {
                this.expandRoutineOnTabChange(this.routines[this.selectedTabName.toLowerCase()][0])
            }
        },
        assignCadenceToCompany() {
            this.apiService.assignCompanyRoutines([this.companyId], [], false, this.selectedAssignCadenceRoutineId).then(resp => {
                this.assignCadence = false
                this.getRoutines()
            })
        },
        loadAssignCadenceModal() {
            this.getAssignableRoutines()
            this.assignCadence = true
        },
        getAssignableRoutines() {
            this.apiService.getAllAvailableRoutineConfigs().then(resp => this.assignableRoutines = resp.data.data)
        },
    }
}
</script>

<style scoped>

</style>
