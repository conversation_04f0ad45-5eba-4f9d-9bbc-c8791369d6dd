<template>
    <!-- Consumer Product Companies -->
    <div id="consumer-product-companies" class="border rounded-lg"
         :class="{'bg-light-module border-light-border': !darkMode, 'bg-dark-module border-dark-border': darkMode}">
        <div class="py-5">
            <h5 class="pl-5 text-primary-500 text-sm uppercase font-semibold pb-4 leading-tight">Companies Assigned To</h5>
            <div class="grid grid-cols-9 mb-2 px-5 ml-2">
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">Name</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">Cost</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-2">Charge/Deliv.</p>
                <p class="text-slate-400 font-medium tracking-wide uppercase text-xs col-span-3">Date</p>
            </div>
            <loading-spinner v-if="loading" :dark-mode="darkMode" />
            <div v-else class="border-t border-l border-r h-auto max-h-48 overflow-y-auto"
                 :class="{'bg-light-background border-light-border': !darkMode, 'bg-dark-background border-dark-border': darkMode}">
                <div class="border-b" :class="{ 'border-light-border': !darkMode, 'border-dark-border': darkMode }" v-if="companies.length > 0">
                    <div class="grid grid-cols-9 items-center px-5 py-1 border-b ml-1" v-for="company in companies"
                         :class="{ 'text-grey-800 hover:bg-light-module border-light-border': !darkMode, 'text-grey-200 hover:bg-dark-module border-dark-border': darkMode }">
                        <p class="text-xs col-span-2">{{ company.company_name }}</p>
                        <p class="text-xs col-span-2">{{ company.cost }}</p>
                        <p class="text-xs col-span-2">{{ company.chargeable }} / {{ company.delivered }}</p>
                        <p class="text-xs col-span-3">{{ formatDateTime(company.date || company.date_delivered) }} MST</p>
                    </div>
                </div>
                <div v-else>
                    <div class="px-5 py-2 text-s border-b"
                         :class="{'text-grey-800 border-light-border': !darkMode, 'text-grey-200 border-dark-border': darkMode}">
                        No companies found
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import ConsumerApiService from "../services/consumer_api";
import LoadingSpinner from "../components/LoadingSpinner.vue";
import { REQUEST } from "../../../../constants/APIRequestKeys";
import dayjs from "dayjs";

export default {
    name: "ConsumerProductCompanies",
    components: {
      LoadingSpinner,
    },
    props: {
        consumerProductId: {
            type: Number,
            default: null
        },
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            consumerApi: ConsumerApiService.make(),
            companies: [],
            loading: false,
        }
    },
    created() {
        if (this.consumerProductId) this.getCompanies();
    },
    methods: {
        getCompanies() {
            if(this.loading) {
                return false;
            }

            this.loading = true;

            this.consumerApi.getProductAssignments(this.consumerProductId)
                .then(resp => {
                    if(resp?.data?.data?.status === true) {
                        this.companies = resp.data.data[REQUEST.ASSIGNMENTS] ?? [];
                    }
                })
                .catch(e => console.error("Error fetching assignments", e))
                .finally(() => this.loading = false);
        },
        formatDateTime(timestamp) {
            return dayjs(timestamp).tz('America/Denver').format('D MMM YYYY h:mma');
        }
    },
    watch: {
        consumerProductId(newVal, oldVal) {
            if (newVal !== oldVal) {
                this.getCompanies();
            }
        }
    },
}
</script>
