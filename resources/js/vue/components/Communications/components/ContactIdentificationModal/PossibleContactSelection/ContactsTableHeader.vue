<template>
    <div class="grid grid-cols-3 gap-x-3 px-5 text-slate-400 font-medium tracking-wide uppercase text-sm items-center">
        <p v-for="header in headers">{{ header }}</p>
    </div>
</template>

<script>
export default {
    name: "ContactsTableHeader",

    computed: {
        headers(){
            return [
                'Type',
                'Name',
                'Interactions count'
            ]
        }
    }
}
</script>
