<template>
    <div class="flex flex-col border rounded-lg p-4">
        <div @click="showEvents = !showEvents" class="flex gap-2 items-center cursor-pointer">
            <simple-icon
                :class="{showEvents}"
                :dark-mode="darkMode"
                :icon="simpleIcon.icons.CHEVRON_RIGHT"
                :color="simpleIcon.colors.BLUE"
                :size="simpleIcon.sizes.MD"
                :style="showEvents ? 'transition duration-150 rotate-90' : 'transition duration-150'"
            />
            <div class="font-semibold">
                Invoice #{{invoiceId}} Timeline
            </div>
        </div>
        <ul v-if="showEvents" role="list" class="space-y-6">
            <li v-if="events.length > 0" v-for="(event, eventIdx) in events" class="relative flex gap-x-4 items-center">
                <event :dark-mode="darkMode" :is-last="eventIdx === events.length - 1">
                    <base-invoice-event
                        :dark-mode="darkMode"
                        :event-data="event"
                    >
                    </base-invoice-event>
                </event>
            </li>
        </ul>
    </div>
</template>
<script>
import Api from "./services/api";
import Event from "./components/EventList/Event.vue";
import BaseInvoiceEvent from "./components/EventList/BaseInvoiceEvent.vue";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../composables/useSimpleIcon";
const simpleIcon = useSimpleIcon()

export default {
    name: "InvoiceEventList",
    components: {SimpleIcon, BaseInvoiceEvent, Event},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        invoiceId: {
            type: Number,
            required: true,
        }
    },
    data() {
        return {
            simpleIcon,
            api: Api.make(),
            loading: false,
            events: [],
            showEvents: false,
        }
    },
    created() {
        this.getInvoiceEvents()
    },
    methods: {
        async getInvoiceEvents() {
            this.loading = true;
            try {
                const response = await this.api.getInvoiceEvents(this.invoiceId)

                this.events = response.data.data

            } catch (e) {
                console.error(e)
            }
            this.loading = false;
        }
    }
}
</script>