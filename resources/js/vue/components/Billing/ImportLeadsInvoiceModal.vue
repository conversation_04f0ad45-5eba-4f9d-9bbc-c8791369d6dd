<template>
    <modal
        :dark-mode="darkMode"
        :container-classes="`min-h-[65vh]`"
        @close="handleClose"
        no-min-height
        no-buttons
    >
        <template v-slot:header>
            Import leads to generate a new invoice
        </template>
        <template v-slot:content>
            <loading-spinner v-if="loading"/>
            <div v-else class="flex flex-col gap-4 p-4 relative h-full w-full">
                <simple-alert
                    v-if="errorHandler.message"
                    :dark-mode="darkMode"
                    :content="errorHandler.message"
                    variant="light-red"
                >
                </simple-alert>
                <div v-if="!importLeadsResponse" class="grid grid-cols-2 gap-4">
                    <labeled-value
                        label="Company"
                    >
                        <company-search-autocomplete
                            v-model="companyId"
                        />
                    </labeled-value>
                    <FileInput
                        v-model="file"
                        accept=".csv"
                        :disabled="loading"
                    />
                    <CustomButton
                        class="w-fit"
                        :disabled="importButtonDisabled"
                        @click="importLeads"
                    >
                        Upload
                    </CustomButton>
                </div>
                <div v-else class="flex flex-col gap-4">
                    <div class="grid grid-cols-4 gap-2">
                        <labeled-value v-if="importLeadsResponse?.company_id" label="Company">
                            <entity-hyperlink
                                type="company"
                                :entity-id="importLeadsResponse?.company_id"
                                :dark-mode="darkMode"
                                :suffix="importLeadsResponse?.company_name"
                            >
                            </entity-hyperlink>
                        </labeled-value>
                        <labeled-value label="Total Delivered & Billable">
                            ${{ importLeadsResponse.total_billable_in_dollars }}
                        </labeled-value>
                        <labeled-value label="Total Delivered & NOT Billable">
                            ${{ importLeadsResponse.total_not_billable_in_dollars }}
                        </labeled-value>
                        <labeled-value label="Total Rejected">
                            ${{ importLeadsResponse.total_rejected_in_dollars }}
                        </labeled-value>
                        <labeled-value
                            v-if="importLeadsResponse.total_discrepancy !== 0"
                            label="Discrepancy Total Delivered & Billable"
                        >
                            ${{ importLeadsResponse.total_discrepancy }}
                        </labeled-value>
                    </div>

                    <div class="grid grid-cols-1 gap-2">
                        <Accordion
                            v-for="category in categories"
                            :dark-mode="darkMode"
                            :total="`${importLeadsResponse[category?.key]?.length} leads for $${importLeadsResponse[`total_${category?.key}_in_dollars`]}`"
                            container-classes="relative flex justify-between items-center cursor-pointer gap-x-6 py-5 px-4 rounded-lg"
                            body-classes="max-h-80 overflow-scroll"
                            class="border rounded-lg"
                        >
                            <template #title>
                                <span class="flex items-center gap-1">
                                    <p>{{ category.title }}</p>
                                    <simple-icon
                                        v-if="category.title_tooltip"
                                        :icon="simpleIcon.icons.INFORMATION_CIRCLE"
                                        :tooltip="category.title_tooltip"
                                    />
                                </span>
                            </template>
                            <template #default>
                                <billing-card-paginated-content
                                    :dark-mode="darkMode"
                                    :headers="headers"
                                    :data="importLeadsResponse[category.key]"
                                >
                                    <template #consumer_product_id="{value}">
                                        <entity-hyperlink
                                            :dark-mode="darkMode"
                                            type="consumer_product"
                                            :entity-id="value"
                                            :suffix="value"
                                        >

                                        </entity-hyperlink>
                                    </template>
                                    <template #status="{value}">
                                        <badge :color="value === 'import' ? 'green' : 'red'">
                                            {{ value }}
                                        </badge>
                                    </template>
                                    <template #system_price="{value}">
                                        ${{ value }}
                                    </template>
                                    <template #import_price="{value}">
                                        ${{ value }}
                                    </template>
                                    <template #billable_price="{value}">
                                        ${{ value }}
                                    </template>
                                    <template #discrepancy="{item}">
                                        <div class="flex items-center gap-1">
                                            <badge
                                                v-if="item.discrepancy !== 0"
                                                :color="item.discrepancy > 0 ? simpleIcon.colors.GREEN : simpleIcon.colors.RED"
                                            >
                                                <simple-icon
                                                    :icon="item.discrepancy > 0 ? simpleIcon.icons.ARROW_TENDING_UP : simpleIcon.icons.ARROW_TENDING_DOWN"
                                                    :color="item.discrepancy > 0 ? simpleIcon.colors.GREEN : simpleIcon.colors.RED"
                                                    :tooltip="item.discrepancy > 0 ? 'INCREASE' : 'DECREASE'"
                                                />
                                                <p>(${{ item.discrepancy }})</p>
                                            </badge>
                                        </div>
                                    </template>
                                </billing-card-paginated-content>
                            </template>
                        </Accordion>
                    </div>
                    <div class="flex gap-2">
                        <CustomButton
                            v-if="showImportButton"
                            class="w-fit"
                            @click="handleImport"
                        >
                            Import
                        </CustomButton>
                        <CustomButton
                            class="w-fit"
                            color="gray"
                            @click="handleResetButton"
                        >
                            Reset
                        </CustomButton>
                    </div>
                </div>
            </div>
        </template>
    </modal>
</template>
<script>
import Modal from "../Shared/components/Modal.vue";
import FileInput from "../Shared/FileInput.vue";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import SimpleTable from "../Shared/components/SimpleTable/SimpleTable.vue";
import Alert from "../Shared/components/Alert.vue";
import SimpleAlert from "../Shared/components/SimpleAlert.vue";
import CompanySearchAutocomplete from "../Shared/components/Company/CompanySearchAutocomplete.vue";
import LabeledValue from "../Shared/components/LabeledValue.vue";
import CustomButton from "../Shared/components/CustomButton.vue";
import ApiService from "./services/api.js";
import useErrorHandler from "../../../composables/useErrorHandler.js";
import {useInvoiceModalStore} from "../../../stores/invoice/invoice-modal.store.js";
import EntityHyperlink from "../BillingManagement/components/EntityHyperlink.vue";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../composables/useSimpleIcon.js";
import BillingCardPaginatedContent
    from "../BillingManagement/components/BillingCardBuilders/BillingCardPaginatedContent.vue";
import BillingCardWrapper from "../BillingManagement/components/BillingCardBuilders/BillingCardWrapper.vue";
import Accordion from "../Shared/components/Accordion.vue";
import Badge from "../Shared/components/Badge.vue";
import {useToastNotificationStore} from "../../../stores/billing/tost-notification.store.js";

export default {
    name: "ImportLeadsInvoiceModal",
    components: {
        Badge,
        Accordion,
        BillingCardWrapper,
        BillingCardPaginatedContent,
        SimpleIcon,
        EntityHyperlink,
        CustomButton,
        LabeledValue,
        CompanySearchAutocomplete, SimpleAlert, Alert, SimpleTable, LoadingSpinner, FileInput, Modal
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
    },
    data() {
        return {
            invoiceStore: useInvoiceModalStore(),
            companyId: null,
            file: null,
            loading: false,
            importing: false,
            api: ApiService.make(),
            errorHandler: useErrorHandler(),
            toastNotificationStore: useToastNotificationStore(),
            importLeadsResponse: null,
            headers: [
                {field: 'consumer_product_id', name: 'Id'},
                {field: 'first_name', name: 'First Name'},
                {field: 'last_name', name: 'Last Name'},
                {field: 'system_price', name: 'System Price'},
                {field: 'import_price', name: 'Import Price'},
                {field: 'discrepancy', name: 'Discrepancy'},
                {field: 'billable_price', name: 'Billable Price'},
                {field: 'campaign', name: 'Campaign'},
                {field: 'zipcode', name: 'Zipcode'},
            ],
            categories: [
                {
                    title: 'Delivered & Billable',
                    key: 'billable',
                    title_tooltip: 'Leads that are delivered but not canceled, rejected, or invoiced. Prices will be updated if they differ.'
                },
                {
                    title: 'Delivered & NOT Billable',
                    key: 'not_billable',
                    title_tooltip: 'Leads that are delivered but either canceled, rejected, or invoiced. Prices will NOT be updated.'
                },
                {
                    title: 'Rejected',
                    key: 'rejected',
                    title_tooltip: 'Leads that WILL be rejected. Prices will NOT be updated.'
                },
            ]
        }
    },
    methods: {
        handleResetButton() {
            this.companyId = null
            this.file = null
            this.resetError()
        },
        resetError() {
            this.errorHandler.resetError()
            this.loading = false
            this.importLeadsResponse = null
        },
        handleClose() {
            if (this.loading) {
                return
            }

            this.$emit('close')
        },
        createFormData() {
            const formData = new FormData()
            formData.set('file', this.file)
            formData.set('company_id', this.companyId)
            return formData
        },
        async importLeads() {
            this.resetError()

            this.loading = true

            try {
                const response = await this.api.getInvoiceLeadsImportData(this.createFormData())

                this.importLeadsResponse = response.data.data
            } catch (err) {
                this.errorHandler.handleError(err)
            }

            this.loading = false
        },
        async handleImport() {
            if (!this.showImportButton) {
                return
            }

            this.loading = true

            try {
                const response = await this.api.importLeadsViaCsv(
                    this.createFormData(),
                    {
                        responseType: 'blob'
                    }
                )
                const disposition = response.headers['content-disposition'];
                let filename = 'import';

                if (disposition && disposition.includes('filename=')) {
                    const match = disposition.match(/filename[^;=\n]*=((['"]).*?\2|[^;\n]*)/);
                    if (match && match[1]) {
                        filename = match[1].replace(/['"]/g, '');
                    }
                }

                const dotIndex = filename.lastIndexOf('.');
                const newFilename = dotIndex !== -1
                    ? `${filename.substring(0, dotIndex)}_reconciliation${filename.substring(dotIndex)}`
                    : `${filename}_reconciliation.csv`;

                const url = window.URL.createObjectURL(new Blob([response.data]));
                const link = document.createElement('a');
                link.href = url;
                link.setAttribute('download', newFilename);
                document.body.appendChild(link);
                link.click();
                link.remove();
                window.URL.revokeObjectURL(url);

                this.$emit('close')

                this.toastNotificationStore.notifySuccess('Success! It will take few minutes to issue invoice and reject leads', 5000);
            } catch (err) {
                this.toastNotificationStore.notifyError('Error importing leads.');
                this.errorHandler.handleError(err)
            }

            this.loading = false
        }
    },
    computed: {
        showImportButton() {
            return this.importLeadsResponse?.billable?.length > 0 || this.importLeadsResponse?.rejected?.length > 0
        },
        simpleIcon() {
            return useSimpleIcon()
        },
        importButtonDisabled() {
            if (!this.companyId || !this.file) {
                return true;
            }

            return this.loading
        }
    }
}
</script>
