<template>
    <div>
        <div class="grid grid-cols-10 border-b pb-1 font-semibold text-xs text-slate-500 uppercase gap-2">
            <div>Id</div>
            <div>Payment method</div>
            <div class="col-span-2">Status</div>
            <div>Amount</div>
            <div class="col-span-2">Author</div>
            <div class="col-span-2">Dates</div>
            <div>Actions</div>
        </div>
        <div v-if="invoicePayments.length === 0"
             class="flex justify-center items-center py-2 text-xs font-semibold text-slate-500"
        >
            No payments
        </div>
        <div v-else v-for="item in invoicePayments">
            <div class="grid grid-cols-10 text-xs font-semibold border-b items-center py-2 gap-2">
                <div>{{ item.id }}</div>
                <div>
                    <div v-if="item.payment_method_types?.length > 0" class="flex flex-col gap-1">
                        <payment-method-badge
                            v-for="type in item.payment_method_types"
                            :type="type"
                        ></payment-method-badge>
                    </div>
                    <p v-else>Pending</p>
                </div>
                <div class="col-span-2 flex flex-col gap-1">
                    <div class="flex gap-1">
                        <invoice-payment-status-badge :status="item.status"></invoice-payment-status-badge>
                        <badge>
                            {{ item.attempt_number }} / {{ item.max_attempts }}
                        </badge>
                    </div>
                    <p>
                        {{item.error_message}}
                    </p>
                </div>
                <div>{{ $filters.centsToFormattedDollars(item.amount) }}</div>
                <div class="flex flex-col gap-1 col-span-2">
                    <author-badge :dark-mode="darkMode" :author="item.author" />
                </div>
                <div class="col-span-2 flex flex-col">
                    <div v-for="displayedDate in displayedDates" class="flex items-center gap-1">
                        <simple-icon :icon="displayedDate.icon" :tooltip="displayedDate.tooltip"/>
                        <p>{{ item[displayedDate.field] ?? displayedDate.default ?? '----' }}</p>
                    </div>
                </div>
                <div class="flex gap-1">
                    <simple-icon
                        @click="showDetails[item.id] = !showDetails[item.id]"
                        :icon="showDetails[item.id] ? this.simpleIcon.icons.CHEVRON_UP : this.simpleIcon.icons.CHEVRON_DOWN"
                        clickable
                        tooltip="Show all attempts"
                    ></simple-icon>
                    <simple-icon
                        v-if="showCancelPaymentButton(item)"
                        @click="handlePaymentScheduleCancel(item.id)"
                        :icon="this.simpleIcon.icons.X_CIRCLE"
                        :clickable="!loadingCancelingPayment"
                        :tooltip="!loadingCancelingPayment ? 'Cancel payment' : 'Loading...'"
                        :color="this.simpleIcon.colors.RED"
                    ></simple-icon>
                </div>
            </div>
            <InvoicePaymentChargesTable
                v-if="showDetails[item.id]"
                :payment-charges="item.charges"
                class="pl-5"
            >
            </InvoicePaymentChargesTable>
        </div>
    </div>
</template>
<script>
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import InvoiceItem from "../components/CreateNewInvoice/InvoiceItem.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import InvoicePaymentStatusBadge from "./InvoicePaymentStatusBadge.vue";
import InvoicePaymentChargesTable from "./InvoicePaymentChargesTable.vue";
import PaymentMethodBadge from "./PaymentMethodBadge.vue";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import Badge from "../../Shared/components/Badge.vue";
import ApiService from "../../BillingManagement/services/invoice-payments.js";
import {useToastNotificationStore} from "../../../../stores/billing/tost-notification.store.js";
import {ROLES, useRolesPermissions} from "../../../../stores/roles-permissions.store.js";
import LabeledValue from "../../Shared/components/LabeledValue.vue";
import AuthorBadge from "../components/AuthorBadge.vue";

export default {
    name: 'InvoicePaymentsTable',
    components: {
        AuthorBadge,
        LabeledValue,
        Badge,
        LoadingSpinner,
        PaymentMethodBadge,
        InvoicePaymentChargesTable,
        InvoicePaymentStatusBadge,
        InvoiceItem,
        SimpleIcon
    },
    props: {
        invoicePayments: {
            type: Array,
            required: true
        },
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            showDetails: {},
            api: ApiService.make(),
            toastNotificationStore: useToastNotificationStore(),
            loadingCancelingPayment: false,
            rolesPermissions: useRolesPermissions(),
        }
    },
    computed: {
        isFinanceOwner() {
            return this.rolesPermissions.hasRole(ROLES.FINANCE_OWNER)
        },
        simpleIcon() {
            return useSimpleIcon()
        },
        displayedDates() {
            return [
                {tooltip: 'Updated At', field: 'updated_at', icon: this.simpleIcon.icons.UPDATED_AT},
                {tooltip: 'Next Attempt At', field: 'next_attempt_at', icon: this.simpleIcon.icons.CALENDAR},
                {tooltip: 'Charged At', field: 'charged_at', icon: this.simpleIcon.icons.CHARGED_AT},
            ]
        }
    },
    methods: {
        useSimpleIcon,
        showCancelPaymentButton(invoicePayment) {
            return invoicePayment.status === 'rescheduled' && this.isFinanceOwner
        },
        async handlePaymentScheduleCancel(invoicePaymentId) {
            if (this.loadingCancelingPayment) {
                return
            }

            this.loadingCancelingPayment = true;

            try {
                await this.api.cancelInvoicePayment(invoicePaymentId)
                this.$emit('refresh')
            } catch (err) {
                console.error(err)
                this.toastNotificationStore.notifyError(
                    'Failed to cancel payment'
                )
            }

            this.loadingCancelingPayment = false;
        }
    },
}
</script>
