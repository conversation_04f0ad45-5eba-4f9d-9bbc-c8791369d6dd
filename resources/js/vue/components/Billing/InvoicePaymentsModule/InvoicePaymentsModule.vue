<template>
    <div
        class="border-y px-8 py-4 flex flex-col"
        :class="{'bg-light-background ': !darkMode, 'bg-dark-background': darkMode}"
    >
        <div class="flex justify-between mb-2">
            <div class="text-lg font-bold ">
                Payments
            </div>
            <CustomButton @click="handleShowIssuePaymentForm">
                {{ showIssuePaymentForm ? 'Cancel' : 'Show create payment form' }}
            </CustomButton>
        </div>
        <request-invoice-charge-form
            v-if="showIssuePaymentForm"
            class="mb-10"
            :dark-mode="darkMode"
            :invoice-id="invoiceId"
            :company-id="companyId"
            @refresh="handleRefresh"
        />
        <div>
            <div v-if="!invoicePaymentsStore.loading">
                <invoice-payments-table
                    :invoice-payments="invoicePaymentsStore.invoicePayments"
                    @refresh="handleRefresh"
                >
                </invoice-payments-table>
                <div class="flex uppercase text-sm font-semibold mt-2">
                    <div class="mr-2">
                        Total Value:
                    </div>
                    <div class="text-red-500">
                        {{ $filters.centsToFormattedDollars(invoicePaymentsStore.totalPaid) }}
                    </div>
                </div>
            </div>
            <div v-else>
                <loading-spinner/>
            </div>
        </div>
    </div>
</template>
<script>
import CustomButton from "../../Shared/components/CustomButton.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import InvoiceItem from "../components/CreateNewInvoice/InvoiceItem.vue";
import {useInvoicePaymentsStore} from "../../../../stores/invoice/invoice-payments-store.js";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import InvoicePaymentStatusBadge from "./InvoicePaymentStatusBadge.vue";
import InvoicePaymentsTable from "./InvoicePaymentsTable.vue";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import {useInvoiceModalStore} from "../../../../stores/invoice/invoice-modal.store.js";
import ApplyCreditToInvoiceModal from "../credits/ApplyCreditToInvoiceForm.vue";
import RequestInvoiceChargeForm from "./RequestInvoiceChargeForm.vue";

export default {
    name: 'InvoicePaymentsModule',
    components: {
        RequestInvoiceChargeForm,
        ApplyCreditToInvoiceModal,
        LoadingSpinner,
        InvoicePaymentsTable,
        InvoicePaymentStatusBadge, InvoiceItem, SimpleIcon, Dropdown, CustomInput, CustomButton
    },
    props: {
        invoiceId: {
            type: Number,
            required: true
        },
        companyId: {
            type: Number,
            required: true
        },
        darkMode: {
            type: Boolean,
            required: false
        }
    },
    data: function () {
        return {
            invoicePaymentsStore: useInvoicePaymentsStore(),
            invoiceStore: useInvoiceModalStore(),
            showDetails: false,
            showIssuePaymentForm: false,
        }
    },
    mounted() {
        this.getPayments()
    },
    unmounted() {
        this.invoicePaymentsStore.$reset();
    },
    methods: {
        useSimpleIcon,
        getPayments() {
            this.invoicePaymentsStore.getInvoicePayments(this.invoiceId)
        },
        handleRefresh() {
            this.getPayments()
            this.invoiceStore.retrieveInvoiceData(this.invoiceId)
        },
        handleShowIssuePaymentForm() {
            this.showIssuePaymentForm = !this.showIssuePaymentForm

            if (!this.showIssuePaymentForm) {
                this.selectedCreditType = null
                this.invoiceCreditsStore.invoiceCredit = {}
            }
        }
    },
}
</script>
