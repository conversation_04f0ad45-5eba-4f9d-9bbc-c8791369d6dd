<template>
    <div
        class="border-y px-8 py-4 flex flex-col"
        :class="{'bg-light-background ': !darkMode, 'bg-dark-background': darkMode}"
    >
        <div class="flex justify-between mb-2">
            <div class="text-lg font-bold ">
                Debt Collection
            </div>
        </div>
        <div
            v-if="invoiceStore.invoiceCollections"
            class="text-xs"
        >
            <div
                class="grid border-b pb-1 font-semibold text-slate-500 uppercase grid-cols-6"
            >
                <div>Sent Date</div>
                <div>User</div>
                <div>Recovery Status</div>
                <div>Amount Collected</div>
                <div>Amount Recovered</div>
                <div class="text-right">Recovery Date</div>
            </div>
            <div class="grid uppercase grid-cols-6 mt-1 items-center">
                <div>{{invoiceStore.invoiceCollections.sent_date}}</div>
                <div>{{ invoiceStore.invoiceCollections.user_name }}</div>
                <div>
                    <custom-inline-select
                        :options="invoiceCollectionsStore.recoveryStatusOptions.map(e => ({name: e.name, value: e.id}))"
                        v-model="invoiceStore.invoiceCollections.recovery_status"
                        :disabled="!invoiceCollectionsStore.canUpdateInvoiceCollections"
                    >
                    </custom-inline-select>
                </div>
                <div>{{ $filters.centsToFormattedDollars(invoiceStore.invoiceCollections.amount_collected) }}</div>
                <div>
                    <custom-inline-input
                        v-model="invoiceStore.invoiceCollections.amount_recovered_in_dollars"
                        prefix="$"
                        :disabled="!invoiceCollectionsStore.canUpdateInvoiceCollections"
                        @update:modelValue="() => invoiceStore.invoiceCollections.amount_recovered = invoiceStore.invoiceCollections.amount_recovered_in_dollars * 100"
                    >
                    </custom-inline-input>
                </div>
                <div class="text-right">{{ invoiceStore.invoiceCollections.recovery_date }}</div>
            </div>
            <custom-button v-if="invoiceCollectionsStore.canUpdateInvoiceCollections && !invoiceStore.hasPendingAction" @click="updateInvoiceCollectionsData" :disabled="invoiceCollectionsStore.saving">
                Update
            </custom-button>
        </div>
        <div v-else class="flex flex-col gap-2 mb-2 justify-between">
            <invoice-debt-collection-form
                :dark-mode="darkMode"
                :total-outstanding="invoiceStore?.invoiceTotals?.outstanding"
                :invoice-id="invoiceStore.invoiceId"
                @created="handleInvoiceCollectionsCreated"
            />
        </div>
    </div>
</template>
<script>
import CustomButton from "../../Shared/components/CustomButton.vue";
import {useInvoiceModalStore} from "../../../../stores/invoice/invoice-modal.store.js";
import CustomInlineInput from "../../Shared/components/CustomInlineInput.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import InvoiceItem from "../components/CreateNewInvoice/InvoiceItem.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import DatePicker from "@vuepic/vue-datepicker";
import InvoiceDebtCollectionForm from "./InvoiceDebtCollectionForm.vue";
import {useInvoiceCollectionsStore} from "../../../../stores/invoice/invoice-collections-store.js";
import CustomInlineSelect from "../../Shared/components/CustomInlineSelect.vue";
const simpleIcon = useSimpleIcon()

export default {
    name: "InvoiceCollection",
    components: {
        CustomInlineSelect,
        InvoiceDebtCollectionForm,
        DatePicker, Dropdown, CustomInput, InvoiceItem, SimpleIcon, CustomInlineInput, CustomButton},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            simpleIcon,
            invoiceStore: useInvoiceModalStore(),
            invoiceCollectionsStore: useInvoiceCollectionsStore(),
            refundReason: null,
        }
    },
    methods: {
        handleInvoiceCollectionsCreated(){
            this.invoiceStore.retrieveInvoiceData(this.invoiceStore.invoiceId)
        },

        updateInvoiceCollectionsData(){
            this.invoiceCollectionsStore.updateInvoiceCollectionsData(
                this.invoiceStore.invoiceId,
                {
                    amountRecovered: this.invoiceStore.invoiceCollections.amount_recovered,
                    recoveryStatus: this.invoiceStore.invoiceCollections.recovery_status,
                }
            )
        }
    }
}
</script>
