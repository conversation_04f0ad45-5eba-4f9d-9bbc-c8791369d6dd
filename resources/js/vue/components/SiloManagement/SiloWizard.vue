<template>
    <div class="relative"
         :class="[saving ? 'pointer-events-none' : '']"
    >
        <AlertsContainer
            v-if="alertActive"
            :text="alertText"
            :alert-type="alertType"
        />
        <div class="py-6 px-5 mx-auto">
            <h5 class="text-left ml-2 text-primary-500 text-sm font-semibold mb-4 uppercase leading-tight">Create New Directory</h5>
            <div class="flex items-center gap-x-4 justify-center">
                <div v-for="(slideId, index) in slideIds"
                    :key="slideId"
                >
                    <div class="flex items-center gap-x-4">
                        <p class=""
                            :class="[currentSlide?.id === slideId
                                ? 'text-primary-500 cursor-default font-bold pointer-events-none'
                                : !slideDataValid(slideId)
                                    ? 'text-slate-500 cursor-pointer'
                                    : darkMode ? 'text-white cursor-pointer' : 'text-black cursor-pointer']"
                           @click="goToSlide(slideId)"
                        >
                            {{ getSlideName(slideId) }}
                        </p>
                        <div v-if="index < (slideIds.length - 1)">
                            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="w-5">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 4.5l7.5 7.5-7.5 7.5" />
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="h-[50vh] border mx-6 overflow-auto p-3 relative"
            :class="[darkMode ? 'bg-dark-background border-dark-border' : 'bg-light-background border-light-border',
                    ]"
        >
            <div class="absolute mt-24 m-auto w-[97%]" v-if="loading || saving">
                <LoadingSpinner class />
            </div>
            <Suspense v-if="!loading">
                <Transition name="slide" mode="out-in"
                    :class="[saving ? 'opacity-30 pointer-events-none' : '']"
                >
                    <Component
                        v-if="currentSlide"
                        :is="getSlideComponent"
                        ref="currentSlideInstance"
                        :dark-mode="darkMode"
                        :slide-id="currentSlide?.id"
                        :silo-data="siloData"
                        :reference-data="referenceData"
                        :stored-data="storedData"
                        :shared-api-service="sharedApiService"
                        :silo-api-service="siloApiService"
                        @slide-error="handleSlideError"
                        @slide-progress="nextSlide"
                        @silo-data-update="handleDataUpdate"
                        @stored-data-update="handleStoredDataUpdate"
                    />
                </Transition>
            </Suspense>
        </div>
        <div class="py-4 px-3 flex items-center mx-auto justify-evenly">
            <CustomButton :dark-mode="darkMode"
                color="slate-outline"
                @click="cancelWizard"
            >
                Cancel Directory
            </CustomButton>
            <CustomButton :dark-mode="darkMode"
                :class="[isFirstSlide ? 'opacity-30 grayscale pointer-events-none' : '']"
                @click="previousSlide"
                color="primary-outline"
            >
                Previous Slide
            </CustomButton>
            <CustomButton :dark-mode="darkMode"
                :class="[isLastSlide ? 'opacity-30 grayscale pointer-events-none' : '']"
                @click="nextSlide"
            >
                Next Slide
            </CustomButton>
            <CustomButton :dark-mode="darkMode"
              :class="[siloDataValid ? '' : 'opacity-30 grayscale pointer-events-none']"
                @click="saveSilo"
                color="green"
            >
                Save Directory
            </CustomButton>
        </div>
    </div>
</template>

<script>

import CustomButton from "../Shared/components/CustomButton.vue";
import { toRaw } from "vue";
import AlertsContainer from "../LeadProcessing/components/AlertsContainer.vue";
import AlertsMixin from "../../mixins/alerts-mixin.js";
import LoadingSpinner from "../LeadProcessing/components/LoadingSpinner.vue";
import SiloDetailSlide from "./SiloWizardSlides/SiloDetailSlide.vue";
import SiloIndustryServiceSlide from "./SiloWizardSlides/SiloIndustryServiceSlide.vue";
import SiloWebsiteSlide from "./SiloWizardSlides/SiloWebsiteSlide.vue";
import SiloLocationTypeSlide from "./SiloWizardSlides/SiloLocationTypeSlide.vue";
import SiloLocationSlide from "./SiloWizardSlides/SiloLocationSlide.vue";
import SiloCollectionEntrySlide from "./SiloWizardSlides/SiloCollectionEntrySlide.vue";
import { transformWizardData } from "./services/SiloDataTransformer.js";

const getDefaultComponent = () => {
    const div = document.createElement('div');
    div.innerText = 'Error: Missing Component';
    return div;
}

export const slideMap = {
    siloDetails: {
        id: 'silo-details',
        component: SiloDetailSlide,
        name: 'Directory Details',
    },
    industryService: {
        id: 'silo-industry-service',
        component: SiloIndustryServiceSlide,
        name: 'Industry',
    },
    website: {
        id: 'silo-website',
        component: SiloWebsiteSlide,
        name: 'Website & Flow',
    },
        locationType: {
        id: 'silo-location-type',
        component: SiloLocationTypeSlide,
        name: 'Location Types',
    },
    location: {
        id: 'silo-location',
        component: SiloLocationSlide,
        name: 'Locations',
    },
    collectionEntry: {
        id: 'silo-collection-entry',
        component: SiloCollectionEntrySlide,
        name: 'Collections & Entries',
    },
};

export default {
    name: "SiloWizard",
    components: {
        LoadingSpinner,
        AlertsContainer,
        CustomButton
    },
    mixins: [AlertsMixin],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        siloApiService: {
            type: Object,
            default: {},
        },
        sharedApiService: {
            type: Object,
            default: {},
        },
        sharedReferenceData: {
            type: Object,
            default: {},
        },
    },
    mounted() {
        Object.assign(this.referenceData, this.sharedReferenceData);
        this.loadAll();
        this.currentSlide = slideMap.siloDetails;
    },
    emits: ['closeWizard'],
    data () {
        return {
            defaultSlide: {
                id: 'default',
                component: getDefaultComponent(),
            },
            currentSlide: null,
            currentSlideInstance: null,
            slideIds: Object.values(slideMap ?? {}).map(slide => slide.id),
            slideNames: Object.values(slideMap ?? {}).map(slide => slide.name),

            loading: false,
            saving: false,

            referenceData: {
                industries: [],
            },
            storedData: {},
            siloData: {},

            errors: {
                generic: 'An unknown error occurred.',
                loadError: 'Errors occurred while fetching reference data.'
            }
        }
    },
    computed: {
        getSlideComponent() {
            return toRaw(this.currentSlide?.component ?? null);
        },
        isLastSlide() {
            return this.slideIds.findIndex(slide => slide === this.currentSlide?.id) === (this.slideIds.length - 1);
        },
        isFirstSlide() {
            return this.slideIds.findIndex(slide => slide === this.currentSlide?.id) === 0;
        },
        siloDataValid() {
            const allOtherSlides = this.slideIds.filter(slideId => slideId !== this.currentSlide?.id);
            const validSlides = allOtherSlides.reduce((total, slideId) => {
                return this.siloData[slideId]?.valid ? total + 1 : total;
            }, 0);

            return allOtherSlides.length === validSlides;
        },
    },
    methods: {
        setSlideById(slideId) {
            if (!this.validateCurrentSlide()) return;
            this.currentSlide = this.getSlideById(slideId);
            if (this.siloData[slideId]) { // Reset the validation on revisiting a slide to make sure it's revalidated on exit
                this.siloData[slideId].valid = false;
            }
        },
        getSlideById(slideId) {
            const targetSlide = Object.values(slideMap).find(slide => slide.id === slideId);
            return targetSlide || this.defaultSlide;
        },
        getSlideIndex(slideId) {
            return this.slideIds.findIndex(slide => slide === slideId);
        },
        nextSlide() {
            const nextSlideId = this.slideIds[this.getSlideIndex(this.currentSlide.id) + 1];
            if (nextSlideId) this.setSlideById(nextSlideId);
        },
        previousSlide() {
            const previousSlideId = this.slideIds[this.getSlideIndex(this.currentSlide.id) - 1];
            if (previousSlideId) this.setSlideById(previousSlideId);
        },
        goToSlide(slideId) {
            this.setSlideById(slideId);
        },
        getSlideName(slideId) {
            return this.getSlideById(slideId).name;
        },
        slideDataValid(slideId) {
            return this.siloData[slideId]?.valid;
        },
        handleSlideError(slideErrors, messageType = 'error') {
            this.showAlert(messageType, slideErrors);
        },
        validateCurrentSlide() {
            if (!this.$refs.currentSlideInstance.validate) {
                console.error(`No validation could be done on slide '${this.currentSlide.name}', as no exposed validate() method was found.`);
                return true;
            }
            else return !!this.$refs.currentSlideInstance.validate();
        },
        // Update Silo data. Slides should only emit this after successful validation
        handleDataUpdate(slideId, siloData) {
            this.siloData[slideId] = this.siloData[slideId] ?? {};
            Object.assign(this.siloData[slideId], {
                ...siloData,
                valid: true,
            });
        },
        // Store custom data for returning to a slide - currently only Locations Slide to persist geography data
        handleStoredDataUpdate(slideId, keys = [], data) {
            this.storedData[slideId] = this.storedData[slideId] ?? {};
            const targetObject = keys.reduce((output, key) => {
                if (!(key in output)) output[key] = {};
                return output[key];
            }, this.storedData[slideId]);

            Object.assign(targetObject, data);
        },
        saveSilo() {
            if (this.saving) return;
            if (this.validateCurrentSlide()) {
                this.saving = true;
                const payload = transformWizardData(this.siloData);

                this.siloApiService.createSiloFromWizard(payload).then(resp => {
                    if (resp.data?.data?.status) {
                        const siloName = resp.data.data.silo?.name ?? 'New Directory'
                        this.showAlert('success', `Successfully created Directory "${siloName}".`);
                        setTimeout(() => {
                            this.saving = false;
                            this.$emit('closeWizard');
                        }, 3000);
                    }
                    else {
                        this.saving = false;
                        this.handleGenericResponse(resp, 'silo');
                    }
                }).catch(err => {
                    this.handleGenericResponse(err, 'silo');
                    console.error(err);
                    this.saving = false;
                });
            }
        },
        cancelWizard() {
            //TODO: confirm cancel modal
            this.$emit('closeWizard');
        },

        async loadAll() {
            this.loading = true;

            const loadErrors = (await Promise.all([
                this.loadIndustries(),
                this.loadLocationTypes(),
                this.loadCountries(),
            ]))
            .filter(res => res !== true);

            if (loadErrors.length) {
                this.showAlert('error', this.errors.loadError);
                // disable wizard on load errors?
            }

            this.loading = false;
        },
        handleGenericResponse(resp, responseDataKey, referenceKey = null) {
            if (resp.data?.data?.status && responseDataKey in resp.data.data) {
                this.referenceData[referenceKey ?? responseDataKey] = resp.data.data[responseDataKey];
                return true;
            }
            else {
                this.showAlert('error', resp?.response?.data?.message || resp?.message || this.errors.generic);
            }

            return false;
        },
        async loadIndustries() {
            const resp = await this.sharedApiService.getOdinIndustries().catch(err => {
                console.error(err);
            });

            return this.handleGenericResponse(resp, 'industries');
        },
        async loadLocationTypes() {
            const resp = await this.siloApiService.getSiloLocationTypes().catch(err => {
                console.error(err);
            });

            return this.handleGenericResponse(resp, 'silo_location_types', 'locationTypes');
        },
        async loadCountries() {
            const resp = await this.sharedApiService.getCountries().catch(err => {
                console.error(err);
            });

            return this.handleGenericResponse(resp, 'countries');
        },
    },
}

</script>

<style scoped>
.slide-enter-active,
.slide-leave-active {
    opacity: 1;
    transition: opacity 0.25s ease-in 0s;
}

.slide-enter-from,
.slide-leave-to {
    opacity: 0;
    transition: opacity 0.25s 0s;
}

</style>
