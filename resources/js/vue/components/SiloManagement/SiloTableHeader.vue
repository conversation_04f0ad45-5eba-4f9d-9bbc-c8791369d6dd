<template>
    <div class="px-5 grid grid-cols-7 gap-5 pb-3" :class="[darkMode ? 'text-slate-400' : 'text-slate-500']">
        <p class="table-header-text col-span-2">Website</p>
        <p class="table-header-text col-span-2">Directory Name</p>
        <p class="table-header-text col-span-2">Industry</p>
        <p class="table-header-text">Status</p>
    </div>
</template>

<script>
export default {
    name: "SiloTableHeader",
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
    }
}
</script>

<style scoped>
.table-header-text {
    @apply uppercase text-xs font-bold rounded;
}
</style>
