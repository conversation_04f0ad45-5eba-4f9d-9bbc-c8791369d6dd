<template>
    <div class="p-6 my-auto border rounded-sm h-full overflow-auto"
         :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']"
    >
        <div class="text-center">
            <h6 class="text-lg font-bold">Directory Website</h6>
        </div>
        <hr class="my-6 w-5/6 mx-auto opacity-80"
            :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
        />
            <div class="mx-auto flex justify-center items-center max-w-screen-sm" :class="[loading ? 'pointer-events-none opacity-50' : '']">
                <div class="grid gap-y-6 h-full items-center pb-8">

                    <div class="items-center pb-4">
                        <p :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="block mb-1 font-semibold">
                            Please select a Website
                        </p>
                        <div>
                            <Dropdown
                                v-model="websiteDetails.selectedWebsite"
                                :dark-mode="darkMode"
                                placeholder="Select Website"
                                :options="websiteOptions"
                                :selected="websiteDetails.selectedWebsite"
                                @update:model-value="fetchStatamicData"
                            />
                        </div>
                    </div>
                    <div>
                        <p class="mb-2 font-semibold">Select a Flow and Revision (Optional)</p>
                        <div class="flex items-center justify-center gap-x-4">
                            <div class="w-64">
                                <p class="mb-1 text-sm font-medium" :class="[darkMode ? 'text-slate-400' : 'text-slate-600']">
                                    Flow
                                </p>
                                <Dropdown
                                    :dark-mode="darkMode"
                                    :options="flowOptions"
                                    v-model="websiteDetails.selectedFlowId"
                                    @update:model-value="updateRevisionOptions"
                                />
                            </div>
                            <div class="w-64">
                                <p class="mb-1 text-sm font-medium" :class="[darkMode ? 'text-slate-400' : 'text-slate-600']">
                                    Revision
                                </p>
                                <Dropdown
                                    :class="[websiteDetails.selectedFlowId ? '' : 'pointer-events-none grayscale-[70%] opacity-50']"
                                    :dark-mode="darkMode"
                                    :options="revisionOptions"
                                    placeholder="Automatic"
                                    :selected="null"
                                    v-model="websiteDetails.selectedRevisionId"
                                />
                            </div>
                        </div>
                    </div>
                </div>
        </div>
        <LoadingSpinner v-if="loading" />
    </div>
</template>

<script>
import CustomInput from "../../Shared/components/CustomInput.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import LoadingSpinner from "../../LeadProcessing/components/LoadingSpinner.vue";

export default {
    name: "SiloWebsiteSlide",
    components: {
        LoadingSpinner,
        Dropdown,
        CustomInput
    },
    mixins: [],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        slideId: {
            type: String,
            default: null,
        },
        siloData: {
            type: Object,
            default: {},
        },
        referenceData: {
            type: Object,
            default: {}
        },
        siloApiService: {
            type: Object,
            default: {},
        },
    },
    emits: ['slideError', 'slideProgress', 'siloDataUpdate', 'storedDataUpdate'],
    mounted() {
        this.websiteOptions = (this.referenceData.websites ?? []).map(website => ({ name: website.name, id: website.id }));
        this.flowOptions = Object.entries(this.referenceData.flowRevisionMetadata ?? {}).map(([flowId, flowData]) => {
            return { id: flowId, name: flowData.name };
        });
        if (this.siloData[this.slideId]) {
            this.websiteDetails.selectedWebsite = this.siloData[this.slideId].selectedWebsite;
            this.websiteDetails.selectedFlowId = this.siloData[this.slideId].selectedFlowId;
            this.websiteDetails.selectedRevisionId = this.siloData[this.slideId].selectedRevisionId;
        }
    },
    data () {
        return {
            websiteOptions: [],
            flowOptions: [],
            revisionOptions: [{ id: null, name: 'Automatic'} ],

            websiteDetails: {
                selectedWebsite: null,
                selectedFlowId: null,
                selectedRevisionId: null,
            },
            loading: false,
        }
    },
    methods: {
        async fetchStatamicData() {
            if (this.loading) return;
            this.loading = true;

            const targetWebsite = this.referenceData.websites.find(website => website.id === this.websiteDetails.selectedWebsite);
            const cleanUrl = this.cleanBaseUrl(targetWebsite.cp_domain || targetWebsite.url);

            const resp = await this.siloApiService.getCollectionsData(cleanUrl).catch(err => {
                console.error(err);
            });
            this.loading = false;

            if (resp?.data?.status) {
                this.$emit('storedDataUpdate', this.slideId, [], { collections: resp?.data?.collections });
                this.$emit('slideError', `Successfully retrieved Collection map from ${cleanUrl}.`, 'success');
            }
            else {
                this.$emit('slideError', 'The selected Website did not respond with valid Collection data. You can continue, and enter the relevant data manually.', 'warning');
            }
        },
        cleanBaseUrl(websiteUrl) {
            return websiteUrl.trim().replace(/^https?:+\/+/, '')
                .replace(/\/$/, '');
        },
        updateRevisionOptions() {
            const flowRevisionOptions = this.referenceData.flowRevisionMetadata?.[this.websiteDetails.selectedFlowId]?.revisions.map(revision => {
                return { id: revision.id, name: revision.name }
            });

            this.revisionOptions = [
                { id: null, name: 'Automatic' },
                ...flowRevisionOptions
            ];
        },
        validate() {
            const errors = [];
            if (this.loading) return;

            if (!this.websiteDetails.selectedWebsite) {
                errors.push('Please select a Website for this Silo');
            }

            if (errors.length) {
                this.$emit('slideError', errors.join("\n"));
                return false;
            }
            else {
                this.$emit('siloDataUpdate', this.slideId, this.websiteDetails);
                return true;
            }
        },
        requestNextSlide() {
            this.$emit('slideProgress');
        }
    },
}

</script>
