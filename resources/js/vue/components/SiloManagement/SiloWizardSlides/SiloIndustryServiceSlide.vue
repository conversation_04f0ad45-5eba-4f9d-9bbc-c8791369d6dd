<template>
    <div class="flex flex-col p-6 my-auto border rounded-sm h-full overflow-auto"
         :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']"
    >
        <div class="text-center">
            <h6 class="text-lg font-bold">Directory Industry</h6>
        </div>
        <hr class="my-6 w-5/6 mx-auto opacity-80"
            :class="[darkMode ? 'border-dark-border' : 'border-light-border']"
        />
        <div class="mx-auto flex justify-center items-center max-w-screen-sm">
            <div class="grid gap-y-6 h-full items-center pb-8">
                <div>
                    <div :class="[darkMode ? 'text-slate-100' : 'text-slate-900']" class="block mb-1 font-semibold">
                        Please select an Industry
                    </div>
                    <div class="w-64">
                        <Dropdown
                            v-model="industryDetails.selectedIndustry"
                            :dark-mode="darkMode"
                            placeholder="Select Industry"
                            :options="industryOptions"
                            :selected="industryDetails.selectedIndustry"
                        />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import CustomInput from "../../Shared/components/CustomInput.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";

export default {
    name: "SiloIndustryServiceSlide",
    components: {
        Dropdown,
        CustomInput
    },
    mixins: [],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        slideId: {
            type: String,
            default: null,
        },
        siloData: {
            type: Object,
            default: {},
        },
        referenceData: {
            type: Object,
            default: {}
        },
    },
    emits: ['slideError', 'slideProgress', 'siloDataUpdate'],
    mounted() {
        this.industryOptions = (this.referenceData.industries ?? []).map(industry => ({ name: industry.name, id: industry.id }));
        if (this.siloData[this.slideId]) {
            this.industryDetails.selectedIndustry = this.siloData[this.slideId].selectedIndustry;
        }
    },
    data () {
        return {
            industryOptions: [],
            industryDetails: {
                selectedIndustry: null,
                selectedService: null, // Not yet implemented
            },
        }
    },
    methods: {
        validate() {
            const errors = [];

            if (!this.industryDetails.selectedIndustry) {
                errors.push('Please select an Industry for this Silo');
            }

            if (errors.length) {
                this.$emit('slideError', errors.join("\n"));
                return false;
            }
            else {
                this.$emit('siloDataUpdate', this.slideId, this.industryDetails);
                return true;
            }
        },
        requestNextSlide() {
            this.$emit('slideProgress');
        }
    },
}

</script>
