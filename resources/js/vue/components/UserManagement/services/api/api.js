import axios from 'axios';
import {BaseApiService} from "./base";

class ApiService extends BaseApiService {
    constructor(baseUrl, baseEndpoint, version) {
        super("ApiService");

        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    getAvailableNumbers() {
        return this.axios().get('/management/available-numbers');
    }

    getUsers() {
        return this.axios().get('/');
    }
    deleteUser(userId) {
        return this.axios().delete(`/management/users/${userId}`);
    }

    createUser(payload) {
        return this.axios().post(`/management/users`, payload);
    }
    updateUser(id, payload) {
        return this.axios().put(`/management/users/${id}`, payload);
    }
    getAllUsers(filter, showDeactivated) {
        return this.axios().get('/all', {
            params: {
                ...filter,
                show_deactivated: showDeactivated
            }
        });
    }
}

export { ApiService };
