<template>
    <div class="relative">
        <NestedCheckboxSelectNavigation
            :navigation-data="navigationSetup"
            :current-nested-index="currentNestedIndex"
            @nav:click="navigateToLevel"
            :dark-mode="darkMode"
        />

        <div class="absolute mt-[12rem] flex justify-center mx-auto w-full z-10">
            <LoadingSpinner v-if="loading || !storesInitialized"
                :dark-mode="darkMode"
            />
        </div>

        <div class="max-h-[55vh] overflow-auto relative border rounded-md" :class="[darkMode ? 'border-dark-border' : 'border-light-border']">
            <div :class="loading && 'grayscale-[50%] opacity-50'">
                <!--  STATE  -->
                <NestedCheckboxSelectChild
                    v-if="currentNestedIndex === 0"
                    :child-table-map="stateTableSetup"
                    :child-table-data="stateData"
                    @toggle-all="toggleAllZipsByState"
                    @select-item="handleSelectState"
                    :dark-mode="darkMode"
                />

                <!--  COUNTY  -->
                <NestedCheckboxSelectChild
                    v-else-if="currentNestedIndex === 1"
                    :child-table-map="(forceAllowZipCodes || campaignStore?.zipCodeCampaign || campaignStore?.unrestrictedZipCodes) ? zipCodeEnabledCountyTableSetup : standardCountyTableSetup"
                    :child-table-data="countyData"
                    @toggle-all="toggleAllZipsByCounty"
                    @select-item="handleSelectCounty"
                    @toggle-checkbox="toggleAllZipsByCounty"
                    :dark-mode="darkMode"
                />

                <!--  ZIP CODE  -->
                <NestedCheckboxSelectChild
                    v-else-if="(forceAllowZipCodes || campaignStore?.zipCodeCampaign || campaignStore?.unrestrictedZipCodes) && currentNestedIndex === 2"
                    :child-table-map="zipCodeTableSetup"
                    :child-table-data="zipCodeData"
                    @toggle-checkbox="handleSelectZipCode"
                    :dark-mode="darkMode"
                />
            </div>
        </div>
    </div>
</template>

<script>
import { toRaw } from "vue";
import NestedCheckboxSelectNavigation from "../../../Shared/SlideWizard/components/NestedCheckboxSelectNavigation.vue";
import NestedCheckboxSelectChild from "../../../Shared/SlideWizard/components/NestedCheckboxSelectChild.vue";
import LoadingSpinner from "../../../Shared/components/LoadingSpinner.vue";
import { useLocalityDataStore } from "../stores/locality-data.js";
import { useFutureCampaignStore } from "../stores/future-campaigns.js";

const NestedLevel = {
    State: 'state',
    County: 'county',
    ZipCode: 'zipCode',
}
const nestedLevelOrder = [NestedLevel.State, NestedLevel.County, NestedLevel.ZipCode];

export default {
    name: 'LocationCheckboxSelect',
    components: {
        NestedCheckboxSelectChild,
        NestedCheckboxSelectNavigation,
        LoadingSpinner,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: Object,
            required: true,
        },
        storesInitialized: {
            type: Boolean,
            default: false,
        },
        inputKey: {
            type: String,
            default: '',
        },
        importedZipCodes: {
            type: Object,
            default: () => ({ append: false, zipCodes: [] }),
        },
        forceAllowZipCodes: {
            type: Boolean,
            default: false
        },
        forcedInitialLocations: {
            type: Array,
            default: null,
        },
    },
    emits: ['update:modelValue'],
    data() {
        return {
            nestedLevelOrder,
            campaignStore: useFutureCampaignStore(),
            localityStore: useLocalityDataStore(),
            selectedState: {},
            selectedCounty: {},
            currentNestedLevel: NestedLevel.State,
            servicedZipCodes: {},
            stateData: [],
            countyData: [],
            zipCodeData: [],
            activeZipCodes: {},
            loading: false,
            standardCountyTableSetup: [
                { header: 'County', key: 'county_name' },
                { header: 'Serviced', type: 'checkbox', key: 'serviced', emitKey: 'county_key', show: true },
                { header: 'Total Zip Codes', key: 'total_zip_codes' },
            ],
            zipCodeEnabledCountyTableSetup: [
                { header: 'County', key: 'county_name', emitClick: true, emitKey: 'county_key' },
                { header: 'Serviced Zip Codes', key: 'serviced_zip_codes' },
                { header: 'Total Zip Codes', key: 'total_zip_codes' },
                { header: 'Actions', type: 'selectAll', key: 'county_key', show: true },
            ],
            zipCodeTableSetup: [
                { header: 'Zip Code', key: 'zip_code' },
                { header: 'City', key: 'city_name' },
                { header: 'Serviced', type: 'checkbox', key: 'active', emitKey: 'id', show: true },
            ],
        }
    },
    computed: {
        // Child component setup
        navigationSetup() {
            return [
                { key: NestedLevel.State, name: 'All States' },
                { key: NestedLevel.County, name: this.selectedStateName },
                { key: NestedLevel.ZipCode, name: this.selectedCountyName }
            ];
        },
        stateTableSetup() {
            return [
                { header: 'State', key: 'state_name', emitClick: true, emitKey: 'state_key' },
                { header: 'Serviced Counties', key: 'serviced_counties' },
                { header: (this.campaignStore.zipCodeCampaign || this.campaignStore.unrestrictedZipCodes) ? 'Available Counties' : 'Total Counties', key: (this.campaignStore.zipCodeCampaign || this.campaignStore.unrestrictedZipCodes) ? 'available_counties' : 'total_counties' },
                { header: 'Actions', type: 'selectAll', key: 'state_key', show: true },
            ];
        },
        currentNestedIndex() {
            return this.nestedLevelOrder.findIndex(level => level === this.currentNestedLevel);
        },
        selectedStateName() {
            return this.selectedState?.state_name ?? '';
        },
        selectedStateKey() {
            return this.selectedState?.state_key ?? '';
        },
        selectedCountyName() {
            return this.selectedCounty?.county_name ?? '';
        },
        selectedCountyKey() {
            return this.selectedCounty?.county_key ?? '';
        },
        servicedCounties() {
            return Object.values(this.activeZipCodes).reduce((output, zipCode) => {
                output[zipCode.state_key] = output[zipCode.state_key] ?? [];
                if (!output[zipCode.state_key].includes(zipCode.county_key)) output[zipCode.state_key].push(zipCode.county_key);
                return output;
            }, {});
        },
    },
    mounted() {
        this.initialize();
    },
    methods: {
        initialize() {
            const initialData = JSON.parse(JSON.stringify(this.modelValue));
            this.activeZipCodes = Object.keys(initialData)?.length ? initialData : {};
            this.updateActiveZipCodeTotals();
            this.updateStateData();
        },
        setNestedLevel(level) {
            this.currentNestedLevel = level;
        },
        async toggleAllZipsByState(stateKey, addCodes) {
            const countyFilter = (!this.campaignStore.unrestrictedZipCodes && this.campaignStore.zipCodeCampaign) ? (this.campaignStore.zipCodeCountyExceptions[stateKey] ?? []) : null;
            const stateCollection = Object.values(await this.localityStore.getFilteredCountiesInState(stateKey, countyFilter));
            const allZipCodes = stateCollection.reduce((output, county) => {
                return Array.isArray(county) ? [...output, ...county] : output;
            }, []);
            this.updateZipCodes(allZipCodes, addCodes);
        },
        async toggleAllZipsByCounty(countyKey, addCodes) {
            await this.localityStore.getCountyDetails(countyKey, this.selectedStateKey);
            const countyZipCodes = toRaw((this.localityStore.countyDetailsStore)[this.selectedStateKey]?.[countyKey] ?? []);
            this.updateZipCodes(countyZipCodes, addCodes);
        },
        handleSelectZipCode(zipCodeId, addCode) {
            const targetZipCode = this.zipCodeData.find(zipCode => zipCode.id === zipCodeId);
            if (targetZipCode) this.updateZipCodes([targetZipCode], addCode);
        },

        updateZipCodes(zipCodeArray, addCodes) {
            this.loading = true;

            if (addCodes) {
                zipCodeArray.forEach(zipCode => {
                    if (zipCode.zip_code)
                        this.activeZipCodes[zipCode.zip_code] = this.activeZipCodes[zipCode.zip_code] ?? zipCode;
                });
            } else {
                zipCodeArray.forEach(zipCode => {
                    if (this.activeZipCodes[zipCode.zip_code]) delete this.activeZipCodes[zipCode.zip_code];
                });
            }
            this.updateActiveZipCodeTotals();
            this.updateModelValue();
            this.loading = false;
        },
        async handleSelectState(stateKey) {
            const targetState = this.stateData.find(state => state.state_key === stateKey);
            if (targetState) {
                this.selectedState = targetState;
                this.loading = true;

                const countyStoreData = await this.localityStore.getStateDetails(stateKey);
                if (this.updateCountyData(countyStoreData)) {
                    this.setNestedLevel(NestedLevel.County);
                }
                this.loading = false;
            }
        },
        async handleSelectCounty(countyKey) {
            const targetCounty = this.countyData.find(county => county.county_key === countyKey);
            if (targetCounty) {
                this.selectedCounty = targetCounty;
                this.loading = true;

                const zipCodeStoreData = await this.localityStore.getCountyDetails(targetCounty.county_key, this.selectedStateKey);
                if (this.updateZipCodeData(zipCodeStoreData)) {
                    this.setNestedLevel(NestedLevel.ZipCode);
                }
                this.loading = false;
            }
        },
        updateStateData() {
            this.stateData = (this.localityStore.states).reduce((output, state) => {
                if ((!this.campaignStore.zipCodeCampaign || this.campaignStore.unrestrictedZipCodes) || Object.keys(this.campaignStore.zipCodeCountyExceptions ?? {}).includes(state.state_key)) {
                    output.push({
                        ...state,
                        serviced_counties: this.servicedCounties[state.state_key]?.length ?? 0,
                        available_counties: (this.campaignStore.unrestrictedZipCodes || !this.campaignStore.zipCodeCampaign)
                            ? state.total_counties
                            : (this.campaignStore.zipCodeCountyExceptions[state.state_key] ?? []).length,
                    });
                }
                return output;
            }, []);
        },
        updateCountyData(countyStoreData) {
            if (!countyStoreData?.length) return false;

            this.countyData = (countyStoreData).reduce((output, county) => {
                if ((!this.campaignStore.zipCodeCampaign || this.campaignStore.unrestrictedZipCodes) || (this.campaignStore.zipCodeCountyExceptions[this.selectedStateKey] ?? []).includes(county.county_key)) {
                    output.push({
                        ...county,
                        serviced_zip_codes: this.servicedZipCodes[this.selectedStateKey]?.[county.county_key]?.total ?? 0,
                        serviced: this.servicedCounties[this.selectedStateKey]?.includes(county.county_key) ?? false,
                    });
                }
                return output;
            }, []);

            return true;
        },
        updateZipCodeData(zipCodeStoreData) {
            if (!zipCodeStoreData?.length) return false;

            const servicedCodes = this.servicedZipCodes[this.selectedStateKey]?.[this.selectedCountyKey]?.ids ?? [];
            this.zipCodeData = (zipCodeStoreData).map(zipCode => {
                return { ...zipCode, active: servicedCodes.includes(zipCode.id) }
            });

            return true;
        },
        updateActiveZipCodeTotals() {
            for (const stateKey in this.servicedZipCodes) this.servicedZipCodes[stateKey] = { total: 0 };
            Object.values(this.activeZipCodes).forEach((zipCode) => {
                this.servicedZipCodes[zipCode.state_key] = this.servicedZipCodes[zipCode.state_key] ?? { total: 0 };
                this.servicedZipCodes[zipCode.state_key][zipCode.county_key] = this.servicedZipCodes[zipCode.state_key][zipCode.county_key] ?? {
                    total: 0,
                    ids: []
                };
                this.servicedZipCodes[zipCode.state_key].total++;
                this.servicedZipCodes[zipCode.state_key][zipCode.county_key].total++;
                this.servicedZipCodes[zipCode.state_key][zipCode.county_key].ids.push(zipCode.id);
            });

            this.updateZipCodeData(this.zipCodeData);
            this.updateCountyData(this.countyData);
            this.updateActiveCountyTotals();
        },
        updateActiveCountyTotals() {
            this.stateData.forEach(state => {
                state.serviced_counties = this.servicedCounties[state.state_key]?.length ?? 0;
            });
        },
        navigateToLevel(nestedLevel) {
            if (nestedLevel < 2)
                this.selectedCounty = null;
            if (nestedLevel < 1)
                this.selectedState = null;
            this.setNestedLevel(nestedLevelOrder[nestedLevel]);
        },
        updateModelValue() {
            const clone = JSON.parse(JSON.stringify({ ...this.activeZipCodes }));
            this.$emit('update:modelValue', clone, this.inputKey);
        },
        importZipCodes(zipCodes, appendCodes) {
            if (!appendCodes)
                this.activeZipCodes = {};

            this.updateZipCodes(zipCodes, true);
        },
    },
    watch: {
        importedZipCodes: {
            deep: true,
            handler(updatedZips) {
                if (updatedZips?.zipCodes?.length) {
                    this.importZipCodes(updatedZips.zipCodes, updatedZips.append);
                }
            },
        },
        forcedInitialLocations(initialLocations){
            this.activeZipCodes = {}
            this.updateZipCodes(JSON.parse(JSON.stringify(initialLocations)), true)
        }
    },
}
</script>
