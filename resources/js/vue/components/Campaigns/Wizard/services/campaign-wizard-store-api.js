import axios from 'axios';

/**
 * This currently re-creates Dashboard API calls to enable create/update operations for the Campaign Wizard
 * this may be unnecessary overhead in loading data that A2 already has access to, and could be streamlined in future
 * by using A2-wide Stores for applicable reference data
 */
export class CampaignWizardStoreApi {
    companyId = null;

    constructor() {
        this.baseUrl = 'internal-api/v1';
        this.baseEndpoint = 'campaign-wizard';
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    setCompanyId(companyId) {
        this.companyId = companyId;
    }

    getCampaignList(paginationPage = null, resultsPerPage = null, filters = {}, initialLoad = false) {
        const initialLoadParams = initialLoad
            ? { all_campaigns_list: initialLoad ? 1 : 0 }
            : {};

        return this.axios().get(`/${this.companyId}/campaign/list`, {
            params: {
                perPage: resultsPerPage,
                page: paginationPage,
                ...filters,
                ...initialLoadParams,
            }
        });
    }

    getCampaignPage(paginationLink, filterOptions) {
        return this.axios().get(paginationLink, {
            params: filterOptions
        });
    }

    updateCampaign(campaignPayload) {
        return this.axios().patch(`/${this.companyId}/campaign/update`, campaignPayload);
    }

    saveNewCampaign(campaignPayload) {
        return this.axios().post(`/${this.companyId}/campaign/new`, campaignPayload);
    }

    pauseCampaign(payload) {
        return this.axios().patch(`/${this.companyId}/campaign/pause`, payload);
    }
    unpauseCampaign(payload) {
        return this.axios().patch(`/${this.companyId}/campaign/unpause`, payload);
    }

    toggleCampaignBiddingStatus(campaignReference) {
        return this.axios().patch(`/${this.companyId}/campaign/${campaignReference}/bidding-status/toggle`);
    }

    toggleAdAutomationStatus(campaignReference, payload= {}) {
        return this.axios().patch(`/${this.companyId}/campaign/${campaignReference}/ad-automation-status/toggle`, payload);
    }

    deleteCampaign(campaignReference) {
        return this.axios().delete(`/${this.companyId}/campaign/delete`, {
            params: { reference: campaignReference }
        });
    }

    editCampaign(campaignReference) {
        return this.axios().get(`/${this.companyId}/campaign/${campaignReference}`);
    }

    getIndustryServiceProductOptions(companyId) {
        return this.axios().get('/industry-service-products', {
            params: { company_id: companyId }
        });
    }

    getNewWizardConfiguration(productKey, industryKey, serviceKey, customBudgetKey) {
        return this.axios().get('/new-wizard-configuration', {
            params: { productKey, industryKey, serviceKey, customBudgetKey }
        });
    }

    // LOCALITY DATA STORE - to be replaced with A2 Locations when ready
    getCountries() {
        return this.axios().get(`/locality-store/countries`);
    }
    getStates() {
        return this.axios().get(`/locality-store/states`);
    }
    getStateDetails(stateKey) {
        return this.axios().get(`/locality-store/state/${stateKey}`);
    }
    getCountyDetails(countyKey, stateKey) {
        return this.axios().get(`/locality-store/state/${stateKey}/county/${countyKey}`);
    }
    getStateZipCodes(stateKey) {
        return this.axios().get(`/locality-store/state-zip-codes/${stateKey}`);
    }
    getZipCodesByRadius(zipCode, radius) {
        return this.axios().get(`/locality-store/zip-codes-radius/${zipCode}/${radius}`);
    }
    getZipCodesByZipCodeStrings(zipCodeArray) {
        return this.axios().post(`/locality-store/zip-codes-by-string/`, {
            zip_codes: zipCodeArray,
        });
    }
    downloadZipCodes(campaignReference) {
        return this.axios().get(`/${this.companyId}/campaign/zip-codes`, {
            params: { reference: campaignReference }
        });
    }
    validateTargetedZipCodes(zipCodeArray) {
        return this.axios().post(`/${this.companyId}/validate-targeted-zip-codes`, {
            zip_codes: zipCodeArray,
        });
    }

    // SCHEDULES
    getTimezones() {
        const baseAxios = axios.create({
            baseURL: `/${this.baseUrl}`
        });

        return baseAxios.get('/reference/timezones');
    }

    getAllCompanySchedules() {
        return this.axios().get(`${this.companyId}/schedules/all`);
    }

    createInternalCalendar(schedulePayload) {
        return this.axios().post(`${this.companyId}/schedules/create-static-calendar`, schedulePayload);
    }

    updateSchedule(schedulePayload) {
        return this.axios().patch(`${this.companyId}/schedules/update-schedule`, schedulePayload);
    }

    deleteSchedule(scheduleId) {
        return this.axios().delete(`${this.companyId}/schedules/delete-schedule`, {
            params: {
                id: scheduleId
            },
        });
    }

    getCalendarEventsByDateRange(calendarId, startDate, endDate) {
        return this.axios().get(`${this.companyId}/schedules/events`, {
            params: {
                id: calendarId,
                start_date: startDate,
                end_date: endDate
            }
        });
    }

    // PRICING STORE
    getPriceRangeForZipCodes(zipCodeIds, productKey, industryKey, serviceKey, propertyTypes) {
        return this.axios().post(`/prices/zip-code-price-range`, {
            zip_codes: zipCodeIds,
            product: productKey,
            industry: industryKey,
            service: serviceKey,
            property_types: propertyTypes,
        });
    }

    // PRODUCT CONFIGURATION STORE
    getProductConfigurations(companyId) {
        return this.axios().get(`${companyId}/products/configurations`);
    }

    // BIDDING STORE
    getStateFloorPrices(stateLocationKey, productKey, serviceKey, campaignType) {
        return this.axios().get(`/${this.companyId}/bidding/state-floor/${productKey}/${serviceKey}`, {
            params: {
                state_location_key: stateLocationKey,
                type: campaignType,
            }
        });
    };

    getCountyFloorPrices(stateLocationKey, countyLocationKey, productKey, serviceKey, campaignType) {
        return this.axios().get(`/${this.companyId}/bidding/county-floor/${productKey}/${serviceKey}`, {
            params: {
                state_location_key: stateLocationKey,
                county_location_key: countyLocationKey,
                type: campaignType,
            }
        });
    };
    getStateBidPrices(stateLocationKey, campaignReference) {
        return this.axios().get(`/${this.companyId}/bidding/${campaignReference}/state`, {
            params: { state_abbr: stateLocationKey }
        });
    }

    getCountyBidPrices(stateLocationKey, countyLocationKey, campaignReference, propertyType) {
        return this.axios().get(`/${this.companyId}/bidding/${campaignReference}/county`, {
            params: { state_abbr: stateLocationKey, county_key: countyLocationKey, property_type_name: propertyType }
        });
    }

    getCustomStateFloorPrices(stateLocationKey, campaignReference) {
        return this.axios().get(`/${this.companyId}/bidding/${campaignReference}/custom-state-floor`, {
            params: { state_abbr: stateLocationKey }
        });
    }
    getCustomCountyFloorPrices(stateLocationKey, countyLocationKey, campaignReference) {
        return this.axios().get(`/${this.companyId}/bidding/${campaignReference}/custom-county-floor`, {
            params: { state_abbr: stateLocationKey, county_key: countyLocationKey }
        });
    }
    saveCustomFloorPrices(campaignReference, usesCustomFloorPrices, pricePayload, bulkApplyToIndustry = false, bidModificationPolicy = null) {
        return this.axios().put(`/${this.companyId}/bidding/${campaignReference}/save-custom-floor`, {
            uses_custom_floor_prices: usesCustomFloorPrices,
            prices: pricePayload,
            bulk_apply_to_industry: bulkApplyToIndustry,
            lowered_floor_price_policy: bidModificationPolicy,
        });
    }

    getStatisticsByLocation(params) {
        return this.axios().get('/location-statistics', {
            params
        });
    }

    getCustomCampaignFloorPrices(campaignReference) {
        return this.axios().get(`/${this.companyId}/campaign/${campaignReference}/custom-floor-prices`);
    }

    // DELIVERIES/COMPANY USERS
    getCompanyUsers() {
        return this.axios().get(`/${this.companyId}/delivery/company-users`);
    }
    updateCompanyUser(companyId, companyUserPayload) {
        return this.axios().patch(`/${this.companyId}/delivery/company-users/update`, companyUserPayload);
    }
    createCompanyUser(companyId, companyUserPayload) {
        return this.axios().post(`/${this.companyId}/delivery/company-users/create`, companyUserPayload);
    }
    deleteCompanyUser(companyId, userId) {
        return this.axios().delete(`/${this.companyId}/delivery/company-users/delete`, {
            params: {
                company_user_id: userId
            }
        });
    }

    // CRMs
    getCrmConfigurations() {
        return this.axios().get(`/${this.companyId}/crm-configurations`);
    }

    executeCrmInteractable(crmId, methodName, delivererId, crmPayload, isTemplate = false) {
        return this.axios().post(`/${this.companyId}/crm/execute-method`, {
            crm_deliverer_id: delivererId ?? null,
            crm_type_id: crmId,
            method_name: methodName,
            payload: crmPayload,
            is_template: isTemplate,
        });
    }

    getCrmImportOptions(campaignReference) {
        return this.axios().get(`/${this.companyId}/crm/import-options`, {
            params: { reference: campaignReference }
        });
    }

    testCrmIntegration(crmId, data) {
        return this.axios().post(`/${this.companyId}/delivery-modules/crm-modules/${crmId}/integrations/test`, data);
    }

    saveCompanyCrmTemplate(payload) {
        return this.axios().put(`${this.companyId}/crm/save-template`, payload);
    }

    deleteCompanyCrmTemplate(templateId) {
        return this.axios().delete(`${this.companyId}/crm/delete-template`, {
            params: { template_id: templateId }
        });
    }

    getFieldReplacerReference() {
        return this.axios().get('/replacer-field-documentation');
    }

    getCustomPricingBidModificationOptions() {
        return this.axios().get('/custom-pricing-bid-modification-options');
    }

    getCampaignFilterOptions(campaignReference) {
        return this.axios().get(`campaigns/${campaignReference}/filter-options`);
    }

    getCampaignFilters(campaignReference) {
        return this.axios().get(`campaigns/${campaignReference}/filters`);
    }

    createCampaignFilter(campaignReference, payload) {
        return this.axios().post(`campaigns/${campaignReference}/filters`, payload);
    }

    updateCampaignFilter(campaignReference, filterId, payload) {
        return this.axios().patch(`campaigns/${campaignReference}/filters/${filterId}`, payload);
    }

    deleteCampaignFilter(campaignReference, filterId) {
        return this.axios().delete(`campaigns/${campaignReference}/filters/${filterId}`);
    }
}
