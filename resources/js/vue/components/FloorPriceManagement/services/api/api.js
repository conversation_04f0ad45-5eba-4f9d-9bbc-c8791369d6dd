import axios from 'axios';
import {BaseApiService} from "./base";

class ApiService extends BaseApiService {
    constructor(baseUrl, baseEndpoint, version) {
        super("ApiService");

        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    getData(product, industryId, industryServiceId, propertyType, qualityTier, stateSelected) {
        const payload = {
            product: product,
            industry_id: industryId,
            industry_service_id: industryServiceId,
            quality_tier: qualityTier,
            property_type: propertyType,
        };
        if(stateSelected){payload.state = stateSelected}
        return this.axios().get('/', {
            params: payload
        });
    }

    updateFloorPricing(floorPricingData) {
        return this.axios().patch('/update-floor-pricing', floorPricingData);
    }

    initialiseFloorPricing(industryServiceId, productName, qualityTier, propertyType) {
        return this.axios().post('/initialise-floor-pricing', {
            product: productName,
            industry_service_id: industryServiceId,
            quality_tier: qualityTier,
            property_type: propertyType,
        });
    }

    importFloorPricing(fromIndustryServiceId, toIndustryServiceId, productName) {
        return this.axios().post('/import-floor-pricing', {
            product: productName,
            from_industry_service_id: fromIndustryServiceId,
            to_industry_service_id: toIndustryServiceId,
        })
    }

    repairFloorPricing(industryServiceId, productName) {
        return this.axios().patch('/repair-floor-pricing', {
            product: productName,
            industry_service_id: industryServiceId,
        });
    }

    getExportableIndustryServices(productName) {
        return this.axios().get('/exportable-industry-services', {
            params: {
                product: productName,
            }
        });
    }

    getDefaultPricing() {
        return this.axios().get('/default-pricing');
    }

    saveDefaultPricing(pricingTable) {
        return this.axios().put('/save-default-pricing', {
            default_pricing: pricingTable,
        });
    }

    getPriceHistory(product, industryId, industryServiceId, propertyType, qualityTier, stateSelected) {
        return this.axios().get('/history', {
            params: {
                product: product,
                industry_id: industryId,
                industry_service_id: industryServiceId,
                quality_tier: qualityTier,
                property_type: propertyType,
                state: stateSelected
            }
        });
    }

    getIndustryServicesByProduct() {
        return this.axios().get('industries/by-product')
    }
}

export { ApiService };
