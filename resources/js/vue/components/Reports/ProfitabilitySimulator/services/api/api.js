import axios from 'axios';

export default class ApiService {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    static make() {
        return new ApiService('internal-api', 'profitability-simulator', 1);
    }

    getProfitability(params) {
        return this.axios().post('/', params);
    }

    getCampaignData(campaignId) {
        return this.axios().get('/campaign-data/' + campaignId);
    }
}
