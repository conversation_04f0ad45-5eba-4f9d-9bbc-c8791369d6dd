<template>
    <div class="flex flex-wrap justify-between items-end w-full">
        <div class="mt-6 inline-flex gap-3 mr-10">
            <div class="z-5">
                <p class="mb-1 font-medium text-sm">Industry</p>
                <Dropdown style="min-width: 110px" :dark-mode="darkMode" :options="industries" v-model="industry"/>
            </div>
            <div class="flex items-end z-40">
                <Filterable
                    class="z-40"
                    :dark-mode="darkMode"
                    :filters="filters"
                    :custom-categories="customCategories"
                    v-model="filterInputs"
                    @update:defaults="updateFilterDefaults"
                    @update:filterOptions="getFilterOptionUpdates"
                    @update:customValue="handleCustomUpdate"
                    @custom:delete-option="openDeletePresetModal"
                />
            </div>
            <div class="flex items-end">
                <div class="relative">
                    <div v-if="columnsPopup" @click="toggleColumnsPopup" class="fixed inset-0 z-0"></div>
                    <div class="relative z-4">
                        <CustomButton @click="toggleColumnsPopup" :dark-mode="darkMode" color="slate-inverse">
                            <svg class="fill-current mr-2" width="17" height="16" viewBox="0 0 17 16" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                      d="M2.66667 2C2.55481 2 2.40141 2.05193 2.25638 2.22597C2.10691 2.40533 2 2.6818 2 3V13C2 13.3182 2.10691 13.5947 2.25638 13.774C2.40141 13.9481 2.55481 14 2.66667 14H4.33333C4.44519 14 4.59859 13.9481 4.74362 13.774C4.89309 13.5947 5 13.3182 5 13V3C5 2.6818 4.89309 2.40533 4.74362 2.22597C4.59859 2.05193 4.44519 2 4.33333 2H2.66667ZM6 0.654839C5.54679 0.248166 4.96618 0 4.33333 0H2.66667C1.89447 0 1.20002 0.369497 0.719934 0.945602C0.244279 1.51639 0 2.25734 0 3V13C0 13.7427 0.24428 14.4836 0.719934 15.0544C1.20002 15.6305 1.89446 16 2.66667 16H4.33333C4.96618 16 5.54679 15.7518 6 15.3452C6.45321 15.7518 7.03382 16 7.66667 16H9.33333C9.96618 16 10.5468 15.7518 11 15.3452C11.4532 15.7518 12.0338 16 12.6667 16H14.3333C15.1055 16 15.8 15.6305 16.2801 15.0544C16.7557 14.4836 17 13.7427 17 13V3C17 2.25734 16.7557 1.51639 16.2801 0.945602C15.8 0.369497 15.1055 0 14.3333 0H12.6667C12.0338 0 11.4532 0.248166 11 0.654839C10.5468 0.248166 9.96618 0 9.33333 0H7.66667C7.03382 0 6.45321 0.248166 6 0.654839ZM10 3C10 2.6818 9.89309 2.40533 9.74362 2.22597C9.59859 2.05193 9.44519 2 9.33333 2H7.66667C7.55481 2 7.40141 2.05193 7.25638 2.22597C7.10691 2.40533 7 2.6818 7 3V13C7 13.3182 7.10691 13.5947 7.25638 13.774C7.40141 13.9481 7.55481 14 7.66667 14H9.33333C9.44519 14 9.59859 13.9481 9.74362 13.774C9.89309 13.5947 10 13.3182 10 13V3ZM12 13C12 13.3182 12.1069 13.5947 12.2564 13.774C12.4014 13.9481 12.5548 14 12.6667 14H14.3333C14.4452 14 14.5986 13.9481 14.7436 13.774C14.8931 13.5947 15 13.3182 15 13V3C15 2.6818 14.8931 2.40533 14.7436 2.22597C14.5986 2.05193 14.4452 2 14.3333 2H12.6667C12.5548 2 12.4014 2.05193 12.2564 2.22597C12.1069 2.40533 12 2.6818 12 3V13Z"/>
                            </svg>
                            Columns
                        </CustomButton>
                        <div v-if="columnsPopup"
                             class="z-40 absolute rounded-lg border w-64 grid p-3 top-10 left-0 shadow-module text-sm font-medium max-h-72 overflow-y-scroll"
                             :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
                            <div class="mb-1">
                                <CustomButton color="slate" @click="toggleAllColumnsShown(false)" :dark-mode="darkMode"
                                              v-if="allColumnsSelected">
                                    Deselect All
                                </CustomButton>
                                <CustomButton @click="toggleAllColumnsShown(true)" :dark-mode="darkMode" v-else>
                                    Select All
                                </CustomButton>
                            </div>
                            <div v-for="key in columnOrder">
                                <div class="inline-flex items-center mb-2">
                                    <input type="checkbox" @input="updateColumnShown(key)" :checked="selectedColumns[key]"
                                           class="mr-2 checked:bg-primary-500 rounded cursor-pointer focus:outline-none focus:ring-0">
                                    <p class="cursor-pointer" @click="updateColumnShown(key)">{{ columns[key].name }}</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex items-end z-50">
                <CustomButton type="submit" :dark-mode="darkMode"
                               @click="generateReport()">
                Generate
                </CustomButton>
            </div>
        </div>
        <div class="inline-flex items-center gap-3">
            <div class="items-end z-5 mt-3">
                <CustomButton
                    color="primary-outline"
                    @click="exportToCsv"
                    style="min-width: 90px"
                    :disabled="loading"
                    :dark-mode="darkMode">
                    Export to CSV
                </CustomButton>
            </div>
            <div class="items-end z-5 mt-3">
                <CustomButton
                    color="slate-inverse"
                    @click="openSavePresetModal"
                    :dark-mode="darkMode">
                    Save Config
                </CustomButton>
            </div>
            <div class="items-end z-5 mt-3">
                <CustomButton :dark-mode="darkMode" color="slate-inverse" type="reset"
                              @click="resetFilters()">
                    Reset Filters
                </CustomButton>
            </div>
        </div>
    </div>
    <div class="flex flex-wrap justify-between items-end w-full">
        <div class="inline-flex align-center gap-3 mt-3">
            <autocomplete
                search-icon
                :dark-mode="darkMode"
                class="w-full max-w-sm"
                v-model="selectedCompany"
                :model-value="Number(selectedCompany)"
                :options="companies"
                :style="{width: '245px'}"
                :loading="companySearchLoading"
                :placeholder="'Search by Company'"
                :create-user-input-option="false"
                @search="searchCompanies($event)"
                @input="handleAutocompleteCompanyInput"
            ></autocomplete>
            <div v-if="showCampaignFilter">
                <MultiSelect
                    :dark-mode="darkMode"
                    :options="companyCampaigns"
                    :style="{width: '175px'}"
                    :show-search-box="false"
                    text-place-holder="Select Campaign"
                    @input="selectedCampaigns = $event"
                    :selected-ids="selectedCampaigns"
                />
            </div>
            <div v-if="selectedCompany" class="items-end">
                <CustomButton :dark-mode="darkMode" color="slate-inverse"
                              @click="clearCompany()">
                    <svg class="fill-current cursor-pointer" fill="#000000" height="15px" width="15px" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 460.775 460.775" xml:space="preserve"><g stroke-width="0"></g><g stroke-linecap="round" stroke-linejoin="round"></g><g> <path d="M285.08,230.397L456.218,59.27c6.076-6.077,6.076-15.911,0-21.986L423.511,4.565c-2.913-2.911-6.866-4.55-10.992-4.55 c-4.127,0-8.08,1.639-10.993,4.55l-171.138,171.14L59.25,4.565c-2.913-2.911-6.866-4.55-10.993-4.55 c-4.126,0-8.08,1.639-10.992,4.55L4.558,37.284c-6.077,6.075-6.077,15.909,0,21.986l171.138,171.128L4.575,401.505 c-6.074,6.077-6.074,15.911,0,21.986l32.709,32.719c2.911,2.911,6.865,4.55,10.992,4.55c4.127,0,8.08-1.639,10.994-4.55 l171.117-171.12l171.118,171.12c2.913,2.911,6.866,4.55,10.993,4.55c4.128,0,8.081-1.639,10.992-4.55l32.709-32.719 c6.074-6.075,6.074-15.909,0-21.986L285.08,230.397z"></path> </g></svg>
                </CustomButton>
            </div>
        </div>
        <div class="inline-flex gap-3 items-end">
            <div class="items-end">
                <CustomButton :dark-mode="darkMode" color="slate-inverse" type="reset"
                              @click="downloadErplZipCodes()">
                    Download ERPL Zip Codes
                </CustomButton>
            </div>
        </div>
    </div>
    <FilterableActivePills
        class="px-8 mb-6 mt-6"
        v-if="filters.length"
        :filters="filters"
        :active-filters="filterInputs"
        :dark-mode="darkMode"
        @reset-filter="clearFilter"
    />
    <Modal
        v-if="showPresetModal"
        @close="closePresetModal"
        @confirm="confirmPresetModal"
        :dark-mode="darkMode"
        :small="true"
        :confirm-text="deletingPreset ? 'Delete' : 'Save'"
    >
        <template v-slot:header>
            {{ deletingPreset ? 'Delete' : 'Save' }} Filter Preset
        </template>
        <template v-slot:content>
            <LoadingSpinner
                v-if="loading || saving"
            />
            <div v-else>
                <div v-if="!deletingPreset">
                    <div @keyup.enter="confirmPresetModal">
                        <CustomInput
                            label="Filter Preset Name"
                            :dark-mode="darkMode"
                            v-model="presetName"
                            placeholder="Enter a unique name..."
                        />
                    </div>
                </div>
                <div v-else>
                    <p>Are you sure you wish to delete the preset '{{ deletingPreset }}'?</p>
                </div>
                <div class="my-2 text-center text-red-500" v-if="modalError">
                    {{ modalError }}
                </div>
            </div>
        </template>
    </Modal>
</template>

<script>

import CustomInput from "../../../Shared/components/CustomInput.vue";
import Dropdown from "../../../Shared/components/Dropdown.vue";
import CustomCheckbox from "../../../Shared/SlideWizard/components/CustomCheckbox.vue";
import CustomButton from "../../../Shared/components/CustomButton.vue";
import ToggleSwitch from "../../../Shared/components/ToggleSwitch.vue";
import Filterable from "../../../Shared/components/Filterables/Filterable.vue";
import FilterableActivePills from "../../../Shared/components/Filterables/FilterableActivePills.vue";
import LoadingSpinner from "../../../LeadProcessing/components/LoadingSpinner.vue";
import Modal from "../../../LeadProcessing/components/Modal.vue";
import Autocomplete from "../../../Shared/components/Autocomplete.vue";
import MultiSelect from "../../../Shared/components/MultiSelect.vue";
import SharedApiService from "../../../Shared/services/api.js";

export default {
    name: "ReportInputs",
    components: {
        MultiSelect,
        Autocomplete,
        Modal,
        LoadingSpinner,
        FilterableActivePills, Filterable, ToggleSwitch, CustomButton, CustomCheckbox, Dropdown, CustomInput},
    data: function () {
        return {
            industries: [],
            industry: 'solar',
            filters: [],
            filterInputs: {},
            filterDefaults: {},
            columns: {},
            columnOrder: [],
            selectedColumns: {},
            columnsPopup: false,
            presets: [],
            deletingPreset: false,
            showPresetModal: false,
            presetName: "",
            modalError: null,
            loading: false,
            saving: false,
            selectedCompany: '',
            companySearchLoading: false,
            companies: [],
            showCampaignFilter: false,
            companyCampaigns: [],
            selectedCampaigns: [],
            sharedApi: SharedApiService.make(),
        }
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        reportsPage: {
            type: Boolean,
            default: false,
        },
        apiService: null,
    },
    emits: ['error-message', 'update-data', 'update-loading', 'export-to-csv'],
    created() {
        this.getReportOptions();
    },
    computed: {
        customCategories () {
            const options = {}
            this.presets.forEach(preset => Object.assign(options, { [preset.name]: preset.name }));

            return [{
                type: 'custom-preset',
                name: 'User Presets',
                id: 'presets',
                options
            }]
        },
        filteredColumns() {
            return Object.fromEntries(
                Object.entries(this.columns).filter(([key]) => this.selectedColumns[key])
            );
        },
        allColumnsSelected() {
            for (const column of Object.keys(this.columns)) {
                if (!this.selectedColumns[column]) {
                    return false;
                }
            }
            return true;
        }
    },
    methods: {
        getReportOptions() {
            this.apiService.getReportOptions().then(resp => {
                this.industries = resp.data.data.industries;
                this.initColumns(resp.data.data.columns, resp.data.data.column_order);
                this.filters = resp.data.data.filters;
                this.presets = this.sortPresets(resp.data.data.presets ?? []);
                this.generateReport();
            }).catch(e => console.log(e));
        },
        initColumns(columnsArray, columnOrder) {
            this.columns = columnsArray;
            this.columnOrder = columnOrder;
            if (Object.keys(this.selectedColumns).length < 1) {
                for (const column of Object.keys(this.columns)) {
                    this.selectedColumns[column] = this.columns[column].default;
                }
            }
        },
        searchCompanies(query) {
            if (!query) {
                this.selectedCompany = '';
                this.companyCampaigns = [];
                this.showCampaignFilter = false;
                this.companySearchLoading = false;
                return;
            }

            this.companySearchLoading = true;
            this.sharedApi.searchCompanyNamesAndId(query).then(res => {
                if (res.data.data.status === true) {
                    this.companies = res.data.data.companies;
                    this.companySearchLoading = false;

                    if (this.companies.length === 0) {
                        this.companyCampaigns = [];
                        this.showCampaignFilter = false;
                    }else{
                        this.showCampaignFilter = false;
                    }
                }
            }).catch(err => {
                this.$emit('error-message',  'Error retrieving companies. ' + err.message);
                this.companySearchLoading = false;
                console.error(err);
            });
        },
        handleAutocompleteCompanyInput(value) {
            if (!value) {
                this.selectedCompany = '';
                this.companySearchLoading = false;
                this.companyCampaigns = [];
                this.showCampaignFilter = false;
            }
        },
        searchCompanyCampaigns(companyId, query) {
            if (!companyId || companyId === 'undefined' || companyId === 'null') {
                this.companyCampaigns = [];
                this.showCampaignFilter = false;
                this.selectedCompany = null;
                return;
            }

            this.sharedApi.searchCompanyCampaigns(companyId, query).then(res => {
                this.companyCampaigns = res.data.data.results;
                for (let i = 0; i < this.companyCampaigns.length; i++) {
                    this.companyCampaigns[i].name = this.companyCampaigns[i].id + ": " + this.companyCampaigns[i].name;
                }
                this.showCampaignFilter = this.companyCampaigns.length > 0;
            }).catch(err => {
                this.$emit('error-message',  'Error retrieving campaigns. ' + err.message);
                console.error(err);
            });
        },
        clearCompany() {
            this.selectedCompany = '';
            this.companyCampaigns = [];
            this.showCampaignFilter = false;
        },
        downloadErplZipCodes() {
            this.apiService.downloadErplZipCodes(this.industry).then(response => {
                const blob = response.data;

                const link = document.createElement('a');
                link.href = URL.createObjectURL(blob);

                const currentDate = new Date();
                const datePart = currentDate.toISOString().split('T')[0];
                const hours = currentDate.getHours().toString().padStart(2, '0');
                const minutes = currentDate.getMinutes().toString().padStart(2, '0');
                const seconds = currentDate.getSeconds().toString().padStart(2, '0');
                const formattedDateTime = `${datePart}_${hours}${minutes}${seconds}`;

                link.download = 'erpl_zipcodes_'+this.industry+'_'+formattedDateTime+'.csv';

                document.body.appendChild(link);
                link.click();
                document.body.removeChild(link);
            }).catch(err => {
                this.$emit('error-message',  'Error downloading ERPL Zip Codes. ' + err.message);
                console.error(err);
            });
        },
        deleteFilterPreset() {
            if (this.saving || !this.deletingPreset) return;
            this.saving = true;

            this.apiService.deleteUserPreset(this.deletingPreset).then(resp => {
                if (resp.data?.data?.status) {
                    this.presets = resp.data.data.presets;
                }
            }).catch(err => {
                console.error(err);
            }).finally(() => {
                this.saving = false;
                this.deletingPreset = null;
                this.showPresetModal = false;
            });
        },
        updatePresets(payload) {
            if (this.saving) return;
            this.saving = true;

            this.apiService.saveUserPreset(payload).then(resp => {
                if (resp.data?.data?.status) {
                    this.presets = this.sortPresets(resp.data.data.presets);
                }
            }).catch(err => {
                this.$emit('error-message',  err.message);
            }).finally(() => {
                this.saving = false;
                this.closePresetModal();
            });
        },
        sortPresets(presetArray) {
            return presetArray.sort((a,b) => a.name > b.name ? 1 : 0);
        },
        openSavePresetModal() {
            this.deletingPreset = false;
            this.showPresetModal = true;
        },
        openDeletePresetModal(deleteOption) {
            this.deletingPreset = deleteOption;
            this.showPresetModal = true;
        },
        closePresetModal() {
            this.deletingPreset = false;
            this.showPresetModal = false;
            this.presetName = "";
            this.modalError = null;
        },
        confirmPresetModal() {
            if (this.deletingPreset) {
                this.deleteFilterPreset();
            }
            else {
                if (!this.validatePresetName()) return;
                this.saveFilterPreset();
            }
        },
        saveFilterPreset() {
            const payload = {
                name:  this.presetName.trim(),
                value: {
                    industry: this.industry,
                    filters: this.filters,
                    filterInputs: this.filterInputs,
                    selectedColumns: this.selectedColumns,
                    companies: this.companies,
                    selectedCompany: this.selectedCompany,
                    selectedCampaigns: this.selectedCampaigns,
                },
            }
            this.updatePresets(payload);
        },
        validatePresetName() {
            this.modalError = null;
            const errors = [];
            const currentName = this.presetName.trim().toLowerCase();
            const invalidNames = this.presets.map(preset => preset.name.toLowerCase());
            if (invalidNames.includes(currentName)) {
                errors.push('That name is already in use. Please enter a unique name.');
            }

            if (errors.length) {
                this.modalError = errors.join("\n");
                return false;
            }
            else return true;
        },
        async handlePresetChange() {
            if (this.selectedPreset) {
                const targetPreset = this.presets.find(preset => preset.name === this.selectedPreset);
                if (targetPreset) {
                    this.industry = targetPreset.value.industry;
                    for (const filterKey in targetPreset.value.filters) {
                        if (targetPreset.value.filters[filterKey]) {
                            for (let searchFilterKey = 0; searchFilterKey < this.filters.length; searchFilterKey++) {
                                if (this.filters[searchFilterKey]['id'] === targetPreset.value.filters[filterKey]['id']) {
                                    this.filters[searchFilterKey] = JSON.parse(JSON.stringify(targetPreset.value.filters[filterKey]));
                                    break;
                                }
                            }
                        }
                    }
                    this.filterInputs = JSON.parse(JSON.stringify(targetPreset.value.filterInputs));
                    this.getFilterOptionUpdates();
                    this.selectedColumns = targetPreset.value.selectedColumns;
                    this.companies = targetPreset.value.companies ? JSON.parse(JSON.stringify(targetPreset.value.companies)) : [];
                    this.selectedCompany = targetPreset.value.selectedCompany;
                    this.industry = targetPreset.value.industry;

                    setTimeout(() => {
                        this.selectedCampaigns = targetPreset.value.selectedCampaigns ? JSON.parse(JSON.stringify(targetPreset.value.selectedCampaigns)) : [];
                        this.generateReport();
                    }, 100);
                }
            }
        },
        handleCustomUpdate(newVal) {
            if ('presets' in newVal) {
                this.selectedPreset = newVal.presets;
                this.handlePresetChange();
            }
        },
        generateReport() {
            this.setLoading(true);
            const columnSelected = value => value;

            this.apiService.getReport({
                'industry': this.industry,
                'columns': Object.keys(this.selectedColumns).filter(key => columnSelected(this.selectedColumns[key])),
                'filters': this.filterInputs,
                'company': this.selectedCompany && this.selectedCompany !== 'undefined' && this.selectedCompany !== 'null' ? this.selectedCompany : null,
                'campaigns': this.selectedCampaigns,
            }).then(resp => {
                this.$emit('update-data',
                    resp.data.data.report,
                    this.filteredColumns,
                    resp.data.data.columns,
                    resp.data.data.column_order,
                    this.industry);
            }).catch(e => this.$emit('error-message', 'Failed to generate report. '+e)).finally(() => this.setLoading(false));
        },
        setLoading(loading) {
            this.loading = loading;
            this.$emit('update-loading', loading);
        },
        resetFilters() {
            this.selectedColumns = {};
            this.initColumns(this.columns);
            this.filterInputs = {};
        },
        clearFilter (filterId) {
            delete this.filterInputs[filterId]
        },
        updateFilterDefaults (filterChange) {
            Object.assign(this.filterDefaults, { ...filterChange })
        },
        getFilterOptionUpdates() {
            this.apiService.getFilterOptionUpdates({
                filters: this.filterInputs,
            }).then(resp => {
                if (resp.data?.data?.status) {
                    this.updateFilterOptions(resp.data.data.filter_updates ?? null);
                }
            }).catch(err => {
                this.$emit('error-message', err.message);
            });
        },
        updateFilterOptions(updatedFilterOptions) {
            if (updatedFilterOptions) {
                for (const filterKey in updatedFilterOptions) {
                    const targetIndex = this.filters.findIndex(filter => {
                        return filter.id === filterKey;
                    });
                    if (targetIndex >= 0) this.filters[targetIndex] = updatedFilterOptions[filterKey];
                }
            }
        },
        exportToCsv() {
            this.$emit('export-to-csv');
        },
        toggleColumnsPopup() {
            this.columnsPopup = !this.columnsPopup;
        },
        updateColumnShown(colKey) {
            this.selectedColumns[colKey] = !this.selectedColumns[colKey];
        },
        toggleAllColumnsShown(show = false) {
            for (const colKey of Object.keys(this.columns)) {
                this.selectedColumns[colKey] = show;
            }
        },
    },
    watch: {
        selectedCompany: function (newVal, oldVal) {
            if (newVal !== oldVal) {
                this.selectedCampaigns = [];
                this.showCampaignFilter = false;
                if (newVal) {
                    this.searchCompanyCampaigns(newVal);
                    this.filterInputs['leads-report-empty-locations'] = ['exclude'];
                } else {
                    this.companyCampaigns = [];
                    this.showCampaignFilter = false;
                }
            }
        },
    }
}
</script>

<style scoped>

</style>
