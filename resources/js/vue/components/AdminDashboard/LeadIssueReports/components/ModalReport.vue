<template>
    <Modal
        :small="true"
        :dark-mode="darkMode"
        :noButtons="true"
        @close="handleModalClosure"
    >
        <template v-slot:header>
            <h4 class="text-xl font-medium">Report Details</h4>
        </template>
        <template v-slot:content>
            <p>Created at: <strong>{{ modal_data.created_date }}</strong></p>
            <p>Quote Id: <strong>{{ modal_data.quote_id }}</strong></p>
            <p>Send Test Lead: <strong>{{ modal_data.send_test_lead != 0 }}</strong></p>
            <br>
            <p>Error Log</p>
            <div class="border-none bg-red-100 border border-red-200 text-sm text-red-800 rounded-lg p-4" role="alert">
                <span class="font-bold">{{ modal_data.error_log }}</span>
            </div>
            <br>
            <p>Delivery Data</p>
            <div class="border-none bg-gray-50 border border-gray-200 text-sm text-gray-600 rounded-lg p-4"
                 role="alert">
                <span class="font-bold">{{ modal_data.delivery_data }}</span>
            </div>
        </template>
    </Modal>
</template>
<script>
import Modal from "../../../Shared/components/Modal.vue";
export default {
    name: "ModalReport",
    components: {
        Modal
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        modal_data: {
            type: Array,
            default: []
        }
    },
    emits: ['close-report-module'],
    methods: {
        handleModalClosure() {
            this.$emit('close-report-module', true);
        },
    }
}
</script>
