<script setup>
import BaseTable from "../Shared/components/BaseTable.vue";
import ApiService from "../Prospects/api.js";
import {onMounted, ref} from "vue";
import Badge from "../Shared/components/Badge.vue";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import SharedApiServiceFactory from "../Shared/services/api.js";

const apiService = ApiService.make();
const sharedApi = SharedApiServiceFactory.make()
const props = defineProps({
    darkMode: false,
    gridClass: ''
})
const registrations = ref([])
const loading = ref(false);
const loadingIndustries = ref(true)
const industries = ref([])
const salesStatusOptions = ref([])
const loadingSaleStatus = ref(false)

onMounted(() => {
    getMostRecentRegistrations()
    getIndustries()
    getSaleStatusTypes()
})

function getMostRecentRegistrations() {
    loading.value = true;
    apiService.getMostRecentRegistrations()
        .then(res => {
            registrations.value = res.data;
        })
        .finally(() => {
            loading.value = false;
        });
}

function getIndustries() {
    sharedApi.getOdinIndustries().then(res => {
        if(res.data.data.status === true) {
            industries.value = res.data.data.industries.map((i) => {
                return {
                    id: i.id,
                    name: i.name
                };
            });
        }
    }).catch(() => {
        console.log('Could not load industries.');
    }).finally(() => {
        loadingIndustries.value = false
    });
}

function getSaleStatusTypes() {
    loadingSaleStatus.value = true;
    sharedApi.getSaleStatusTypes().then(resp => {
        const statuses = resp.data.data.statuses;
        salesStatusOptions.value = Object.keys(statuses).map((status) => ({id: statuses[status], name: status}));
    }).catch(e => console.error("Get Sales Status Options Error: ", e))
        .finally(() => {
            loadingSaleStatus.value = false;
        });
}
</script>

<template>
    <div class="border rounded-lg" :class="[gridClass, darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
        <div class="p-5">
            <h3 class="font-bold text-sm uppercase text-primary-500">
                Registrations
            </h3>
            <p class="text-slate-500 text-sm">Displays the 25 most recent company registrations.</p>
        </div>
        <div>
            <BaseTable :dark-mode="darkMode" :loading="loading">
                <template #head>
                    <tr>
                        <th>Company Name</th>
                        <th>Industries</th>
                        <th>State</th>
                        <th>Decision Maker</th>
                        <th>Website</th>
                        <th>Sales Status</th>
                        <th>Last Contact Date</th>
                        <th>Last Contact Type</th>
                    </tr>
                </template>
                <template #body>
                    <tr v-for="registration in registrations.companies" :key="registration.id">
                        <td>
                            <a target="_blank" :href="registration.profile_link" class="cursor-pointer inline-flex items-center hover:text-primary-500 font-medium truncate">
                                {{registration.name}}
                                <svg class="w-3.5 ml-2" width="18" height="19" viewBox="0 0 18 19" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M10 0.833344L13.293 4.12634L6.293 11.1263L7.707 12.5403L14.707 5.54034L18 8.83334V0.833344H10Z" fill="#0081FF"/><path d="M16 16.8333H2V2.83334H9L7 0.833344H2C0.897 0.833344 0 1.73034 0 2.83334V16.8333C0 17.9363 0.897 18.8333 2 18.8333H16C17.103 18.8333 18 17.9363 18 16.8333V11.8333L16 9.83334V16.8333Z" fill="#0081FF"/></svg>
                            </a>
                        </td>
                        <td>
                            <span>
                            <loading-spinner size="w-6 h-6" v-if="loadingIndustries"></loading-spinner>
                            <span v-else>
                                <badge v-if="registration.industries.length > 1" class="group relative" :dark-mode="darkMode" color="green" >
                                <svg class="inline mr-1 w-3.5" viewBox="0 0 22 16" fill="none" xmlns="http://www.w3.org/2000/svg"><path fill-rule="evenodd" clip-rule="evenodd" d="M11.3994 6C10.869 6 10.3603 6.21071 9.9852 6.58579C9.61013 6.96086 9.39941 7.46957 9.39941 8C9.39941 8.53043 9.61013 9.03914 9.9852 9.41421C10.3603 9.78929 10.869 10 11.3994 10C11.9298 10 12.4386 9.78929 12.8136 9.41421C13.1887 9.03914 13.3994 8.53043 13.3994 8C13.3994 7.46957 13.1887 6.96086 12.8136 6.58579C12.4386 6.21071 11.9298 6 11.3994 6ZM8.57099 5.17157C9.32113 4.42143 10.3385 4 11.3994 4C12.4603 4 13.4777 4.42143 14.2278 5.17157C14.978 5.92172 15.3994 6.93913 15.3994 8C15.3994 9.06087 14.978 10.0783 14.2278 10.8284C13.4777 11.5786 12.4603 12 11.3994 12C10.3385 12 9.32113 11.5786 8.57099 10.8284C7.82084 10.0783 7.39941 9.06087 7.39941 8C7.39941 6.93913 7.82084 5.92172 8.57099 5.17157Z" fill="currentColor"/><path fill-rule="evenodd" clip-rule="evenodd" d="M2.91143 8C4.14693 11.4964 7.48265 14 11.3994 14C15.3171 14 18.6519 11.4965 19.8874 8C18.6519 4.50354 15.3171 2 11.3994 2C7.48265 2 4.14693 4.50359 2.91143 8ZM0.903357 7.7004C2.3045 3.23851 6.47319 0 11.3994 0C16.3267 0 20.4944 3.23856 21.8955 7.7004C21.9567 7.89544 21.9567 8.10456 21.8955 8.2996C20.4944 12.7614 16.3267 16 11.3994 16C6.47319 16 2.3045 12.7615 0.903357 8.2996C0.84211 8.10456 0.84211 7.89544 0.903357 7.7004Z" fill="currentColor"/></svg>
                                Multi-Industry
                                <span class="absolute z-50 top-0 left-0 group-hover:visible invisible rounded-md px-3 border shadow-md py-2" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
                                    <span class="mr-2 inline" v-for="id in registration.industries" >
                                        <span>{{id.name}}</span>
                                    </span>
                                </span>
                            </badge>
                            <badge v-else :dark-mode="darkMode" color="primary" class="mr-1" v-for="id in registration.industries">
                                {{ id.name }}
                            </badge>
                            </span>
                        </span>
                        </td>
                        <td>{{registration.state}}</td>
                        <td>{{registration.decision_maker}}</td>
                        <td>{{registration.website}}</td>
                        <td>
                            <span>
                                <loading-spinner v-if="loadingSaleStatus" size="w-6 h-6"></loading-spinner>
                                <badge color="primary" :dark-mode="darkMode" v-else>
                                    <span v-for="status in salesStatusOptions">
                                        <span v-if="status.id === registration.sales_status">
                                            {{status.name}}
                                        </span>
                                    </span>
                                </badge>
                        </span>
                        </td>
                        <td>{{registration?.last_contact_date}}</td>
                        <td>{{registration?.last_contact_type}}</td>
                    </tr>
                </template>
            </BaseTable>
        </div>
    </div>
</template>

<style scoped>

</style>
