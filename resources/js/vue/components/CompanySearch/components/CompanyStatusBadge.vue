<template>
    <div class="text-cyan-500 flex flex-col gap-3">
        <tooltip large :dark-mode="darkMode" show-on-hover variant="less_border">
            <template #icon></template>
            <template #title>
                <Badge
                    :color="company.can_receive_leads ? 'green' : 'red'"
                    :dark-mode="darkMode"
                >
                    <simple-icon
                        :icon="company?.can_receive_leads ? simpleIcon.icons.CHECK : simpleIcon.icons.X_MARK"
                        :dark-mode="darkMode"
                    />
                    <div class="flex truncate">
                        {{ company?.status ?? 'Unknown Consolidated Status' }}
                    </div>
                </Badge>
            </template>
            <div class="flex flex-col p-2 gap-2">
                <div class="flex flex-col text-xs">
                    <div class="flex items-center gap-1">
                        <simple-icon :icon="companyStatus.adminStyle[company.admin_status.value]?.icon"/>
                        <div>{{company.admin_status.label}}</div>
                    </div>
                </div>
                <div class="flex flex-col text-xs">
                    <div class="flex items-center gap-1">
                        <simple-icon :icon="companyStatus.systemStyle[company.system_status.value]?.icon"/>
                        <div>{{company.system_status.label}}</div>
                    </div>
                </div>
                <div class="flex flex-col text-xs">
                    <div class="flex items-center gap-1">
                        <simple-icon :icon="companyStatus.campaignStyle[company.campaign_status.value]?.icon"/>
                        <div>{{company.campaign_status.label}}</div>
                    </div>
                </div>
            </div>
        </tooltip>
    </div>
</template>
<script>
import SimpleIcon from "../../Mailbox/components/SimpleIcon.vue";
import Tooltip from "../../Shared/components/Tooltip.vue";
import Badge from "../../Shared/components/Badge.vue";
import useSimpleIcon from "../../../../composables/useSimpleIcon.js";
import useCompanyStatus from "../../../../composables/useCompanyStatus.js";
const simpleIcon = useSimpleIcon();
const companyStatus = useCompanyStatus();

export default {
    name: "CompanyStatusBadge",
    components: {Badge, Tooltip, SimpleIcon},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        company: {
            type: Object,
            default: {},
        }
    },
    data() {
        return {
            simpleIcon,
            companyStatus,
        }
    }
}
</script>
