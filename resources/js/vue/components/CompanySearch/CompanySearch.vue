<template>
    <div>
        <div class="border rounded-lg"
             :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border',
                saving ? 'pointer-events-none opacity-50 greyscale-[50%]' : '']"
        >
            <AlertsContainer
                v-if="alertActive"
                :alert-type="alertType"
                :text="alertText"
                :dark-mode="darkMode"
            />
            <div class="p-5 flex items-center">
                <h5 class="text-blue-550 text-sm uppercase font-semibold leading-tight">Company Search</h5>
            </div>
            <div class="grid grid-rows-2 items-center space-y-2 pb-5 pl-6 flex-1">
                <div class="flex flex-wrap space-x-2">
                    <CustomInput
                        :dark-mode="darkMode"
                        class="w-full max-w-sm"
                        search-icon
                        placeholder="Search by company name or id"
                        v-model="searchInputGeneral"
                        @keyup.enter="submitSearch()"
                    />
                    <CustomInput
                        :dark-mode="darkMode"
                        class="w-full max-w-lg"
                        search-icon
                        placeholder="Search by contact phone number, name, email, or company website"
                        v-model="searchInputContact"
                        @keyup.enter="submitSearch()"
                    />
                    <Filterable
                        :dark-mode="darkMode"
                        :filters="filters"
                        :custom-categories="customCategories"
                        v-model="filterInputs"
                        @update:defaults="updateFilterDefaults"
                        @update:filterOptions="getFilterOptionUpdates"
                        @update:customValue="handleCustomUpdate"
                        @custom:delete-option="openDeletePresetModal"
                    />
                    <CustomButton type="submit" :dark-mode="darkMode"
                                  @click="submitSearch()"
                    >
                        Search
                    </CustomButton>
                    <CustomButton :dark-mode="darkMode" color="slate-inverse" type="reset"
                                  @click="resetFilters"
                    >
                        Reset
                    </CustomButton>
                    <CompanySearchFieldsToggle
                        :dark-mode="darkMode"/>
                </div>
                <div class="flex flex-wrap space-x-2">
                    <CustomButton @click="showCreateTasksModal" :dark-mode="darkMode" :icon="true"
                                  color="slate-inverse"
                                  v-show="!noData"
                    >
                        <template v-slot:icon>
                            <svg class="fill-current" width="15" height="15" viewBox="0 0 18 18" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M7.93297 10.5189L5.70697 8.29289L4.29297 9.70689L8.06697 13.4809L13.769 6.64089L12.231 5.35889L7.93297 10.5189Z"/>
                                <path
                                    d="M16 0H2C0.897 0 0 0.897 0 2V16C0 17.103 0.897 18 2 18H16C17.103 18 18 17.103 18 16V2C18 0.897 17.103 0 16 0ZM2 16V2H16L16.002 16H2Z"/>
                            </svg>
                        </template>
                        Create tasks
                    </CustomButton>

                    <ConfirmCreateTaskModal
                        v-show="confirmCreateTasksModalIsActive"
                        :dark-mode="darkMode"
                        :small="true"
                        @close="confirmCreateTasksModalIsActive = false"
                        close-text="Cancel"
                        :company-data="companyData"
                        @companies-data="getCompaniesConfirmData"/>

                    <create-task-modal
                        :dark-mode="darkMode"
                        z-index-hundred
                        v-model:show="taskModalIsActive"
                        :company-data="companyData"
                        :payload="lastSearchParams"
                    ></create-task-modal>

                    <CustomButton @click="assignCadenceModalIsActive = true" :dark-mode="darkMode" :icon="true"
                                  v-if="companyData.paginationData.total && companyData.paginationData.total > 0"
                                  color="slate-inverse" id="assign-cadence-button">
                        <template v-slot:icon>
                            <svg class="fill-current" width="16" height="16" viewBox="0 0 16 16" fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M8 0C3.5888 0 0 3.5888 0 8C0 12.4112 3.5888 16 8 16C12.4112 16 16 12.4112 16 8C16 3.5888 12.4112 0 8 0ZM8 14.4C4.4712 14.4 1.6 11.5288 1.6 8C1.6 4.4712 4.4712 1.6 8 1.6C11.5288 1.6 14.4 4.4712 14.4 8C14.4 11.5288 11.5288 14.4 8 14.4Z"/>
                                <path d="M8.80001 4H7.20001V8.8H12V7.2H8.80001V4Z"/>
                            </svg>
                        </template>
                        Assign/Terminate Cadence
                    </CustomButton>

                    <CustomButton
                        v-if="companyData.paginationData.total && companyData.paginationData.total > 0"
                        @click="showSetupOpportunityNotifications = true"
                        :dark-mode="darkMode"
                        icon
                        color="slate-inverse"
                    >
                        <template v-slot:icon>
                            <svg
                                class="w-4 h-4"
                                viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg" fill="currentColor"><g id="SVGRepo_bgCarrier" stroke-width="0"></g><g id="SVGRepo_tracerCarrier" stroke-linecap="round" stroke-linejoin="round"></g><g id="SVGRepo_iconCarrier"><path fill="currentColor" d="M384 960v-64h192.064v64H384zm448-544a350.656 350.656 0 0 1-128.32 271.424C665.344 719.04 640 763.***********.504V832H320v-14.336c0-48-19.392-95.36-57.216-124.992a351.552 351.552 0 0 1-128.448-344.256c25.344-136.448 133.888-248.128 269.76-276.48A352.384 352.384 0 0 1 832 416zm-544 32c0-132.288 75.***********-224v-64c-154.432 0-256 122.752-256 288h64z"></path></g></svg>
                        </template>
                        Setup Opportunity Notifications
                    </CustomButton>

                    <AssignCadenceModal
                        v-show="assignCadenceModalIsActive"
                        :dark-mode="darkMode"
                        @toggle-assign-cadence-modal="toggleAssignCadenceModal"
                        :company-data="companyData"
                        :selected-company-ids="useSelectionStore().selection.map(company => company.id)"
                        :current-user-id="userId"
                        :total-company-count="companyData.paginationData.total"
                        :company-search-criteria="lastSearchParams"
                    />

                    <SetupOpportunityNotifications
                        v-show="showSetupOpportunityNotifications"
                        :dark-mode="darkMode"
                        @close="showSetupOpportunityNotifications = false"
                        :selected-preset="selectedPresetData"
                        :total-companies-filtered="paginationData?.meta?.total"
                        :filter-inputs="filterInputs"
                        :presets="presets"
                    />

                    <CustomButton
                        :dark-mode="darkMode"
                        color="slate-inverse"
                        @click="openSavePresetModal"
                        :disabled="filtersUsedCount === 0"
                    >
                        Save Preset
                    </CustomButton>

                    <CustomButton
                        v-if="permissionStore.hasPermission(PERMISSIONS.PERMISSION_COMPANY_EXPORT_DATA)"
                        :dark-mode="darkMode"
                        color="slate-inverse"
                        @click="exportCSV()"
                        :disabled="data?.length < 1"
                    >
                        Export CSV
                    </CustomButton>

	                <button
		                v-if="canCreateCompany"
		                class="transition duration-200 font-semibold bg-primary-500 hover:bg-blue-500 text-white text-sm font-medium focus:outline-none py-2 rounded-md px-5"
		                @click="showAddCompanyModule = true"
	                >
		                + Add New Company
	                </button>

	                <add-company-v2
		                v-if="showAddCompanyModule"
		                :dark-mode="darkMode"
		                @close-add-company-module="showAddCompanyModule = false"
		                :industries="industries"
	                >
	                </add-company-v2>
                </div>
            </div>
            <FilterableActivePills
                class="px-8 mb-6"
                v-if="filters.length"
                :filters="filters"
                :active-filters="filterInputs"
                :dark-mode="darkMode"
                @reset-filter="clearFilter"
            />
            <div class="overflow-x-auto relative">
                <table class="w-full text-sm text-left rtl:text-right text-gray-500 dark:text-gray-400">
                    <TableHeader
                        :data="data"
                        :dark-mode="darkMode"
                        :showComments="showComments"
                        :leads-purchased-filter="filterInputs.hasOwnProperty('company-leads-purchased') ? filterInputs['company-leads-purchased'] : {}"
                        :current-page="paginationData?.meta?.current_page ?? 1"
                    />
                    <TableBody
                       :loading="loading"
                       :data="data"
                       :dark-mode="darkMode"
                       :showComments="showComments"
                       :filter-inputs="filterInputs"
                       :current-page="paginationData?.meta?.current_page ?? 1"
                       @search="submitSearch()"
                    />
                </table>
            </div>
            <div class="p-3 flex items-center justify-end gap-2">
                <div>
                    <span class="text-sm text-slate-500">Results Per Page</span>
                </div>
                <div>
                    <Dropdown :dark-mode="darkMode" placement="top" :options="perPageOptions"
                              v-model="perPageSelection"
                              :selected="10"
                    />
                </div>
                <Pagination :dark-mode="darkMode" :pagination-data="paginationData.meta ?? {}" :show-pagination="true"
                            @change-page="submitSearch($event?.newPage ?? 1)"
                />
            </div>
        </div>
        <Modal
            v-if="showPresetModal"
            @close="closePresetModal"
            @confirm="confirmPresetModal"
            :dark-mode="darkMode"
            :small="true"
            :confirm-text="deletingPreset ? 'Delete' : 'Save'"
            confirm-id="confirm-saving-preset"
        >
            <template v-slot:header>
                {{ deletingPreset ? 'Delete' : 'Save' }} Filter Preset
            </template>
            <template v-slot:content>
                <LoadingSpinner
                    v-if="loading || saving"
                />
                <div v-else>
                    <div v-if="!deletingPreset">
                        <div @keyup.enter="confirmPresetModal">
                            <CustomInput
                                label="Filter Preset Name"
                                :dark-mode="darkMode"
                                v-model="presetName"
                                placeholder="Enter a unique name..."
                            />
                        </div>
                    </div>
                    <div v-else>
                        <p>Are you sure you wish to delete the preset '{{ deletingPreset }}'?</p>
                    </div>
                    <div class="my-2 text-center text-red-500" v-if="modalError">
                        {{ modalError }}
                    </div>
                </div>
            </template>
        </Modal>
        <Modal
            v-if="showExportModal"
            @close="toggleExportModal(false)"
            @confirm="exportCSV(true)"
            :hide-confirm="paginationData.meta?.total > exportLimit"
            :dark-mode="darkMode"
            :small="true"
            close-text="Cancel"
            confirm-text="Continue"
        >
            <template v-slot:header>
                Export CSV
            </template>
            <template v-slot:content>
                <div class="whitespace-pre-line">
                    {{ this.exportWarning }}
                </div>
            </template>
        </Modal>
    </div>
</template>

<script>
import CustomButton from '../Shared/components/CustomButton.vue'
import CustomInput from '../Shared/components/CustomInput.vue'
import Filterable from '../Shared/components/Filterables/Filterable.vue'
import { ApiFactory } from './services/api/factory.js'
import Dropdown from '../Shared/components/Dropdown.vue'
import Pagination from '../Shared/components/Pagination.vue'
import TableHeader from './components/CompanySearchTableHeader.vue'
import TableBody from './components/CompanySearchTableBody.vue'
import LoadingSpinner from '../Shared/components/LoadingSpinner.vue'
import FilterableActivePills from '../Shared/components/Filterables/FilterableActivePills.vue'
import { nextTick } from 'vue'
import Modal from '../Shared/components/Modal.vue'
import AlertsContainer from '../Shared/components/AlertsContainer.vue'
import AlertsMixin from '../../mixins/alerts-mixin.js'
import CompanySearchFieldsToggle from './components/CompanySearchFieldsToggle.vue'
import ConfirmCreateTaskModal from '../Shared/components/ConfirmCreateTaskModal.vue'
import CreateTaskModal from './components/CreateTaskModal.vue'
import { useFieldsToggleStore, usePaginationStore, useSelectionStore, useSortingStore } from './services/service'
import AssignCadenceModal from './components/AssignCadenceModal.vue'
import AddCompanyV2 from '../Shared/modules/AddCompanyV2.vue'
import { downloadCsvString } from "../../../composables/exportToCsv.js";
import { IDS, PERMISSIONS, ROLES, useRolesPermissions } from "../../../stores/roles-permissions.store.js";
import { stripSpecialChars } from "../../../modules/helpers";
import SetupOpportunityNotifications from "./components/SetupOpportunityNotifications.vue";

/**
 * returnAllResults returns full query for CSV downloads, does not update pagination/display
 */
const search = async (here, page, returnAllResults = false) => {
    const sortingColumns = useSortingStore().fields

    const searchParams = {
        search_id: here.searchInputId,
        search_text: stripSpecialChars(here.searchInputGeneral),
        search_contact: here.searchInputContact,
        filters: here.filterInputs,
        page: page,
        per_page: here.perPageSelection,
        sort_array: sortingColumns,
        return_all_results: !!returnAllResults,
    }

    const results = await here.api.search(searchParams).then(resp => {
        if (resp.data?.data?.status) {
            if (returnAllResults) {
                return resp?.data?.data?.companies;
            }

            const paginationData = resp?.data?.data?.companies ?? []
            here.data = paginationData.data
            here.paginationData = paginationData

            useSelectionStore().$patch({
                currentCompaniesForPage: here.data
            })

	        here.lastSearchParams = searchParams
        }
    }).catch(err => {
        here.showAlert('error', err?.response?.data?.message ?? 'An error occurred while searching.')
    }).finally(() => {
        here.loading = false
    });

    if (returnAllResults) return results;
}

export default {
    name: 'CompanySearch',
    components: {
        SetupOpportunityNotifications,
	    AddCompanyV2,
        AssignCadenceModal,
        CreateTaskModal,
        ConfirmCreateTaskModal,
        CompanySearchFieldsToggle,
        AlertsContainer,
        Modal,
        FilterableActivePills,
        LoadingSpinner,
        TableBody,
        TableHeader,
        Pagination, Dropdown, Filterable, CustomInput, CustomButton
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        apiDriver: {
            type: String,
            default: 'api',
        },
        userId: {
            type: [Number, String],
            required: false,
        },
	    industries: {
		    type: Array,
		    required: true,
	    },
	    userPermissions: {
			type: Array,
		    required: true,
	    },
    },
    mixins: [AlertsMixin],
    data () {
        return {
            api: ApiFactory.makeApiService(this.apiDriver),
            filtersPopup: false,
            selectedFilter: null,
            selectedOption: null,
            filters: [],
            filterInputs: {},
            filterDefaults: {},
            searchInputGeneral: '',
            searchInputContact: '',
            searchInputId: '',

            loading: false,
            saving: false,
            data: [],
            paginationData: {},
            perPageOptions: [{ id: 10, name: '10' }, { id: 20, name: '20' }, { id: 50, name: '50' }, {
                id: 100,
                name: '100'
            }],
            perPageSelection: 10,

            presets: [],
            selectedPreset: null,
            presetName: '',
            showPresetModal: false,
            updatingPreset: false,
            deletingPreset: null,
            modalError: null,

            showSetupOpportunityNotifications: false,
            selectedPresetData: null,

            showComments: false, //TODO: Comments are still legacy, disabled for now

            confirmCreateTasksModalIsActive: false,
            taskModalIsActive: false,
            allCompaniesTaskCreation: false,

            assignCadenceModalIsActive: false,

	        showAddCompanyModule: false,
            lastSearchParams: [],

            showExportModal: false,
            exportWarning: '',
            exportWarningLimit: 250,
            exportLimit: 750,

            permissionStore: useRolesPermissions(),
            PERMISSIONS: PERMISSIONS,
        }
    },
    computed: {
        canCreateCompany () {
		    return this.userPermissions.filter(x => x?.name === 'companies/create').length > 0
	    },
        filtersUsedCount () {
            return Object.values(this.filterInputs).filter(x => x).length
        },
        noData () {
            return this.data.length === 0
        },
        companyData () {
            return {
                all: this.allCompaniesTaskCreation,
                paginationData: {
                    total: this.paginationData?.meta?.total ?? 0,
                },
                companySelected: {
                    companies: useSelectionStore().selection,
                },
                data: {
                    companies: useSelectionStore().selection,
                },
            }
        },
        customCategories () {
            const options = {}
            this.presets.forEach(preset => Object.assign(options, { [preset.name]: preset.name }))

            return [{
                type: 'custom-preset',
                name: 'User Presets',
                id: 'presets',
                options
            }]
        }
    },
    mounted () {
        this.getFilterOptions(true)
    },
    methods: {
        getCompaniesConfirmData (data) {
            this.confirmCreateTasksModalIsActive = false
            this.taskModalIsActive = true
            this.allCompaniesTaskCreation = data?.all
        },
        useSelectionStore,
        showCreateTasksModal () {
            this.confirmCreateTasksModalIsActive = true
        },
        toggleAssignCadenceModal () {
            this.assignCadenceModalIsActive = !this.assignCadenceModalIsActive
        },
        getFilterOptions (initialSearch = false) {
            this.loading = true;
            this.api.getFilterOptions().then(resp => {
                if (resp.data?.data?.status) {
                    this.filters = resp.data.data.filter_options
                    this.presets = this.sortPresets(resp.data.data.presets ?? [])
                    if (initialSearch)
                        this.getInitialFilters();
                    else
                        this.resetFilters();
                }
            }).catch(err => {
                this.showAlert('error', err.message)
            })
        },
        async submitSearch (page, fetchAllResults) {
            this.loading = true;

            if (fetchAllResults) {
                return await search(this, 1, true);
            }

            if (typeof page !== 'number' || isNaN(page)) {
                page = usePaginationStore().currentPage

                if (typeof page !== 'number' || isNaN(page)) {
                    page = 1
                }
            }

            usePaginationStore().$patch({
                currentPage: page
            })

            search(this, page)

            if (this.data?.length === 0 && this.paginationData?.meta?.current_page !== 1) {
                search(this, 1)

                usePaginationStore().$patch({
                    currentPage: 1
                })
            }
        },
        setFilterDefaults() {
            this.filters.forEach(filter => {
                this.filterInputs[filter.id] = this.filterDefaults?.[filter.id] ?? null
            });
        },
        // Set initial filters as per user's role, AM / CSM / SM
        getInitialFilters() {
            this.setFilterDefaults();

            const accountManagerIds = this.permissionStore.getId(IDS.SALES_TEAM_MEMBERS);
            if (this.permissionStore.hasRole(ROLES.SALES_MANAGER) && accountManagerIds) {
                this.filterInputs['company-account-manager'] = accountManagerIds ?? [];
                if (this.permissionStore.getId(IDS.ACCOUNT_MANAGER)) {
                    this.filterInputs['company-account-manager'].push(this.permissionStore.getId(IDS.ACCOUNT_MANAGER));
                }
            }
            else if (this.permissionStore.getId(IDS.ACCOUNT_MANAGER)) {
                this.filterInputs['company-account-manager'] = [this.permissionStore.getId(IDS.ACCOUNT_MANAGER)];
            }
            else if (this.permissionStore.getId(IDS.SUCCESS_MANAGER)) {
                this.filterInputs['company-success-manager'] = [this.permissionStore.getId(IDS.SUCCESS_MANAGER)];
            }

            this.submitSearch()
        },
        async resetFilters () {
            await nextTick()
            this.searchInputGeneral = ''
            this.searchInputId = ''
            this.searchInputContact = ''
            this.setFilterDefaults();

            this.submitSearch()
        },
        toggleFiltersPopup () {
            this.filtersPopup = !this.filtersPopup
            if (this.filtersPopup === false) {
                this.selectedFilter = null
            }
        },
        selectFilter (filter) {
            this.selectedFilter = filter
            this.selectedOption = null
        },
        selectOption (option) {
            this.selectedOption = option
        },
        clearFilter (filterId) {
            delete this.filterInputs[filterId]
        },
        getFilterOptionUpdates() {
            this.api.getFilterOptionUpdates({
                filters: this.filterInputs,
            }).then(resp => {
                if (resp.data?.data?.status) {
                    this.updateFilterOptions(resp.data.data.filter_updates ?? null);
                }
            }).catch(err => {
                this.showAlert('error', err.message);
            });
        },
        updateFilterOptions(updatedFilterOptions) {
            if (updatedFilterOptions) {
                for (const filterKey in updatedFilterOptions) {
                    const targetIndex = this.filters.findIndex(filter => {
                        return filter.id === filterKey;
                    });
                    if (targetIndex >= 0) this.filters[targetIndex] = updatedFilterOptions[filterKey];
                    this.validateInputsOnUpdatedFilter(updatedFilterOptions[filterKey]);
                }
            }
        },
        validateInputsOnUpdatedFilter(updatedFilter) {
            if (!this.filterInputs) return;

            const filterValues = (filterComponent, parentId) => {
                const id = filterComponent.id;
                if (id in this.filterInputs[parentId]) {
                    const validValues = Object.values(filterComponent.options);
                    this.filterInputs[parentId][id] = this.filterInputs[parentId][id].filter(input => validValues.includes(input));
                }
            }
            const parentId = updatedFilter.id;

            if (!parentId in this.filterInputs || !this.filterInputs[parentId]) return;

            filterValues(updatedFilter, parentId);
            for (const child in (updatedFilter.children ?? {})) {
                filterValues(updatedFilter.children[child], parentId);
            }
        },
        updateFilterDefaults (filterChange) {
            Object.assign(this.filterDefaults, { ...filterChange })
        },
        saveFilterPreset () {
            const payload = {
                name: this.presetName.trim(),
                value: this.filterInputs,
            }
            this.updatePresets(payload)
        },
        deleteFilterPreset () {
            if (this.saving || !this.deletingPreset) return
            this.saving = true

            this.api.deleteUserPreset(this.deletingPreset).then(resp => {
                if (resp.data?.data?.status) {
                    this.presets = resp.data.data.presets
                }
            }).catch(err => {
                console.error(err)
            }).finally(() => {
                this.saving = false
                this.deletingPreset = null
                this.showPresetModal = false
            })
        },
        updatePresets (payload) {
            if (this.saving) return
            this.saving = true

            this.api.saveUserPreset(payload).then(resp => {
                if (resp.data?.data?.status) {
                    this.presets = this.sortPresets(resp.data.data.presets)
                }
            }).catch(err => {
                this.showAlert('error', err.message)
            }).finally(() => {
                this.saving = false
                this.closePresetModal()
            })
        },
        sortPresets (presetArray) {
            return presetArray.sort((a, b) => a.name > b.name ? 1 : 0)
        },
        openSavePresetModal () {
            this.deletingPreset = false
            this.showPresetModal = true
        },
        openDeletePresetModal (deleteOption) {
            this.deletingPreset = deleteOption
            this.showPresetModal = true
        },
        closePresetModal () {
            this.deletingPreset = false
            this.showPresetModal = false
            this.presetName = ''
            this.modalError = null
        },
        confirmPresetModal () {
            if (this.deletingPreset) {
                this.deleteFilterPreset()
            } else {
                if (!this.validatePresetName()) return
                this.saveFilterPreset()
            }
        },
        validatePresetName () {
            this.modalError = null
            const errors = []
            const currentName = this.presetName.trim().toLowerCase()
            const invalidNames = this.presets.map(preset => preset.name.toLowerCase())
            if (invalidNames.includes(currentName)) {
                errors.push('That name is already in use. Please enter a unique name.')
            }

            if (errors.length) {
                this.modalError = errors.join('\n')
                return false
            } else return true
        },
        async handlePresetChange () {
            if (this.selectedPreset) {
                const targetPreset = this.presets.find(preset => preset.name === this.selectedPreset)
                this.selectedPresetData = targetPreset
                if (targetPreset) {
                    const validIds = this.filters.map(filter => filter.id)
                    for (const key in targetPreset.value) {
                        if (validIds.includes(key)) {
                            const clone = { ...targetPreset.value }
                            this.filterInputs[key] = clone[key]
                        }
                    }

                    this.submitSearch()
                }
            }
        },
        handleCustomUpdate (newVal) {
            if ('presets' in newVal) {
                this.selectedPreset = newVal.presets
                this.handlePresetChange()
            }
        },
        showSimilarCompanies(companyId) {
            this.showSimilarCompaniesModal = true;
            this.checkSimilarCompaniesForId = companyId;
        },
        async exportCSV(confirm = false) {
            const selection = useSelectionStore().selection;
            if (!selection?.length && !this.data?.length) return;

            const total = this.paginationData?.meta?.total;

            if (!selection?.length && total > this.exportWarningLimit && !confirm && !this.permissionStore.hasPermission(PERMISSIONS.PERMISSION_ADMIN)) {
                this.exportWarning = total > this.exportLimit
                    ? `The current Company Search results are too large to export.\nYou can use the Select All checkbox to limit the export to the current page.`
                    : `The current Company Search Results are large and may take up to a minute to download.\nYou can use the Select All checkbox to limit the export to the current page, or click Continue to export the full ${total} Company results.`;
                this.showExportModal = true;

                return;
            }
            this.toggleExportModal(false);

            const columns = useFieldsToggleStore().fieldsGetter;
            const { columnHeaders, dataKeys } = Object.values(columns).reduce((output, column) => {
                if (column.shown) {
                    output.columnHeaders.push(column.name);
                    output.dataKeys.push(column.dataKey);
                }
                return output;
            }, { columnHeaders: [], dataKeys: [] });

            const filenameSuffix = (new Date()).toISOString()
                .replace(/:/g, '-')
                .replace(/T/, '_')
                .replace(/Z.*/, '');
            const defaultFilename = `CompanySearchResults_${filenameSuffix}.csv`;

            if (selection.length) {
                const transformedData = selection.map(companyData => this.transformCompanyForCSV(companyData, dataKeys));

                downloadCsvString(columnHeaders, transformedData, defaultFilename);
            }
            else {
                const allQueryResults = await this.submitSearch(1, true);
                const transformedData = allQueryResults.map(companyData => this.transformCompanyForCSV(companyData, dataKeys));

                downloadCsvString(columnHeaders, transformedData, defaultFilename);
            }
        },
        transformCompanyForCSV(company, activeDataKeys) {
            // transforms {} should mimic the behaviour of the CompanySearchTableBody for any field which mutates the data from the search response
            const transforms = {
                name: company.name ?? company.entity_name ?? '-',
                industries: `${company.industries}`,
                services: `${company.services}`,
                address: this.transformAddressesForCsv(company.addresses),
                state: this.transformStatesForCsv(company.addresses),
                last_contacted_at: `${company.last_contacted_at_direction ?? ''} ${company.last_contacted_at_type ?? ''} ${company.last_contacted_at_date ?? ''}`,
                campaigns: `${company.campaigns_active_count}/${company.campaigns_total}`,
                lead_rejection: `${parseFloat(company.lead_rejection_percentage ?? 0).toFixed(2)}`,
                appointment_rejection: `${parseFloat(company.appointment_rejection_percentage ?? 0).toFixed(2)}`,
                leads_purchased: company.lead_cost_one ?? company.lead_cost_spent ?? 'N/A',
                google_rating_with_count: `${company.google_rating ?? 0} (${company.google_review_count})`,
                other: `Reviews: ${company.review_count ?? 0}/Last Login: ${company.last_login ?? 'N/A'}/Revised: ${company.last_revised}/Pre-screened: ${company.prescreened}`,
            };

            return activeDataKeys.map(key => {
                return key in transforms
                    ? transforms[key]
                    : company[key] ?? '-';
            });
        },
        transformAddressesForCsv(addresses) {
            let addressString = ``;
            for (let i = 0; i < addresses.length; i++) {
                addressString = addressString + `Address ${i+1}: ${addresses[i].address.address_1}, ${addresses[i].address.address_2} ${addresses[i].address.city}, ${addresses[i].address.state} ${addresses[i].address.zip_code}. `;
            }
            return addressString;
        },
        transformStatesForCsv(addresses) {
            let states = [];
            for (let i = 0; i < addresses.length; i++) {
                if (!states.includes(addresses[i].address.state)) {
                    states.push(addresses[i].address.state);
                }
            }
            return states;
        },
        toggleExportModal(show) {
            this.showExportModal = show === undefined
                ? !this.showExportModal
                : !!show;
        },
    },
    watch: {
        perPageSelection () {
            this.submitSearch()
        }
    }
}
</script>
