import { defineStore } from 'pinia'
import { computed, reactive, ref } from 'vue'

export const usePaginationStore = defineStore('pagination', () => {
    const currentPage = ref(1)

    return {
        currentPage,
    }
})

export const useFieldsToggleStore = defineStore('fields-toggle', () => {
    // dataKey is currently used by CSV download for mapping from the CompanySearch response object
    const fields = reactive({
        id: {
            name: 'Id',
            shown: true,
            dataKey: 'id',
        },
        companyName: {
            name: 'Company Name',
            shown: true,
            dataKey: 'name',
        },
        status: {
            name: 'Status',
            shown: true,
            dataKey: 'status',
        },
        salesStatus: {
            name: 'Sales Status',
            shown: true,
            dataKey: 'sales_status',
        },
        industries: {
            name: 'Industries',
            shown: true,
            dataKey: 'industries',
        },
        services: {
            name: 'Services',
            shown: false,
            dataKey: 'services',
        },
        addresses: {
            name: 'Addresses',
            shown: true,
            dataKey: 'address',
        },
        state: {
            name: 'State',
            shown: true,
            dataKey: 'state',
        },
        campaigns: {
            name: 'Campaigns',
            shown: true,
            dataKey: 'campaigns',
        },
        leadRejection: {
            name: 'Lead Rejection',
            shown: false,
            dataKey: 'lead_rejection',
        },
        leadsPurchased: {
            name: 'Leads Purchased',
            shown: true,
            dataKey: 'leads_purchased',
        },
        lastTimeLeadsPurchased: {
            name: 'Last Time Lead Purchased',
            shown: false,
            dataKey: 'last_time_lead_received',
        },
        lastTimeContacted: {
            name: 'Last Time Contacted',
            shown: true,
            dataKey: 'last_contacted_at',
        },
        lastTimeCalled: {
            name: 'Last Time Called',
            shown: false,
            dataKey: 'last_time_called',
        },
        googleRating: {
            name: 'Google Rating',
            shown: true,
            dataKey: 'google_rating',
        },
        googleReviewCount: {
            name: 'Google Review Count',
            shown: true,
            dataKey: 'google_review_count',
        },
        estimatedRevenue: {
            name: 'Estimated Revenue',
            shown: false,
            dataKey: 'estimated_revenue',
        },
        estimatedMonthlyAdSpend: {
            name: 'Estimated Monthly Ad Spend',
            shown: false,
            dataKey: 'estimated_monthly_ad_spend',
        },
        cadenceName: {
            name: 'Cadence Name',
            shown: false,
            dataKey: 'cadence_name',
        },
        lifetimeRevenue: {
            name: 'Lifetime Revenue',
            shown: false,
            dataKey: 'lifetime_revenue',
        },
        other: {
            name: 'Other',
            shown: true,
            dataKey: 'other',
        },
        actions: {
            name: 'Actions',
            shown: false,
            dataKey: 'actions'
        },
        bdm: {
            name: 'BDM',
            shown: false,
            dataKey: 'bdm'
        },
        am: {
            name: 'AM',
            shown: false,
            dataKey: 'am'
        },
        om: {
            name: 'OM',
            shown: false,
            dataKey: 'om'
        }
    })

    const fieldsGetter = computed(() => {
        const result = fields

        const allFieldsAreNotShown = Object.values(fields).filter(field => field.shown).length === 0

        if (allFieldsAreNotShown) {
            result.id.shown = true
        }

        return result
    })

    const updateFieldShown = (key, status) => {
        fields[key].shown = status
    }

    const toggleAllFieldsShown = () => {
        if (allFieldsExceptIdAreNotShown.value) {
            for (const field in fields) {
                fields[field].shown = true
            }
        } else {
            for (const field in fields) {
                fields[field].shown = false
            }
        }
    }

    const allFieldsExceptIdAreNotShown = computed(() => {
        return Object.values(fieldsGetter.value).filter(field => field.shown && field.name !== 'Id').length === 0
    })

    return {
        allFieldsExceptIdAreNotShown,
        fields,
        fieldsGetter,
        updateFieldShown,
        toggleAllFieldsShown,
    }
})

export const useSelectionStore = defineStore('selection', () => {
    const selection = ref([])

    const currentCompaniesForPage = ref([])

    /**
     * Check if all rows for the current page are selected
     * @type {ComputedRef<boolean|boolean>}
     */
    const allRowsAreSelectedForPage = computed(() => {
        const currentlySelected = selection.value

        const currentCompanies = currentCompaniesForPage.value

        const result = currentCompanies.every(item => currentlySelected.filter(x => item?.id === x?.id).length > 0)

        return currentCompanies.length > 0 ? result : false
    })

    /**
     * Select all rows for the current page that haven't been selected yet
     */
    const selectAll = () => {
        const currentCompanies = currentCompaniesForPage.value

        const allRowsAreAlreadySelected = allRowsAreSelectedForPage.value

        if (allRowsAreAlreadySelected) {
            selection.value = selection.value.filter(item => !currentCompanies.filter(x => item?.id === x?.id).length > 0)
        } else {
            currentCompanies.forEach(company => {
                const selectedCompanies = selection.value

                const currentCompanyIsSelected = selectedCompanies.filter(x => x?.id === company?.id)

                if (!currentCompanyIsSelected.length) {
                    selection.value.push(company)
                }
            })
        }
    }

    return {
        selection,
        allRowsAreSelectedForPage,
        currentCompaniesForPage,
        selectAll,
    }
})

export const useSortingStore = defineStore('sorting', () => {
	const fields = reactive([])

	const updateSortedColumns = (id, direction) => {
		const field = fields.find(field => field?.id === id)

		const deleteField = (field) => {
			fields.splice(fields.indexOf(field), 1)
		}

		if (['asc', 'desc'].includes(direction)) {
			if (field) {
				deleteField(field)
			}

			fields.push({
				id,
				direction,
			})
		} else {
			if (field) {
				deleteField(field)
			}
		}
    }

    /**
     * Get the sort column precedence for the given id
     * @param id
     * @return {number|null}
     */
    const getSortColumnPrecedence = (id) => {
        const field = fields.find(field => field?.id === id)

        if (field) {
            return fields.indexOf(field) + 1
        }

        return null
    }

	return {
		fields,
		updateSortedColumns,
        getSortColumnPrecedence,
	}
})
