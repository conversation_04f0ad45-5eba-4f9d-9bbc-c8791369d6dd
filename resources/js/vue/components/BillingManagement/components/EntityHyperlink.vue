<template>
    <div class="text-primary-500 hover:text-blue-600">
        <a class="cursor-pointer" @click="handleClick" v-if="prefix">{{ prefix }}</a>
        <a class="cursor-pointer ml-1" @click="handleClick" v-if="delimiter">{{ delimiter }}</a>
        <a class="cursor-pointer" @click="handleClick" v-if="suffix">{{ suffix }}</a>
    </div>
</template>
<script>
import GrossVolumeSummaryCard from "./BillingSummaryCards/GrossVolumeSummaryCard.vue";
import PendingInvoicesSummaryCard from "./BillingSummaryCards/PendingInvoicesSummaryCard.vue";
import TotalBilledInvoicesSummaryCard from "./BillingSummaryCards/TotalBilledInvoicesSummaryCard.vue";
import useQueryParams from "../../../../composables/useQueryParams.js";

export default {
    name: "EntityHyperlink",
    components: {TotalBilledInvoicesSummaryCard, PendingInvoicesSummaryCard, GrossVolumeSummaryCard},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        entityId: {
            type: [String, Number],
        },
        prefix: {
            type: [String, Number],
        },
        suffix: {
            type: [String, Number],
        },
        type: {
            type: String,
        },
        delimiter: {
            type: [String, Number],
        },
    },
    computed: {
        computedRedirectData() {
            return {
                invoice: {
                    params: {
                        tab: 'Invoices',
                        invoice_id: this.entityId,
                    },
                    url: '/billing-management'
                },
                company: {
                    url: `/companies/${this.entityId}`
                },
                billing_profile: {
                    url: `/billing-management?tab=Billing+Profiles&id=${this.entityId}`
                },
                consumer_product: {
                    url: `/consumer-product/?consumer_product_id=${this.entityId}`
                },
                product_assignment: {
                    url: `/consumer-product/?product_assignment_id=${this.entityId}`
                }
            }[this.type]
        },
    },
    methods: {
        handleClick() {
            const redirectData = this.computedRedirectData

            if (!redirectData) {
                return;
            }

            const {
                params = {},
                url
            } = redirectData

            const redirectUrl = useQueryParams()
                .mountUrlWithSearchParams(params, url)

            window.open(redirectUrl, '_blank')
        }
    }
}
</script>
