<template>
    <div>
        <billing-card-wrapper
            :heading="customTitle"
            :dark-mode="darkMode"
            :loading="loading"
        >
            <template v-slot:headerAction>
                <simple-icon
                    :dark-mode="darkMode"
                    @click="showInvoiceManagementModal = true"
                    clickable
                    :icon="simpleIcon.icons.ARROW_TOP_RIGHT_ON_SQUARE"
                    :color="simpleIcon.colors.BLUE"
                />
            </template>
            <billing-card-paginated-content
                :dark-mode="darkMode"
                v-if="!loading && invoicePayload.length > 0"
                :data="invoicePayload"
                :headers="headers"
                :pagination-data="paginationData"
                @page-change="handlePageChange"
            >
                <template v-slot:issue_at="{item,value}">
                    {{$filters.dateFromTimestamp(value,'MMMM D, YYYY')}}
                </template>
                <template v-slot:due_at="{item,value}">
                    {{$filters.dateFromTimestamp(value,'MMMM D, YYYY')}}
                </template>
                <template v-slot:amount="{item}">
                    {{$filters.centsToFormattedDollars(item.items_total)}}
                </template>
                <template v-slot:status_data="{item,value}">
                    {{value.title}}
                </template>
            </billing-card-paginated-content>
        </billing-card-wrapper>
        <invoice-management-modal
            v-if="showInvoiceManagementModal"
            :dark-mode="darkMode"
            @close="showInvoiceManagementModal = false"
        />
    </div>
</template>
<script>
import BillingCardTotalsContent from "../BillingCardBuilders/BillingCardTotalsContent.vue";
import BillingCardWrapper from "../BillingCardBuilders/BillingCardWrapper.vue";
import {useBillingManagementStore} from "../../../../../stores/billing/billing-management.store";
import BillingCardBarChartContent from "../BillingCardBuilders/BillingCardBarChartContent.vue";
import BillingCardPaginatedContent from "../BillingCardBuilders/BillingCardPaginatedContent.vue";
import SimpleIcon from "../../../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../../../composables/useSimpleIcon";
import InvoiceManagementModal from "../Modals/InvoiceManagementModal.vue";
const simpleIcon = useSimpleIcon();

export default {
    name: "InvoicesBillingCard",
    components: {
        InvoiceManagementModal,
        SimpleIcon,
        BillingCardPaginatedContent,
        BillingCardBarChartContent,
        BillingCardWrapper,
        BillingCardTotalsContent
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        customTitle: {
            type: String,
            default: "Invoices"
        },
        listenerId: {
            type: String,
            default: "invoices-billing"
        }
    },
    inject: ['baseFilters'],
    data() {
        return {
            simpleIcon,
            billingStore: useBillingManagementStore(),
            invoicePayload: [],
            headers: [
                {name: 'Id', field: 'id'},
                {name: 'Issued At', field: 'issue_at'},
                {name: 'Due At', field: 'due_at'},
                {name: 'Amount', field: 'amount'},
                {name: 'Status', field: 'status_data'},
            ],
            paginationData: null,
            page: 1,
            perPage: 3,
            loading: false,
            showInvoiceManagementModal: false,
        }
    },
    created() {
        this.getInvoices();
        this.billingStore.addPeriodUpdateEventListener(this.listenerId, this.getInvoices)

    },
    methods: {
        async getInvoices() {
            this.loading = true;
            try {
                const response = await this.billingStore.invoiceApi.getInvoices(
                    {
                        ...this.baseFilters,
                        ...this.billingStore.currentPeriod,
                        page: this.page,
                        perPage: this.perPage
                    });
                const { data, links, meta} = response.data
                this.invoicePayload = data ?? []
                this.paginationData = {
                    links,
                    page: meta.current_page,
                    perPage: meta.per_page,
                    total: meta.total
                }
            } catch (e) {
                console.error(e);
            }
            this.loading = false;
        },
        handlePageChange(newPage){
            if (this.page === newPage) return
            this.page = newPage
            this.getInvoices()
        },
    }
}
</script>
