<template>
    <div class="grid grid-cols-2 gap-3">
        <div>
            <div class="text-xs uppercase text-slate-500">Currency</div>
            <Dropdown
                v-model="modelValue.currency"
                :options="currencyOptions"
                :dark-mode="darkMode"
                :selected="currencyOptions"
            />
            <span v-if="errors['props.currency']" class="text-red-400 text-sm">{{ errors['props.currency'] }}</span>
        </div>
        <div>
            <div class="text-xs uppercase text-slate-500">Support Email</div>
            <custom-input
                :dark-mode="darkMode"
                type="text"
                v-model="modelValue.support_email"
            />
            <span v-if="errors['props.support_email']" class="text-red-400 text-sm">{{
                    errors['props.support_email']
                }}</span>
        </div>
        <div>
            <div class="text-xs uppercase text-slate-500">Contact Address</div>
            <custom-input
                class="pb-2"
                v-for="(key, field) in modelValue.invoice_contact_address"
                :dark-mode="darkMode"
                :placeholder="field"
                v-model="modelValue.invoice_contact_address[field]"
            />
            <span v-if="errors['props.invoice_contact_address']"
                  class="text-red-400 text-sm">{{ errors['props.invoice_contact_address'] }}</span>
        </div>
        <div>
            <div class="text-xs uppercase text-slate-500">Logo</div>
            <Dropdown
                v-model="modelValue.invoice_logo"
                :dark-mode="darkMode"
                :options="invoiceLogoOptions"
                :selected="invoiceLogoOptions"
            />
            <span v-if="errors['props.invoice_logo']"
                  class="text-red-400 text-sm">{{ errors['props.invoice_logo'] }}</span>
        </div>
        <div>
            <div class="text-xs uppercase text-slate-500">Billing Account</div>
            <Dropdown
                v-model="modelValue.billing_account"
                :dark-mode="darkMode"
                :options="billingOptions"
                :selected="billingOptions"
            />
            <span v-if="errors['props.invoice_logo']"
                  class="text-red-400 text-sm">{{ errors['props.billing_details'] }}</span>
        </div>
    </div>
</template>
<script>
import CustomInput from "../../../../Shared/components/CustomInput.vue";
import Dropdown from "../../../../Shared/components/Dropdown.vue";

export default {
    name: "InvoiceComponentPropsForm",
    components: {Dropdown, CustomInput},
    props: {
        component: {
            type: String
        },
        modelValue: {
            type: Object,
            default: {}
        },
        errors: {
            type: Object,
            default: {}
        },
        darkMode: {
            type: Boolean,
            default: false
        }
    },

    data() {
        return {
            currencyOptions: [
                'USD',
                'EUR',
            ].map(c => ({id: c, name: c})),

            invoiceLogoOptions: [
                {
                    name: 'Solarreviews',
                    id: 'solarreviews'
                },
                {
                    name: 'Fixr',
                    id: 'fixr'
                },
            ],
            billingOptions: [
                {
                    name: 'SolarReviews Account',
                    id: 'solarreviews_account'
                },
                {
                    name: 'Fixr Account',
                    id: 'fixr_account'
                },
            ]
        }
    },
}
</script>
