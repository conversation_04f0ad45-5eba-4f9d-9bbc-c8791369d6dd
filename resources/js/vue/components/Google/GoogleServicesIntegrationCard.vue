<template>
    <div class="flex p-6 border rounded-lg flex-col gap-6" :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']">
        <h5 class="text-xl font-medium pb-0 leading-none mr-5">Google Integration</h5>
        <div v-if="loading" class="flex-1 justify-center items-center flex">
            <loading-spinner/>
        </div>
        <div v-else-if="!loading && !errorMessage" class="flex flex-col gap-2">
            <div v-for="service in servicesData" class="flex gap-2 items-center">
                <p class="flex-1">{{ service.service }}</p>
                <custom-button v-if="!service.has_given_consent" @click="openSyncPage(service.link)" height="h-7" :disabled="service.has_given_consent">
                    Sync
                </custom-button>
                <badge v-else>Sync Enabled</badge>
            </div>
        </div>
        <p v-else class="text-red-500">
            {{ errorMessage }}
        </p>
    </div>
</template>
<script>
import CustomButton from "../Shared/components/CustomButton.vue";
import {useMailboxStore} from "../../../stores/mailbox/mailbox.js";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import Api from "./services/api.js";
import Badge from "../Shared/components/Badge.vue";

export default {
    name: 'MailboxSyncCard',
    components: {Badge, LoadingSpinner, CustomButton},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        }
    },
    data() {
        return {
            loading: true,
            errorMessage: null,
            servicesData: null,
            apiService: Api.make()
        }
    },
    mounted() {
        this.getServicesData()
    },
    methods: {
        openSyncPage(link) {
            if (!link) {
                this.errorMessage = 'No URL found'
                return
            }

            window.open(link, '_blank')
        },
        handleError(err){
            if (err?.response?.status === 403) {
                this.errorMessage = 'Feature unavailable while impersonating'
            } else {
                this.errorMessage = 'Server Error'
            }
        },
        async getServicesData() {
            this.loading = true
            try {
                const response = await this.apiService.listGoogleServiceIntegrationLinks()
                this.servicesData = response.data
            } catch (err) {
                this.handleError(err)
            }
            this.loading = false
        }
    }
}
</script>
