import axios from 'axios';
import {BaseApiService} from "./base";

class ApiService extends BaseApiService {

    ROUTE_MANAGEMENT = '/management';
    ROUTE_ACCOUNT_ASSIGNMENT = '/account-assignment-participants';

    constructor(baseUrl, baseEndpoint, version) {
        super("ApiService");

        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    searchUserNames(query) {
        return this.axios().get(`${this.ROUTE_MANAGEMENT}/admins/search`, {
            params: {
                name: query
            }
        });
    }

    getAccountAssignmentParticipants() {
        return this.axios().get(`${this.ROUTE_MANAGEMENT}${this.ROUTE_ACCOUNT_ASSIGNMENT}`);
    }

    updateAccountAssignmentParticipants(addParticipants, removeParticipants) {
        return this.axios().patch(`${this.ROUTE_MANAGEMENT}${this.ROUTE_ACCOUNT_ASSIGNMENT}`, {
            add_user_ids: addParticipants ?? [],
            remove_user_ids: removeParticipants ?? [],
        });
    }

    searchUsers(searchText, excludeIds) {
        return axios.get(`/${this.baseUrl}/v${this.version}/sales-overview/demos/users`, {
            params: {
                search: searchText,
                exclude_ids: excludeIds,
            }
        });
    }
}

export { ApiService };

