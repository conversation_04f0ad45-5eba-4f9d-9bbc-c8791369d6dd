import axios from 'axios';

export default class ApiService {
    constructor(baseUrl, baseEndpoint, version) {
        this.baseEndpoint = baseEndpoint;
        this.baseUrl = baseUrl;
        this.version = version;
    }

    axios() {
        const axiosConfig = {
            baseURL: `/${this.baseUrl}/v${this.version}/${this.baseEndpoint}`
        }

        return axios.create(axiosConfig);
    }

    static make() {
        return new ApiService('internal-api', 'company-manager-assignment-requests', 2);
    }

    getPendingOwnershipRequests() {
        return this.axios().get(`/get-pending-requests`);
    }

    getOwnershipRequestHistory() {
        return this.axios().get(`/get-request-history`);
    }
    approveOwnershipRequest(requestId) {
        return this.axios().patch(`/${requestId}/approve`);
    }
    denyOwnershipRequest(requestId) {
        return this.axios().patch(`/${requestId}/deny`);
    }
    requestOwnershipAssignment(companyId, role) {
        return this.axios().post(`/request-assignment`, {
            'company_id': companyId,
            'role': role
        });
    }
    getMyOwnershipRequests() {
        return this.axios().get(`/get-my-requests`);
    }

}
