<template>

    <button
        type="button"
        :class="{'bg-red-500 hover:bg-red-100 focus-visible:outline-red-100 text-white': !darkMode, 'bg-red-600 hover:bg-red-400 focus-visible:outline-red-400 text-white': darkMode}"
        class="invoice--action-cancel mr-6 rounded-md px-2.5 py-1.5 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
        v-if="transitionAllowed(invoiceTransitions.cancel)"
        @click.stop="executeTransition(invoiceTransitions.cancel)">
        Cancel Credit
    </button>

    <button
        type="button"
        :class="{'bg-red-500 hover:bg-red-400 focus-visible:outline-red-400 text-white': !darkMode, 'bg-red-600 hover:bg-red-400 focus-visible:outline-red-400 text-white': darkMode}"
        class="invoice--action-deny mr-6 rounded-md px-2.5 py-1.5 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
        v-if="transitionAllowed(invoiceTransitions.deny)"
        @click.stop="executeTransition(invoiceTransitions.deny)">
        Deny Credit
    </button>

    <button
        type="button"
        class="invoice--action-approve mr-6 bg-green-500 hover:bg-green-400 focus-visible:outline-green-600 rounded-md px-2.5 py-1.5 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 "
        v-if="transitionAllowed(invoiceTransitions.approve)"
        @click.stop="executeTransition(invoiceTransitions.approve)">
        Approve Credit
    </button>

    <button
        type="button"
        class="invoice--action-fail mr-6 bg-orange-600 hover:bg-orange-400 focus-visible:outline-orange-600 rounded-md px-2.5 py-1.5 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
        v-if="transitionAllowed(invoiceTransitions.fail)"
        @click.stop="showFailReasonModal = true">
        Mark as Failed
    </button>

    <button
        type="button"
        class="invoice--action-paid mr-6 bg-primary-500 hover:bg-blue-500  focus-visible:outline-blue-500 rounded-md px-2.5 py-1.5 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
        v-if="bundleInvoice.has_legacy_invoice_paid === true && transitionAllowed(invoiceTransitions.paid)"
        @click.stop="executeTransition(invoiceTransitions.paid)">
        Mark as Paid
    </button>

    <button
        type="button"
        class="invoice--action-issue mr-6 bg-primary-500 hover:bg-blue-500 focus-visible:outline-blue-500 rounded-md px-2.5 py-1.5 text-sm font-semibold text-white shadow-sm focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2"
        v-if="!bundleInvoice.is_auto_approved && transitionAllowed(invoiceTransitions.issue)"
        @click.stop="executeTransition(invoiceTransitions.issue)">
        Issue Invoice
    </button>

    <Modal
        v-if="showFailReasonModal"
        @confirm="submitFailReason"
        @close="showFailReasonModal = false"
        :close-text="'Close'"
        :dark-mode="darkMode"
        :small="true">
        <template v-slot:header>
            <h4 class="text-xl">Mark as failed</h4>
        </template>
        <template v-slot:content>
            <div class="my-4">
                <div class="w-full flex rounded-lg bg-blue-100 p-4 mb-3 text-blue-800">
                    <InformationCircleIcon class="h-5 w-5"/>
                    <p class="ml-4 text-sm font-medium">
                        Please provide a reason for marking this invoice as failed.
                    </p>
                </div>
                <div>
                    <label for="fail-reason"
                           class="block text-sm font-medium leading-6"
                           :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                        Fail Reason
                    </label>
                    <input
                        type="text"
                        v-model="failReason"
                        name="fail-reason"
                        id="fail-reason"
                        placeholder="Reason *"
                        class="block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        :class="{
                            'text-gray-900 placeholder:text-gray-400 border-grey-350 bg-light-module disabled:bg-slate-50 disabled:text-slate-500 disabled:border-slate-200 disabled:shadow-none': !this.darkMode,
                            'text-blue-400 placeholder:text-blue-500 border-blue-400 bg-dark-background disabled:bg-dark-background disabled:text-blue-100 disabled:border-blue-800 disabled:shadow-none': this.darkMode,
                        }">
                </div>
            </div>
        </template>
    </Modal>
</template>

<script>
import {useBundlesStore,invoiceStatusMap, invoiceTransitions} from "../../../../stores/bundle-management.store";
import Modal from "../../Shared/components/Modal.vue";
import {InformationCircleIcon} from "@heroicons/vue/solid";
export default {
    name: "BundleInvoiceActionButtons",
    components: {InformationCircleIcon, Modal},
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        bundleInvoice: {
            type: Object,
            required: true
        }
    },
    emits: ['close'],
    data() {
        return {
            bundleStore: useBundlesStore(),
            invoiceTransitions: invoiceTransitions,
            showFailReasonModal: false,
            failReason: ''
        }
    },
    mounted() {
    },
    watch: {
        failReason(newVal, oldVal) {
            if (newVal !== oldVal && !!newVal.id) this.failReason = newVal;
        }
    },
    methods: {
        transitionAllowed(transitionName) {
            if (!invoiceStatusMap[this.bundleInvoice.status]) {
                console.error('Unknown invoice status.');
                return false;
            }

            return invoiceStatusMap[this.bundleInvoice.status]?.transitions.includes(transitionName)
        },
        executeTransition(transitionName, options = {}) {
            this.bundleStore.executeInvoiceTransition(transitionName, this.bundleInvoice.id, options, this.bundleInvoice.billing_version);
            this.$emit('close');
        },
        submitFailReason() {
            this.showFailReasonModal = false;
            this.executeTransition(invoiceTransitions.fail, {'fail_reason': this.failReason});
        },
    }
}

</script>
