<template>
    <Modal
        v-if="showModal"
        :container-classes="editingInvoice.id ? 'overflow-scroll max-h-[90vh] p-8' : 'overflow-scroll max-h-[90vh] h-[520px] p-8'"
        @confirm="saveInvoice"
        @close="closeModal"
        :no-buttons="!bundleStore.canEditInvoices()"
        :hide-confirm="!!editingInvoice.id"
        :close-text="'Close'"
        :dark-mode="darkMode"
        :small="false"
        :confirm-text="confirmText"
    >
        <template v-slot:header>
            <h4 class="text-xl">{{ editingInvoice.id ? 'Edit Bundle Invoice' : 'New Bundle Invoice' }}</h4>
        </template>
        <template v-slot:content>
            <div class="bundle-invoice--edit-modal" :class="darkMode ? 'darkmode' : ''">
                <div class="w-full flex rounded-lg bg-blue-100 p-4 mb-3 text-blue-800">
                    <InformationCircleIcon class="h-5 w-5"/>
                    <p class="ml-4 text-sm font-medium">
                        Bundle invoice fields are read-only.
                    </p>
                </div>
                <Tab
                    v-if="!!editingInvoice?.id"
                    :dark-mode="darkMode"
                    :tabs="tabs"
                    :showTotal="false"
                    @selected="(tabName) => this.activeModalTab = tabName"
                    :tabs-classes="'w-full'"
                >
                </Tab>
                <div v-if="activeModalTab === 'General'">
                    <div class="my-4">
                        <label class="block text-sm font-medium leading-6"
                               :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                            Recipient Company
                        </label>
                        <autocomplete
                            v-if="!editingInvoice?.id"
                            :dark-mode="darkMode"
                            :inputClass="[
                                'block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6',
                                !darkMode ? 'border-grey-350 bg-light-module' : 'border-blue-400 bg-dark-background text-blue-400',
                                (!darkMode && !!editingInvoice?.id) || (!darkMode && bundleStore.canEditInvoices()) ? 'disabled:bg-slate-50 disabled:text-slate-500 disabled:border-slate-200 disabled:shadow-none' : '',
                                (darkMode && !!editingInvoice?.id) || (darkMode && bundleStore.canEditInvoices()) ? 'bg-dark-800 text-blue-100 border-blue-800' : ''
                            ]"
                            v-model="editingInvoice.company_id"
                            :options="bundleStore.companyNameResults"
                            placeholder="Company Name"
                            :value="company"
                            :create-user-input-option="false"
                            @search="bundleStore.searchCompanyNames('name', $event)" />
                        <input
                            disabled
                            v-if="!!editingInvoice?.id"
                            :value="editingInvoice.company_name"
                            type="text"
                            placeholder="Enter company name"
                            :disabled="!!editingInvoice?.id"
                            class="darkmode-input block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                        <div v-if="bundleStore.searchError" class="lg:col-span-4 rounded-lg bg-red-100 p-4 mb-3">
                            <p class="text-sm font-medium text-red-800">{{ bundleStore.searchError }}</p>
                        </div>
                    </div>
                    <div class="mb-4">
                        <label class="block text-sm font-medium leading-6"
                               :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                            Bundle
                        </label>
                        <Dropdown
                            :disabled="!!editingInvoice?.id"
                            :dark-mode="darkMode"
                            v-model="editingInvoice.bundle_id"
                            placeholder="Choose a bundle"
                            :options="Array.from(bundleDropdownOptions.values())"
                            @click="setDropdownOptions"
                        ></Dropdown>
                    </div>
                    <div v-if="!!editingInvoice?.id" class="mb-4">
                        <label for="invoice-status"
                               class="block text-sm font-medium leading-6"
                               :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                            Status
                        </label>
                        <input
                            id="invoice-status"
                            name="invoice-status"
                            type="text"
                            disabled
                            :value="editingInvoice.status"
                            placeholder="Enter company name"
                            :disabled="!!editingInvoice?.id"
                            class="darkmode-input block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                        />
                    </div>
                    <div v-if="!!editingInvoice?.id" class="grid grid-cols-2 gap-x-3">
                        <div class="mb-4">
                            <label for="invoice-cost"
                                   class="block text-sm font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Cost
                            </label>
                            <input
                                id="invoice-cost"
                                name="invoice-cost"
                                :value="editingInvoice.cost"
                                disabled
                                class="darkmode-input block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                type="number"
                            />
                        </div>
                        <div class="mb-4">
                            <label for="invoice-credit"
                                   class="block text-sm font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Credit
                            </label>
                            <input
                                id="invoice-credit"
                                name="invoice-credit"
                                :value="editingInvoice.credit"
                                disabled
                                class="darkmode-input block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                                type="number"
                            />
                        </div>
                        <div class="mb-4">
                            <label for="invoice-issued-by"
                                   class="block text-sm font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Issued By
                            </label>
                            <input
                                id="invoice-issued-by"
                                name="invoice-issued-by"
                                :value="editingInvoice.issued_by"
                                disabled
                                type="text"
                                class="darkmode-input block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                        </div>
                        <div class="mb-4">
                            <label for="invoice-issued-at"
                                   class="block text-sm font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Issued At
                            </label>
                            <input
                                id="invoice-issued-at"
                                name="invoice-issued-at"
                                :value="editingInvoice.issued_at ? $filters.absoluteDatetimeFromTimestamp(editingInvoice.issued_at) : ''"
                                disabled
                                type="text"
                                class="darkmode-input block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                        </div>
                        <div class="mb-4">
                            <label for="invoice-approved-by"
                                   class="block text-sm font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Approved By
                            </label>
                            <input
                                id="invoice-approved-by"
                                name="invoice-approved-by"
                                :value="editingInvoice.approved_by"
                                disabled
                                type="text"
                                class="darkmode-input block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                        </div>
                        <div class="mb-4">
                            <label for="invoice-approved-at"
                                   class="block text-sm font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Approved At
                            </label>
                            <input
                                id="invoice-approved-at"
                                name="invoice-approved-at"
                                :value="editingInvoice.approved_at ? $filters.absoluteDatetimeFromTimestamp(editingInvoice.approved_at) : ''"
                                disabled
                                type="text"
                                class="darkmode-input block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                        </div>
                        <div class="mb-4">
                            <label for="invoice-denied-by"
                                   class="block text-sm font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Denied By
                            </label>
                            <input
                                id="invoice-denied-by"
                                name="invoice-denied-by"
                                v-model="editingInvoice.denied_by"
                                disabled
                                type="text"
                                class="darkmode-input block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                        </div>
                        <div class="mb-4">
                            <label for="invoice-denied-at"
                                   class="block text-sm font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Denied At
                            </label>
                            <input
                                id="invoice-denied-at"
                                name="invoice-denied-at"
                                :value="editingInvoice.denied_at ? $filters.absoluteDatetimeFromTimestamp(editingInvoice.denied_at) : ''"
                                disabled
                                type="text"
                                class="darkmode-input block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                        </div>
                        <div class="mb-4">
                            <label for="invoice-cancelled-at"
                                   class="block text-sm font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Cancelled At
                            </label>
                            <input
                                id="invoice-cancelled-at"
                                name="invoice-cancelled-at"
                                :value="editingInvoice.cancelled_at ? $filters.absoluteDatetimeFromTimestamp(editingInvoice.cancelled_at) : ''"
                                disabled
                                type="text"
                                class="darkmode-input block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                        </div>
                        <div class="mb-4">
                            <label for="invoice-cancelled-by"
                                   class="block text-sm font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Cancelled By
                            </label>
                            <input
                                id="invoice-cancelled-by"
                                name="invoice-cancelled-by"
                                v-model="editingInvoice.cancelled_by"
                                disabled
                                type="text"
                                class="darkmode-input block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                        </div>
                        <div class="mb-4">
                            <label for="invoice-failed-at"
                                   class="block text-sm font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Failed At
                            </label>
                            <input
                                id="invoice-failed-at"
                                name="invoice-failed-at"
                                :value="editingInvoice.failed_at ? $filters.absoluteDatetimeFromTimestamp(editingInvoice.failed_at) : ''"
                                disabled
                                type="text"
                                class="darkmode-input block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                        </div>
                        <div class="mb-4">
                            <label for="invoice-failed-reason"
                                   class="block text-sm font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Failed Reason
                            </label>
                            <input
                                id="invoice-failed-reason"
                                name="invoice-failed-reason"
                                v-model="editingInvoice.fail_reason"
                                disabled
                                type="text"
                                class="darkmode-input block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                        </div>
                        <div class="mb-4">
                            <label for="invoice-paid-at"
                                   class="block text-sm font-medium leading-6"
                                   :class="{'text-gray-900': !this.darkMode,'text-blue-400': this.darkMode}">
                                Paid At
                            </label>
                            <input
                                id="invoice-paid-at"
                                name="invoice-paid-at"
                                :value="editingInvoice.paid_at ? $filters.absoluteDatetimeFromTimestamp(editingInvoice.paid_at) : ''"
                                disabled
                                type="text"
                                class="darkmode-input block w-full rounded-md border-0 py-1.5 shadow-sm ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-inset focus:ring-indigo-600 sm:text-sm sm:leading-6"
                            />
                        </div>
                    </div>
                    <div v-if="modalError" class="lg:col-span-4 rounded-lg bg-red-100 p-4 mb-3">
                        <p class="text-sm font-medium text-red-800">{{ modalError }}</p>
                    </div>
                </div>
                <div v-if="activeModalTab === 'History'">
                    <div class="grid grid-cols-5 gap-x-3 mb-2 px-5 py-3">
                        <p class="col-span-3 text-slate-400 font-medium tracking-wide uppercase text-xs ">
                            Description
                        </p>
                        <p class="col-span-2 text-right text-slate-400 font-medium tracking-wide uppercase text-xs ">
                            Recorded At
                        </p>
                    </div>
                    <div
                        v-for="(historyItem, index) in historyItems" :key="index"
                        class="grid grid-cols-5 gap-x-3 border-b px-5 py-3"
                        :class="{'text-slate-900 hover:bg-light-module border-light-border': !darkMode, 'text-slate-100 hover:bg-dark-module border-dark-border': darkMode}">
                        <p class="col-span-2 text-sm" v-html="convertBreaksToList(historyItem.description)">
                        </p>
                        <p class="col-span-2 text-sm">
                            {{ historyItem.note }}
                        </p>
                        <p class="col-span-1 text-sm text-right">
                            {{ $filters.absoluteDatetimeFromTimestamp(historyItem.created_at) }}
                        </p>
                    </div>
                </div>
                <div v-if="companyBilling.isV2() && activeModalTab === 'Invoice'">
                    <view-create-invoice-modal-content
                        :dark-mode="darkMode"
                        :invoice-id="editingInvoice.payable_invoice_id"
                        readonly
                    />
                </div>
            </div>
        </template>
        <template v-slot:buttons>
            <bundle-invoice-action-buttons
                v-if="bundleStore.canEditInvoices() && !!editingInvoice?.id"
                @close="closeModal"
                :bundle-invoice="editingInvoice"
                :dark-mode="darkMode"
            />
        </template>
    </Modal>
</template>

<script>
import Modal from "../../Shared/components/Modal.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import {dateFromTimestamp} from "../../../../modules/helpers";
import Autocomplete from "../../Shared/components/Autocomplete.vue";
import {useBundlesStore} from "../../../../stores/bundle-management.store";
import BundleInvoiceActionButtons from "./BundleInvoiceActionButtons.vue";
import Tab from "../../Shared/components/Tab.vue";
import {InformationCircleIcon} from "@heroicons/vue/solid";
import ViewCreateInvoiceModalContent from "../../Billing/ViewCreateInvoiceModalContent.vue";
import Api from "../../Billing/services/company-billing-profiles.js";
import {useCompanyBillingStore} from "../../../../stores/billing/company-billing.js";


export default {
    name: "BundleInvoiceModal",
    components: {
        ViewCreateInvoiceModalContent,
        InformationCircleIcon,
        BundleInvoiceActionButtons,
        Autocomplete,
        Dropdown,
        Modal,
        Tab,
    },
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        showModal: {
            type: Boolean,
            default: false
        },
        editingInvoice: {
            type: Object,
            default: {}
        }
    },
    emits: ['close'],
    data() {
        return {
            company: null,
            modalError: null,
            bundleStore: useBundlesStore(),
            bundleDropdownOptions: [],
            activeModalTab: 'General',
            historyItems: [],
            companyBillingProfilesApi: Api.make(),
            companyBilling: useCompanyBillingStore(),
            confirmText: 'Save',
            tabs: [
                {name: 'General', current: true},
                {name: 'History', current: false},
            ]
        }
    },
    created() {
        this.setDropdownOptions();
    },
    watch: {
        'editingInvoice.bundle_id': {
            handler(bundleId){
                if (bundleId) {
                    const bundle = this.bundleDropdownOptions.find(e => e.id === bundleId)

                    this.confirmText = bundle.is_auto_approved ? 'Save & Issue' : 'Save'
                }
            },
        },
        'editingInvoice.company_id': {
            handler(companyId){
                if (companyId) {
                    this.companyBilling.getBillingVersion(companyId)
                }
            },
        },
        editingInvoice(newVal, oldVal) {
            if (newVal !== oldVal && !!newVal.id) this.getInvoiceHistoryItems(newVal.id);

            this.tabs =[
                {name: 'General', current: true},
                {name: 'History', current: false},
            ]

            if (newVal?.billing_version === 'v2') {
                this.tabs.push({name: 'Invoice', current: false})
            }
        },
        showModal(newVal, oldVal) {
            // Refresh the list of selectable bundles only when creating new invoices
            if (newVal !== oldVal && this.editingInvoice?.id === null) this.setDropdownOptions();
        },
    },
    methods: {
        dateFromTimestamp,
        setDropdownOptions() {
            this.bundleStore.getBundles().then(resp => {
                if(resp.status) {
                    const bundles = resp.data.data.bundles;
                    if (bundles) {
                        Object.assign(this.bundleDropdownOptions, bundles);
                    }
                }
                else {
                    throw new Error("Status was not successful");
                }
            }).catch((e) => {
                console.error("Error fetching bundles: ", e); // Log to console for bug reports if needed.
            });
        },
        getInvoiceHistoryItems(invoiceId) {
            this.bundleStore.getInvoiceHistory(invoiceId).then(resp => {
                if(resp.status) {
                    const history = resp.data.data.historyRecords;
                    if (history) {
                        this.historyItems = history.reverse()
                    }
                }
                else {
                    throw new Error("Status was not successful");
                }
            }).catch((e) => {
                console.error("Error fetching invoice history: ", e); // Log to console for bug reports if needed.
            });

            setTimeout(()=>{console.log(this.historyItems)},2000)

        },
        saveInvoice() {
            if (!this.editingInvoice.company_id || !this.editingInvoice.bundle_id) {
                this.modalError = "Please select both a company and a bundle."
                return;
            }

            const payload = {
                ...this.editingInvoice,
                billing_version: this.companyBilling.version
            }

            if (this.editingInvoice.id) this.bundleStore.updateInvoice(payload);
            else this.bundleStore.createInvoice(payload);

            this.closeModal();
        },
        closeModal() {
            this.$emit('close');
            this.activeModalTab = 'General';
        },
        convertBreaksToList(description) {
            if(!description.includes("<br>")) return description;

            let textParts = description.split("<br>");
            const firstPart = textParts.shift();
            return firstPart + "<li>" + textParts.join("</li><li>") + "</li>";
        }
    }
}
</script>

<style lang="postcss" scoped>
.bundle-invoice--edit-modal input:disabled {
    @apply bg-slate-50 text-slate-500 border-slate-200 shadow-none;
}
.bundle-invoice--edit-modal input {
    @apply border-grey-350 bg-light-module;
}
.bundle-invoice--edit-modal.darkmode input {
    @apply border-grey-350 bg-light-module;
}
.bundle-invoice--edit-modal.darkmode input:disabled {
    @apply bg-dark-background text-blue-100 border-blue-800 shadow-none;
}
</style>
