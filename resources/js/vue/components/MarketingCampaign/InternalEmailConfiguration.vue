<template>
    <simple-card :dark-mode="darkMode">
        <loading-spinner
            :dark-mode="darkMode"
            v-if="loading"
        />
        <div v-else class="grid grid-cols-2 gap-4 p-4">
            <div class="flex flex-col gap-2">
                <labeled-value label="Email Template">
                    <email-template-dropdown
                        :dark-mode="darkMode"
                        :filters="{'email-template-type': [5]}"
                        v-model="modelValue.email_template_id"
                    />
                </labeled-value>
            </div>
            <div class="flex flex-col gap-2 relative">
                <labeled-value label="Send At">
                    <Datepicker
                        :dark-mode="darkMode"
                        v-model="modelValue.sent_at"
                        :min-date="new Date()"
                        :dark="darkMode"
                        teleport="body"
                    />
                </labeled-value>
            </div>
            <from-email-address
                v-model="modelValue"
                :dark-mode="darkMode"
            />
            <labeled-value label="From Name">
                <custom-input
                    :dark-mode="darkMode"
                    v-model="modelValue.sender_name"
                />
            </labeled-value>
            <labeled-value label="Send Test Email">
                <template #label>
                    <div class="flex items-center gap-1 font-semibold">
                        Send Test Email
                        <simple-icon
                            :dark-mode="darkMode"
                            :icon="simpleIcon.icons.INFORMATION_CIRCLE"
                            :color="simpleIcon.colors.BLUE"
                            tooltip="Provide a comma separated list of all recipients"
                        />
                    </div>
                </template>
                <div class="flex gap-2">
                    <custom-input
                        :dark-mode="darkMode"
                        v-model="testEmail"
                        placeholder="<EMAIL>, <EMAIL>, <EMAIL>"
                        type="textarea"
                    />
                    <custom-button
                        :dark-mode="darkMode"
                        @click="sendTestEmail"
                        :disabled="!canTest || sendingTestEmail"
                    >
                     <div class="flex gap-2 items-center">
                         <div>
                             Send
                         </div>
                         <simple-icon
                             :icon="simpleIcon.icons.PAPER_AIRPLANE"
                             :dark-mode="darkMode"
                             :color="simpleIcon.colors.WHITE"
                         />
                     </div>
                    </custom-button>

                </div>

            </labeled-value>
            <labeled-value>
                <template #label>
                    <div class="flex gap-2 items-center font-semibold">
                        <div>Distributed Send</div>
                        <simple-icon
                            :icon="simpleIcon.icons.INFORMATION_CIRCLE"
                            :color="simpleIcon.colors.BLUE"
                            tooltip="Emails will be distributed across all validated domains in system"
                        />
                    </div>
                </template>
                <toggle-switch
                    :dark-mode="darkMode"
                    :value="modelValue.sending_strategy === marketingCampaignStrategies.sendingStrategies.DISTRIBUTED"
                    @change="handleToggle"
                />
            </labeled-value>
        </div>
        <display-toast-notifications />
    </simple-card>
</template>
<script>
import SimpleCard from "./SimpleCard.vue";
import Api from "../EmailTemplates/API/api.js";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import Dropdown from "../Shared/components/Dropdown.vue";
import Datepicker from "@vuepic/vue-datepicker";
import ApiService from "./services/api.js";
import CustomInput from "../Shared/components/CustomInput.vue";
import useMarketingCampaign from "../../../composables/useMarketingCampaign.js";
import LabeledValue from "../Shared/components/LabeledValue.vue";
import CustomInlineInput from "../Shared/components/CustomInlineInput.vue";
import SimpleIcon from "../Mailbox/components/SimpleIcon.vue";
import useSimpleIcon from "../../../composables/useSimpleIcon.js";
import CustomButton from "../Shared/components/CustomButton.vue";
import DisplayToastNotifications from "../Billing/components/ToastNotification/DisplayToastNotifications.vue";
import {useToastNotificationStore} from "../../../stores/billing/tost-notification.store.js";
import EmailTemplateDropdown from "./EmailTemplateDropdown.vue";
import ToggleSwitch from "../Shared/components/ToggleSwitch.vue";
import useMarketingCampaignStrategies from "../../../composables/useMarketingCampaignStrategies.js";
import useMarketingDomain from "../../../composables/useMarketingDomain.js";
import FromEmailAddress from "./FromEmailAddress.vue";
const simpleIcon = useSimpleIcon();
const marketingCampaignStrategies = useMarketingCampaignStrategies();
const marketingDomain = useMarketingDomain();
export default {
    name: "InternalEmailConfiguration",
    components: {
        FromEmailAddress,
        ToggleSwitch,
        EmailTemplateDropdown,
        DisplayToastNotifications,
        CustomButton,
        SimpleIcon,
        CustomInlineInput, LabeledValue, CustomInput, Datepicker, Dropdown, LoadingSpinner, SimpleCard},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: Object,
            default: {},
        }
    },
    data() {
        return {
            loading: false,
            domainLoading: false,
            emailTemplatesApi: Api.make(),
            api: ApiService.make(),
            emailTemplateOptions: [],
            emailDomainOptions: [],
            marketingCampaign: null,
            testEmail: null,
            simpleIcon,
            toastNotification: useToastNotificationStore(),
            sendingTestEmail: false,
            marketingCampaignStrategies,
            marketingDomain,
        }
    },
    computed: {
        isValidConfiguration() {
            const requiredFields = [
                'email_template_id',
                'sender_domain',
                'sender_local',
                'sender_name',
                'sent_at',
            ]

            return requiredFields.every(key => this.modelValue[key] != null)
        },
        canTest() {
            const requiredFields = [
                'email_template_id',
                'sender_domain',
                'sender_local',
            ]

            return requiredFields.every(key => this.modelValue[key] != null) && Boolean(this.testEmail);
        }
    },
    mounted() {
        this.marketingCampaign = useMarketingCampaign()
        this.setDefaultConfiguration();
        this.listEmailTemplates();
        this.listEmailDomains();
    },
    methods: {
        async listEmailTemplates() {
            this.loading = true;
            const response = await this.emailTemplatesApi.retrieveEmailTemplateList({filters: {'email-template-type': [5]}});
            this.emailTemplateOptions = response.data.data;
            this.loading = false;
        },
        async listEmailDomains() {
            this.domainLoading = true;
            const response = await this.api.getMarketingDomains({all: true, status: marketingDomain.status.VERIFIED});
            this.emailDomainOptions = response.data.data.map(domain => ({id: domain.name, name: domain.name}));
            this.domainLoading = false;
        },
        setDefaultConfiguration() {
            const defaultConfig = {
                sender_local: "no_reply",
                sender_name: "SolarReviews"
            }

            Object.entries(defaultConfig).forEach(([key,value]) => {
                if (this.modelValue[key] === null || this.modelValue[key] === undefined || this.modelValue[key] === '') {
                    this.modelValue[key] = value
                }
            })
        },
        async sendTestEmail() {
            this.sendingTestEmail = true;

            const cleanEmails = this.testEmail.split(',').map(email => email.trim()).filter(email => email.length > 0);

            try {
                await this.api.sendTestEmail(
                    this.modelValue.email_template_id,
                    this.modelValue.sender_local + '@' + this.modelValue.sender_domain,
                    this.modelValue.sender_name,
                    cleanEmails
                )
                this.toastNotification.notifySuccess(`Test Email Sent!`)
            } catch (err) {
                this.toastNotification.notifyError(`Failed to send Email`)
            }
            this.testEmail = null;

            this.sendingTestEmail = false;
        },
        handleToggle(value) {
            this.modelValue.sending_strategy = value ? this.marketingCampaignStrategies.sendingStrategies.DISTRIBUTED : this.marketingCampaignStrategies.sendingStrategies.DEFAULT;
        }
    }
}
</script>
