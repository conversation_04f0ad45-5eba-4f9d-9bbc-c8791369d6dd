<template>
    <loading-spinner v-if="loading" :dark-mode="darkMode"/>
    <div v-else class="flex flex-col gap-4 relative">
        <simple-card :dark-mode="darkMode">
            <div class="grid grid-cols-2 gap-4 p-4">
                <labeled-value label="Name">
                    {{ marketingCampaign.name }}
                </labeled-value>
                <div class="flex flex-col gap-2">
                    <labeled-value label="Status">
                        <marketing-campaign-badge
                            v-if="marketingCampaign.status"
                            :dark-mode="darkMode"
                            :status="marketingCampaign.status"
                        />
                    </labeled-value>
                </div>
                <div class="flex flex-col col-span-2 gap-2">
                    <div class="font-semibold text-sm">Update Description</div>
                    <textarea
                        class="flex flex-1 w-full border rounded pl-4  focus:outline-none focus:border focus:border-primary-500 pr-4 py-2"
                        placeholder="Enter a brief description..." type="text" v-model="marketingCampaign.description"
                        :class="{'border-grey-200': !darkMode, 'border-blue-700 bg-dark-background text-blue-400': darkMode}"
                    />
                </div>
            </div>
        </simple-card>
        <component
            v-if="marketingCampaignComposable.typeConfigurationComponentMap[marketingCampaign.type] && canEditConfiguration"
            :is="marketingCampaignComposable.typeConfigurationComponentMap[marketingCampaign.type]"
            :dark-mode="darkMode"
            v-model="marketingCampaign.configuration"
        />
        <simple-card :dark-mode="darkMode">
            <marketing-campaign-verification-type class="gap-4 p-4" :dark-mode="darkMode" v-model="marketingCampaign"/>
        </simple-card>
        <div class="flex flex-1 justify-end sticky bottom-0 bg-light-module pt-5">
            <custom-button @click="handleSave" :dark-mode="darkMode">Save</custom-button>
        </div>
    </div>
</template>
<script>
import ApiService from "./services/api.js";
import Dropdown from "../Shared/components/Dropdown.vue";
import SimpleAlert from "../Shared/components/SimpleAlert.vue";
import SimpleCard from "./SimpleCard.vue";
import CustomInput from "../Shared/components/CustomInput.vue";
import LoadingSpinner from "../Shared/components/LoadingSpinner.vue";
import useMarketingCampaign from "../../../composables/useMarketingCampaign.js";
import MarketingCampaignBadge from "./MarketingCampaignBadge.vue";
import MarketingCampaignVerificationType from "./MarketingCampaignVerificationType.vue";
import CustomButton from "../Shared/components/CustomButton.vue";
import LabeledValue from "../Shared/components/LabeledValue.vue";
import marketing from "./Marketing.vue";
const marketingCampaignComposable = useMarketingCampaign();

export default {
    name: "EditMarketingCampaignModelContent",
    components: {
        LabeledValue,
        CustomButton,
        MarketingCampaignVerificationType,
        MarketingCampaignBadge, LoadingSpinner, CustomInput, SimpleCard, SimpleAlert, Dropdown},
    props: {
        darkMode: {
            type: Boolean,
            default: false,
        },
        marketingCampaignId: {
            type: Number,
            required: true,
        }
    },
    emits: ['save'],
    data() {
        return {
            marketingCampaign: {
                id: null,
                name: null,
                status: null,
                description: null,
                validation_type: null,
                validation_type_inputs: {},
                type: null,
                configuration: {},
            },
            api: ApiService.make(),
            loading: true,
            campaignTypes: [],
            marketingCampaignComposable
        }
    },
    created() {
        this.loadData()
    },
    computed: {
        canEditConfiguration() {
            if (this.marketingCampaign.type === this.marketingCampaignComposable.types.DRIP_EMAIL) {
                return true;
            }

            return this.marketingCampaign.status === this.marketingCampaignComposable.statuses.DRAFT || this.marketingCampaign.status === this.marketingCampaignComposable.statuses.ACTIVE;
        }
    },
    methods: {
        async loadData() {
            this.loading = true
            await this.getMarketingCampaign()
            this.loading = false
        },
        async getMarketingCampaign() {
            const response = await this.api.getMarketingCampaign(this.marketingCampaignId);
            this.marketingCampaign = response.data.data.campaign;
        },
        async handleSave() {
            this.loading = true;
            await this.api.updateMarketingCampaign(this.marketingCampaignId, this.marketingCampaign)
            this.loading = false;
            this.$emit('save')
        }
    }

}
</script>
