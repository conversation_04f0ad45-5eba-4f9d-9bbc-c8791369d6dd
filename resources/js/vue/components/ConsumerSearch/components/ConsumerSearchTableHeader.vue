<template>
    <div class="px-5 grid gap-5 pb-3" :class="[darkMode ? 'text-slate-400' : 'text-slate-500', consumers.length > 5 ? 'mr-3' : '', gridCols]">
        <SortColumnHeader @sort="emitSortDate" class="table-header-text">ID / TYPE / DATE</SortColumnHeader>
        <p class="table-header-text">STATUS / ORIGIN</p>
        <p class="table-header-text">CONSUMER</p>
        <p class="table-header-text">ADDRESS</p>
        <p class="table-header-text">DETAILS</p>
        <p v-if="showComments" class="table-header-text">ACTIVITY</p>
        <p class="table-header-text col-span-2">COMPANIES SENT TO</p>
        <p v-if="hasAvailableBudget" class="table-header-text">AVAILABLE BUDGET/CAMPAIGNS</p>
    </div>
</template>

<script>
import SortColumnHeader from "./SortColumnHeader.vue";

export default {
    name: "ConsumerSearchTableHeader",
    components: {
        SortColumnHeader,
    },
    emits: ['sortDate'],
    props: {
        darkMode: {
            type: Boolean,
            default: false
        },
        consumers: {
            type: Array,
            default: []
        },
        showComments: {
            type: Boolean,
            default: false
        }
    },
    computed: {
        hasAvailableBudget() {
            return this.consumers[0]?.available_budget != null;
        },
        gridCols() {
            let cols = 7
            if (this.hasAvailableBudget) {
                cols++
            }
            if (this.showComments) {
                cols++
            }

            return `grid-cols-${cols}`
        }
    },
    methods: {
        emitSortDate(direction) {
            this.$emit("sortDate", direction);
        }
    }
}
</script>

<style scoped>
    .table-header-text {
        @apply uppercase text-xs font-bold rounded;
    }
</style>
