<script setup>
import Modal from "../../Shared/components/Modal.vue";
import CustomInput from "../../Shared/components/CustomInput.vue";
import {computed, defineEmits, onBeforeMount, ref, watch} from "vue";
import MarkdownEditor from "../../Shared/components/MarkdownEditor.vue";
import ApiService from "../api.js";
import LoadingSpinner from "../../Shared/components/LoadingSpinner.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import shortcodesPlugin from "../../Shared/includes/shortcodesPlugin.js";
import Dropdown from "../../Shared/components/Dropdown.vue";
import ToggleSwitch from "../../Shared/components/ToggleSwitch.vue";
import ShortcodeInputCached from "../../Shared/components/ShortcodeInputCached.vue";
import EmailRecipients from "./EmailRecipients.vue";
import { useUserStore} from "../../../../stores/user-store.js";
import useErrorHandler from "../../../../composables/useErrorHandler.js";

const props = defineProps({
    darkMode: false,
    emailModal: false,
    emailTemplateType: null,
    recipientId: null,
    recipientEmail: '',
    companyId: null
})

const emit = defineEmits(['close', 'flash-alert']);

const userStore = useUserStore();
const apiService = ApiService.make();
const subject = ref('')
const content = ref('')
const loading = ref(false);
const previewing = ref(false);
const shortcodes = ref([]);
const previewContent = ref('');
const previewSubject = ref('');
const editorPlugin = ref([]);
const templateName = ref('');
const savingTemplate = ref(false);
const emailTemplates = ref([]);
const selectedTemplate = ref(null);
const createNew = ref(false);
const imageTypes = ['png', 'jpg', 'jpeg'];
const cc = ref([]);
const bcc = ref([]);
const contacts = ref([])

onBeforeMount(() => {
    loading.value = true;
    apiService.getEmailContent(props.emailTemplateType, props.companyId)
        .then(resp => {
            content.value = resp.data.content;
            subject.value = resp.data.subject;
            contacts.value = resp.data.contacts;
            shortcodes.value = resp.data.shortcodes;
            emailTemplates.value = resp.data.templates;

            editorPlugin.value = [
                [shortcodesPlugin, shortcodes.value]
            ];
        })
        .catch(e => console.error(e))
        .finally(() => loading.value  = false);
});

const reportError = (message) => emit("flash-alert", 'error', message);
const reportSuccess = (message) => emit("flash-alert", 'success', message);
const errorHandler = useErrorHandler()


const sendEmail = () => {
    if (!content.value) {
        reportError('Email content cannot be empty');
        return;
    }

    if (!subject.value) {
        reportError('Email subject cannot be empty');
        return;
    }

    apiService.sendEmail(
        props.emailTemplateType,
        props.recipientId,
        props.recipientEmail,
        subject.value,
        content.value,
        cc.value?.map(c => c?.email?.trim()).filter(e => e?.length > 0),
        bcc.value?.map(c => c?.email?.trim()).filter(e => e?.length > 0)
    )
        .then(resp => {
            if (resp.data?.message) {
                reportError(resp.data?.message)
            } else {
                reportSuccess('Email sent')
            }
            emit('close');
        })
        .catch(e => {
            errorHandler.handleError(e)

            reportError('Failed to send. ' + (errorHandler?.message?.value ?? ''));
            console.error(e);
        });
}

const preview = () => {
    previewContent.value = 'Generating preview...';
    previewSubject.value = 'Generating preview...';

    apiService.getEmailPreview(props.emailTemplateType, props.recipientId, props.recipientEmail, subject.value, content.value)
        .then(resp => {
            previewContent.value = resp.data.content;
            previewSubject.value = resp.data.subject;
        })
        .catch(e => {
            reportError('Failed to generate preview');
            console.error(e);
        });
}

const templateOptions = computed(() => emailTemplates.value.map(template => {
    return {id: template.id, name: template.name}
}));
const selectedTemplateName = computed(() => emailTemplates.value.find(emailTemplate => emailTemplate.id === selectedTemplate.value)?.name);

watch(() => previewing.value, () => {
    if (previewing.value) {
        preview();
    }
});

const createEmailTemplate = () => {
    if (!templateName.value) {
        reportError('Please enter a template name');
        return;
    }

    savingTemplate.value = true;

    apiService.saveEmailTemplate(props.emailTemplateType, subject.value, content.value, templateName.value)
        .then((resp) => {
            emailTemplates.value.push(resp.data.template);
            createNew.value = false;
        })
        .catch(e => {
            reportError('Failed to save email template.');
            console.error(e);
        })
        .finally(() => savingTemplate.value = false);
}

const updateEmailTemplate = () => {
    if (!selectedTemplate.value) {
        return;
    }

    savingTemplate.value = true;

    apiService.saveEmailTemplate(props.emailTemplateType, subject.value, content.value, selectedTemplateName.value, selectedTemplate.value)
        .then(() => {
            const template = emailTemplates.value.find(t => t.id === selectedTemplate.value);

            if (template) {
                template.content = content.value;
                template.subject = subject.value;
            }
        })
        .catch(e => {
            reportError('Failed to save email template.');
            console.error(e);
        })
        .finally(() => savingTemplate.value = false);
}

const loadTemplate = () => {
    const template = emailTemplates.value.find(emailTemplate => emailTemplate.id === selectedTemplate.value);

    if (!template) {
        return;
    }

    content.value = template.content;
    subject.value = template.subject;
    previewing.value = false;
}

const syncMailbox = () => {
    window.open('/dashboard')
}

</script>

<template>
    <modal
        v-if="props.emailModal"
        :dark-mode="darkMode"
        @close="emit('close')"
        :no-buttons="true"
        :no-min-height="true"
        :content-max-height="'max-h-[90vh]'"
    >
        <template #header>
            <p class="font-semibold">Email {{props.recipientEmail}}</p>
            <div v-if="!userStore.user.has_enabled_mailbox" class="text-red-500 p-2">
                Your mailbox is not synced and so this email will not appear in your sent items. Click <span @click="syncMailbox">here</span> to sync your mailbox.
            </div>
        </template>
        <template #content>
            <div v-if="loading">
                <LoadingSpinner/>
            </div>
            <div class="flex justify-between items-center mb-5" v-if="!loading">
                <div class="mr-auto flex gap-5">
                    <dropdown :placeholder="'Select Template'" :options="templateOptions" v-model="selectedTemplate" :dark-mode="darkMode" class="min-w-[18rem]"
                              :disabled="savingTemplate"></dropdown>
                    <custom-button :color="'primary-outline'" @click="loadTemplate" :dark-mode="darkMode" :disabled="savingTemplate">Load</custom-button>
                </div>
                <div class="ml-auto flex gap-5" v-if="createNew">
                    <custom-input v-model="templateName" :dark-mode="darkMode" :placeholder="'Template Name'"></custom-input>
                    <custom-button :color="'primary-outline'" @click="createEmailTemplate" :disabled="savingTemplate" :dark-mode="darkMode">Save</custom-button>
                    <custom-button :color="'slate-outline'" @click="createNew = false" :dark-mode="darkMode">Cancel</custom-button>
                </div>
                <div class="ml-auto flex gap-5" v-else>
                    <custom-button :color="'primary-outline'" @click="updateEmailTemplate" :disabled="savingTemplate" :dark-mode="darkMode" v-if="selectedTemplate">
                        Update Template
                    </custom-button>
                    <custom-button :color="'primary-outline'" @click="createNew = true" :disabled="savingTemplate" :dark-mode="darkMode">Save as New Template</custom-button>
                </div>
            </div>
            <div class="grid gap-4" v-if="!loading">
                <div class="grid grid-cols-1 text-sm items-center gap-2">
                    <EmailRecipients v-model="cc" label="CC" :dark-mode="darkMode" :contacts="contacts" :disabled="previewing"></EmailRecipients>
                    <EmailRecipients v-model="bcc" label="BCC" :dark-mode="darkMode" :contacts="contacts" :disabled="previewing"></EmailRecipients>
                </div>
                <div>
                    <label class="inline-flex mb-1 text-sm font-semibold">
                        Subject
                    </label>
                    <p v-if="previewing">{{ previewSubject }}</p>
                    <shortcode-input-cached :all-shortcodes="shortcodes" :dark-mode="darkMode" v-model="subject" v-else></shortcode-input-cached>
                </div>
                <div>
                    <div class="flex justify-between items-center">
                        <label class="inline-flex mb-1 text-sm font-semibold">
                            Content
                        </label>
                    </div>
                    <iframe v-if="previewing" :srcdoc="previewContent" class="w-full h-[40vh]"></iframe>
                    <markdown-editor
                        v-model:content="content"
                        :dark-mode="darkMode"
                        rows="8"
                        :hide-tabs="true"
                        v-else
                        :additional-plugins="editorPlugin"
                        :images="{}"
                        :mime-types="imageTypes"
                    ></markdown-editor>
                </div>
                <div class="flex justify-between items-center">
                    <div class="my-3 flex gap-2 items-center">
                        <toggle-switch v-model="previewing"/>Preview
                    </div>
                    <div class="flex gap-3">
                        <custom-button :color="'primary'" @click="sendEmail">Send Email</custom-button>
                        <custom-button :color="'slate-light'" @click="emit('close')">Close</custom-button>
                    </div>
                </div>
            </div>
        </template>
    </modal>
</template>

<style scoped>

</style>
