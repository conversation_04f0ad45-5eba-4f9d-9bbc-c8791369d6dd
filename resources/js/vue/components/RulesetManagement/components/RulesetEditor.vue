<template>
    <div
        class="border col-span-2 rounded-lg"
        :class="{'bg-light-background  border-light-border': !darkMode, 'bg-dark-40 border-dark-border': darkMode}"
    >
        <div class="p-5 border-b grid grid-cols-3 gap-3" :class="{'border-light-border': !darkMode, 'border-dark-border': darkMode}">
            <div class="flex flex-col gap-3">
                <div class="flex flex-col gap-3">
                    <div class="w-full flex flex-col">
                        <label class="text-sm font-medium">
                            Name
                        </label>
                        <text-field v-model="ruleset.name" :dark-mode="darkMode" height="h-9"/>
                    </div>
                </div>
                <div class="flex flex-col gap-3">
                    <div class="w-full flex flex-col">
                        <label class="text-sm font-medium">
                            Ruleset type
                        </label>
                        <Dropdown
                            v-model="ruleset.type"
                            :dark-mode="darkMode"
                            placeholder="Type"
                            :options="['ranking', 'filter']"
                            class="h-9"
                        />
                    </div>
                </div>
            </div>
            <ruleset-data-filter
                class="col-span-2"
                :source="ruleset.source"
                :filter="ruleset.filter"
                :dark-mode="darkMode"
                :us-states="usStates"
                @update:source="v => ruleset.source = v"
                @update:filter="v => ruleset.filter = v"
            />
        </div>
        <div class="grid grid-cols-3 max-h-[50vh]">
            <div class="p-5 pr-2 border-r overflow-y-scroll max-h-[50vh]" :class="{'border-light-border': !darkMode, 'border-dark-border': darkMode}">
                <div
                    v-for="(rule, index) in editingRuleset.rules"
                    :key="index"
                    class="relative border flex flex-col rounded p-2 my-2 cursor-pointer hover:border-primary-400 hover:bg-primary-500 hover:text-white"
                    :class="[selectedRuleIndex === index ? 'bg-primary-500 border-primary-400 text-white'
                        : darkMode ? 'bg-dark-module border-dark-border'
                            : 'bg-light-module border-light-border',
                        !rule.is_active ? 'opacity-30' : ''
                    ]"
                    @click="(ev) => selectRule(ev, index)"
                >
                    <div class="grid items-center grid-cols-3 gap-2 justify-between">
                        <div class="flex flex-col gap-1 col-span-2">
                            <div class="flex items-center gap-2">
                                <input
                                    type="checkbox"
                                    class="rounded"
                                    :class="[darkMode ? 'bg-dark-background border-dark-border' : 'bg-light-background  border-light-border']"
                                    :checked="rule.is_active"
                                    @change="toggleRule(index)"
                                />
                                <p>
                                    {{ $filters.toProperCase(rule.data.label) }}
                                </p>
                            </div>
                            <p v-if="rule.data.description" class="text-sm font-light ml-6">{{ rule.data.description }}</p>
                        </div>
                        <div class="flex justify-end pr-5">
                            <custom-input
                                v-if="ruleset.type === 'ranking'"
                                input-classes="w-[4rem] text-center col-span-1"
                                :dark-mode="darkMode"
                                type="number"
                                v-model="rule.data.rule_data.max_points"
                                @change="recalculateTotalPoints"
                            />
                        </div>
                    </div>

                    <div
                        v-if="selectedRuleIndex === index"
                        class="absolute right-[-0.5rem] top-[50%] translate-y-[-50%] w-0 h-0 border-8 border-r-0 border-primary-500 border-t-transparent border-b-transparent"
                    />
                </div>
                <div class="pt-4 mx-auto" v-if="ruleset.type === 'ranking'">
                    <div class="flex items-center justify-center border rounded-lg gap-x-4 w-[75%] p-2 mx-auto"
                         :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']"
                    >
                        <p>Total <span class="text-[1.2rem]">{{ localTotalPoints }}</span> points.</p>
                    </div>
                </div>
            </div>
            <div class="col-span-2 p-4 overflow-y-scroll max-h-[50vh]">
                <div v-if="selectedRuleIndex != null">
                    <div class="flex flex-col items-center justify-center mb-2 p-4 gap-2">
                        <h4 class="font-semibold">{{ $filters.toProperCase(editingRuleset.rules[selectedRuleIndex].data.label) }} <span v-if="ruleset.type === 'ranking'">- {{ editingRuleset.rules[selectedRuleIndex].data.max_points }} points</span></h4>
                        <p v-if="ruleset.type === 'ranking'">Distribute a max of {{ editingRuleset.rules[selectedRuleIndex].data.max_points }} points in each condition</p>
                        <p v-else-if="ruleset.type === 'filter'">Create your custom query using the available operations</p>
                        <p class="text-sm font-light" v-if="editingRuleset.rules[selectedRuleIndex].data.description">{{ editingRuleset.rules[selectedRuleIndex].data.description }}</p>
                    </div>
                    <div
                    >
                        <div v-if="editingRuleset.rules[selectedRuleIndex].data.rule_data.conditions.length < 1">
                            No Conditions for Rule.
                        </div>
                        <div v-else>
                            <div
                                v-for="(condition, index) in editingRuleset.rules[selectedRuleIndex].data.rule_data.conditions"
                                :key="Math.random()"
                                class="p-2 border rounded flex flex-col mb-2"
                                :class="[darkMode ? 'bg-dark-module border-dark-border' : 'bg-light-module border-light-border']"
                            >
                                <ruleset-condition
                                    :rule="editingRuleset.rules[selectedRuleIndex]"
                                    :ruleset-type="ruleset.type"
                                    :conditions="editingRuleset.rules[selectedRuleIndex].data.rule_data.conditions"
                                    :condition="condition"
                                    :condition-index="index"
                                    :dark-mode="darkMode"
                                    :available-variables="availableRuleVariables[selectedRuleIndex]"
                                    @delete-condition="deleteCondition(index)"
                                />
                            </div>
                        </div>
                        <custom-button
                            color="primary"
                            @click="addCondition"
                            :icon="true"
                            :dark-mode="darkMode"
                        >
                            <template v-slot:icon>
                                <svg  class="w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 4.5v15m7.5-7.5h-15" />
                                </svg>
                            </template>
                            <p class="ml-2">Add Condition</p>
                        </custom-button>
                    </div>
                </div>
            </div>
        </div>
        <div class="p-5 border-t" :class="{'border-light-border': !darkMode, 'border-dark-border': darkMode}">
            <div class="flex items-center gap-x-6 justify-center relative">
                <custom-button
                    @click="cancelRuleset"
                    color="slate-outline"
                >
                    Cancel
                </custom-button>
                <custom-button
                    @click="saveRuleset"
                >
                    Save
                </custom-button>
            </div>
        </div>
    </div>
</template>

<script>
import CustomInput from "../../Shared/components/CustomInput.vue";
import Tooltip from "../../Shared/components/Tooltip.vue";
import CustomButton from "../../Shared/components/CustomButton.vue";
import CustomInlineInput from "../../Shared/components/CustomInlineInput.vue";
import RulesetCondition from "../conditions/RulesetCondition.vue";
import RulesetDataFilter from "./RulesetDataFilter/RulesetDataFilter.vue";
import Dropdown from "../../Shared/components/Dropdown.vue";
import TextField from "../../IndustryManagement/WebsiteManagement/ApiKeys/components/TextField.vue";

export default {
    name: "RulesetEditor",
    components: {
        TextField,
        Dropdown,
        RulesetDataFilter,
        CustomInlineInput,
        CustomButton,
        Tooltip,
        CustomInput,
        RulesetCondition
    },
    props: {
        api: {
            type: Object,
            required: true,
        },
        darkMode: {
            type: Boolean,
            default: false
        },
        ruleset: {
            type: Object,
            default: {},
        },
        usStates: {
            type: Array,
            required: true
        }
    },
    emits: [
        'ruleset:cancel',
        'ruleset:save',
    ],
    data() {
        return {
            rulesetDataFilter: {},
            editingRuleset: null,
            hasUnsavedChanges: false,
            selectedRuleIndex: 0,
            localTotalPoints: 0,
            availableRuleVariables: [],
        }
    },
    beforeMount() {
        this.editingRuleset = this.ruleset ?? {};
        this.transformAvailableVariables();
    },
    mounted() {
        this.recalculateTotalPoints();
    },
      methods: {
        selectRule(ev, ruleIndex) {
            if (ev.target.tagName === 'INPUT') return;
            this.selectedRuleIndex = ruleIndex;
        },
        toggleRule(ruleIndex) {
            this.editingRuleset.rules[ruleIndex].is_active = !this.editingRuleset.rules[ruleIndex].is_active;
            this.recalculateTotalPoints();
        },
        addCondition() {
            this.editingRuleset.rules[this.selectedRuleIndex].data.rule_data.conditions.push(JSON.parse(JSON.stringify(this.editingRuleset.rules[this.selectedRuleIndex].data.rule_data.conditions[0])))
        },
        cancelRuleset() {
            this.$emit('ruleset:cancel');
        },
        saveRuleset() {
            this.$emit('ruleset:save');
        },
        deleteCondition(conditionIndex) {
            if (this.editingRuleset.rules[this.selectedRuleIndex].data.rule_data.conditions.length === 1) return;

            this.editingRuleset.rules[this.selectedRuleIndex].data.rule_data.conditions.splice(conditionIndex, 1);
        },
        transformAvailableVariables() {
            this.availableRuleVariables = this.ruleset.rules.map(rule => {
                return rule.data?.rule_reference?.available_variables
                    ? Object.entries(rule.data.rule_reference.available_variables).map(([value, name]) => ({ value, name }))
                    : [{ value: 'value', name: 'value' }];
            });
        },
        recalculateTotalPoints() {
            this.localTotalPoints = this.editingRuleset.rules.reduce((output, rule) => rule.is_active ? output + parseInt(rule.data.rule_data.max_points) : output, 0);
        },
    },
}

</script>
