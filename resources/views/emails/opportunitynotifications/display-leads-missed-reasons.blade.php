@if(isset($missedProductData['total_available']))
<!--[if mso | IE]><table role="presentation" border="0" cellpadding="0" cellspacing="0"><tr><td class="" style="vertical-align:top;width:510px;" ><![endif]-->
<table border="0" cellpadding="0" cellspacing="0" role="presentation" style="vertical-align:top;" width="100%">
    <tbody>
    <tr>
        <td align="center" style="font-size:0px;padding:10px 25px;word-break:break-word;">
            <div style="font-family:Helvetica Neue;font-size:16px;line-height:1.3;text-align:center;color:#525252;">
                <p style="margin:0">There were <strong>{{$missedProductData['total_available']}}</strong> unsold or undersold leads in your areas and industries in the last week</p>
            </div>
        </td>
    </tr>
    </tbody>
</table>
@endif

@foreach ($missedProductData['campaigns'] as $campaignSummary)
    @if($campaignSummary['campaign_total'] > 0)
        <!-- 2 columns section -->
        <!--[if mso | IE]><table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="presentation" style="width:570px;" width="570" bgcolor="white" ><tr><td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->
        <div style="background:white;background-color:white;margin:0px auto;max-width:570px;">
            <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="background:white;background-color:white;width:100%;">
                <tbody>
                <tr>
                    <td style="direction:ltr;font-size:0px;padding:20px 0;text-align:center;">
                        <!--[if mso | IE]><table role="presentation" border="0" cellpadding="0" cellspacing="0"><tr><td class="" style="vertical-align:top;width:510px;" ><![endif]-->
                        <div class="mj-column-px-510 mj-outlook-group-fix" style="font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;">
                            <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="vertical-align:top;" width="100%">
                                <tbody>
                                <tr>
                                    <td align="center" style="font-size:0px;padding:10px 25px;word-break:break-word;">
                                        <div style="font-family:Helvetica Neue;font-size:13px;line-height:1.3;text-align:center;color:#525252;">
                                            <h2 style="margin:0">{{ $campaignSummary['industry'] }} Campaign "{{ $campaignSummary['name'] }}" missed out on {{ $campaignSummary['campaign_total'] }} {{ $campaignSummary['product'] }}{{ $campaignSummary['campaign_total'] > 1 ? "s" : '' }}</h2>
                                        </div>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <!--[if mso | IE]></td></tr></table><![endif]-->
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <!--[if mso | IE]></td></tr></table><![endif]-->

        <!--[if mso | IE]><table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="presentation" style="width:570px;" width="570" bgcolor="white" ><tr><td style="line-height:0px;font-size:0px;mso-line-height-rule:exactly;"><![endif]-->

        @if(count($campaignSummary['preview']) > 0)
        <div style="background:white;background-color:white;margin:0px auto;max-width:570px;">
            <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="background:white;background-color:white;width:100%;">
                <tbody>
                <tr>
                    <td style="direction:ltr;font-size:0px;padding:20px 0;text-align:center;">
                        <!--[if mso | IE]><table role="presentation" border="0" cellpadding="0" cellspacing="0"><tr><![endif]-->
                        <!-- Left image -->
                        <!--[if mso | IE]><td class="" style="vertical-align:top;width:165.3px;" ><![endif]-->
                        @php
                            $roofImageUrlField = array_filter($campaignSummary['preview'][0]['fields'], function ($field) {
                                return $field['field'] === 'roof_image_url';
                            });

                            if (count($roofImageUrlField)) {
                                $roofImageUrl = reset($roofImageUrlField)['value'] ?? '';
                                unset($campaignSummary['preview'][0]['fields'][key($roofImageUrlField)]);
                            }
                        @endphp
                        @if(!empty($roofImageUrl))
                            <div class="opn-column-37 mj-outlook-group-fix opn-h-125" style="font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%; height: auto">
                                <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%" style="height: 100%; width: 100%">
                                    <tbody>
                                    <tr class="opn-hide-desktop">
                                        <td align="left" style="font-size:0px;padding:0;word-break:break-word;">
                                            <div style="text-align:left;margin-bottom: 5px">
                                                @if ($campaignSummary['preview'][0]['reason'] ?? false)
                                                <div style="font-family:inter, Helvetica, Arial, sans-serif;font-weight: 700; font-size: 18px; line-height: 19px; color: #1A2530">Missed lead reason: {{ $campaignSummary['preview'][0]['reason'] }}</div>
                                                @endif
                                            </div>
                                        </td>
                                    </tr>
                                    <tr  style="border-top:solid 1px #94A3B8;font-size:1px" class="opn-hide-desktop">
                                        <td>&nbsp;</td>
                                    </tr>
                                    <tr>
                                        <td style="vertical-align:top;">
                                            <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="height: 100%; width: 100%" width="100%">
                                                <tbody>
                                                <tr>
                                                    <td align="center" style="font-size:0px;word-break:break-word;" class="opn-px-25">
                                                        <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="border-collapse:collapse;border-spacing:0px; height: 100%; width: 100%">
                                                            <tbody>
                                                            <tr>
                                                                <td style="padding-top: 12px;">
                                                                    <img src="{{ $roofImageUrl }}" style="border:0;display:block;outline:none;text-decoration:none;font-size:13px; width: auto; border-radius: 5px;"/>
                                                                </td>
                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            @endif
                            <!--[if mso | IE]></td><![endif]-->
                            <!-- right paragraph -->
                            <!--[if mso | IE]><td class="" style="vertical-align:top;width:393.3px;" ><![endif]-->
                            <div class="opn-column-63 mj-outlook-group-fix" style="font-size:0px;text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;">
                                <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%">
                                    <tbody>
                                    <tr>
                                        <td style="vertical-align:top;padding:10px 0px;">
                                            <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="" width="100%">
                                                <tbody>
                                                <tr class="show-on-desktop">
                                                    <td align="left" style="font-size:0px;padding:0;word-break:break-word;">
                                                        <div style="text-align:left;margin-bottom: 5px">
                                                            @if ($campaignSummary['preview'][0]['reason'] ?? false)
                                                            <div style="font-family:Inter, Helvetica, Arial, sans-serif;font-weight: 700; font-size: 18px; line-height: 19px; color: #1A2530">Missed reason: {{ $campaignSummary['preview'][0]['reason'] }}</div>
                                                            @endif
                                                        </div>
                                                    </td>
                                                </tr>
                                                <tr style="border-top:solid 1px #94A3B8;font-size:1px;"  class="show-on-desktop">
                                                    <td>&nbsp</td>
                                                </tr>
                                                <tr>
                                                    <td align="left" style="font-size:0; padding-top: 10px; word-break:break-word;">
                                                        <table class="show-on-mobile" cellpadding="0" cellspacing="0" width="100%" border="0" style="color:#000000;font-family:Ubuntu, Helvetica, Arial, sans-serif;font-size:12px;line-height:22px;table-layout:auto;width:100%;border:none;">
                                                            @foreach($campaignSummary['preview'][0]['fields'] as $field)
                                                                <tr>
                                                                    <td style="padding: 0 15px 0 0; font-family:lato, Helvetica, Arial, sans-serif; font-size: 14px; line-height: 24px; color: #1A2530; vertical-align: top;">
                                                                        <div style="font-weight: 600; padding-top: 10px" class="opn-pt-0">
                                                                            {{$field['title']}}:
                                                                        </div>
                                                                        <div style="font-weight: 300">
                                                                            {{ $field['value'] ?? '-' }}
                                                                        </div>
                                                                    </td>
                                                                </tr>

                                                            @endforeach
                                                        </table>
                                                        <table class="show-on-desktop" cellpadding="0" cellspacing="0" width="100%" border="0" style="color:#000000;font-family:Ubuntu, Helvetica, Arial, sans-serif;font-size:12px;line-height:22px;table-layout:auto;width:100%;border:none;">
                                                            @foreach($campaignSummary['preview'][0]['fields'] as $field)
                                                                <td style="font-family:lato, Helvetica, Arial, sans-serif; font-size: 14px; line-height: 24px; color: #1A2530; width:49%; vertical-align: top; display: inline-block;">
                                                                    <div style="font-weight: 600">
                                                                        {{$field['title']}}:
                                                                    </div>
                                                                    <div style="font-weight: 300">
                                                                        {{ $field['value'] ?? '-' }}
                                                                    </div>
                                                                </td>
                                                            @endforeach
                                                        </table>
                                                    </td>
                                                </tr>
                                                </tbody>
                                            </table>
                                        </td>
                                    </tr>
                                    </tbody>
                                </table>
                            </div>
                            <!--[if mso | IE]></td></tr></table><![endif]-->
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <!--[if mso | IE]></td></tr></table><![endif]-->
        @endif

        <!--[if mso | IE]><table align="center" border="0" cellpadding="0" cellspacing="0" class="" role="presentation" style="width:570px;" width="570" bgcolor="white" ><tr><td style="line-height:0px;font-size:12px;mso-line-height-rule:exactly;"><![endif]-->
        <div style="background:white;background-color:white;margin:0px auto;max-width:570px;font-size:0">
            <table align="center" border="0" cellpadding="0" cellspacing="0" role="presentation" style="background:white;background-color:white;width:100%;">
                <tbody>
                <tr>
                    <td style="direction:ltr;text-align:center;font-size:0">
                        <!--[if mso | IE]><table role="presentation" border="0" cellpadding="0" cellspacing="0"><tr><td class="" style="vertical-align:top;width:570px;" ><![endif]-->
                        <div class="mj-column-per-100 mj-outlook-group-fix" style="text-align:left;direction:ltr;display:inline-block;vertical-align:top;width:100%;">
                            <table border="0" cellpadding="0" cellspacing="0" role="presentation" width="100%">
                                <tbody>
                                <tr>
                                    <td style="vertical-align:top;font-size:0">
                                        <table border="0" cellpadding="0" cellspacing="0" role="presentation" style="" width="100%">
                                            <tbody>
                                            <tr>
                                                <td align="left" vertical-align="middle" style="padding:10px 25px;word-break:break-word;">
                                                    @if ($campaignSummary['campaign_total'] > 0)
                                                        @foreach ($campaignSummary['summary'] as $reason)
                                                            @if ($reason['products'] > 0)
                                                                <p style="font-size: 18px; text-align:center;">• {{ $reason['products'] }} products missed because {{ $reason['title'] ?? 'the campaign was unavailable' }}.</p>
                                                            @endif
                                                        @endforeach
                                                    @endif
                                                </td>
                                            </tr>
                                            </tbody>
                                        </table>
                                    </td>
                                </tr>
                                </tbody>
                            </table>
                        </div>
                        <!--[if mso | IE]></td></tr></table><![endif]-->
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
        <!--[if mso | IE]></td></tr></table><![endif]-->
    @endif
@endforeach