<?php

namespace Tests\Feature\Auth;

use App\Models\Odin\Website;
use App\Models\Odin\WebsiteApiKey;
use App\Models\Odin\WebsiteApiKeyOrigin;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Tests\TestCase;

class ExternalSiteApiKeysTest extends TestCase
{
    use RefreshDatabase;

    public function test_site_key_middleware_check()
    {
        $website = Website::factory()->create();

        $websiteApikey = WebsiteApiKey::factory()->create(
            [WebsiteApiKey::FIELD_WEBSITE_ID => $website->{Website::FIELD_ID}]
        );

        $websiteApikeyOrigin = WebsiteapiKeyOrigin::factory()->create(
            [
                WebsiteapiKeyOrigin::FIELD_WEBSITE_API_KEY_ID => $websiteApikey->{WebsiteApikey::FIELD_ID},
                WebsiteapiKeyOrigin::FIELD_ORIGIN => '*',
            ]
        );

        $response = $this->get('/api/industries',
            [
                'Authorization' => $websiteApikey->{WebsiteApiKey::FIELD_KEY},
                'origin' => $websiteApikeyOrigin->{WebsiteApiKeyOrigin::FIELD_ORIGIN},
            ]
        );

        $response->assertStatus(200);
    }
}
