<?php

namespace Tests\Feature\Endpoints\PrivacyRequests;

use App\Enums\PrivacyManagement\PrivacyRequestStatuses;
use App\Jobs\PrivacyRequestRedactJob;
use App\Jobs\PrivacyRequestSearchJob;
use App\Models\PrivacyRequest;
use App\Models\Role;
use App\Models\User;
use Database\Seeders\PermissionsSeeder;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Queue;
use Tests\TestCase;

use function fake;

class PrivacyManagementTest extends TestCase
{
    protected function setUp(): void
    {
        parent::setUp(); // TODO: Change the autogenerated stub
        $this->markTestSkipped('Temporarily disabled for launch');
        $this->seed(PermissionsSeeder::class);
        $user = User::factory()->create();

        $adminRole = Role::findByName('admin');

        $user->assignRole($adminRole);

        $this->actingAs($user);
    }

    public function test_can_get_privacy_requests(): void
    {
        $privacyRequests = PrivacyRequest::factory()
            ->count(10)
            ->create();

        $response = $this->get('internal-api/v1/privacy-management')
            ->assertSuccessful()
            ->assertJsonCount(10, 'data');

        foreach ($privacyRequests as $privacyRequest) {
            $response->assertJsonFragment(['uuid' => $privacyRequest->uuid]);
        }
    }

    public function test_can_filter_privacy_requests(): void
    {
        $pendingPrivacyRequests = PrivacyRequest::factory()
            ->count(10)
            ->create([
                PrivacyRequest::FIELD_STATUS => PrivacyRequestStatuses::PENDING,
            ]);

        $privacyRequests = PrivacyRequest::factory()
            ->count(10)
            ->create([
                PrivacyRequest::FIELD_STATUS => fake()->randomElement([
                    PrivacyRequestStatuses::INITIAL,
                    PrivacyRequestStatuses::DONE,
                ]),
            ]);

        $response = $this->get('internal-api/v1/privacy-management?'.Arr::query([
            'status' => PrivacyRequestStatuses::PENDING->value,
        ]))
            ->assertSuccessful()
            ->assertJsonCount(10, 'data');

        foreach ($pendingPrivacyRequests as $privacyRequest) {
            $response->assertJsonFragment(['uuid' => $privacyRequest->uuid]);
        }

        foreach ($privacyRequests as $privacyRequest) {
            $response->assertJsonMissing(['uuid' => $privacyRequest->uuid]);
        }
    }

    public function test_can_search_privacy_requests(): void
    {
        $email = fake()->email();

        $privacyRequest = PrivacyRequest::factory()
            ->create([
                'payload' => [
                    'first_name' => fake()->firstName(),
                    'last_name' => fake()->lastName(),
                    'email' => $email,
                    'phone' => fake()->phoneNumber(),
                    'description' => fake()->paragraph(),
                    'address' => fake()->address(),
                ],
            ]);

        $privacyRequests = PrivacyRequest::factory()
            ->count(10)
            ->create();

        $response = $this->get('internal-api/v1/privacy-management?'.Arr::query([
            'search' => $email,
        ]))
            ->assertSuccessful()
            ->assertJsonCount(1, 'data')
            ->assertJsonFragment(['uuid' => $privacyRequest->uuid]);

        foreach ($privacyRequests as $privacyRequest) {
            $response->assertJsonMissing(['uuid' => $privacyRequest->uuid]);
        }
    }

    public function test_can_get_privacy_request(): void
    {
        $privacyRequest = PrivacyRequest::factory()
            ->create();

        $privacyRequests = PrivacyRequest::factory()
            ->count(10)
            ->create();

        $response = $this->get("internal-api/v1/privacy-management/{$privacyRequest->id}?".Arr::query([
            'request_id' => $privacyRequest->id,
        ]))
            ->assertSuccessful()
            ->assertJsonFragment(['uuid' => $privacyRequest->uuid])
            ->assertJsonFragment(['email' => $privacyRequest->payload['email']]);

        foreach ($privacyRequests as $privacyRequest) {
            $response->assertJsonMissing(['uuid' => $privacyRequest->uuid]);
        }
    }

    public function test_can_create_privacy_request(): void
    {
        $privacyRequest = PrivacyRequest::factory()
            ->make();

        $this->post('internal-api/v1/privacy-management', [
            'first_name' => $privacyRequest->payload['first_name'],
            'last_name' => $privacyRequest->payload['last_name'],
            'email' => $privacyRequest->payload['email'],
            'phone' => $privacyRequest->payload['phone'],
            'address' => $privacyRequest->payload['address'],
            'description' => $privacyRequest->payload['description'],
            'source' => $privacyRequest->source,

        ])
            ->assertSuccessful()
            ->assertJsonCount(2, 'data')
            ->assertJsonFragment([
                'status' => true,
                'message' => 'Successfully created new privacy request.',
            ]);

        $payload = $privacyRequest->payload;

        $this->assertDatabaseHas(PrivacyRequest::class, [
            PrivacyRequest::FIELD_APPROVED_BY_ID => null,
            PrivacyRequest::FIELD_PAYLOAD.'->'.PrivacyRequest::JSON_PAYLOAD_FIELD_FIRST_NAME => $payload['first_name'],
            PrivacyRequest::FIELD_PAYLOAD.'->'.PrivacyRequest::JSON_PAYLOAD_FIELD_LAST_NAME => $payload['last_name'],
            PrivacyRequest::FIELD_PAYLOAD.'->'.PrivacyRequest::JSON_PAYLOAD_FIELD_EMAIL => $payload['email'],
            PrivacyRequest::FIELD_PAYLOAD.'->'.PrivacyRequest::JSON_PAYLOAD_FIELD_PHONE => $payload['phone'],
            PrivacyRequest::FIELD_PAYLOAD.'->'.PrivacyRequest::JSON_PAYLOAD_FIELD_ADDRESS => $payload['address'],
            PrivacyRequest::FIELD_PAYLOAD.'->'.PrivacyRequest::JSON_PAYLOAD_FIELD_DESCRIPTION => $payload['description'],
            PrivacyRequest::FIELD_STATUS => PrivacyRequestStatuses::INITIAL->value,
            PrivacyRequest::FIELD_SOURCE => $privacyRequest->source,
        ]);
    }

    public function test_can_start_system_scan(): void
    {
        $privacyRequest = PrivacyRequest::factory()
            ->create();

        Queue::fake();

        $this->post("internal-api/v1/privacy-management/scan/$privacyRequest->id")
            ->assertSuccessful()
            ->assertJsonCount(2, 'data')
            ->assertJsonFragment([
                'status' => true,
                'message' => 'Scanning Database for PPI, may take a few minutes.',
            ]);

        Queue::assertPushed(
            PrivacyRequestSearchJob::class,
            fn ($job) => $job->privacyRequest->id === $privacyRequest->id
        );
    }

    public function test_can_start_redact_process(): void
    {
        $privacyRequest = PrivacyRequest::factory()
            ->create();

        Queue::fake();

        $this->post("internal-api/v1/privacy-management/redact/$privacyRequest->id")
            ->assertSuccessful()
            ->assertJsonCount(2, 'data')
            ->assertJsonFragment([
                'status' => true,
                'message' => 'Redacting PPI From Database, may take a few minutes.',
            ]);

        Queue::assertPushed(
            PrivacyRequestRedactJob::class,
            fn ($job) => $job->privacyRequest->id === $privacyRequest->id
        );
    }
}
