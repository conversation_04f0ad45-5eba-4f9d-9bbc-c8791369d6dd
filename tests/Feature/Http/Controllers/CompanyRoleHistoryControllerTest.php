<?php

namespace Tests\Feature\Http\Controllers;

use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Role;
use Tests\TestCase;

class CompanyRoleHistoryControllerTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function history_with_empty_data(): void
    {
        $this->actingAs(User::factory()->create())
            ->get(route('internal-api.v1.companies.history.role', [
                'company' => Company::factory()->createQuietly()->id,
                'role' => Role::findOrCreate('account-manager')->name,
            ]))
            ->assertOk()
            ->assertJson([
                'data' => [],
            ]);
    }

    #[Test, DataProvider('roles')]
    public function history_with_full_data(string $role): void
    {
        $company = Company::factory()->createQuietly();

        $user = User::factory()->createQuietly();

        $role = Role::findOrCreate($role);

        $company->assign($user)->as($role->name);

        $this->actingAs(User::factory()->create())
            ->get(route('internal-api.v1.companies.history.role', [
                'company' => $company->id,
                'role' => $role->name,
            ]))
            ->assertOk()
            ->assertJsonStructure([
                'data' => [
                    '*' => [
                        'title',
                        'date',
                        'payload',
                    ],
                ],
            ])
            ->assertJsonPath('data.0.title', "Assigned to $user->name")
            ->assertJsonPath('data.0.payload.0', 'Author: System');
    }

    public static function roles(): array
    {
        return [
            'account-manager' => ['account-manager'],
            'business-development-manager' => ['business-development-manager'],
            'customer-success-manager' => ['customer-success-manager'],
            'onboarding-manager' => ['onboarding-manager'],
            'sales-development-representative' => ['sales-development-representative'],
        ];
    }
}
