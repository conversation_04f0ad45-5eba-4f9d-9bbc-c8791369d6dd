<?php

namespace Tests\Feature\Http\Controllers\Prospects;

use App\DataModels\Odin\ConfigurableFieldDataModel;
use App\Models\GlobalConfiguration;
use App\Models\Odin\Industry;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Auth;
use PHPUnit\Framework\Attributes\Test;
use Spatie\Permission\Models\Permission;
use Tests\TestCase;

class ProspectingGlobalConfigurationControllerTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function a_guest_cannot_view_global_role_configurations(): void
    {
        Auth::logout();

        $this->assertGuest()
            ->getJson(route('internal-api.v2.prospecting.configuration.global.index'))
            ->assertUnauthorized();
    }

    #[Test]
    public function a_user_without_the_right_permissions_cannot_view_global_configurations(): void
    {
        $this->actingAs(User::factory()->create()->givePermissionTo())
            ->getJson(route('internal-api.v2.prospecting.configuration.global.index'))
            ->assertForbidden();

        $this->actingAs(User::factory()->create()->givePermissionTo('prospecting'))
            ->getJson(route('internal-api.v2.prospecting.configuration.global.index'))
            ->assertForbidden();

        $this->actingAs(User::factory()->create()->givePermissionTo('update-prospecting-configurations'))
            ->getJson(route('internal-api.v2.prospecting.configuration.global.index'))
            ->assertForbidden();
    }

    #[Test]
    public function a_user_can_view_all_global_configurations(): void
    {
        GlobalConfiguration::truncate();
        Industry::all()->each->delete();

        $industries = Industry::factory(5)->create();

        GlobalConfiguration::factory()->create([
            'configuration_key' => 'role_configurations',
            'configuration_payload' => new ConfigurableFieldDataModel(collect([
                'processing_time_limit_days' => 42,
                'industries' => $industries->take(2)->pluck('id'),
                'foo' => 'bar',
                'bazz' => 'buzz',
            ])),
        ]);

        $this->getJson(route('internal-api.v2.prospecting.configuration.global.index'))
            ->assertOk()
            ->assertExactJson([
                'data' => [
                    [
                        'display_name' => 'Foo',
                        'key' => 'foo',
                        'placeholder' => null,
                        'options' => null,
                        'type' => 'input',
                        'value' => 'bar',
                    ],
                    [
                        'display_name' => 'Bazz',
                        'key' => 'bazz',
                        'placeholder' => null,
                        'options' => null,
                        'type' => 'input',
                        'value' => 'buzz',
                    ],
                    [
                        'key' => 'industries',
                        'value' => $industries->take(2)->pluck('id'),
                        'display_name' => 'Industries',
                        'type' => 'multiselect',
                        'options' => $industries->map->only('id', 'name'),
                        'placeholder' => 'All Industries',
                    ],
                    [
                        'display_name' => 'Processing Time Limit Days',
                        'key' => 'processing_time_limit_days',
                        'placeholder' => null,
                        'options' => null,
                        'type' => 'input',
                        'value' => 42,
                    ],
                ],
            ]);
    }

    #[Test]
    public function a_user_can_update_global_configurations(): void
    {
        GlobalConfiguration::truncate();

        $globalConfiguration = GlobalConfiguration::factory()->create([
            'configuration_key' => 'role_configurations',
            'configuration_payload' => new ConfigurableFieldDataModel(collect([
                'processing_time_limit_days' => 42,
            ])),
        ]);

        $configs = [
            [
                'key' => 'foo',
                'value' => 'bar',
            ],
        ];

        $route = route('internal-api.v2.prospecting.configuration.global.update');

        $this->patchJson($route, compact('configs'))->assertNoContent();

        $this->assertEquals([
            'processing_time_limit_days' => 42,
            'foo' => 'bar',
        ], $globalConfiguration->refresh()->configuration_payload->data->toArray());
    }

    protected function setUp(): void
    {
        parent::setUp();

        $user = User::factory()->create()->givePermissionTo(
            Permission::findOrCreate('prospecting'),
            Permission::findOrCreate('update-prospecting-configurations'),
        );

        $this->actingAs($user);
    }
}
