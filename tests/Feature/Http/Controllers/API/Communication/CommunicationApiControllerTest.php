<?php

namespace Tests\Feature\Http\Controllers\API\Communication;

use App\Models\Call;
use App\Models\Phone;
use App\Models\Text;
use App\Models\User;
use App\Models\UserPhone;
use App\Services\Communication\TwilioCommunicationService;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Config;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class CommunicationApiControllerTest extends TestCase
{
    use RefreshDatabase;

    protected User $user;

    protected Phone $phone;

    public function setUp(): void
    {
        parent::setUp();

        $this->mock(TwilioCommunicationService::class);

        Config::set('services.communication.driver', 'dummy');
    }

    #[Test, DataProvider('return_zero_logs')]
    public function getLogs_with_return_zero_logs(callable $setup): void
    {
        $this->skipTestForTempRevert();

        $setup($this);

        $this->actingAs($this->user)
            ->get(route('internal-api.v1.communication.get-logs'))
            ->assertNotFound();
    }

    public static function return_zero_logs(): array
    {
        return [
            'no phone' => [
                function (self $class) {
                    $class->user = User::factory()->createQuietly();
                }
            ],
            'phone exists but no logs' => [
                function (self $class) {
                    $class->user = User::factory()->has(Phone::factory())->create();
                }
            ],
            'calls.created_at is less than user_phones.created_at and not between user_phones.created_at and user_phones.deleted_at' => [
                function (self $class) {
                    $class->user = User::factory()->createQuietly();

                    $phone = Phone::factory()->createQuietly();

                    UserPhone::createQuietly([
                        'user_id'    => $class->user->id,
                        'phone_id'   => $phone->id,
                        'created_at' => now()->subDay(),
                    ]);

                    Call::factory()->createQuietly([
                        'phone_id'   => $phone->id,
                        'created_at' => now()->subDays(2),
                    ]);
                }
            ],
            'user_phones is soft deleted and calls.created_at is not between user_phones.created_at and user_phones.deleted_at' => [
                function (self $class) {
                    $class->user = User::factory()->createQuietly();

                    $phone = Phone::factory()->createQuietly();

                    UserPhone::createQuietly([
                        'user_id'    => $class->user->id,
                        'phone_id'   => $phone->id,
                        'created_at' => now()->subDays(2),
                        'deleted_at' => now()->subDays(3),
                    ]);

                    Call::factory()->createQuietly([
                        'phone_id'   => $phone->id,
                        'created_at' => now()->subDay(),
                    ]);
                }
            ],
        ];
    }

    #[Test, DataProvider('return_call_logs')]
    public function getLogs_with_return_call_logs(callable $setup): void
    {
        $setup($this);

        $this->actingAs($this->user)
            ->get(route('internal-api.v1.communication.get-logs'))
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.phone_id', $this->phone->id);
    }

    public static function return_call_logs(): array
    {
        return [
            'calls.created_at is greater than user_phones.created_at and user_phones is not soft deleted' => [
                function (self $class) {
                    $class->user = User::factory()->createQuietly();

                    $class->phone = Phone::factory()->createQuietly();

                    UserPhone::createQuietly([
                        'user_id'    => $class->user->id,
                        'phone_id'   => $class->phone->id,
                        'created_at' => now()->subDays(2),
                    ]);

                    Call::factory()->createQuietly([
                        'phone_id'   => $class->phone->id,
                        'created_at' => now()->subDay(),
                    ]);
                }
            ],
            'user_phones is soft deleted but calls.created_at is between user_phones.created_at and user_phones.deleted_at' => [
                function (self $class) {
                    $class->user = User::factory()->createQuietly();

                    $class->phone = Phone::factory()->createQuietly();

                    UserPhone::createQuietly([
                        'user_id'    => $class->user->id,
                        'phone_id'   => $class->phone->id,
                        'created_at' => now()->subDays(2),
                        'deleted_at' => now()->subMinute(),
                    ]);

                    Call::factory()->createQuietly([
                        'phone_id'   => $class->phone->id,
                        'created_at' => now()->subDay(),
                    ]);
                }
            ]
        ];
    }

    #[Test, DataProvider('return_text_logs')]
    public function getLogs_with_return_text_logs(callable $setup): void
    {
        $setup($this);

        $this->actingAs($this->user)
            ->get(route('internal-api.v1.communication.get-logs'))
            ->assertJsonCount(1, 'data')
            ->assertJsonPath('data.0.phone_id', $this->phone->id);
    }

    public static function return_text_logs(): array
    {
        return [
            'texts.created_at is greater than user_phones.created_at and user_phones is not soft deleted' => [
                function (self $class) {
                    $class->user = User::factory()->createQuietly();

                    $class->phone = Phone::factory()->createQuietly();

                    UserPhone::createQuietly([
                        'user_id'    => $class->user->id,
                        'phone_id'   => $class->phone->id,
                        'created_at' => now()->subDays(2),
                    ]);

                    Text::factory()->createQuietly([
                        'phone_id'   => $class->phone->id,
                        'created_at' => now()->subDay(),
                    ]);
                }
            ],
            'user_phones is soft deleted but texts.created_at is between user_phones.created_at and user_phones.deleted_at' => [
                function (self $class) {
                    $class->user = User::factory()->createQuietly();

                    $class->phone = Phone::factory()->createQuietly();

                    UserPhone::createQuietly([
                        'user_id'    => $class->user->id,
                        'phone_id'   => $class->phone->id,
                        'created_at' => now()->subDays(2),
                        'deleted_at' => now()->subMinute(),
                    ]);

                    Text::factory()->createQuietly([
                        'phone_id'   => $class->phone->id,
                        'created_at' => now()->subDay(),
                    ]);
                }
            ]
        ];
    }

    #[Test, DataProvider('return_call_and_text_logs')]
    public function getLogs_with_return_call_and_text_logs(callable $setup): void
    {
        $setup($this);

        $this->actingAs($this->user)
            ->get(route('internal-api.v1.communication.get-logs'))
            ->assertJsonCount(2, 'data')
            ->assertJsonPath('data.0.phone_id', $this->phone->id)
            ->assertJsonPath('data.1.phone_id', $this->phone->id)
            ->assertExactJsonStructure([
                'data' => [
                    '*' => [
                        'call_duration_in_seconds',
                        'communication_type',
                        'relation_type',
                        'other_number',
                        'relation_id',
                        'created_at',
                        'result',
                        'direction',
                        'phone',
                        'phone_id',
                        'id',
                        'contact',
                    ]
                ],
                'links' => [
                    'first',
                    'last',
                    'prev',
                    'next',
                ],
                'meta' => [
                    'current_page',
                    'from',
                    'last_page',
                    'links' => [
                        '*' => [
                            'url',
                            'label',
                            'active',
                        ]
                    ],
                    'path',
                    'per_page',
                    'to',
                    'total',
                ],
            ]);
    }

    public static function return_call_and_text_logs(): array
    {
        return [
            'calls.created_at and texts.created_at is greater than user_phones.created_at and user_phones is not soft deleted' => [
                function (self $class) {
                    $class->user = User::factory()->createQuietly();

                    $class->phone = Phone::factory()->createQuietly();

                    UserPhone::createQuietly([
                        'user_id'    => $class->user->id,
                        'phone_id'   => $class->phone->id,
                        'created_at' => now()->subDays(2),
                    ]);

                    Call::factory()->createQuietly([
                        'phone_id'   => $class->phone->id,
                        'created_at' => now()->subDay(),
                    ]);

                    Text::factory()->createQuietly([
                        'phone_id'   => $class->phone->id,
                        'created_at' => now()->subDay(),
                    ]);
                }
            ],
            'user_phones is soft deleted but calls.created_at and text.created_at is between user_phones.created_at and user_phones.deleted_at' => [
                function (self $class) {
                    $class->user = User::factory()->createQuietly();

                    $class->phone = Phone::factory()->createQuietly();

                    UserPhone::createQuietly([
                        'user_id'    => $class->user->id,
                        'phone_id'   => $class->phone->id,
                        'created_at' => now()->subDays(2),
                        'deleted_at' => now()->subMinute(),
                    ]);

                    Call::factory()->createQuietly([
                        'phone_id'   => $class->phone->id,
                        'created_at' => now()->subDay(),
                    ]);

                    Text::factory()->createQuietly([
                        'phone_id'   => $class->phone->id,
                        'created_at' => now()->subDay(),
                    ]);
                }
            ]
        ];
    }

    #[Test]
    public function getLogs_return_one_call_log_with_date_range(): void
    {
        $this->user = User::factory()->createQuietly();

        $this->phone = Phone::factory()->createQuietly();

        UserPhone::createQuietly([
            'user_id'    => $this->user->id,
            'phone_id'   => $this->phone->id,
            'deleted_at' => now()->subDays(6),
            'created_at' => now()->subDays(10),
        ]);

        Call::factory()->createQuietly([
            'phone_id'   => $this->phone->id,
            'created_at' => now()->subDays(7),
        ]);

        $this->actingAs($this->user)
            ->get(route('internal-api.v1.communication.get-logs', [
                'date_time' => [
                    'from' => now()->subDays(10)->toDateTimeString(),
                    'to'   => now()->subDays(5)->toDateTimeString(),
                ]
            ]))
            ->assertJsonCount(1, 'data');
    }

    #[Test]
    public function getLogs_return_no_calls_log_with_date_range(): void
    {
        $this->skipTestForTempRevert();

        $this->user = User::factory()->createQuietly();

        $this->phone = Phone::factory()->createQuietly();

        UserPhone::createQuietly([
            'user_id'    => $this->user->id,
            'phone_id'   => $this->phone->id,
            'deleted_at' => now()->subDays(8),
            'created_at' => now()->subDays(10),
        ]);

        Call::factory()->createQuietly([
            'phone_id'   => $this->phone->id,
            'created_at' => now()->subDays(7),
        ]);

        $this->actingAs($this->user)
            ->get(route('internal-api.v1.communication.get-logs', [
                'date_time' => [
                    'from' => now()->subDays(10)->toDateTimeString(),
                    'to'   => now()->subDays(5)->toDateTimeString(),
                ]
            ]))
            ->assertNotFound();
    }

    private function skipTestForTempRevert(): void
    {
        $this->markTestSkipped('This was intended to work for the commit (https://bitbucket.org/heapsgoodservices/admin-2.0/commits/191b7e6cb24917d2e58a41ab2565a141762f62ed). However, due to an issue with the query not working with the Production database, the commit was reverted (https://bitbucket.org/heapsgoodservices/admin-2.0/commits/5ee76abbfb048f2e018b17586b19e29eaf6a7a64). Upon rewriting the query, this test should no longer be skipped.');
    }
}
