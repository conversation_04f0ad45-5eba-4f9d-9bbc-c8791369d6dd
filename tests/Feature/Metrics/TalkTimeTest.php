<?php

namespace Tests\Feature\Metrics;

use App\Enums\Metrics\MetricFormat;
use App\Enums\Metrics\MetricRange;
use App\Models\Call;
use App\Models\Phone;
use App\Models\User;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class TalkTimeTest extends TestCase
{
    use RefreshDatabase;

    protected $route;

    protected $phone;

    protected function setUp(): void
    {
        parent::setUp();

        Carbon::setTestNow('2025-02-18');

        $this->route = route('internal-api.v2.metrics.talk-time');

        $user = User::factory()->create();

        $this->phone = Phone::factory()->create();

        $user->phones()->attach($this->phone);

        $this->generateCalls();

        $this->actingAs($user);
    }

    #[Test]
    public function guests_cannot_view_talk_time_metrics()
    {
        Auth::logout();

        $this->assertGuest()
            ->getJson($this->route)
            ->assertUnauthorized();
    }

    #[Test]
    public function users_with_no_phones_see_empty_talk_time_metrics()
    {
        $user = User::factory()->create();

        $this->assertEmpty($user->phones);

        $this->actingAs($user)
            ->getJson(url()->query($this->route, [
                'range' => MetricRange::TODAY->value,
            ]))->assertJson([
                'data' => [
                    'format' => MetricFormat::SECONDS->value,
                    'current' => 0,
                    'change' => 0,
                    'percentage' => 0,
                ],
            ]);
    }

    #[Test]
    public function it_can_calculate_the_sum_of_talk_time_for_today(): void
    {
        $this->getJson(url()->query($this->route, [
            'range' => MetricRange::TODAY->value,
        ]))->assertJson([
            'data' => [
                'format' => MetricFormat::SECONDS->value,
                'current' => 60,
                'change' => -60,
                'percentage' => 50,
            ],
        ]);
    }

    #[Test]
    public function it_can_calculate_the_sum_of_talk_time_for_this_week(): void
    {
        $this->getJson(url()->query($this->route, [
            'range' => MetricRange::THIS_WEEK->value,
        ]))->assertJson([
            'data' => [
                'format' => MetricFormat::SECONDS->value,
                'current' => 180,
                'change' => 120,
                'percentage' => 200,
            ],
        ]);
    }

    #[Test]
    public function it_can_calculate_the_sum_of_talk_time_for_this_month(): void
    {
        $this->getJson(url()->query($this->route, [
            'range' => MetricRange::THIS_MONTH->value,
        ]))->assertJson([
            'data' => [
                'format' => MetricFormat::SECONDS->value,
                'current' => 240,
                'change' => 180,
                'percentage' => 300,
            ],
        ]);
    }

    #[Test]
    public function it_can_calculate_the_sum_of_talk_time_for_this_quarter(): void
    {
        $this->getJson(url()->query($this->route, [
            'range' => MetricRange::THIS_QUARTER->value,
        ]))->assertJson([
            'data' => [
                'format' => MetricFormat::SECONDS->value,
                'current' => 300,
                'change' => 240,
                'percentage' => 400,
            ],
        ]);
    }

    #[Test]
    public function it_can_calculate_the_sum_of_talk_time_for_this_year(): void
    {
        $this->getJson(url()->query($this->route, [
            'range' => MetricRange::THIS_YEAR->value,
        ]))->assertJson([
            'data' => [
                'format' => MetricFormat::SECONDS->value,
                'current' => 300,
                'change' => 180,
                'percentage' => 150,
            ],
        ]);
    }

    private function generateCalls()
    {
        $today = Call::factory()->for($this->phone)->create([
            'call_start' => now(),
            'call_end' => now()->addMinute(),
        ]);

        $yesterday = Call::factory(2)->for($this->phone)->create([
            'call_start' => now()->subDay(),
            'call_end' => now()->subDay()->addMinute(),
        ]);

        Call::factory()->for($this->phone)->create([
            'call_start' => now()->subWeek()->startOfWeek(),
            'call_end' => now()->subWeek()->startOfWeek()->addMinute(),
        ]);

        Call::factory()->for($this->phone)->create([
            'call_start' => now()->subMonth()->startOfMonth(),
            'call_end' => now()->subMonth()->startOfMonth()->addMinute(),
        ]);

        Call::factory()->for($this->phone)->create([
            'call_start' => now()->subQuarter()->startOfQuarter(),
            'call_end' => now()->subQuarter()->startOfQuarter()->addMinute(),
        ]);

        Call::factory()->for($this->phone)->create([
            'call_start' => now()->subYear()->startOfYear(),
            'call_end' => now()->subYear()->startOfYear()->addMinute(),
        ]);
    }
}
