<?php

namespace Tests\Unit\Transformers\Companies;

use App\Jobs\CreateLegacyCompanyJob;
use App\Models\Legacy\EloquentCompany;
use App\Models\Odin\Company;
use App\Models\User;
use App\Repositories\ComputedRejectionStatisticRepository;
use App\Repositories\DashboardShadowRepository;
use App\Transformers\Companies\CompanyProfileTransformer;
use Database\Seeders\ProductsSeeder;
use Illuminate\Foundation\Testing\DatabaseTruncation;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Bus;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class CompanyProfileTransformerTest extends TestCase
{
    use DatabaseTruncation, RefreshDatabase;

    protected array $connectionsToTruncate = ['mysql', 'readonly'];

    protected array $tablesToTruncate = [
        'mysql' => ['companies', 'users', 'roles', 'model_has_permissions', 'model_has_roles', 'permissions', 'role_has_permissions', 'products'],
        'readonly' => ['tblcompany'],
    ];

    protected function setUp(): void
    {
        parent::setUp();

        Bus::fake([
            CreateLegacyCompanyJob::class,
        ]);
    }

    #[Test]
    public function transform_company_profile_overview(): void
    {
        $this->seed(ProductsSeeder::class);

        $legacyCompany = EloquentCompany::factory()->createQuietly([
            'companyid' => random_int(1, 1000),
        ]);

        $this->assertDatabaseCount('tblcompany', 1, 'readonly');

        $company = Company::factory()->createQuietly([
            'legacy_id' => $legacyCompany->companyid,
        ]);

        $user = User::factory()->withRole('account-manager')->createQuietly();

        $company->assign($user)->as('account-manager');

        $result = app(CompanyProfileTransformer::class)->transformCompanyProfileOverview($legacyCompany);

        $computedRejectionStatisticRepository = app(ComputedRejectionStatisticRepository::class);
        $shadowRepository = app(DashboardShadowRepository::class);

        $this->assertEqualsCanonicalizing([
            'id' => $legacyCompany->companyid,
            'name' => $legacyCompany->companyname,
            'website' => $legacyCompany->website,
            'phone' => $legacyCompany->defaultAddress?->address?->phone,
            'image' => $legacyCompany->logoUrl(),
            'type' => $legacyCompany->type,
            'active' => $legacyCompany->status === EloquentCompany::STATUS_ACTIVE,
            'status' => $legacyCompany->status,
            'ranking' => 'Excellent',
            'prescreened' => $legacyCompany->isPrescreeed(),
            'lead_rejection_percentage' => number_format($computedRejectionStatisticRepository->getCompanyLeadRejectionPercentage($legacyCompany->miCompany), 1).'%',
            'appointment_rejection_percentage' => '0%',
            'mainOfficeLocation' => $legacyCompany->defaultAddress?->address?->getFullAddress() ?? $legacyCompany->addresses()->first()?->address?->getFullAddress() ?? 'No Default Address',
            'employees' => $legacyCompany->employee_count,
            'revenue' => $legacyCompany->revenue_in_thousands,
            'shadow_token' => $shadowRepository->getShadowToken($legacyCompany),
            'shadow_url' => $shadowRepository->getShadowUrl($legacyCompany),
            'account_manager' => [
                'id' => $user->id,
                'name' => $user->name,
            ],
            'success_manager' => [
                'id' => $legacyCompany->successManagerClient()?->success_manager_id,
                'name' => $legacyCompany->successManagerClient()?->successManager?->user?->name ?? 'None Assigned',
            ],
        ], $result);
    }
}
