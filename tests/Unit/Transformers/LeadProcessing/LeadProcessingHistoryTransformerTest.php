<?php

namespace Tests\Unit\Transformers\LeadProcessing;

use App\Models\LeadProcessingHistory;
use App\Models\LeadProcessingQueueConfiguration;
use App\Models\Legacy\EloquentQuote;
use App\Transformers\LeadProcessing\LeadProcessingHistoryTransformer;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Support\Facades\Schema;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class LeadProcessingHistoryTransformerTest extends TestCase
{
    use RefreshDatabase;

    protected LeadProcessingHistory $leadProcessingHistoryEntry;

    protected function setUp(): void
    {
        parent::setUp();

        Schema::connection('readonly')->disableForeignKeyConstraints();

        EloquentQuote::query()->truncate();

        Schema::connection('readonly')->enableForeignKeyConstraints();
    }

    #[Test]
    public function it_transforms_lead_processing_history_entry()
    {
        $this->leadProcessingHistoryEntry = LeadProcessingHistory::query()->createQuietly([
            'lead_id' => EloquentQuote::factory()->create()->quoteid,
            'queue_configuration_id' => LeadProcessingQueueConfiguration::query()->createQuietly([
                'name' => 'Test',
                'primary_status' => 'initial',
                'last_round' => true,
            ])->id,
            'action' => 'Marked Pending Review',
        ]);

        $this->assertEquals([
            'id' => $this->leadProcessingHistoryEntry->id,
            'lead_id' => $this->leadProcessingHistoryEntry->lead_id,
            'queue' => $this->leadProcessingHistoryEntry->queueConfiguration->name,
            'action' => $this->leadProcessingHistoryEntry->action,
            'created_time' => $this->leadProcessingHistoryEntry->created_at->format('m/d/Y H:i T'),
        ], app(LeadProcessingHistoryTransformer::class)->transformLeadProcessingHistoryEntry($this->leadProcessingHistoryEntry));
    }

    #[Test]
    public function it_transforms_lead_processing_history_entry_with_no_related_queue_configuration()
    {
        if (LeadProcessingQueueConfiguration::query()->find(1000)) {
            LeadProcessingQueueConfiguration::query()->where('id', 1000)->delete();
        }

        $this->leadProcessingHistoryEntry = LeadProcessingHistory::query()->createQuietly([
            'lead_id' => EloquentQuote::factory()->create()->quoteid,
            'queue_configuration_id' => 1000,
            'action' => 'Marked Pending Review',
        ]);

        $this->assertEquals([
            'id' => $this->leadProcessingHistoryEntry->id,
            'lead_id' => $this->leadProcessingHistoryEntry->lead_id,
            'queue' => null,
            'action' => $this->leadProcessingHistoryEntry->action,
            'created_time' => $this->leadProcessingHistoryEntry->created_at->format('m/d/Y H:i T'),
        ], app(LeadProcessingHistoryTransformer::class)->transformLeadProcessingHistoryEntry($this->leadProcessingHistoryEntry));
    }
}
