<?php

namespace Tests\Unit\Models;

use App\Models\Phone;
use App\Models\User;
use App\Models\UserPhone;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class UserTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_gets_the_primary_phone(): void
    {
        $user = User::factory()->create();

        $inactivePhone = Phone::factory()->create([
            'status' => 'inactive',
        ]);

        UserPhone::factory()->count(3)->sequence(
            ['deleted_at' => null, 'created_at' => now()->subDay()],
            ['deleted_at' => null, 'created_at' => now()->subDays(2)],
            ['deleted_at' => now(), 'created_at' => now()],
            ['deleted_at' => null, 'created_at' => now(), 'phone_id' => $inactivePhone->id],
        )->create([
            'user_id' => $user->id,
        ]);

        /**
         * The primary phone number used should be the one where the user phone relationship is not deleted, was most recently created, and the associated phone is active.
         */
        $expectedPrimaryNumber = UserPhone::orderByDesc('created_at')->whereHas('phone', function (Builder $query) {
            $query->where('status', 'active');
        })->first()->phone;

        $this->assertTrue($user->primaryPhone()->is($expectedPrimaryNumber), "The expected primary phone number ({$expectedPrimaryNumber->phone}) is not the same as the actual phone number ({$user->primaryPhone()->phone}).");
    }

    #[Test]
    public function it_gets_phones_where_relationship_is_not_soft_deleted(): void
    {
        $user = User::factory()->create();

        UserPhone::factory()->count(3)->sequence(
            ['deleted_at' => null, 'user_id' => $user->id],
            ['deleted_at' => null, 'user_id' => $user->id],
            ['deleted_at' => now(), 'user_id' => $user->id],
        )->createQuietly();

        $this->assertCount(2, $user->phones);
    }
}
