<?php

namespace Tests\Unit\Models;

use App\Models\Phone;
use App\Models\User;
use App\Models\UserPhone;
use Illuminate\Foundation\Testing\RefreshDatabase;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class UserPhoneTest extends TestCase
{
    use RefreshDatabase;

    #[Test]
    public function it_gets_the_user(): void
    {
        $userPhone = UserPhone::factory()->create();

        $this->assertInstanceOf(User::class, $userPhone->user);

        $this->assertTrue($userPhone->user->is(User::first()));
    }

    #[Test]
    public function it_gets_the_phone(): void
    {
        $userPhone = UserPhone::factory()->create();

        $this->assertInstanceOf(Phone::class, $userPhone->phone);

        $this->assertTrue($userPhone->phone->is(Phone::first()));
    }
}
