FROM php:8.3-fpm
# Install required dependencies
RUN apt update

RUN apt install \
    libcurl4=7.88.1-10+deb12u7 \
    libcurl4-openssl-dev=7.88.1-10+deb12u7 \
    curl=7.88.1-10+deb12u7 \
    zip=3.0-13 \
    unzip=6.0-28 \
    libzip-dev=1.7.3-1+b1 \
    libnss3=2:3.87.1-1 \
    libnspr4=2:4.35-1 \
    libatk1.0-0=2.46.0-5 \
    libatk-bridge2.0-0=2.46.0-5 \
    libcups2=2.4.2-3+deb12u8 \
    libdrm2=2.4.114-1+b1\
    libxkbcommon0=1.5.0-1 \
    libxcomposite1=1:0.4.5-1 \
    libxdamage1=1:1.1.6-1 \
    libxfixes3=1:6.0.0-2 \
    libxrandr2=2:1.5.2-2+b1 \
    libgbm1=22.3.6-1+deb12u1 \
    libasound2=1.2.8-1+b1 \
    -y

RUN apt install \
    build-essential=12.9 \
    libpng-dev=1.6.39-2 \
    libjpeg62-turbo-dev=1:2.1.5-2 \
    jpegoptim=1.4.7-1 \
    optipng=0.7.7-2+b1 \
    pngquant=2.17.0-1 \
    nginx=1.22.1-9 \
    libmagickwand-dev=8:*********+dfsg-1.6+deb12u2 \
    cron=3.0pl1-162 \
    supervisor=4.2.5-1 \
    redis-server=5:7.0.15-1~deb12u1 \
    nodejs=18.19.0+dfsg-6~deb12u2 \
    npm=9.2.0~ds1-1 \
    --no-install-recommends \
    -y

RUN docker-php-ext-install mysqli pdo pdo_mysql curl gd zip exif intl bcmath

RUN pecl install grpc-1.65.2
RUN pecl install protobuf-4.27.3

RUN docker-php-ext-enable grpc
RUN docker-php-ext-enable protobuf

RUN touch /var/log/grpc.log

COPY server/php.ini /usr/local/etc/php/php.ini

EXPOSE 80
ENTRYPOINT cron && supervisord -n
