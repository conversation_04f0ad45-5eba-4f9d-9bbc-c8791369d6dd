<?php

namespace App\Transformers;

use App\Enums\ActivityType;
use App\Http\Controllers\API\CompanyActivityController;
use App\Http\Resources\Mailbox\MailboxEmailResource;
use App\Models\ActivityFeed;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;

class ActivityTransformer
{
    /**
     * @param UserTransformer $userTransformer
     */
    public function __construct(protected UserTransformer $userTransformer)
    {
    }

    /**
     * @param Collection|null $activities
     * @return array
     */
    public function transformActivities(?Collection $activities = null): array
    {
        if (!$activities) return [];

        return $activities->map(function (ActivityFeed $activity) {
            return $this->transformActivity($activity);
        })->toArray();
    }

    /**
     * @param ActivityFeed $activity
     * @return array
     */
    public function transformActivity(ActivityFeed $activity): array
    {
        return [
            'id'            => $activity->{ActivityFeed::FIELD_ID},
            'company_id'    => $activity->{ActivityFeed::FIELD_COMPANY_ID}, //todo: should return a company collection once the A2 models are added
            'user'          => $activity->{ActivityFeed::RELATION_USER} ? $this->userTransformer->transform($activity->{ActivityFeed::RELATION_USER}) : null,
            'item'          => $this->prepareItemData($activity),
            'created_at'    => $activity->{CompanyActivityController::ITEM_FIRST_CREATED_AT}
                ? Carbon::createFromFormat("Y-m-d H:i:s", $activity->{CompanyActivityController::ITEM_FIRST_CREATED_AT})->toDateTimeString()
                : $activity->{ActivityFeed::FIELD_CREATED_AT}?->toDateTimeString(),
            'updated_at'    => $activity->{CompanyActivityController::ITEM_LAST_UPDATED_AT}
                ? Carbon::createFromFormat("Y-m-d H:i:s", $activity->{CompanyActivityController::ITEM_LAST_UPDATED_AT})->toDateTimeString()
                : $activity->{ActivityFeed::FIELD_UPDATED_AT}?->toDateTimeString(),
            'comment_count' => $activity->conversations_count ?? 0,
        ];
    }

    /**
     * @param ActivityFeed $activity
     * @return array
     */
    protected function prepareItemData(ActivityFeed $activity): array
    {
        return [
            'type' => $activity->{ActivityFeed::FIELD_ITEM_TYPE},
            'data' => $this->formatItemData($activity->{ActivityFeed::FIELD_ITEM_TYPE}, $activity->{ActivityFeed::RELATION_ACTIVITY_ITEM} ?? [])
        ];
    }

    /**
     * @param string $type
     * @param mixed $data
     * @return MailboxEmailResource|mixed
     */
    protected function formatItemData(string $type, mixed $data): mixed
    {
        if (empty($data)) {
            return [];
        };

        return match ($type) {
            ActivityType::MAILBOX_EMAIL->value => new MailboxEmailResource($data),
            default                            => $data,
        };
    }
}
