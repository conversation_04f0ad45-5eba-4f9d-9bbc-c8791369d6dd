<?php

namespace App\Transformers\ReferenceLists;

use App\Models\Legacy\Location;
use Illuminate\Support\Collection;

class CityTransformer
{
    /**
     * Handles transforming a collection of cities to their respective client side representation
     *
     * @param Collection $cities
     * @return array
     */
    public function transformCities(Collection $cities): array
    {
        return $cities->map(function ($city) {
            return $this->transformCity($city);
        })->values()->toArray();
    }

    /**
     * Handles transforming a given city to its respective client side representation.
     *
     * @param $city
     * @return array
     */
    public function transformCity($city): array
    {
        return [
            "id" => $city->{Location::CITY_KEY},
            "num_id"   => $city->{Location::ID},
            "name" => $city->{Location::CITY}
        ];
    }
}
