<?php

namespace App\Transformers\Odin;

use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ServiceProduct;
use App\Repositories\Odin\ConsumerRepository;

class ConsumerProductProcessingTransformer
{
    public function __construct(protected AddressTransformer $addressTransformer, protected ConsumerRepository $consumerRepository) {}

    /**
     * @param ConsumerProduct|null $consumerProduct
     * @param string $queue
     *
     * @return array[]
     */
    public function transform(?ConsumerProduct $consumerProduct, string $queue): array
    {
        if(!$consumerProduct) return [];

        return [
            'basic' =>$this->transformBasicData($consumerProduct, $queue)
        ];
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param string $queue
     *
     * @return array
     */
    protected function transformBasicData(ConsumerProduct $consumerProduct, string $queue): array
    {
        return [
            'id'                      => $consumerProduct->id,
            'legacy_lead_id'          => $consumerProduct->consumer->legacyLead?->quoteid,
            'queue'                   => $queue,
            'status'                  => ConsumerProduct::STATUS_TEXT[$consumerProduct->status],
            'classification'          => Consumer::CLASSIFICATION_TEXT[$consumerProduct->consumer->classification] ?? 'Unverified',
            'industry'                => $consumerProduct->serviceProduct?->service?->industry?->name ?? 'Solar',
            'industry_color_light'    => $consumerProduct->serviceProduct?->service?->industry?->{Industry::FIELD_COLOR_LIGHT} ?? null,
            'industry_color_dark'     => $consumerProduct->serviceProduct?->service?->industry?->{Industry::FIELD_COLOR_DARK} ?? null,
            'industry_service'        => $consumerProduct->industryService,
            'type'                    => $consumerProduct->consumer->legacyLead?->leadCategory?->name ?? 'Residential',
            'status_reason'           => $this->getStatusReason($consumerProduct),
            'review_reason'           => $consumerProduct->underReview?->existing_reason,
            'address'                 => $this->addressTransformer->transformAddress($consumerProduct->address),
            'next_allocation_attempt' => $this->consumerRepository->getNextAllocationTime($consumerProduct->consumer),
            'future_campaign'         => $consumerProduct->serviceProduct?->service?->industry?->industryConfiguration?->future_campaigns_active ?? false,
            'secondary_services'      => $this->getSecondaryServices($consumerProduct->consumer),
            'affiliate'               => $consumerProduct->pingPostAffiliate,
            'email_marketing_clone'   => $consumerProduct->fromMarketingCampaignConsumer()->exists(),
        ];
    }

    /**
     * @param ConsumerProduct $consumerProduct
     *
     * @return string|null
     */
    protected function getStatusReason(ConsumerProduct $consumerProduct): string|null
    {
        return $consumerProduct->pendingReview?->reason ?? $consumerProduct->underReview?->reason ?? $consumerProduct->consumer->legacyLead?->statusreason;
    }

    /**
     * @param Consumer $consumer
     * @return array|null
     */
    protected function getSecondaryServices(Consumer $consumer): ?array
    {
        return $consumer->hasSecondaryServices()
            ? $consumer->consumerProducts()
                ->where(ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_IS_SECONDARY_SERVICE, true)
                ->join(ServiceProduct::TABLE, ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_ID, '=', ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_SERVICE_PRODUCT_ID)
                ->join(IndustryService::TABLE, IndustryService::TABLE .'.'. IndustryService::FIELD_ID, '=', ServiceProduct::TABLE .'.'. ServiceProduct::FIELD_INDUSTRY_SERVICE_ID)
                ->select(IndustryService::TABLE .'.'. IndustryService::FIELD_NAME)
                ->pluck(IndustryService::FIELD_NAME)
                ->toArray()
            : null;
    }
}
