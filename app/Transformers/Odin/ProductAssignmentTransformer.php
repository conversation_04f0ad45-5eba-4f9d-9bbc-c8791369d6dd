<?php

namespace App\Transformers\Odin;

use App\Models\Odin\Company;
use App\Models\Odin\ProductAssignment;
use Illuminate\Support\Collection;

class ProductAssignmentTransformer
{
    /**
     * @param Collection<ProductAssignment> $productAssignments
     * @return array
     */
    public function transformAll(Collection $productAssignments): array
    {
        return $productAssignments
            ->map(fn(ProductAssignment $productAssignment) => $this->transform($productAssignment))
            ->toArray();
    }

    /**
     * @param ProductAssignment $productAssignment
     * @return array
     */
    public function transform(ProductAssignment $productAssignment): array
    {
        return [
            'company_name'   => $productAssignment->{ProductAssignment::RELATION_COMPANY}->{Company::FIELD_NAME},
            'cost'           => $productAssignment->{ProductAssignment::FIELD_COST},
            'chargeable'     => $productAssignment->{ProductAssignment::FIELD_CHARGEABLE}  ? 'Yes' : 'No',
            'delivered'      => $productAssignment->{ProductAssignment::FIELD_DELIVERED}   ? 'Yes' : 'No',
            'date'           => $productAssignment->{ProductAssignment::CREATED_AT}  ?->toIso8601String(),
            'date_delivered' => $productAssignment->{ProductAssignment::FIELD_DELIVERED_AT}?->toIso8601String(),
        ];
    }
}
