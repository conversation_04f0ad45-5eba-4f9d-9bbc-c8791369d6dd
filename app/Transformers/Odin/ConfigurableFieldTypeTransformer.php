<?php

namespace App\Transformers\Odin;

use App\Models\Odin\ConfigurableFieldType;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Collection;
use JetBrains\PhpStorm\ArrayShape;

class ConfigurableFieldTypeTransformer
{
    /**
     * @param Collection $configurableFieldTypes
     * @return array
     */
    public function transform(Collection $configurableFieldTypes): array
    {
        return $configurableFieldTypes->map(fn(ConfigurableFieldType $configurableFieldType) => $this->transformConfigurableFieldType($configurableFieldType))->toArray();
    }

    /**
     * @param ConfigurableFieldType $fieldType
     * @return array
     */
    #[ArrayShape(['id' => "mixed", 'name' => "mixed", 'label' => "mixed", 'created_at' => "mixed"])]
    public function transformConfigurableFieldType(ConfigurableFieldType $fieldType): array
    {
        return
            [
                'id'         => $fieldType->{ConfigurableFieldType::FIELD_ID},
                'name'       => $fieldType->{ConfigurableFieldType::FIELD_TYPE},
                'label'      => $fieldType->{ConfigurableFieldType::FIELD_LABEL},
                'created_at' => $fieldType->{Model::CREATED_AT}?->toIso8601String()
            ];
    }
}

