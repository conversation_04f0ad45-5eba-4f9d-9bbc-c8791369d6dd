<?php

namespace App\Transformers\Odin;

use App\Models\Odin\CompanyUser;
use Illuminate\Support\Collection;

class CompanyUserTransformer
{
    public function __construct(protected CompanyUserDataTransformer $dataTransformer) {}

    /**
     * @param Collection $users
     * @return array
     */
    public function transformUsers(Collection $users): array
    {
        return $users->map(function (CompanyUser $user) {
            return $this->transformUser($user);
        })->toArray();
    }

    /**
     * @param CompanyUser $user
     * @return array
     */
    public function transformUser(CompanyUser $user): array
    {
        return
            [
                'id'                                => $user->{CompanyUser::FIELD_ID},
                'name'                              => $user->completeName(),
                'first_name'                        => $user->{CompanyUser::FIELD_FIRST_NAME},
                'last_name'                         => $user->{CompanyUser::FIELD_LAST_NAME},
                'title'                             => $user->{CompanyUser::FIELD_TITLE},
                'department'                        => $user->{CompanyUser::FIELD_DEPARTMENT},
                'email'                             => $user->{CompanyUser::FIELD_EMAIL},
                'notes'                             => $user->{CompanyUser::FIELD_NOTES},
                'status'                            => $user->{CompanyUser::FIELD_STATUS},
                'pinned'                            => $user->{CompanyUser::FIELD_PINNED},
                'date_registered'                   => $user->{CompanyUser::CREATED_AT}?->toIso8601String(),
                'cell_phone'                        => $user->{CompanyUser::FIELD_CELL_PHONE},
                'office_phone'                      => $user->{CompanyUser::FIELD_OFFICE_PHONE},
                'is_contact'                        => $user->{CompanyUser::FIELD_IS_CONTACT},
                'is_decision_maker'                 => $user->{CompanyUser::FIELD_IS_DECISION_MAKER},
                'can_receive_promotions'            => $user->{CompanyUser::FIELD_CAN_RECEIVE_PROMOTIONS},
                'total_calls_count'                 => $user['office_phone_calls_count'] + $user['cell_phone_calls_count'],
                'total_calls_over_one_minute_count' => $user->office_phone_calls_over_one_minute->count() + $user->cell_phone_calls_over_one_minute->count(),
                'latest_call_timestamp'             => $user->latest_call?->call_end?->getTimestamp(),
                'created_by_id'                     => $user->{CompanyUser::FIELD_CREATED_BY_ID},
                'updated_by_id'                     => $user->{CompanyUser::FIELD_UPDATED_BY_ID},
                'user_fields'                        => $this->dataTransformer->transform($user),
            ];
    }
}
