<?php

namespace App\Transformers\Odin\v2;

use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\Odin\Product;
use App\Enums\Odin\SaleTypes;
use App\Models\Campaigns\CompanyCampaignRelation;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Odin\Company;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductRejection;
use App\Services\LeadProcessing\LeadProcessingService;
use Carbon\Carbon;

class EloquentQuoteCompanyTransformer
{
    const TRANSFORM_ERROR_PREFIX = "Admin2 ProductAssignment sync error: ";

    public function transform(ProductAssignment $productAssignment, int $campaignId): ?array
    {
        $consumerProduct = $productAssignment->consumerProduct;
        $consumer = $consumerProduct->consumer;
        $legacyCompanyId = $this->getLegacyCompanyId($productAssignment);
        $legacyQuoteId = $this->getLegacyQuoteId($consumer);

        if (!$legacyCompanyId || !$legacyQuoteId) {
            $errorSource = !$legacyCompanyId
                ? "legacy company ID"
                : "legacy quote ID";
            $this->handleSyncTransformError("Failed to transform Product Assignment ID " . $productAssignment->id . " due to missing " . $errorSource);

            return null;
        }

        return [
            EloquentQuoteCompany::QUOTE_COMPANY_ID                          => $productAssignment->legacy_id ?? null,
            EloquentQuoteCompany::COMPANY_ID                                => $legacyCompanyId,
            EloquentQuoteCompany::QUOTE_ID                                  => $legacyQuoteId,
            EloquentQuoteCompany::LEAD_CAMPAIGN_SALES_TYPE_CONFIGURATION_ID => $this->getSaleTypeConfigurationId($productAssignment, $campaignId),
            EloquentQuoteCompany::COST                                      => $productAssignment->cost,
            EloquentQuoteCompany::REJECT_NOTES                              => $this->getRejectionReasons($productAssignment),
            EloquentQuoteCompany::CHARGEABLE                                => $productAssignment->chargeable,
            EloquentQuoteCompany::DELIVERED                                 => $productAssignment->delivered,
            EloquentQuoteCompany::INCLUDE_IN_BUDGET                         => !$productAssignment->exclude_budget,
            EloquentQuoteCompany::SALE_TYPE                                 => $this->getSaleTypeString($productAssignment),
            EloquentQuoteCompany::LEAD_COST_TYPE                            => $this->getLegacyLeadCostType($consumerProduct, $consumer),
            EloquentQuoteCompany::CHARGE_STATUS                             => $this->getChargeStatus($productAssignment),
            EloquentQuoteCompany::TIMESTAMP_REJECTED_AT                     => $this->getRejectionTimestamp($productAssignment) ?? 0,
            EloquentQuoteCompany::TIMESTAMP_DELIVERED                       => $this->getLegacyTimestamp($productAssignment->delivered_at) ?? 0,
            EloquentQuoteCompany::TIMESTAMP_REJECTION_EXPIRY                => $this->getLegacyTimestamp($productAssignment->rejection_expiry) ?? 0,
            EloquentQuoteCompany::TIMESTAMP_ADDED                           => $this->getLegacyTimestamp($productAssignment->created_at),
        ];
    }

    /**
     * @param ProductAssignment $productAssignment
     * @return array
     */
    public function transformForUpdate(ProductAssignment $productAssignment): array
    {
        $consumerProduct = $productAssignment->consumerProduct;

        return [
            EloquentQuoteCompany::ID                                        => $productAssignment->legacy_id,
            EloquentQuoteCompany::COST                                      => $productAssignment->cost,
            EloquentQuoteCompany::REJECT_NOTES                              => $this->getRejectionReasons($productAssignment),
            EloquentQuoteCompany::CHARGEABLE                                => $productAssignment->chargeable,
            EloquentQuoteCompany::DELIVERED                                 => $productAssignment->delivered,
            EloquentQuoteCompany::INCLUDE_IN_BUDGET                         => !$productAssignment->exclude_budget,
            EloquentQuoteCompany::CHARGE_STATUS                             => $this->getChargeStatus($productAssignment),
            EloquentQuoteCompany::SALE_TYPE                                 => $this->getSaleTypeString($productAssignment),
            EloquentQuoteCompany::TIMESTAMP_REJECTED_AT                     => $this->getRejectionTimestamp($productAssignment) ?? 0,
            EloquentQuoteCompany::TIMESTAMP_DELIVERED                       => $this->getLegacyTimestamp($productAssignment->delivered_at) ?? 0,
            EloquentQuoteCompany::TIMESTAMP_REJECTION_EXPIRY                => $this->getLegacyTimestamp($productAssignment->rejection_expiry) ?? 0,
        ];
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @return array
     */
    public function transformQuoteStatusUpdate(ConsumerProduct $consumerProduct): array
    {
        return [
            EloquentQuote::QUOTE_ID => $consumerProduct->consumer->legacy_id,
            EloquentQuote::STATUS   => $this->getEloquentQuoteStatus($consumerProduct),
        ];
    }

    /**
     * @param ProductAssignment $productAssignment
     * @return int
     */
    protected function getLegacyCompanyId(ProductAssignment &$productAssignment): int
    {
        return Company::query()->find($productAssignment->company_id)->legacy_id;
    }

    /**
     * @param Consumer $consumer
     * @return int
     */
    protected function getLegacyQuoteId(Consumer &$consumer): int
    {
        return $consumer->legacy_id;
    }

    /**
     * @param ProductAssignment $productAssignment
     * @param int $campaignId
     * @return int|null
     */
    protected function getSaleTypeConfigurationId(ProductAssignment &$productAssignment, int $campaignId): ?int
    {
        $odinSaleType = SaleTypes::tryFrom($productAssignment->saleType->name);
        $legacySaleTypeId = SaleTypes::mapSaleTypeToLegacyId($odinSaleType);
        $leadCampaignId = CompanyCampaignRelation::query()
            ->where(CompanyCampaignRelation::FIELD_COMPANY_CAMPAIGN_ID, $campaignId)
            ->first()
            ?->{CompanyCampaignRelation::FIELD_RELATION_ID};

        return LeadCampaignSalesTypeConfiguration::query()
            ->where(LeadCampaignSalesTypeConfiguration::LEAD_CAMPAIGN_ID, $leadCampaignId)
            ->where(LeadCampaignSalesTypeConfiguration::LEAD_SALES_TYPE_ID, $legacySaleTypeId)
            ->first()
            ?->{LeadCampaignSalesTypeConfiguration::ID}
            ?? null;
    }

    /**
     * @param ProductAssignment $productAssignment
     * @return string
     */
    protected function getRejectionReasons(ProductAssignment &$productAssignment): string
    {
        return $productAssignment->productRejections
            ->map(fn(ProductRejection $rejection) => $rejection->reason)
            ->join('; ');
    }

    /**
     * @param ProductAssignment $productAssignment
     * @return string
     */
    protected function getSaleTypeString(ProductAssignment &$productAssignment): string
    {
        return $productAssignment->saleType?->key === SaleTypes::EXCLUSIVE->value
            ? EloquentQuoteCompany::LEGACY_SALE_TYPE_EXCLUSIVE
            : EloquentQuoteCompany::LEGACY_SALE_TYPE_MULTIPLE;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param Consumer $consumer
     * @return string
     */
    protected function getLegacyLeadCostType(ConsumerProduct &$consumerProduct, Consumer &$consumer): string
    {
        if ($consumerProduct->serviceProduct->product->name === Product::APPOINTMENT->value)
            return EloquentQuoteCompany::VALUE_LEAD_COST_TYPE_APPOINTMENT;
        else if ($this->isPremiumLead($consumerProduct, $consumer))
            return EloquentQuoteCompany::VALUE_LEAD_COST_TYPE_PREMIUM;
        else
            return EloquentQuoteCompany::VALUE_LEAD_COST_TYPE_PAYPERLEAD;
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param Consumer $consumer
     * @return bool
     */
    protected function isPremiumLead(ConsumerProduct &$consumerProduct, Consumer &$consumer): bool
    {
        $dataPayload = $consumerProduct->consumerProductData->payload;
        $electricCost = $consumer->legacyLead?->electriccost ?? 0;

        return (
            $electricCost >= LeadProcessingService::PREMIUM_ELECTRICAL_USAGE
            && !(
                empty($dataPayload[GlobalConfigurableFields::BEST_TIME_TO_CALL->value])
                && empty($dataPayload[GlobalConfigurableFields::BEST_TIME_TO_CALL_OTHER->value])
            )
        );
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @return string
     */
    protected function getEloquentQuoteStatus(ConsumerProduct &$consumerProduct): string
    {
        return match($consumerProduct->status) {
            ConsumerProduct::STATUS_ALLOCATED => EloquentQuote::VALUE_STATUS_ALLOCATED,
            ConsumerProduct::STATUS_UNSOLD    => EloquentQuote::VALUE_STATUS_NO_COMPANIES,
            default                           => EloquentQuote::VALUE_STATUS_INITIAL
        };
    }

    /**
     * @param ProductAssignment $productAssignment
     * @return string
     */
    protected function getChargeStatus(ProductAssignment &$productAssignment): string
    {
        return $productAssignment->productRejections()
            ->whereNull(ProductRejection::FIELD_DELETED_AT)
            ->count() > 0
                ? EloquentQuoteCompany::VALUE_CHARGE_STATUS_REJECTED
                : EloquentQuoteCompany::VALUE_CHARGE_STATUS_INITIAL;
    }

    /**
     * @param ProductAssignment $productAssignment
     * @return int|null
     */
    protected function getRejectionTimestamp(ProductAssignment &$productAssignment): ?int
    {
        return $this->getLegacyTimestamp(
            $productAssignment->productRejections()
                ->latest()
                ->first()
                ?->created_at ?? null
        );
    }

    /**
     * @param Carbon|null $date
     * @return int|null
     */
    protected function getLegacyTimestamp(?Carbon $date): ?int
    {
        return $date?->timestamp ?? null;
    }

    /**
     * @param string $errorMessage
     * @return void
     */
    protected function handleSyncTransformError(string $errorMessage): void
    {
        logger()->error(self::TRANSFORM_ERROR_PREFIX . $errorMessage);
    }
}
