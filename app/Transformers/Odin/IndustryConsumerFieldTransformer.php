<?php

namespace App\Transformers\Odin;

use App\Http\Resources\Odin\ConsumerConfigurableFieldCategoryResource;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryConsumerField;
use Illuminate\Support\Collection;

class IndustryConsumerFieldTransformer
{
    /**
     * @param ConfigurableFieldTypeTransformer $fieldTypeTransformer
     */
    public function __construct(protected ConfigurableFieldTypeTransformer $fieldTypeTransformer){}

    /**
     * @param Collection $industryConsumerFields
     * @return array
     */
    public function transform(Collection $industryConsumerFields): array
    {
        return $industryConsumerFields->map(fn(IndustryConsumerField $industryConsumerField) => $this->transformIndustryConsumerField($industryConsumerField))->toArray();
    }

    /**
     * @param IndustryConsumerField $industryConsumerField
     * @return array
     */
    public function transformIndustryConsumerField(IndustryConsumerField $industryConsumerField): array
    {
        $category = new ConsumerConfigurableFieldCategoryResource($industryConsumerField->{IndustryConsumerField::RELATION_FIELD_CATEGORY});

        return
            [
                'id'                => $industryConsumerField->{IndustryConsumerField::FIELD_ID},
                'industry'          => $industryConsumerField->{IndustryConsumerField::RELATION_INDUSTRY}?->{Industry::FIELD_NAME},
                'name'              => $industryConsumerField->{IndustryConsumerField::FIELD_NAME},
                'key'               => $industryConsumerField->{IndustryConsumerField::FIELD_KEY},
                'type'              => $industryConsumerField->{IndustryConsumerField::RELATION_FIELD_TYPE} ?
                                     $this->fieldTypeTransformer->transformConfigurableFieldType($industryConsumerField->{IndustryConsumerField::RELATION_FIELD_TYPE}) : null,
                'send_to_company'   => $industryConsumerField->{IndustryConsumerField::FIELD_SEND_TO_COMPANY} === 1 ? 'Yes' : 'No',
                'created_at'        => $industryConsumerField->{IndustryConsumerField::CREATED_AT}?->toIso8601String(),
                'category_id'       => $industryConsumerField->{IndustryConsumerField::FIELD_CATEGORY_ID},
                'category'          => $category,
                'show_on_dashboard' => $industryConsumerField->{IndustryConsumerField::FIELD_SHOW_ON_DASHBOARD} === 1 ? 'Yes' : 'No',
            ];
    }
}

