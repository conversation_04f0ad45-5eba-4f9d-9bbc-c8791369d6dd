<?php

namespace App\Transformers\Odin;

use App\Enums\ActivityType;
use App\Enums\CompanyConsolidatedStatus;
use App\Enums\CompanyMetrics\CompanyMetricRequestTypes;
use App\Enums\CompanySalesStatus;
use App\Helpers\NumberHelper;
use App\Http\Resources\Odin\Company\Status\AdminStatusResource;
use App\Http\Resources\Odin\Company\Status\CampaignStatusResource;
use App\Http\Resources\Odin\Company\Status\SystemStatusResource;
use App\Models\ActivityFeed;
use App\Models\Cadence\CompanyCadenceRoutine;
use App\Models\CompanyMetric;
use App\Models\ComputedRejectionStatistic;
use App\Models\Odin\Industry;
use App\Enums\Odin\SolarConfigurableFields;
use App\Jobs\CalculateAndStoreGoogleRatingsJob;
use App\Models\Call;
use App\Models\Cadence\CadenceRoutine;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentCompanyCRMAction;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Company;
use App\Models\Odin\Product;
use App\Repositories\ComputedRejectionStatisticRepository;
use App\Repositories\Legacy\CompanyRepository as LegacyCompanyRepository;
use App\Repositories\Legacy\CompanyCRMRepository;
use App\Repositories\Legacy\QuoteCompanyRepository;
use App\Services\Companies\Delete\CompanyDeleteService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Arr;
use Illuminate\Support\Number;
use Illuminate\Support\Collection;

class CompanySearchTransformer
{

    protected Collection $products;
    protected Collection $computedRejectionPercentageStatistics;
    protected array $deliveredChargeableAndBudgetQuoteCompanies;
    protected Collection $companiesLastUpdated;
    protected array $mostRecentQuoteCompaniesByCompanyIds;
    protected Collection $companyMetrics;

    /**
     * @param LegacyCompanyRepository $legacyCompanyRepository
     * @param ComputedRejectionStatisticRepository $rejectionRepository
     * @param QuoteCompanyRepository $quoteCompanyRepository
     * @param CompanyCRMRepository $companyCRMRepository
     */
    public function __construct(
        protected LegacyCompanyRepository              $legacyCompanyRepository,
        protected ComputedRejectionStatisticRepository $rejectionRepository,
        protected QuoteCompanyRepository               $quoteCompanyRepository,
        protected CompanyCRMRepository                 $companyCRMRepository,
    )
    {
    }

    /**
     * @param array $deliveredChargeableAndBudgetQuoteCompanies
     * @return void
     */
    public function setDeliveredChargeableAndBudgetQuoteCompanies(array $deliveredChargeableAndBudgetQuoteCompanies): void
    {
        $this->deliveredChargeableAndBudgetQuoteCompanies = $deliveredChargeableAndBudgetQuoteCompanies;
    }

    /**
     * @param Collection $companiesLastUpdated
     * @return void
     */
    public function setCompaniesLastUpdated(Collection $companiesLastUpdated): void
    {
        $this->companiesLastUpdated = $companiesLastUpdated;
    }

    /**
     * @param array $mostRecentQuoteCompaniesByCompanyIds
     * @return void
     */
    public function setMostRecentQuoteCompaniesByCompanyIds(array $mostRecentQuoteCompaniesByCompanyIds): void
    {
        $this->mostRecentQuoteCompaniesByCompanyIds = $mostRecentQuoteCompaniesByCompanyIds;
    }

    /**
     * @param Collection $activityFeeds
     * @return void
     */
    public function setActivityFeeds(Collection $activityFeeds): void
    {
        $this->activityFeeds = $activityFeeds;
    }

    /**
     * @param Collection $companyMetrics
     * @return void
     */
    public function setCompanyMetrics(Collection $companyMetrics): void
    {
        $this->companyMetrics = $companyMetrics;
    }

    /**
     * @param Collection $products
     * @return void
     */
    public function setProducts(Collection $products): void
    {
        $this->products = $products;
    }

    /**
     * @param Collection $computedRejectionPercentageStatistics
     * @return void
     */
    public function setComputedRejectionPercentageStatistics(Collection $computedRejectionPercentageStatistics): void
    {
        $this->computedRejectionPercentageStatistics = $computedRejectionPercentageStatistics;
    }

    /**
     * @param Collection $companies
     * @return array
     */
    public function transformCompanies(Collection $companies): array
    {
        return $companies->map(fn(Company $company) => $this->transform($company))->toArray();
    }

    /**
     * @param Company $company
     * @return array
     */
    private function getLegacyDataArray(
        Company $company,
    ): array
    {
        $moneyFmt = new \NumberFormatter('en_US', \NumberFormatter::CURRENCY);

        if ($company->{Company::FIELD_LEGACY_ID}) {
            $leadCost = Arr::get($this->deliveredChargeableAndBudgetQuoteCompanies, $company->{Company::FIELD_LEGACY_ID});

            $crmActions = $this->getCrmActions($this->companiesLastUpdated, $company->{Company::FIELD_LEGACY_ID});

            $mostRecentQuoteTime = Arr::get($this->mostRecentQuoteCompaniesByCompanyIds, $company->{Company::FIELD_LEGACY_ID});

            $leadProduct        = $this->products->where(Product::FIELD_NAME, \App\Enums\Odin\Product::LEAD->value)->first();
            $appointmentProduct = $this->products->where(Product::FIELD_NAME, \App\Enums\Odin\Product::APPOINTMENT->value)->first();

            $leadRejection = $this->getFirstComputedRejectionPercentageThatMatchesCompanyAndProduct(
                $company,
                $leadProduct,
                $this->computedRejectionPercentageStatistics
            );

            $overallLeadRejectionPercentage        = $leadRejection?->{ComputedRejectionStatistic::FIELD_OVERALL_REJECTION_PERCENTAGE} ?? 0;
            $overallAppointmentRejectionPercentage = $appointmentRejection?->{ComputedRejectionStatistic::FIELD_OVERALL_REJECTION_PERCENTAGE} ?? 0;

            return [
                'legacy_id'                        => $company->{Company::FIELD_LEGACY_ID},
                'lead_rejection_percentage'        => $overallLeadRejectionPercentage,
                'appointment_rejection_percentage' => $overallAppointmentRejectionPercentage,
                'campaigns_total'                  => $company->{Company::RELATION_CAMPAIGNS}?->count(),
                'campaigns_active_count'           => $company->{Company::RELATION_CAMPAIGNS}?->where(LeadCampaign::STATUS, LeadCampaign::STATUS_ACTIVE)?->count(),
                'total_cost_spent'                 => $leadCost,
                'last_revised'                     => !empty($crmActions[$company->{Company::FIELD_LEGACY_ID}][EloquentCompanyCRMAction::TIMESTAMP_ADDED]) ? Carbon::createFromTimestamp($crmActions[$company->{Company::FIELD_LEGACY_ID}][EloquentCompanyCRMAction::TIMESTAMP_ADDED])->format('m/d/Y') : '',
                'last_time_lead_received'          => $mostRecentQuoteTime ? Carbon::createFromTimestamp($mostRecentQuoteTime)->format('m/d/Y') : '',
                'lead'                             => [
                    'rejection_percentage' => [
                        'manual'  => $leadRejection?->{ComputedRejectionStatistic::FIELD_MANUAL_REJECTION_PERCENTAGE} ?? 0,
                        'crm'     => $leadRejection?->{ComputedRejectionStatistic::FIELD_CRM_REJECTION_PERCENTAGE} ?? 0,
                        'overall' => $overallLeadRejectionPercentage,
                    ]
                ],
            ];
        }

        return [
            'legacy_id'                        => null,
            'lead_rejection_percentage'        => 0,
            'appointment_rejection_percentage' => 0,
            'campaigns_total'                  => 0,
            'campaigns_active_count'           => 0,
            'total_cost_spent'                 => $moneyFmt->formatCurrency(0, "USD"),
            'last_revised'                     => null,
            'last_time_lead_received'          => null,
        ];
    }

    /**
     * @param Company $company
     * @return array
     */
    public function transform(
        Company $company,
    ): array
    {
        $legacyData         = $this->getLegacyDataArray($company);
        $companyPayloadData = $this->getCompanyPayloadDataArray($company);

        $leadCostOne = $company?->lead_cost_one ?? $legacyData['total_cost_spent'];

        if (gettype($leadCostOne) == 'string') {
            $leadCostOne = intval(str_replace('$', '', $leadCostOne));
        }

        $leadCostTwo = $company?->lead_cost_two ? $company->lead_cost_two : null;

        $leadCostOneFormatted = '$' . number_format(round($leadCostOne), 0, '.', ',');
        $leadCostTwoFormatted = $leadCostTwo ? '$' . number_format(round($leadCostTwo), 0, '.', ',') : null;

        $companyLastContactedAt = $company->getLastContactedAt($this->activityFeeds);

        $companyLastContactedAt = !!$companyLastContactedAt && !!data_get($companyLastContactedAt,
            'last_contacted_at_type')
            ? $companyLastContactedAt
            : [
                'last_contacted_at_type'      => null,
                'last_contacted_at_date'      => null,
                'last_contacted_at_direction' => null,
            ];

        $latestCall            = $this->getLatestCall($this->activityFeeds, $company->{Company::FIELD_ID})?->{Call::FIELD_CALL_END};
        $latestCompanyPPCSpend = $this->latestCompanyPPCSpendMetricsFromCompanyMetricsCollection($this->companyMetrics, $company->{Company::FIELD_ID});
        $companyDeleteService = new CompanyDeleteService(company: $company);
        $companyDeleteService->validate();

        return array_merge($legacyData, $companyPayloadData, $companyLastContactedAt, [
            'id'                         => $company->{Company::FIELD_ID},
            'name'                       => $company->{Company::FIELD_NAME},
            'entity_name'                => $company->{Company::FIELD_ENTITY_NAME},
            'admin_status'               => new AdminStatusResource($company->admin_status),
            'industries'                 => $company->{Company::RELATION_INDUSTRIES}->pluck(Industry::FIELD_NAME)->toArray(),
            'services'                   => $company->{Company::RELATION_SERVICES}->toArray(),
            'can_receive_leads'          => $company->consolidated_status === CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS,
            'status'                     => CompanyConsolidatedStatus::label($company->consolidated_status),
            'sales_status'               => CompanySalesStatus::label($company->sales_status),
            'consolidated_status'        => $company->{Company::FIELD_CONSOLIDATED_STATUS},
            'consolidated_status_object' => $company->consolidated_status_object,
            'system_status'              => new SystemStatusResource($company->system_status),
            'campaign_status'            => new CampaignStatusResource($company->campaign_status),
            'addresses'                  => $company->{Company::RELATION_LOCATIONS},
            'review_count'               => $company->{Company::RELATION_REVIEWS}->count(),
            'lead_cost_one'              => $leadCostOne,
            'lead_cost_two'              => $leadCostTwo,
            'last_time_called'           => $latestCall ? $latestCall->format("F j Y g:i A") : null,
            'lead_cost_one_formatted'    => $leadCostOneFormatted,
            'lead_cost_two_formatted'    => $leadCostTwoFormatted,
            'prescreened'                => $company->prescreened(),
            'cadence_name'               => $company->{Company::RELATION_CADENCE_ROUTINES}?->first()->{CompanyCadenceRoutine::RELATION_CADENCE_ROUTINE}?->{CadenceRoutine::FIELD_NAME} ?? null,
            'estimated_monthly_ad_spend' => $latestCompanyPPCSpend ? Number::currency($latestCompanyPPCSpend->{CompanyMetric::FIELD_REQUEST_RESPONSE}['monthlySpend']) : null,
            'lifetime_revenue'           => NumberHelper::currencyFromCents($company->lifetime_revenue ?? 0),
            'deletable'                  => $companyDeleteService->isDeletable(),
            'queued'                     => $companyDeleteService->markedForDeletion(),
            'bdm'                        => $company->businessDevelopmentManager?->name,
            'am'                         => $company->accountManager?->name,
            'om'                         => $company->onboardingManager?->name,
        ]);
    }

    /**
     * @param Company $company
     * @return array
     */
    protected function getCompanyPayloadDataArray(Company $company): array
    {
        $companyData = $company->data?->payload ?? [];

        return [
            'estimated_revenue'   => ($companyData[SolarConfigurableFields::REVENUE_IN_THOUSANDS->value] ?? 0) * 1000,
            'google_rating'       => $companyData[CalculateAndStoreGoogleRatingsJob::GOOGLE_RATING] ?? 0,
            'google_review_count' => $companyData[CalculateAndStoreGoogleRatingsJob::GOOGLE_REVIEW_COUNT] ?? 0,
        ];
    }

    /**
     * @param Collection $companiesLastUpdated
     * @param int $legacyCompanyId
     * @return array
     */
    private function getCrmActions(Collection $companiesLastUpdated, int $legacyCompanyId): array
    {
        return $companiesLastUpdated->where(EloquentCompanyCRMAction::COMPANY_ID, $legacyCompanyId)->toArray();
    }

    /**
     * @param Collection $deliveredChargeableAndBudgetQuoteCompanies
     * @param int $legacyCompanyId
     * @return array
     */
    private function getCompaniesSpent(Collection $deliveredChargeableAndBudgetQuoteCompanies, int $legacyCompanyId): array
    {
        $companiesSpent = $deliveredChargeableAndBudgetQuoteCompanies;

        $companyIdsInitDeliveryTimes = [];
        $companyIdsInitDeliveryTimes[$legacyCompanyId] = Carbon::today()->subDays(29)->timestamp; // 30 days ago including today

        $result = $companiesSpent->where(function ($query) use ($companyIdsInitDeliveryTimes) {
            foreach ($companyIdsInitDeliveryTimes as $companyId => $timestampBudgetStart) {
                $query->orWhere(function ($query) use ($companyId, $timestampBudgetStart) {
                    $query
                        ->where(EloquentQuoteCompany::COMPANY_ID, '=', $companyId)
                        ->where(EloquentQuoteCompany::TIMESTAMP_INITIAL_DELIVERY, '>=', $timestampBudgetStart);
                });
            }
        });

        return $result->toArray();
    }

    /**
     * @param Collection $mostRecentQuoteCompaniesByCompanyIds
     * @param int $legacyCompanyId
     * @return int|null
     */
    private function getMostRecentQuoteTime(array $mostRecentQuoteCompaniesByCompanyIds, int $legacyCompanyId): int|null
    {
        return $mostRecentQuoteCompaniesByCompanyIds
            ->where(EloquentCompany::COMPANY_ID, $legacyCompanyId)
            ->first()?->{EloquentQuoteCompany::TIMESTAMP_DELIVERED};
    }

    /**
     * @param Collection $companyMetrics
     * @param int $companyId
     * @return CompanyMetric|Model
     */
    private function latestCompanyPPCSpendMetricsFromCompanyMetricsCollection(Collection $companyMetrics, int $companyId): CompanyMetric|null
    {
        return $companyMetrics
            ->where(CompanyMetric::FIELD_COMPANY_ID, $companyId)
            ->where(CompanyMetric::FIELD_REQUEST_TYPE, '=', CompanyMetricRequestTypes::PPC_SPEND)
            ->sortByDesc(CompanyMetric::FIELD_CREATED_AT)
            ->first();
    }

    /**
     * @param Collection $activityFeeds
     * @param int $companyId
     * @return Collection|Call|null
     */
    private function getLatestCall(Collection $activityFeeds, int $companyId): Collection|Call|null
    {
        return $activityFeeds
            ->where(ActivityFeed::FIELD_COMPANY_ID, $companyId)
            ->whereIn(ActivityFeed::FIELD_ITEM_TYPE, [ActivityType::CALL->value])
            ->sortByDesc(ActivityFeed::UPDATED_AT)
            ->first()?->item;
    }

    /**
     * @param Company $company
     * @param Product $product
     * @param Collection $computedRejectionStatistics
     * @return ComputedRejectionStatistic|null
     */
    private function getFirstComputedRejectionPercentageThatMatchesCompanyAndProduct(
        Company    $company,
        Product    $product,
        Collection $computedRejectionStatistics
    ): ?ComputedRejectionStatistic
    {
        return $computedRejectionStatistics
            ->where(ComputedRejectionStatistic::FIELD_COMPANY_ID, $company->{Company::FIELD_ID})
            ->where(ComputedRejectionStatistic::FIELD_PRODUCT_ID, $product->{Product::FIELD_ID})
            ->first();
    }
}
