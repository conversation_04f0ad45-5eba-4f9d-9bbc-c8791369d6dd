<?php

namespace App\Transformers\LeadProcessing;

use App\Models\LeadProcessingTeam;
use App\Models\Odin\Industry;
use Illuminate\Support\Collection;

class TeamTransformer
{
    /**
     * Handles transforming a collection of teams to their respective client side representation
     *
     * @param Collection $teams
     * @return array
     */
    public function transformLeadProcessingTeams(Collection $teams): array
    {
        return $teams->map(function ($team) {
            return $this->transformLeadProcessingTeam($team);
        })->values()->toArray();
    }

    /**
     * Handles transforming a given team to its respective client side representation.
     *
     * @param LeadProcessingTeam $team
     * @return array
     */
    public function transformLeadProcessingTeam(LeadProcessingTeam $team): array
    {
        return [
            "id"             => $team->id,
            "name"           => $team->name,
            "queue"          => $team->primaryQueue ? $team->primaryQueue->name : "No Queue",
            "queue_id"       => $team->primary_queue_configuration_id,
            "timezone"       => $this->formatTimezone($team->primary_utc_offset),
            "timezone_value" => $team->primary_utc_offset,
            "industry"       => $team->industry,
            "industry_name"  => $team->industry?->name,
            "industries" => $team->industries->map(fn(Industry $industry) => ['id' => $industry->id, 'name' => $industry->name])
        ];
    }

    /**
     * Formats an utc offset as a human-readable timezone.
     *
     * @param int $offset
     * @return string
     */
    protected function formatTimezone(int $offset): string
    {
        return match ($offset) {
            -5 => "Eastern",
            -6 => "Central",
            -7 => "Mountain",
            -8 => "Pacific",
            default => "Other",
        };

    }
}
