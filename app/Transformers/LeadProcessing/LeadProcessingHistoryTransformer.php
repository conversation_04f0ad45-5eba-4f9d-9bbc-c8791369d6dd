<?php

namespace App\Transformers\LeadProcessing;

use App\Models\LeadProcessingHistory;
use App\Models\LeadProcessingQueueConfiguration;
use Illuminate\Support\Collection;

class LeadProcessingHistoryTransformer
{
    /**
     * Handles transforming a list of history entries to their client side representation.
     *
     * @param Collection $entries
     * @return array
     */
    public function transformLeadProcessingHistoryEntries(Collection $entries): array
    {
        return $entries->map(function(LeadProcessingHistory $entry) {
            return $this->transformLeadProcessingHistoryEntry($entry);
        })->values()->toArray();
    }

    /**
     * Handles transforming a single history entry to its client side representation
     *
     * @param LeadProcessingHistory $entry
     * @return array
     */
    public function transformLeadProcessingHistoryEntry(LeadProcessingHistory $entry): array
    {
        return [
            "id" => $entry->{LeadProcessingHistory::FIELD_ID},
            "lead_id" => $entry->{LeadProcessingHistory::FIELD_LEAD_ID},
            "queue" => $entry->{LeadProcessingHistory::RELATION_QUEUE_CONFIGURATION}?->{LeadProcessingQueueConfiguration::FIELD_NAME},
            "action" => $entry->{LeadProcessingHistory::FIELD_ACTION},
            "created_time" => $entry->{LeadProcessingHistory::CREATED_AT}->format("m/d/Y H:i T")
        ];
    }
}
