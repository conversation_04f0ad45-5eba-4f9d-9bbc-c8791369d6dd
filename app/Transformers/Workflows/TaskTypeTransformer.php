<?php

namespace App\Transformers\Workflows;

use App\Models\TaskType;
use Illuminate\Support\Collection;

class TaskTypeTransformer
{
    /**
     * Handles transforming a collection of task types to their client side representation.
     *
     * @param Collection $items
     * @return array
     */
    public function transformTaskTypes(Collection $items): array
    {
        return $items->map(fn(TaskType $taskType) => $this->transform($taskType))->toArray();
    }

    /**
     * Handles transforming a task type to it's client side representation.
     *
     * @param TaskType $taskType
     * @return array
     */
    public function transform(TaskType $taskType): array
    {
        return [
            "id"      => $taskType->id,
            "name"    => $taskType->name,
            "modules" => $taskType->modules ?? [],
        ];
    }
}
