<?php

namespace App\ConsumerProcessing\Jobs;

use App\ConsumerProcessing\Events\PubSubAllocationEventDispatcher;
use App\ConsumerProcessing\Services\AllocationSchedulingService;
use App\ConsumerProcessing\Services\ConsumerProjectProcessingService;
use App\Contracts\ProductAssignment\MultiProductAssignmentStrategyContract;
use App\DataModels\Campaigns\ConsumerProject;
use App\DataModels\ProductAssignment\ProposedProductAssignment;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Enums\Odin\JobStatus;
use App\Enums\Odin\JobTrackingRelation;
use App\Jobs\OpportunityNotifications\CreateMissedProductJob;
use App\Jobs\UpdateJobTrackingJob;
use App\Mediators\CampaignMediator;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\LeadProcessingAllocation;
use App\Models\LeadProcessor;
use App\Models\Legacy\EloquentQuote;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\JobTracking;
use App\Repositories\LeadProcessing\LeadProcessingQueueRepository;
use App\Repositories\Legacy\LeadProcessingRepository;
use App\Services\ConsumerProductLifecycleTrackingService;
use App\Services\Odin\ConsumerProductService;
use Carbon\Carbon;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;
use Throwable;

class AttemptConsumerProjectAllocationJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    const int ONE_HOUR              = 60 * 60;
    const int TWO_HOURS             = 60 * 60 * 2;
    const int SIX_HOURS             = 60 * 60 * 6;
    const int FIVE_MINUTES          = 5 * 60;
    const int ONE_HOUR_FIVE_MINUTES = (60 * 60) + (60 * 5);

    const int GLOBAL_ALLOCATION_LIMIT = 4;
    const int MAX_ATTEMPTS = 3;

    const int DEFAULT_EVENT_EXPIRY = 60 * 60 * 24;

    const string REASON_ALLOCATION_FAILED = 'All allocation attempts failed to find buyer';
    const string REASON_ZERO_REQUESTS     = 'Qualify Angi';

    public int $tries = 3;

    protected ConsumerProductLifecycleTrackingService $tracker;

    /**
     * @param ConsumerProject $consumerProject
     * @param bool $finalAttempt
     * @param int $allocationAttempt
     * @param array|null $jobAttempts
     */
    public function __construct(
        public ConsumerProject $consumerProject,
        public bool $finalAttempt = false,
        public int $allocationAttempt = 1,
        public ?array $jobAttempts = [],
    ) {
        $this->onQueue(config('queue.named_queues.lead_allocation_queue'));
    }

    /**
     * Handles finding available campaigns/budgets for lead allocation, and scheduling allocation
     *
     * @return void
     * @throws BindingResolutionException
     * @throws Exception
     */
    public function handle(
        CampaignMediator                        $campaignMediator,
        AllocationSchedulingService             $schedulingService,
        ConsumerProjectProcessingService        $processingService,
        ConsumerProductLifecycleTrackingService $tracker
    )
    {
        $this->tracker = $tracker;
        $this->tracker->beginAllocationAttempt($this->consumerProject->leadConsumerProduct(), $this->job?->uuid(), $this->job?->attempts());
        $this->consumerProject->setTracker($tracker);

        $this->jobAttempts[] = Carbon::now()->timestamp;

        logger()->info('AttemptConsumerProjectAllocationJob started ', [
            $this->consumerProject,
            $this->finalAttempt,
        ]);

        // Catch 0-request leads attempting allocation to avoid no-budget purgatory
        if (!$this->consumerProject->max_contact_requests) {
            $this->tracker->concludeAttempt(ConsumerProductLifecycleTrackingService::CONCLUSION_ZERO_CONTACT_REQUESTS);
            $this->moveProductToPendingQueue();
            return;
        }

        //  if TZ isn't open we schedule one more attempt tomorrow midday
        $delayToTZOpen = $schedulingService->delayToTimeZoneOpenForConsumerProject($this->consumerProject);
        $this->tracker->delayToTimeZoneOpen($delayToTZOpen);
        logger()->info('$delayToTZOpen: ', [
            $delayToTZOpen
        ]);

        if (!$this->finalAttempt) {
            if ($this->consumerProject->skipTimezoneDelay && $this->allocationAttempt > self::MAX_ATTEMPTS) {
                $this->finalAttemptNextDay();
                return;
            } elseif ($delayToTZOpen > 0) {
                $this->shouldBeFinalAttempt() ? $this->finalAttemptNextDay() : $this->attemptNextDay();
                return;
            }
        }

        // todo: track campaigns after each module filter
        $campaigns = $processingService->getAvailableCampaigns($this->consumerProject);

        $this->tracker->campaigns($campaigns->pluck(CompanyCampaign::FIELD_ID)->toArray());

        logger()->info('$campaigns : ', [
            $campaigns
        ]);

        if ($campaigns->count() < 1) {
            logger()->info('$campaign count: ', [
                $campaigns->count()
            ]);
            $this->handleNoCompanies();
            return;
        }

        /** @var MultiProductAssignmentStrategyContract $assignmentStrategy */
        $assignmentStrategy = app(MultiProductAssignmentStrategyContract::class);

        $proposedAssignments = $assignmentStrategy->calculate(
            project: $this->consumerProject,
            campaigns: $campaigns,
            productTypes: $processingService->getPotentialProductTypes($this->consumerProject),
            payload: [],
            tracker: $tracker
        );

        $this->tracker->proposedAssignments($proposedAssignments);
        logger()->info('$proposedAssignment count after BRS: ', [
            $proposedAssignments->count()
        ]);

        $notEnoughCampaignsAvailable = !$this->consumerProject->had_budget_coverage && $proposedAssignments->count() < $this->consumerProject->max_contact_requests;
        //TODO: we should persist has_budget_coverage (or similar) to the DB so we know whether or not this is a real undersold lead
        if ($notEnoughCampaignsAvailable) {
            if ($proposedAssignments->count() < 1)
                $this->handleNoCompaniesWithBudget();
            else
                $this->handleUndersold();
        }

        logger()->info('Has proposed $proposedAssignments: ', [
            $proposedAssignments
        ]);

        $newAssignments     = $this->getNewAssignments($proposedAssignments);
        $updatedAssignments = $this->getUpdatedAssignments($proposedAssignments);
        $processingService->updateExistingProductAssignments($updatedAssignments);

        logger()->info('Updating existing ProductAssignments for processingService : ', [
            $processingService
        ]);

        if (!$processingService->companiesEligibleToReceiveAssignments($this->consumerProject, $newAssignments)) {
            logger()->info('No companies eligible to receive assignments for processingService: ', [
                $processingService
            ]);
            $this->handleCompaniesNotEligible();
            logger()->info('handleCompaniesNotEligible worked: ', [
                $processingService
            ]);
            return;
        }

        $allocationData = $campaignMediator->allocate($newAssignments);
        $campaignMediator->postAllocation($this->consumerProject, $allocationData->getProductAssignments());
        $this->tracker->allocationData($allocationData);
        $this->tracker->concludeAttempt($allocationData->hasAssignments() ? ConsumerProductLifecycleTrackingService::CONCLUSION_ALLOCATED : ConsumerProductLifecycleTrackingService::CONCLUSION_UNSOLD);
        logger()->info('CampaignMediator PostAllocation : ', [
            $allocationData
        ]);
        $this->handleConsumerProductStatusUpdates();
        logger()->info('handleConsumerProductStatusUpdates');
        $this->trackJobStatus(JobStatus::COMPLETED);
        logger()->info('trackJobStatus : Job Completed');
    }

    /**
     * @param Collection<int, ProposedProductAssignment> $proposedAssignments
     * @return Collection< int, ProposedProductAssignment>
     */
    private function getNewAssignments(Collection $proposedAssignments): Collection
    {
        return $proposedAssignments->filter(function (ProposedProductAssignment $assignment) {
            return !$assignment->isExistingAssignment;
        });
    }

    /**
     * @param Collection<int, ProposedProductAssignment> $proposedAssignments
     * @return Collection<int, ProposedProductAssignment>
     */
    private function getUpdatedAssignments(Collection $proposedAssignments): Collection
    {
        return $proposedAssignments->filter(function (ProposedProductAssignment $assignment) {
            return $assignment->salesTypeAndPriceUpdated;
        });
    }

    /**
     * @param int $delay
     * @return void
     * @throws BindingResolutionException
     * @throws Exception
     */
    private function scheduleReattempt(int $delay): void
    {
        logger()->info('scheduleReattempt :', [$delay]);
        if ($this->finalAttempt) {
            $this->tracker->concludeAttempt(ConsumerProductLifecycleTrackingService::CONCLUSION_UNSOLD);
            $this->handleConsumerProductStatusUpdates();
            $this->trackJobStatus(JobStatus::COMPLETED);
            logger()->info('handleConsumerProductStatusUpdates & trackJobStatus(JobStatus::COMPLETED)');

            $this->moveProductToUnderReviewQueue();
            $this->createMissedProducts();
            return;
        }

        logger()->info('release job back to queue with delay', [$delay]);

        $this->tracker->concludeAttempt(ConsumerProductLifecycleTrackingService::CONCLUSION_RELEASED_BACK_TO_QUEUE);
        $this->updateJobTrackingAvailableAt($delay);

        self::dispatch(
            consumerProject: $this->consumerProject,
            finalAttempt: false,
            allocationAttempt: $this->allocationAttempt + 1,
            jobAttempts: $this->jobAttempts)->delay($delay);

        ConsumerProductLifecycleTrackingService::allocationAttemptScheduled($this->consumerProject->leadConsumerProduct(), $delay);
        $this->trackJobStatus(JobStatus::COMPLETED);

        // We'll no longer attempt to allocate as an approved lead
        // todo: handle for alternate flow i.e. unverified sale, UR, etc..
    }

    /**
     * @return void
     * @throws BindingResolutionException
     */
    private function handleNoCompanies(): void
    {
        $eventDispatcher = $this->getEventDispatcher();
        $eventProperties = $this->getProductEventProperties();
        $eventDispatcher->dispatchUniqueEvent(
            EventCategory::LEADS,
            EventName::LEAD_UNSOLD_NO_COMPANIES,
            $eventProperties,
            "product-allocation-no-companies-" . $eventProperties['consumer_product_id'],
            self::DEFAULT_EVENT_EXPIRY
        );

        $this->scheduleReattempt(self::ONE_HOUR);
    }

    /**
     * @return void
     * @throws Exception
     */
    private function handleNoCompaniesWithBudget(): void
    {
        $eventDispatcher = $this->getEventDispatcher();
        $eventProperties = $this->getProductEventProperties();
        $eventDispatcher->dispatchUniqueEvent(
            EventCategory::LEADS,
            EventName::LEAD_UNSOLD_NO_BUDGET,
            $eventProperties,
            "product-allocation-no-budget-" . $eventProperties['consumer_product_id'],
            self::DEFAULT_EVENT_EXPIRY
        );
    }

    /**
     * @return void
     * @throws Exception
     */
    private function handleUndersold(): void
    {
        $eventDispatcher = $this->getEventDispatcher();
        $eventProperties = $this->getProductEventProperties();
        $eventDispatcher->dispatchUniqueEvent(
            EventCategory::LEADS,
            EventName::LEAD_UNDERSOLD,
            $eventProperties,
            "product-allocation-undersold-" . $eventProperties['consumer_product_id'],
            self::DEFAULT_EVENT_EXPIRY
        );
        $this->createMissedProducts();
    }

    /**
     * @return void
     * @throws Exception
     */
    private function finalAttemptNextDay(): void
    {
        /** @var AllocationSchedulingService $scheduleService */
        $scheduleService = app(AllocationSchedulingService::class);
        $delay = $scheduleService->getNextDayDelay(
            $this->consumerProject->zipCode(),
            true
        );

        logger()->info('Final attempt tomorrow', [$delay]);

        //final attempt tomorrow
        $this->tracker->concludeAttempt(ConsumerProductLifecycleTrackingService::CONCLUSION_FINAL_ATTEMPT_SCHEDULED);
        $this->trackJobStatus(JobStatus::COMPLETED);

        self::dispatch(
            consumerProject: $this->consumerProject,
            finalAttempt: true,
            allocationAttempt: $this->allocationAttempt + 1,
            jobAttempts: $this->jobAttempts)->delay($delay);

        ConsumerProductLifecycleTrackingService::allocationAttemptScheduled($this->consumerProject->leadConsumerProduct(), $delay);
    }

    /**
     * @return void
     * @throws Exception
     */
    private function attemptNextDay(): void
    {
        /** @var AllocationSchedulingService $scheduleService */
        $scheduleService = app(AllocationSchedulingService::class);
        $delay = $scheduleService->getNextDayDelay($this->consumerProject->zipCode());
        $this->scheduleReattempt($delay);
    }

    private function handleCompaniesNotEligible(): void
    {
        $this->scheduleReattempt($this->getRandomSeconds());
    }

    /**
     * @return int
     */
    protected function getRandomSeconds(): int
    {
        $randomMinutes = [3, 6, 9, 12, 15];

        return $randomMinutes[array_rand($randomMinutes)] * 60;
    }

    /**
     * @return void
     * @throws BindingResolutionException
     */
    private function handleConsumerProductStatusUpdates(): void
    {
        /** @var ConsumerProductService $consumerProductService */
        $consumerProductService = app()->make(ConsumerProductService::class);

        $this->consumerProject->consumer->consumerProducts()
            ->select(ConsumerProduct::FIELD_ID)
            ->get()
            ->each(fn(ConsumerProduct $consumerProduct) => $consumerProductService->updateConsumerProductStatus($consumerProduct->id));
    }

    /**
     * @param Throwable|null $exception
     * @return void
     */
    public function failed(?Throwable $exception): void
    {
        //TODO - logic for failed lead allocation, e.g. re-classify as unverified and attempt to re-sell (currently solar/roofing only)
        $this->handleConsumerProductStatusUpdates();
    }

    /**
     * @param JobStatus $status
     *
     * @return void
     */
    public function trackJobStatus(JobStatus $status): void
    {
        $uuid = $this->job?->uuid();

        if (!$uuid) {
            return;
        }

        UpdateJobTrackingJob::dispatch(JobTrackingRelation::CONSUMER_PRODUCT, $uuid, [
            JobTracking::STATUS => $status,
            JobTracking::ATTEMPTS => $this->attempts()
        ]);
    }

    /**
     * @param int $delay
     *
     * @return void
     */
    public function updateJobTrackingAvailableAt(int $delay): void
    {
        $uuid = $this->job?->uuid();

        if (!$uuid) {
            return;
        }

        UpdateJobTrackingJob::dispatch(JobTrackingRelation::CONSUMER_PRODUCT, $uuid, [
            JobTracking::AVAILABLE_AT => now()->addSeconds($delay)->timestamp,
            JobTracking::ATTEMPTS => $this->attempts()
        ]);
    }

    /**
     * Using the previous jobAttempts, calculate if next attempt should be the last
     * Current logic: attempt allocation for 2 days, with a final attempt on the 3rd
     * @todo pull this into a service to make it product specific
     * @return bool
     */
    public function shouldBeFinalAttempt(): bool
    {
        $attemptsByDate = collect($this->jobAttempts)->map(function ($attemptDate) {
            return Carbon::createFromTimestamp($attemptDate)->format('Y-m-d');
        })->countBy()->sortKeys();

        return $attemptsByDate->count() >= 2;
    }

    /**
     * @return string[]
     */
    public function tags(): array
    {
        return ['AttemptConsumerProjectAllocationJob', 'consumer:'.$this->consumerProject->consumer->id];
    }

    /**
     * @return PubSubAllocationEventDispatcher
     */
    private function getEventDispatcher(): PubSubAllocationEventDispatcher
    {
        return app(PubSubAllocationEventDispatcher::class);
    }

    /**
     * Fetch common properties for firing an allocation event
     * @return array
     */
    private function getProductEventProperties(): array
    {
        $consumerProductId = $this->consumerProject->leadConsumerProduct()?->id
            ?? $this->consumerProject->consumer->consumerProducts()->first()->id;
        $legacyId = $this->consumerProject->legacyLead()?->{EloquentQuote::ID};
        $initiatorId = LeadProcessingAllocation::query()
            ->where(LeadProcessingAllocation::FIELD_CONSUMER_PRODUCT_ID, $consumerProductId)
            ->first()
            ?->leadProcessor
            ?->user
            ?->legacy_user_id
            ?? 0;

        return ['lead_id' => $legacyId, 'consumer_product_id' => $consumerProductId, 'initiator_id' => $initiatorId];
    }

    /**
     * @return void
     * @throws BindingResolutionException
     */
    private function moveProductToUnderReviewQueue(): void
    {
        $queueRepository = app(LeadProcessingQueueRepository::class);
        $leadRepository = app(LeadProcessingRepository::class);
        $product = $this->consumerProject->legacyLead();
        $consumerProduct = $this->consumerProject->leadConsumerProduct();

        if ($queueRepository->changeQueues(
            lead: $product,
            processor: LeadProcessor::systemProcessor(),
            newProcessingStatus: LeadProcessingQueueRepository::STATUS_UNDER_REVIEW,
            reason: self::REASON_ALLOCATION_FAILED,
            publicComment: null,
            automatic: true,
            consumerProduct: $consumerProduct
        )) {
            logger()->info("ConsumerProduct $consumerProduct->id allocation attempts exhausted, moved to UnderReview queue.");
        }
        $leadRepository->releaseLead($product->quoteid);
    }

    /**
     * @param string|null $reason
     * @return void
     * @throws BindingResolutionException
     */
    private function moveProductToPendingQueue(?string $reason = self::REASON_ZERO_REQUESTS): void
    {
        $queueRepository = app(LeadProcessingQueueRepository::class);
        $leadRepository = app(LeadProcessingRepository::class);
        $product = $this->consumerProject->legacyLead();
        $consumerProduct = $this->consumerProject->leadConsumerProduct();

        if ($queueRepository->changeQueues(
            lead: $product,
            processor: LeadProcessor::systemProcessor(),
            newProcessingStatus: LeadProcessingQueueRepository::STATUS_PENDING_REVIEW,
            reason: $reason,
            publicComment: null,
            consumerProduct: $consumerProduct,
        )) {
            logger()->info("ConsumerProduct $consumerProduct->id attempted to allocate with 0 contact requests - moved to Pending as '$reason'.");
        }
        $leadRepository->releaseLead($product->quoteid);
    }

    /**
     * @return void
     * @throws Exception
     */
    protected function createMissedProducts(): void
    {
        $age = (int) $this->consumerProject->consumer->created_at->diffInHours(now(), false);
        $delay = config('sales.missed_products_minimum_hours') - $age;

        $delay > 0
            ? CreateMissedProductJob::dispatch($this->consumerProject->consumer)->delay(now()->addHours($delay))
            : CreateMissedProductJob::dispatch($this->consumerProject->consumer);
    }
}
