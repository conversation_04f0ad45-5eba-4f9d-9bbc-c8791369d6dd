<?php
namespace App\Contracts\Repositories\Odin;

use App\Models\Odin\ConsumerConfigurableFieldCategory;
use Illuminate\Support\Collection;

interface ConsumerConfigurableFieldCategoriesRepositoryContract
{
    /**
     * Returns all consumer configurable field categories from database
     * 
     * @return Collection<ConsumerConfigurableFieldCategory>
     */
    public function getAll(): Collection;
}

