<?php
namespace App\Contracts\Repositories\Odin;

use App\Models\Odin\IndustryConsumerField;
use Illuminate\Support\Collection;

interface IndustryConsumerFieldRepositoryContract
{
    /**
     * @param int $industry
     * @return Collection<IndustryConsumerField>
     */
    public function getIndustryConsumerFields(int $industry): Collection;

    /**
     * @param int $industry
     * @param string $name
     * @param string $key
     * @param int $type
     * @param int $categoryId
     * @param int $sendToCompany
     * @param int $showOnDashboard
     * @param int|null $id
     * @return bool
     */
    public function updateOrCreateIndustryConsumerField(
        int    $industry,
        string $name,
        string $key,
        int    $type,
        int    $categoryId,
        int    $sendToCompany,
        int    $showOnDashboard,
        ?int   $id = null
    ): bool;

    /**
     * @param int $id
     * @return bool
     */
    public function deleteIndustryConsumerField(int $id): bool;
}

