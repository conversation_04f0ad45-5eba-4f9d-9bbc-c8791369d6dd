<?php

namespace App\Contracts\Repositories\Odin;

use App\Models\Odin\IndustryService;
use Illuminate\Support\Collection;

interface IndustryServiceRepositoryContract
{
    /**
     * @param mixed $industryId
     * @return Collection<IndustryService>
     */
    public function getIndustryServices(mixed $industryId): Collection;

    /**
     * @param int $industry
     * @param string $name
     * @param string $slug
     * @param bool $showOnWebsite
     * @param bool $showOnRegistration
     * @param bool $showOnDashboard
     * @param bool $campaignFilterEnabled
     * @param int|null $id
     *
     * @return bool
     */
    public function updateOrCreateIndustryService(
        int $industry,
        string $name,
        string $slug,
        bool $showOnWebsite,
        bool $showOnRegistration,
        bool $showOnDashboard,
        bool $campaignFilterEnabled,
        ?int $id = null
    ): bool;

    /**
     * @param int $id
     * @return bool
     */
    public function deleteIndustryService(int $id): bool;

    /**
     * @return Collection<IndustryService>
     */
    public function getAllServices(): Collection;

    /**
     * @param mixed $serviceId
     * @return Collection<IndustryService>
     */
    public function getIndustryServicesById(mixed $serviceId): Collection;
}
