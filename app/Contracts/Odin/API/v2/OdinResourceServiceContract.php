<?php

namespace App\Contracts\Odin\API\v2;

use App\Models\BaseModel;
use App\Models\Odin\IndustryService;
use App\Services\Odin\API\APIFieldModel;
use Illuminate\Support\Collection;

/**
 * /v2 version of contract - allow Create/update methods to return a model instead of boolean
 */
interface OdinResourceServiceContract
{
    /**
     * <PERSON>les creating a resource via the Odin API.
     * @param Collection<APIFieldModel> $data
     * @param IndustryService $industryService
     * @return ?BaseModel
     */
    public function create(Collection $data, IndustryService $industryService): ?BaseModel;

    /**
     * Handles updating a resource via the Odin API.
     * @param mixed $primaryKey
     * @param Collection<APIFieldModel> $data
     * @param IndustryService $industryService
     * @return bool
     */
    public function update(mixed $primaryKey, Collection $data, IndustryService $industryService): bool;

    /**
     * Handles deleting a resource via the Odin API.
     *
     * @param mixed $primaryKey
     * @return bool
     */
    public function delete(mixed $primaryKey): bool;
}
