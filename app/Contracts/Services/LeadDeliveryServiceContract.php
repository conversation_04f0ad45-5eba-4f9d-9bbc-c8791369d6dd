<?php

namespace App\Contracts\Services;

use Illuminate\Foundation\Bus\PendingDispatch;

interface LeadDeliveryServiceContract
{
    /**
     * @param string $leadReference
     * @param array $selectedQuoteCompanies
     * @param int|null $legacyUserId
     * @param int|bool $sendAlertCustomer
     * @param string|null $leadProcessing
     * @param array $ignoreQuoteCompanies
     * @return array
     */
    public function saveAndDeliverQuoteCompany(
        string      $leadReference,
        array       $selectedQuoteCompanies,
        ?int        $legacyUserId = null,
        int|bool    $sendAlertCustomer = false,
        string|null $leadProcessing = null,
        array       $ignoreQuoteCompanies = []
    ): array;

    /**
     * @param string $leadReference
     * @param array $selectedQuoteCompanies
     * @param int|null $legacyUserId
     * @param int|bool $sendAlertCustomer
     * @param string|null $leadProcessing
     * @param array $ignoreQuoteCompanies
     * @return array
     */
    public function saveAndDeliverQuoteCompanyForBounceBack(
        string      $leadReference,
        array       $selectedQuoteCompanies,
        ?int        $legacyUserId = null,
        int|bool    $sendAlertCustomer = false,
        string|null $leadProcessing = null,
        array       $ignoreQuoteCompanies = []
    ): array;

    /**
     * @param string $leadReference
     * @param string $companyReference
     * @param int $leadCampaignId
     * @param float $cost
     * @param string $soldStatus
     * @param string $chargeStatus
     * @param bool $chargeable
     * @param bool $delivered
     * @param bool $includeInBudget
     * @param int $rejectionExpiry
     * @param int $deliveredAt
     * @param int $rejectedAt
     * @param int $legacyUserId
     * @param int $legacySaleTypeId
     * @param int $quoteCompanyId
     * @param bool $async
     * @return int|PendingDispatch
     */
    public function saveQuoteCompanyForAppointment(
        string $leadReference,
        string $companyReference,
        int $leadCampaignId,
        float $cost,
        string $soldStatus,
        string $chargeStatus,
        bool $chargeable,
        bool $delivered,
        bool $includeInBudget,
        int $rejectionExpiry,
        int $deliveredAt,
        int $rejectedAt,
        int $legacyUserId,
        int $legacySaleTypeId,
        int $quoteCompanyId = 0,
        bool $async = false
    ): int|PendingDispatch;
}
