<?php

namespace App\Contracts\Services;

interface AddressIdentificationServiceInterface
{
    /**
     * @param string $address1
     * @param string $city
     * @param string $stateAbbr
     * @param string $zipCode
     * @param string|null $address2
     * @return string
     */
    public function getIdFromAddressComponents(string $address1, string $city, string $stateAbbr, string $zipCode, ?string $address2 = null): string;
}
