<?php

namespace App\Repositories;

use App\DTO\Notes\CreateNoteParam;
use App\Enums\Notes\NoteRelationType;
use App\Models\Note;
use Illuminate\Database\Eloquent\Collection;

class NoteRepository
{
    /**
     * @param int $relationId
     * @param NoteRelationType $relationType
     * @return Collection
     */
    public function getNotesForEntity(int $relationId, NoteRelationType $relationType): Collection
    {
        return Note::query()
            ->where(Note::FIELD_RELATION_ID, $relationId)
            ->where(Note::FIELD_RELATION_TYPE, $relationType->getModelClass())
            ->orderByDesc(Note::FIELD_CREATED_AT)
            ->get();
    }
    /**
     * @param CreateNoteParam $createNoteParam
     * @return Note
     */
    public function create(CreateNoteParam $createNoteParam): Note
    {
        /** @var Note */
        return Note::query()->create([
            Note::FIELD_CONTENT       => $createNoteParam->getContent(),
            Note::FIELD_RELATION_ID   => $createNoteParam->getRelationId(),
            Note::FIELD_RELATION_TYPE => $createNoteParam->getRelationType()->getModelClass(),
            Note::FIELD_AUTHOR_ID     => $createNoteParam->getUserId(),
            Note::FIELD_PARENT_ID     => $createNoteParam->getParentId(),
        ]);
    }
}
