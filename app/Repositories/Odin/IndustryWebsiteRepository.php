<?php

namespace App\Repositories\Odin;

use App\Contracts\Repositories\Odin\IndustryWebsiteRepositoryContract;
use App\Models\Odin\IndustryWebsite;
use App\Models\Odin\Website;
use Illuminate\Support\Collection;

class IndustryWebsiteRepository implements IndustryWebsiteRepositoryContract
{
    /**
     * @inheritDoc
     */
    public function getIndustryWebsites(int $industry): Collection
    {
        return IndustryWebsite::query()->where(IndustryWebsite::FIELD_INDUSTRY_ID, $industry)->latest()->get();
    }

    /**
     * @inheritDoc
     */
    public function updateOrCreateIndustryWebsite(int $industry, int $website, string $slug, ?int $id = null): bool
    {
        return IndustryWebsite::query()->updateOrCreate(
                [
                    IndustryWebsite::FIELD_ID => $id
                ],
                [
                    IndustryWebsite::FIELD_INDUSTRY_ID => $industry,
                    IndustryWebsite::FIELD_WEBSITE_ID  => $website,
                    IndustryWebsite::FIELD_SLUG        => $slug
                ]
            ) !== null;
    }

    /**
     * @inheritDoc
     */
    public function deleteIndustryWebsite(int $id): bool
    {
        return IndustryWebsite::query()->where(IndustryWebsite::FIELD_ID, $id)->delete();
    }

    /**
     * @inheritDoc
     */
    public function getNonAddedWebsitesAgainstIndustryId(int $industry): Collection
    {
        /** @var IndustryWebsite|null $industryWebsites */
        $industryWebsites = IndustryWebsite::query()
                                ->where(IndustryWebsite::FIELD_INDUSTRY_ID, $industry)
                                ->distinct()->pluck(IndustryWebsite::FIELD_WEBSITE_ID);

        return Website::query()->whereNotIn(Website::FIELD_ID, $industryWebsites)->latest()->get();
    }
}
