<?php

namespace App\Repositories\Odin;

use App\Enums\Odin\Product;
use App\Models\Odin\ConsumerProduct;

class DirectLeadsRepository
{
    public function __construct(protected ServiceProductRepository $serviceProductRepository, protected ConsumerProductRepository $consumerProductRepository) {}

    /**
     * @param ConsumerProduct $leadConsumerProduct
     * @param int $status
     * @param bool $goodToSel
     *
     * @return bool
     */
    public function createDirectLeadConsumerProduct(ConsumerProduct $leadConsumerProduct, int $status = ConsumerProduct::STATUS_PENDING_ALLOCATION, bool $goodToSel = true): bool
    {
        if ($leadConsumerProduct->optInCompanies->isEmpty()) {
            return false;
        }

        $directLeadsServiceProduct = $this->serviceProductRepository->getServiceProductByProductAndIndustryService(
            $leadConsumerProduct->serviceProduct->industry_service_id,
            Product::DIRECT_LEADS
        );

        if (!$directLeadsServiceProduct) {
            return false;
        }

        $now = now();
        $directLeadsConsumerProduct = $leadConsumerProduct->replicate();

        $directLeadsConsumerProduct->service_product_id = $directLeadsServiceProduct->id;
        $directLeadsConsumerProduct->status = $status;
        $directLeadsConsumerProduct->good_to_sell = $goodToSel;
        $directLeadsConsumerProduct->created_at = $now;
        $directLeadsConsumerProduct->updated_at = $now;

        return $directLeadsConsumerProduct->save();
    }

    /**
     * @param int $consumerProductId
     *
     * @return bool
     */
    public function isConsumerProductEligibleForDirectLeads(int $consumerProductId): bool
    {
        $consumerProduct = $this->consumerProductRepository->findOrFail($consumerProductId);

        return $consumerProduct->optInCompanies->isNotEmpty()
            && $this->serviceProductRepository->getServiceProductByProductAndIndustryService($consumerProduct->serviceProduct->industry_service_id, Product::DIRECT_LEADS);
    }
}
