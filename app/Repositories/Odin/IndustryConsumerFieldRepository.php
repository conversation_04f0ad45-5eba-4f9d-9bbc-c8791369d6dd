<?php

namespace App\Repositories\Odin;

use App\Contracts\Repositories\Odin\IndustryConsumerFieldRepositoryContract;
use App\Enums\Odin\ConsumerConfigurableFieldCategory as ConsumerConfigurableFieldCategoryEnum;
use App\Models\Odin\ConsumerConfigurableFieldCategory;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryConsumerField;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class IndustryConsumerFieldRepository implements IndustryConsumerFieldRepositoryContract
{
    /**
     * @inheritDoc
     */
    public function getIndustryConsumerFields(int $industry): Collection
    {
        return IndustryConsumerField::query()->where(IndustryConsumerField::FIELD_INDUSTRY_ID, $industry)->latest()->get();
    }

    /**
     * @inheritDoc
     */
    public function updateOrCreateIndustryConsumerField(
        int    $industry,
        string $name,
        string $key,
        int    $type,
        int    $categoryId,
        int    $sendToCompany,
        int    $showOnDashboard,
        ?int   $id = null
    ): bool
    {
        return IndustryConsumerField::query()->updateOrCreate(
                [
                    IndustryConsumerField::FIELD_ID => $id
                ],
                [
                    IndustryConsumerField::FIELD_INDUSTRY_ID        => $industry,
                    IndustryConsumerField::FIELD_NAME               => $name,
                    IndustryConsumerField::FIELD_KEY                => $key,
                    IndustryConsumerField::FIELD_TYPE               => $type,
                    IndustryConsumerField::FIELD_CATEGORY_ID        => $categoryId,
                    IndustryConsumerField::FIELD_SEND_TO_COMPANY    => $sendToCompany === 1 ?? 0,
                    IndustryConsumerField::FIELD_SHOW_ON_DASHBOARD  => $showOnDashboard === 1 ?? 0,
                ]
            ) !== null;
    }

    /**
     * @inheritDoc
     */
    public function deleteIndustryConsumerField(int $id): bool
    {
        return IndustryConsumerField::query()->where(IndustryConsumerField::FIELD_ID, $id)->delete();
    }

    /**
     * @param Industry $industry
     * @param ConsumerConfigurableFieldCategoryEnum[] $categories
     *
     * @return Collection<IndustryConsumerField>
     */
    public function getConsumerFieldsForIndustry(Industry $industry, array $categories = []): Collection
    {
        $query = $industry->consumerFields();

        if ($categories) {
            $query->whereHas(
                IndustryConsumerField::RELATION_FIELD_CATEGORY,
                fn(Builder $query) => $query->whereIn(ConsumerConfigurableFieldCategory::FIELD_SLUG, $categories)
            );
        }

        return $query->get();
    }
}

