<?php

namespace App\Repositories\Odin;

use App\Builders\Odin\CompanyRevenueBuilder;
use App\Models\Odin\Company;
use App\Models\Legacy\EloquentInvoice;
use App\Models\Legacy\EloquentInvoiceItem;
use App\Models\Odin\ProductAssignment;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;

class RevenueRepository
{

    /**
     * @return Builder
     */
    protected function getBaseQuery(): Builder
    {
        return EloquentInvoice::query()
            ->join(EloquentInvoiceItem::TABLE, EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::INVOICE_ID, '=', EloquentInvoice::TABLE . '.' . EloquentInvoice::INVOICE_ID);
    }

    /**
     * Uses Odin model, but legacy ID
     *
     * @param Company $company
     *
     * @return mixed
     */
    public function getTotalOutstanding(Company $company): mixed
    {
        return $this->getBaseQuery()
            ->selectRaw('SUM((' . EloquentInvoiceItem::QUANTITY . ' * ' . EloquentInvoiceItem::ITEM_EX_TAX_PRICE . ') + ' . EloquentInvoiceItem::TOTAL_TAX_AMOUNT . ') AS total')
            ->where(EloquentInvoice::COMPANY_ID, $company->{Company::FIELD_LEGACY_ID})
            ->where(EloquentInvoice::TABLE . '.' . EloquentInvoice::STATUS, EloquentInvoice::VALUE_STATUS_INITIAL)
            ->first()->total;
    }

    /**
     * @param Company $company
     *
     * @return ProductAssignment|null
     */
    public function getLastLead(Company $company): ?ProductAssignment
    {
        /** @var ProductAssignment|null $productAssignment */
        $productAssignment = $company->productAssignments()
            ->where(ProductAssignment::FIELD_DELIVERED, true)
            ->orderBy(ProductAssignment::FIELD_DELIVERED_AT, 'DESC')
            ->first();

        return $productAssignment;
    }

    /**
     * Uses Odin company model but legacy id
     *
     * @param Company $company
     * @param Carbon|null $time
     * @return mixed
     */
    public function getTotalPaid(Company $company, ?Carbon $time = null): mixed
    {
        $query = $this->getBaseQuery()
            ->selectRaw('SUM((' . EloquentInvoiceItem::QUANTITY . ' * ' . EloquentInvoiceItem::ITEM_EX_TAX_PRICE . ') + ' . EloquentInvoiceItem::TOTAL_TAX_AMOUNT . ') AS total')
            ->where(EloquentInvoice::COMPANY_ID, $company->{Company::FIELD_LEGACY_ID})
            ->where(EloquentInvoice::TABLE . '.' . EloquentInvoice::STATUS, EloquentInvoice::VALUE_STATUS_PAID);

        if($time !== null)
            $query->where(EloquentInvoiceItem::TABLE . '.' . EloquentInvoiceItem::TIMESTAMP_ADDED, '>=', $time->getTimestamp());

        return $query->first()?->total;
    }

    /**
     * @param Company $company
     * @param Carbon|null $time
     * @return float
     */
    public function getTotalChargeable(Company $company, ?Carbon $time = null): float
    {
        /** @var ProductAssignmentRepository $productAssignmentRepository */
        $productAssignmentRepository = new(ProductAssignmentRepository::class);

        return $productAssignmentRepository->getTotalProductAssignmentsCostForCompany(
            $company,
            true,
            true,
            null,
            null,
            null,
            null,
            $time->timestamp,
            null,
            true,
            true
        );
    }

    /**
     * @param Company $company
     * @param string $period
     * @param int $duration
     * @return array
     */
    public function getRevenueGraphData(Company $company, string $period = CompanyRevenueBuilder::PERIOD_ALL_TIME, int $duration = 1): array
    {
        return CompanyRevenueBuilder::query()
            ->forCompany($company->{Company::FIELD_ID})
            ->setPeriod($period)
            ->setPeriodDuration($duration)
            ->groupBy($duration === 1 && $period === CompanyRevenueBuilder::GROUP_MONTHLY ? CompanyRevenueBuilder::GROUP_DAILY : CompanyRevenueBuilder::GROUP_MONTHLY)
            ->get()
            ->toArray();
    }

    /**
     * @param Company $company
     *
     * @return array
     */
    public function getRevenueOverview(Company $company): array
    {
        $thirtyDaysAgo = Carbon::today()->subDays(29); // Today counts as one day, so subtract 29 days
        $sixMonthsAgo  = Carbon::today()->subMonths(6);
        $startOfYear   = Carbon::today()->startOfYear();

        $query = ProductAssignment::query()
            ->where(ProductAssignment::FIELD_CHARGEABLE, true)
            ->where(ProductAssignment::FIELD_DELIVERED, true)
            ->where(ProductAssignment::FIELD_COMPANY_ID, $company->{Company::FIELD_ID})
            ->where(ProductAssignment::FIELD_DELIVERED_AT, '>=', $thirtyDaysAgo);

        return
            [
                'outstanding_invoice'      => round($this->getTotalOutstanding($company) ?? 0, 2),
                'last_lead'                => $this->getLastLead($company)?->delivered_at,
                'revenue_all_time'         => $this->getRevenueOfAllTime($company),
                'chargeable_last_30_days'  => round($this->getTotalChargeable($company, $thirtyDaysAgo) ?? 0, 2), // 30 days ago including today
                'revenue_last_30_days'     => round($this->getTotalPaid($company, $thirtyDaysAgo) ?? 0, 2), // 30 days ago including today
                'revenue_last_6_months'    => round($this->getTotalPaid($company, $sixMonthsAgo) ?? 0, 2),
                'graph_data'               => $this->getRevenueGraphData($company),
                'revenue_year_to_date'     => round($this->getTotalPaid($company, $startOfYear) ?? 0, 2),
                'last_30_days_start_date'  => $thirtyDaysAgo->format('M j, Y h:iA e'),
                'last_6_month_start_date'  => $sixMonthsAgo->format('M j, Y h:iA e'),
                'start_of_year_start_date' => $startOfYear->format('M j, Y h:iA e'),
                'average_daily_spend_last_30_days' => round($query->sum(ProductAssignment::FIELD_COST) / 30, 2),
                'count_of_leads_last_30_days' => $query->count(),
            ];

    }

    public function getRevenueOfAllTime(Company $company): int
    {
        return round($this->getTotalPaid($company) ?? 0, 2);
    }
}
