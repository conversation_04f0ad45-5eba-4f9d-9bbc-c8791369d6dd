<?php

namespace App\Repositories\Odin;

use App\ConsumerProcessing\Jobs\AttemptConsumerProjectAllocationJob;
use App\Enums\Odin\JobStatus;
use App\Enums\Odin\JobTrackingRelation;
use App\Models\Odin\JobTracking;
use Exception;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;

class JobTrackingRepository
{
    /**
     * @param AttemptConsumerProjectAllocationJob $job
     * @param string|null $payload
     *
     * @return void
     */
    public function createConsumerProjectAllocationJobTracking(AttemptConsumerProjectAllocationJob $job, ?string $payload): void
    {
        if (!$payload) {
            logger()->error('Failed to create allocation job tracking. Error: job payload is null');
            return;
        }

        try {
            $payload = json_decode($payload, true);
            $uuid = Arr::get($payload, 'uuid');

            $consumerProductId = $job->consumerProject?->leadConsumerProduct()->id;

            if (!$consumerProductId || !$uuid) {
                logger()->error('Failed to create allocation job tracking. Error: job uuid or consumer_product_id is null');
                return;
            }

            $this->createNewJobTracking(
                JobTrackingRelation::CONSUMER_PRODUCT,
                $consumerProductId,
                JobStatus::SCHEDULED,
                $uuid,
                now()->addSeconds($job->delay ?? 0)->timestamp
            );
        } catch (Exception $e) {
            logger()->error("Failed to create allocation job tracking. Error: {$e->getMessage()}");
        }
    }

    /**
     * @param JobTrackingRelation $relation
     * @param int $relationId
     * @param JobStatus $jobStatus
     * @param string $jobUuid
     * @param int|null $availableAt
     *
     * @return JobTracking
     */
    public function createNewJobTracking(JobTrackingRelation $relation, int $relationId, JobStatus $jobStatus, string $jobUuid, int $availableAt = null): JobTracking
    {
        /** @var JobTracking */
        return JobTracking::query()->create([
            JobTracking::RELATION => $relation,
            JobTracking::RELATION_ID => $relationId,
            JobTracking::STATUS => $jobStatus,
            JobTracking::JOB_UUID => $jobUuid,
            JobTracking::AVAILABLE_AT => $availableAt
        ]);
    }

    /**
     * @param string $jobUuid
     * @param JobStatus $status
     *
     * @return void
     */
    public function updateJobTrackingStatusByRelationAndUuid(JobTrackingRelation $relation, JobStatus $status, string $jobUuid): void
    {
        JobTracking::query()
            ->where(JobTracking::RELATION, $relation)
            ->where(JobTracking::JOB_UUID, $jobUuid)
            ->update([JobTracking::STATUS => $status]);
    }

    /**
     * @param JobTrackingRelation $relation
     * @param int $relationId
     *
     * @return Collection
     */
    public function getJobTrackingByRelationAndId(JobTrackingRelation $relation, int $relationId): Collection
    {
        return JobTracking::query()
            ->where(JobTracking::RELATION, $relation)
            ->where(JobTracking::RELATION_ID, $relationId)
            ->latest()
            ->get();
    }

    /**
     * @param JobTrackingRelation $relation
     * @param string $jobUuid
     * @param array $payload
     *
     * @return void
     */
    public function updateJobTrackingByRelationAndUuid(JobTrackingRelation $relation, string $jobUuid, array $payload): void
    {
        JobTracking::query()
            ->where(JobTracking::RELATION, $relation)
            ->where(JobTracking::JOB_UUID, $jobUuid)
            ->update($payload);
    }
}
