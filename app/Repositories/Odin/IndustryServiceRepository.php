<?php

namespace App\Repositories\Odin;

use App\Contracts\Repositories\Odin\IndustryServiceRepositoryContract;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ServiceProduct;
use Illuminate\Support\Collection;
use phpDocumentor\Reflection\Types\Boolean;

class IndustryServiceRepository implements IndustryServiceRepositoryContract
{
    /**
     * @inheritDoc
     */
    public function getIndustryServices(mixed $industryId): Collection
    {
        if(!is_array($industryId) && !is_numeric($industryId)) return collect();

        $baseQuery = IndustryService::query()->with(IndustryService::RELATION_INDUSTRY);

        $baseQuery = is_array($industryId)
            ? $baseQuery->whereIn(IndustryService::FIELD_INDUSTRY_ID, $industryId)
            : $baseQuery->where(IndustryService::FIELD_INDUSTRY_ID, $industryId);

        return $baseQuery->latest()->get();
    }

    /**
     * @inheritDoc
     */
    public function updateOrCreateIndustryService(
        int $industry,
        string $name,
        string $slug,
        bool $showOnWebsite,
        bool $showOnRegistration,
        bool $showOnDashboard,
        bool $campaignFilterEnabled,
        ?int $id = null
    ): bool
    {
        return IndustryService::query()->updateOrCreate(
                [
                    IndustryService::FIELD_ID => $id
                ],
                [
                    IndustryService::FIELD_INDUSTRY_ID             => $industry,
                    IndustryService::FIELD_NAME                    => $name,
                    IndustryService::FIELD_SLUG                    => $slug,
                    IndustryService::FIELD_SHOW_ON_WEBSITE         => $showOnWebsite,
                    IndustryService::FIELD_SHOW_ON_REGISTRATION    => $showOnRegistration,
                    IndustryService::FIELD_SHOW_ON_DASHBOARD       => $showOnDashboard,
                    IndustryService::FIELD_CAMPAIGN_FILTER_ENABLED => $campaignFilterEnabled
                ]
            ) !== null;
    }

    /**
     * @inheritDoc
     */
    public function deleteIndustryService(int $id): bool
    {
        $deleted = IndustryService::query()->where(IndustryService::FIELD_ID, $id)->delete();
        if ($deleted) {
            ServiceProduct::query()
                ->where(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $id)
                ->delete();
        }

        return $deleted;
    }

    /**
     * @inheritDoc
     */
    public function getAllServices(): Collection
    {
        return IndustryService::query()->latest()->get();
    }

    /**
     * @param string $slug
     * @return IndustryService|null
     */
    public function getIndustryServiceBySlug(string $slug): ?IndustryService
    {
        /** @var IndustryService $service */
        $service = IndustryService::query()->where(IndustryService::FIELD_SLUG, $slug)->first();
        return $service;
    }

    /**
     * @param array $slugs
     * @return array
     */
    public function getIndustryServiceIdsBySlugs(array $slugs): array
    {
        return IndustryService::query()
            ->whereIn(IndustryService::FIELD_SLUG, $slugs)
            ->pluck(Industry::FIELD_ID)
            ->toArray();
    }

    /**
     * @param string $industryName
     * @param string $serviceName
     * @return IndustryService|null
     */
    public function findIndustryServiceByNames(string $industryName, string $serviceName): ?IndustryService
    {
        $industryId = Industry::query()
            ->where(Industry::FIELD_NAME, $industryName)
            ->first()
            ?->{Industry::FIELD_ID};
        /** @var IndustryService $service */
        $service = $industryId
            ? IndustryService::query()
                ->where(IndustryService::FIELD_INDUSTRY_ID, $industryId)
                ->where(IndustryService::FIELD_NAME, $serviceName)
                ->first()
            : null;
        return $service;
    }

    /**
     * @inheritDoc
     */
    public function getIndustryServicesById(mixed $serviceId): Collection
    {
        if(!is_array($serviceId) && !is_numeric($serviceId)) return collect();

        $baseQuery = IndustryService::query()->with(IndustryService::RELATION_INDUSTRY);

        $baseQuery = is_array($serviceId)
            ? $baseQuery->whereIn(IndustryService::FIELD_ID, $serviceId)
            : $baseQuery->where(IndustryService::FIELD_ID, $serviceId);

        return $baseQuery->latest()->get();
    }

    /**
     * @param string $slug
     *
     * @return IndustryService
     */
    public function findBySlugOrFail(string $slug): IndustryService
    {
        /** @var IndustryService */
        return IndustryService::query()->where(IndustryService::FIELD_SLUG, $slug)->firstOrFail();
    }
}
