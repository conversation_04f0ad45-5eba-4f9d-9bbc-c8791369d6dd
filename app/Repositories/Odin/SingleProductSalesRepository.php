<?php


namespace App\Repositories\Odin;

use App\Enums\SingleProductSaleStatus;
use App\Models\MissedProducts\MissedProduct;
use App\Models\Odin\SingleProductSale;

class SingleProductSalesRepository
{

    /**
     * @param array $data
     * @return SingleProductSale
     */
    public function createSingleProductSale(array $data): SingleProductSale
    {
        $singleProductSale = new SingleProductSale();
        $singleProductSale->fill($data);

        $singleProductSale->save();

        return $singleProductSale;
    }

    /**
     * @param MissedProduct $missedProduct
     * @param int $companyId
     * @param int|null $paidInvoiceId
     * @return SingleProductSale
     */
    public function createInitialSaleForMissedProduct(MissedProduct $missedProduct, int $companyId, ?int $paidInvoiceId = null): SingleProductSale
    {
        return $this->createSingleProductSale([
            SingleProductSale::FIELD_STATUS => SingleProductSaleStatus::INITIAL->value,
            SingleProductSale::FIELD_COMPANY_ID => $companyId,
            SingleProductSale::FIELD_CONSUMER_PRODUCT_ID => $missedProduct->{MissedProduct::FIELD_CONSUMER_PRODUCT_ID},
            SingleProductSale::FIELD_PRICE => $missedProduct->{MissedProduct::FIELD_PRICE},
            SingleProductSale::FIELD_INVOICE_ID => $paidInvoiceId
        ]);

    }

    /**
     * @param MissedProduct $missedProduct
     * @return int|null
     */
    public function getPaidInvoiceIdByMissedProduct(MissedProduct $missedProduct): ?int
    {
        $saleRecord = SingleProductSale::query()
            ->select(SingleProductSale::FIELD_INVOICE_ID)
            ->where(SingleProductSale::FIELD_CONSUMER_PRODUCT_ID, $missedProduct->{MissedProduct::FIELD_CONSUMER_PRODUCT_ID})
            ->where(SingleProductSale::FIELD_STATUS, SingleProductSaleStatus::PAID->value)
            ->first();

        return $saleRecord?->{SingleProductSale::FIELD_INVOICE_ID} ?? null;
    }

}
