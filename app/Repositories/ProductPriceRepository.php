<?php

namespace App\Repositories;

use App\Enums\Odin\PropertyType as PropertyTypeEnum;
use App\Enums\Odin\QualityTier as QualityTierEnum;
use App\Enums\Odin\SaleTypes;
use App\Enums\Odin\StateAbbreviation;
use App\Models\Legacy\Location;
use App\Models\Odin\ProductCountyBidPrice;
use App\Models\Odin\ProductCountyFloorPrice;
use App\Models\Odin\ProductStateBidPrice;
use App\Models\Odin\ProductStateFloorPrice;
use App\Models\Odin\QualityTier as QualityTierModel;
use App\Models\SaleType;
use App\Services\DatabaseHelperService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use App\Models\Odin\PropertyType as PropertyTypeModel;
use Illuminate\Support\Facades\DB;

class ProductPriceRepository
{
    const COLUMN_SALE_TYPE_NAME = 'sale_type_name';
    const COLUMN_QUALITY_TIER_NAME = 'quality_tier_name';
    const COLUMN_PROPERTY_TYPE_NAME = 'property_type_name';

    /**
     * @param int $serviceProductId
     * @param Collection $productCampaignIds
     * @param PropertyTypeEnum|null $propertyType
     * @param StateAbbreviation|null $stateAbbreviation
     * @param SaleTypes|null $saleType
     * @param QualityTierEnum|null $qualityTier
     * @return Collection
     */
    public function getStateBids(
        int $serviceProductId,
        Collection $productCampaignIds,
        ?StateAbbreviation $stateAbbreviation = null,
        ?PropertyTypeEnum $propertyType = null,
        ?SaleTypes $saleType = null,
        ?QualityTierEnum $qualityTier = null
    ): Collection
    {
        return $this->getStateBidsQuery(
            $serviceProductId,
            $productCampaignIds,
            $stateAbbreviation,
            $propertyType,
            $saleType,
            $qualityTier
        )->get();
    }

    /**
     * @param int $serviceProductId
     * @param Collection $productCampaignIds
     * @param StateAbbreviation|null $stateAbbreviation
     * @param PropertyTypeEnum|null $propertyType
     * @param SaleTypes|null $saleType
     * @param QualityTierEnum|null $qualityTier
     * @return Builder
     */
    private function getStateBidsQuery(
        int $serviceProductId,
        Collection $productCampaignIds,
        ?StateAbbreviation $stateAbbreviation = null,
        ?PropertyTypeEnum $propertyType = null,
        ?SaleTypes $saleType = null,
        ?QualityTierEnum $qualityTier = null
    ): Builder
    {
        $query = ProductStateBidPrice::query()
            ->join(DatabaseHelperService::readOnlyDatabase().'.'.Location::TABLE, function($join) {
                $join->on(
                    DatabaseHelperService::readOnlyDatabase().'.'.Location::TABLE.'.'.Location::ID,
                    '=',
                    DatabaseHelperService::database().'.'.ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_STATE_LOCATION_ID
                );
            })
            ->join(DatabaseHelperService::database().'.'.PropertyTypeModel::TABLE, function($join) {
                $join->on(
                    DatabaseHelperService::database().'.'.ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_PROPERTY_TYPE_ID,
                    '=',
                    DatabaseHelperService::database().'.'.PropertyTypeModel::TABLE.'.'.PropertyTypeModel::FIELD_ID
                );
            })
            ->join(DatabaseHelperService::database().'.'.SaleType::TABLE, function($join) {
                $join->on(
                    DatabaseHelperService::database().'.'.SaleType::TABLE.'.'.SaleType::FIELD_ID,
                    '=',
                    DatabaseHelperService::database().'.'.ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_SALE_TYPE_ID
                );
            })
            ->join(QualityTierModel::TABLE, function($join) {
                $join->on(
                    DatabaseHelperService::database().'.'.QualityTierModel::TABLE.'.'.QualityTierModel::FIELD_ID,
                    '=',
                    DatabaseHelperService::database().'.'.ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_QUALITY_TIER_ID
                );
            })
            ->where(Location::TABLE.'.'.Location::TYPE, Location::TYPE_STATE)
            ->whereIn(ProductStateBidPrice::FIELD_PRODUCT_CAMPAIGN_ID, $productCampaignIds)
            ->where(ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_SERVICE_PRODUCT_ID, $serviceProductId)
            ->selectRaw(implode(',', [
                ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_ID,
                ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_STATE_LOCATION_ID,
                Location::TABLE.'.'.Location::TYPE,
                Location::TABLE.'.'.Location::STATE_ABBREVIATION,
                ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_PRODUCT_CAMPAIGN_ID,
                ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_SERVICE_PRODUCT_ID,
                ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_SALE_TYPE_ID,
                SaleType::TABLE.'.'.SaleType::FIELD_NAME." AS ".self::COLUMN_SALE_TYPE_NAME,
                ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_QUALITY_TIER_ID,
                QualityTierModel::TABLE.'.'.QualityTierModel::FIELD_NAME." AS ".self::COLUMN_QUALITY_TIER_NAME,
                ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_PROPERTY_TYPE_ID,
                PropertyTypeModel::TABLE.'.'.PropertyTypeModel::FIELD_NAME." AS ".self::COLUMN_PROPERTY_TYPE_NAME,
                ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_PRICE
            ]));

        if($stateAbbreviation) {
            $query->where(Location::TABLE.'.'.Location::STATE_ABBREVIATION, $stateAbbreviation->value);
        }

        if($propertyType) {
            $query->where(PropertyTypeModel::TABLE.'.'.PropertyTypeModel::FIELD_NAME, $propertyType->value);
        }

        if($saleType) {
            $query->where(SaleType::TABLE.'.'.SaleType::FIELD_NAME, $saleType->value);
        }

        if($qualityTier) {
            $query->where(QualityTierModel::TABLE.'.'.QualityTierModel::FIELD_NAME, $qualityTier->value);
        }

        return $query;
    }

    /**
     * @param int $serviceProductId
     * @param Collection $productCampaignIds
     * @param StateAbbreviation|null $stateAbbreviation
     * @param PropertyTypeEnum|null $propertyType
     * @param SaleTypes|null $saleType
     * @param QualityTierEnum|null $qualityTier
     * @param string|null $countyKey
     * @return Collection
     */
    public function getCountyBids(
        int $serviceProductId,
        Collection $productCampaignIds,
        ?StateAbbreviation $stateAbbreviation = null,
        ?PropertyTypeEnum $propertyType = null,
        ?SaleTypes $saleType = null,
        ?QualityTierEnum $qualityTier = null,
        ?string $countyKey = ''
    ): Collection
    {
        $query = ProductCountyBidPrice::query()
                    ->join(DatabaseHelperService::readOnlyDatabase().'.'.Location::TABLE, function($join) {
                        $join->on(
                            DatabaseHelperService::readOnlyDatabase().'.'.Location::TABLE.'.'.Location::ID,
                            '=',
                            DatabaseHelperService::database().'.'.ProductCountyBidPrice::TABLE.'.'.ProductCountyBidPrice::FIELD_COUNTY_LOCATION_ID
                        );
                    })
                    ->join(DatabaseHelperService::database().'.'.PropertyTypeModel::TABLE, function($join) {
                        $join->on(
                            DatabaseHelperService::database().'.'.ProductCountyBidPrice::TABLE.'.'.ProductCountyBidPrice::FIELD_PROPERTY_TYPE_ID,
                            '=',
                            DatabaseHelperService::database().'.'.PropertyTypeModel::TABLE.'.'.PropertyTypeModel::FIELD_ID
                        );
                    })
                    ->join(DatabaseHelperService::database().'.'.SaleType::TABLE, function($join) {
                        $join->on(
                            DatabaseHelperService::database().'.'.SaleType::TABLE.'.'.SaleType::FIELD_ID,
                            '=',
                            DatabaseHelperService::database().'.'.ProductCountyBidPrice::TABLE.'.'.ProductCountyBidPrice::FIELD_SALE_TYPE_ID
                        );
                    })
                    ->join(QualityTierModel::TABLE, function($join) {
                        $join->on(
                            DatabaseHelperService::database().'.'.QualityTierModel::TABLE.'.'.QualityTierModel::FIELD_ID,
                            '=',
                            DatabaseHelperService::database().'.'.ProductCountyBidPrice::TABLE.'.'.ProductCountyBidPrice::FIELD_QUALITY_TIER_ID
                        );
                    })
                    ->whereIn(ProductCountyBidPrice::TABLE.'.'.ProductCountyBidPrice::FIELD_PRODUCT_CAMPAIGN_ID, $productCampaignIds)
                    ->where(ProductCountyBidPrice::TABLE.'.'.ProductCountyBidPrice::FIELD_SERVICE_PRODUCT_ID, $serviceProductId)
                    ->selectRaw(implode(',', [
                        ProductCountyBidPrice::TABLE.'.'.ProductCountyBidPrice::FIELD_ID,
                        ProductCountyBidPrice::TABLE.'.'.ProductCountyBidPrice::FIELD_COUNTY_LOCATION_ID,
                        ProductCountyBidPrice::TABLE.'.'.ProductCountyBidPrice::FIELD_STATE_LOCATION_ID,
                        Location::TABLE.'.'.Location::TYPE,
                        Location::TABLE.'.'.Location::STATE_ABBREVIATION,
                        Location::TABLE.'.'.Location::COUNTY_KEY,
                        ProductCountyBidPrice::TABLE.'.'.ProductCountyBidPrice::FIELD_PRODUCT_CAMPAIGN_ID,
                        ProductCountyBidPrice::TABLE.'.'.ProductCountyBidPrice::FIELD_SERVICE_PRODUCT_ID,
                        ProductCountyBidPrice::TABLE.'.'.ProductCountyBidPrice::FIELD_SALE_TYPE_ID,
                        SaleType::TABLE.'.'.SaleType::FIELD_NAME." AS ".self::COLUMN_SALE_TYPE_NAME,
                        ProductCountyBidPrice::TABLE.'.'.ProductCountyBidPrice::FIELD_QUALITY_TIER_ID,
                        QualityTierModel::TABLE.'.'.QualityTierModel::FIELD_NAME." AS ".self::COLUMN_QUALITY_TIER_NAME,
                        ProductCountyBidPrice::TABLE.'.'.ProductCountyBidPrice::FIELD_PROPERTY_TYPE_ID,
                        PropertyTypeModel::TABLE.'.'.PropertyTypeModel::FIELD_NAME." AS ".self::COLUMN_PROPERTY_TYPE_NAME,
                        ProductCountyBidPrice::TABLE.'.'.ProductCountyBidPrice::FIELD_PRICE
                    ]));

        if($stateAbbreviation) {
            $query->where(Location::TABLE.'.'.Location::STATE_ABBREVIATION, $stateAbbreviation->value);
        }

        if($propertyType) {
            $query->where(PropertyTypeModel::TABLE.'.'.PropertyTypeModel::FIELD_NAME, $propertyType->value);
        }

        if($saleType) {
            $query->where(SaleType::TABLE.'.'.SaleType::FIELD_NAME, $saleType->value);
        }

        if($qualityTier) {
            $query->where(QualityTierModel::TABLE.'.'.QualityTierModel::FIELD_NAME, $qualityTier->value);
        }

        if(!empty($countyKey)) {
            $query
                ->where(Location::TABLE.'.'.Location::COUNTY_KEY, $countyKey)
                ->where(Location::TABLE.'.'.Location::TYPE, Location::TYPE_COUNTY);
        }

        $stateBidsQuery =
            $this->getStateBidsQuery(
                $serviceProductId,
                $productCampaignIds,
                $stateAbbreviation,
                $propertyType,
                $saleType,
                $qualityTier
            )
            ->select([
                ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_ID,
                DB::raw("0 AS ".ProductCountyBidPrice::FIELD_COUNTY_LOCATION_ID),
                ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_STATE_LOCATION_ID,
                Location::TABLE.'.'.Location::TYPE,
                Location::TABLE.'.'.Location::STATE_ABBREVIATION,
                Location::TABLE.'.'.Location::COUNTY_KEY,
                ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_PRODUCT_CAMPAIGN_ID,
                ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_SERVICE_PRODUCT_ID,
                ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_SALE_TYPE_ID,
                SaleType::TABLE.'.'.SaleType::FIELD_NAME." AS ".self::COLUMN_SALE_TYPE_NAME,
                ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_QUALITY_TIER_ID,
                QualityTierModel::TABLE.'.'.QualityTierModel::FIELD_NAME." AS ".self::COLUMN_QUALITY_TIER_NAME,
                ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_PROPERTY_TYPE_ID,
                PropertyTypeModel::TABLE.'.'.PropertyTypeModel::FIELD_NAME." AS ".self::COLUMN_PROPERTY_TYPE_NAME,
                ProductStateBidPrice::TABLE.'.'.ProductStateBidPrice::FIELD_PRICE
            ])
            ->getQuery();

        $query->union($stateBidsQuery);

        return $query->get();
    }

    /**
     * @param int $serviceProductId
     * @param int $stateLocationId
     * @param int $countyLocationId
     * @param PropertyTypeEnum|null $propertyType
     * @param SaleTypes|SaleTypes[]|null $saleType
     * @param QualityTierEnum|null $qualityTier
     *
     * @return Collection
     */
    public function getCountyFloorPrices(
        int $serviceProductId,
        int $stateLocationId,
        int $countyLocationId,
        ?PropertyTypeEnum $propertyType = null,
        SaleTypes|array|null $saleType = null,
        ?QualityTierEnum  $qualityTier = null
    ): Collection
    {
        $query = $this->getQueryForCountyFloorPrices($serviceProductId, $stateLocationId, $countyLocationId, $propertyType, $saleType, $qualityTier)
            ->selectRaw(implode(',', $this->selectForCounty()));

        $stateQuery =  $this->getQueryForStateFloorPrices($serviceProductId, $stateLocationId, $propertyType, $saleType, $qualityTier)
            ->selectRaw(implode(',', $this->selectForState()))->getQuery();

        return $query->union($stateQuery)->get();
    }

    /**
     * @param int $serviceProductId
     * @param int $stateLocationId
     * @param PropertyTypeEnum|null $propertyType
     * @param SaleTypes|SaleTypes[]|null $saleType
     * @param QualityTierEnum|null $qualityTier
     *
     * @return Collection
     */
    public function getStateFloorPrices(
        int $serviceProductId,
        int $stateLocationId,
        ?PropertyTypeEnum $propertyType = null,
        SaleTypes|array|null $saleType = null,
        ?QualityTierEnum  $qualityTier = null,
    ): Collection
    {
        return $this->getQueryForStateFloorPrices($serviceProductId, $stateLocationId, $propertyType, $saleType, $qualityTier)
            ->selectRaw(implode(',', $this->selectForState()))
            ->get();
    }

    /**
     * @param int $serviceProductId
     * @param int $stateLocationId
     * @param int $countyLocationId
     * @param PropertyTypeEnum|null $propertyType
     * @param SaleTypes|SaleTypes[]|null $saleType
     * @param QualityTierEnum|null $qualityTier
     *
     * @return Builder
     */
    protected function getQueryForCountyFloorPrices(
        int $serviceProductId,
        int $stateLocationId,
        int $countyLocationId,
        ?PropertyTypeEnum $propertyType = null,
        SaleTypes|array|null $saleType = null,
        ?QualityTierEnum  $qualityTier = null,
    ): Builder
    {
        $query = ProductCountyFloorPrice::query()
            ->join(
                DatabaseHelperService::readOnlyDatabase() . '.' . Location::TABLE,
                ProductCountyFloorPrice::TABLE . '.' . ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID,
                '=',
                DatabaseHelperService::readOnlyDatabase() . '.' . Location::TABLE . '.' . Location::ID
            )
            ->join(PropertyTypeModel::TABLE,
                ProductCountyFloorPrice::TABLE . '.' . ProductCountyFloorPrice::FIELD_PROPERTY_TYPE_ID,
                '=',
                PropertyTypeModel::TABLE . '.' . PropertyTypeModel::FIELD_ID
            )
            ->join(
                SaleType::TABLE,
                ProductCountyFloorPrice::TABLE . '.' . ProductCountyFloorPrice::FIELD_SALE_TYPE_ID,
                '=',
                SaleType::TABLE . '.' . SaleType::FIELD_ID
            )
            ->join(
                QualityTierModel::TABLE,
                ProductCountyFloorPrice::TABLE . '.' . ProductCountyFloorPrice::FIELD_QUALITY_TIER_ID,
                '=',
                QualityTierModel::TABLE . '.' . QualityTierModel::FIELD_ID
            )
            ->where(ProductCountyFloorPrice::TABLE . '.' . ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID, $serviceProductId)
            ->where(ProductCountyFloorPrice::TABLE . '.' . ProductCountyFloorPrice::FIELD_STATE_LOCATION_ID, $stateLocationId)
            ->where(ProductCountyFloorPrice::TABLE . '.' . ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID, $countyLocationId);

        if ($propertyType) {
            $query->where(PropertyTypeModel::TABLE . '.' . PropertyTypeModel::FIELD_NAME, $propertyType);
        }

        if ($saleType !== null) {
            if (is_array($saleType))
                $query->whereIn(SaleType::TABLE . '.' . SaleType::FIELD_NAME, $saleType);
            else
                $query->where(SaleType::TABLE . '.' . SaleType::FIELD_NAME, $saleType);
        }

        if ($qualityTier) {
            $query->where(QualityTierModel::TABLE . '.' . QualityTierModel::FIELD_NAME, $qualityTier);
        }

        return $query;
    }

    /**
     * @param int $serviceProductId
     * @param int $stateLocationId
     * @param PropertyTypeEnum|null $propertyType
     * @param SaleTypes|SaleTypes[]|null $saleType
     * @param QualityTierEnum|null $qualityTier
     *
     * @return Builder
     */
    protected function getQueryForStateFloorPrices(
        int $serviceProductId,
        int $stateLocationId,
        ?PropertyTypeEnum $propertyType = null,
        SaleTypes|array|null $saleType = null,
        ?QualityTierEnum  $qualityTier = null,
    ): Builder
    {
        $query = ProductStateFloorPrice::query()
            ->join(
                DatabaseHelperService::readOnlyDatabase() . '.' . Location::TABLE,
                ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_STATE_LOCATION_ID,
                '=',
                DatabaseHelperService::readOnlyDatabase() . '.' . Location::TABLE . '.' . Location::ID
            )
            ->join(
                PropertyTypeModel::TABLE,
                ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID,
                '=',
                PropertyTypeModel::TABLE . '.' . PropertyTypeModel::FIELD_ID
            )
            ->join(
                SaleType::TABLE,
                ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_SALE_TYPE_ID,
                '=',
                SaleType::TABLE . '.' . SaleType::FIELD_ID
            )->join(
                QualityTierModel::TABLE,
                ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_QUALITY_TIER_ID,
                '=',
                QualityTierModel::TABLE . '.' . QualityTierModel::FIELD_ID
            )
            ->where(ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID, $serviceProductId)
            ->where(ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_STATE_LOCATION_ID, $stateLocationId);

        if ($propertyType) {
            $query->where(PropertyTypeModel::TABLE . '.' . PropertyTypeModel::FIELD_NAME, $propertyType);
        }

        if ($saleType !== null) {
            if (is_array($saleType))
                $query->whereIn(SaleType::TABLE . '.' . SaleType::FIELD_NAME, $saleType);
            else
                $query->where(SaleType::TABLE . '.' . SaleType::FIELD_NAME, $saleType);
        }

        if ($qualityTier) {
            $query->where(QualityTierModel::TABLE . '.' . QualityTierModel::FIELD_NAME, $qualityTier);
        }

        return $query;
    }

    /**
     * @return string[]
     */
    protected function selectForState(): array
    {
        return [
            ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_ID,
            DB::raw('0 AS ' . ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID)->getValue(DB::connection()->getQueryGrammar()),
            ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_STATE_LOCATION_ID,
            Location::TABLE . '.' . Location::TYPE,
            Location::TABLE . '.' . Location::STATE_ABBREVIATION,
            Location::TABLE . '.' . Location::COUNTY_KEY,
            ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID,
            ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_SALE_TYPE_ID,
            SaleType::TABLE . '.' . SaleType::FIELD_NAME . ' AS ' . self::COLUMN_SALE_TYPE_NAME,
            ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_QUALITY_TIER_ID,
            QualityTierModel::TABLE . '.' . QualityTierModel::FIELD_NAME . ' AS ' . self::COLUMN_QUALITY_TIER_NAME,
            ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID,
            PropertyTypeModel::TABLE . '.' . PropertyTypeModel::FIELD_NAME . ' AS ' . self::COLUMN_PROPERTY_TYPE_NAME,
            ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_PRICE
        ];
    }

    /**
     * @return string[]
     */
    protected function selectForCounty(): array
    {
        return [
            ProductCountyFloorPrice::TABLE . '.' . ProductCountyFloorPrice::FIELD_ID,
            ProductCountyFloorPrice::TABLE . '.' . ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID,
            ProductCountyFloorPrice::TABLE . '.' . ProductCountyFloorPrice::FIELD_STATE_LOCATION_ID,
            Location::TABLE . '.' . Location::TYPE,
            Location::TABLE . '.' . Location::STATE_ABBREVIATION,
            Location::TABLE . '.' . Location::COUNTY_KEY,
            ProductCountyFloorPrice::TABLE . '.' . ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID,
            ProductCountyFloorPrice::TABLE . '.' . ProductCountyFloorPrice::FIELD_SALE_TYPE_ID,
            SaleType::TABLE . '.' . SaleType::FIELD_NAME . " AS " . self::COLUMN_SALE_TYPE_NAME,
            ProductCountyFloorPrice::TABLE . '.' . ProductCountyFloorPrice::FIELD_QUALITY_TIER_ID,
            QualityTierModel::TABLE . '.' . QualityTierModel::FIELD_NAME . " AS " . self::COLUMN_QUALITY_TIER_NAME,
            ProductCountyFloorPrice::TABLE . '.' . ProductCountyFloorPrice::FIELD_PROPERTY_TYPE_ID,
            PropertyTypeModel::TABLE . '.' . PropertyTypeModel::FIELD_NAME . " AS " . self::COLUMN_PROPERTY_TYPE_NAME,
            ProductCountyFloorPrice::TABLE . '.' . ProductCountyFloorPrice::FIELD_PRICE
        ];
    }

    /**
     * @param int $countyLocationId
     * @param int $campaignId
     * @param int $propertyTypeId
     * @param int $saleTypeId
     * @param int $qualityTierId
     * @param int $serviceProductId
     * @return ?float
     */
    public function getProductBidPrice(
        int $countyLocationId,
        int $stateLocationId,
        int $campaignId,
        int $propertyTypeId,
        int $saleTypeId,
        int $qualityTierId,
        int $serviceProductId
    ): float
    {
        $countyBid = ProductCountyBidPrice::query()
            ->where(ProductCountyBidPrice::FIELD_PRODUCT_CAMPAIGN_ID, $campaignId)
            ->where(ProductCountyBidPrice::FIELD_COUNTY_LOCATION_ID, $countyLocationId)
            ->where(ProductCountyBidPrice::FIELD_PROPERTY_TYPE_ID, $propertyTypeId)
            ->where(ProductCountyBidPrice::FIELD_SALE_TYPE_ID, $saleTypeId)
            ->where(ProductCountyBidPrice::FIELD_QUALITY_TIER_ID, $qualityTierId)
            ->where(ProductCountyBidPrice::FIELD_SERVICE_PRODUCT_ID, $serviceProductId)
            ->first()
            ?->{ProductCountyBidPrice::FIELD_PRICE};

        $stateBid = ProductStateBidPrice::query()
            ->where(ProductStateBidPrice::FIELD_PRODUCT_CAMPAIGN_ID, $campaignId)
            ->where(ProductStateBidPrice::FIELD_STATE_LOCATION_ID, $stateLocationId)
            ->where(ProductStateBidPrice::FIELD_PROPERTY_TYPE_ID, $propertyTypeId)
            ->where(ProductStateBidPrice::FIELD_SALE_TYPE_ID, $saleTypeId)
            ->where(ProductStateBidPrice::FIELD_QUALITY_TIER_ID, $qualityTierId)
            ->where(ProductStateBidPrice::FIELD_SERVICE_PRODUCT_ID, $serviceProductId)
            ->first()
            ?->{ProductCountyBidPrice::FIELD_PRICE};

        return floatval(max($countyBid, $stateBid));
    }

    /**
     * @param int $countyLocationId
     * @param int $stateLocationId
     * @param int $propertyTypeId
     * @param int $saleTypeId
     * @param int $qualityTierId
     * @param int $serviceProductId
     * @return float
     */
    public function getProductFloorPrice(
        int $countyLocationId,
        int $stateLocationId,
        int $propertyTypeId,
        int $saleTypeId,
        int $qualityTierId,
        int $serviceProductId
    ): float
    {
        $countyFloor = ProductCountyFloorPrice::query()
            ->where(ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID, $countyLocationId)
            ->where(ProductCountyFloorPrice::FIELD_PROPERTY_TYPE_ID, $propertyTypeId)
            ->where(ProductCountyFloorPrice::FIELD_SALE_TYPE_ID, $saleTypeId)
            ->where(ProductCountyFloorPrice::FIELD_QUALITY_TIER_ID, $qualityTierId)
            ->where(ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID, $serviceProductId)
            ->first()
            ?->{ProductCountyFloorPrice::FIELD_PRICE};

        if($countyFloor > 0) {
            return $countyFloor;
        }

        return ProductStateFloorPrice::query()
            ->where(ProductStateFloorPrice::FIELD_STATE_LOCATION_ID, $stateLocationId)
            ->where(ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID, $propertyTypeId)
            ->where(ProductStateFloorPrice::FIELD_SALE_TYPE_ID, $saleTypeId)
            ->where(ProductStateFloorPrice::FIELD_QUALITY_TIER_ID, $qualityTierId)
            ->where(ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID, $serviceProductId)
            ->first()
            ?->{ProductStateFloorPrice::FIELD_PRICE} ?? 0.0;
    }
}
