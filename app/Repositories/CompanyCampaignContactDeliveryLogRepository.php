<?php

namespace App\Repositories;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignContactDeliveryLog;
use Illuminate\Pagination\LengthAwarePaginator;

class CompanyCampaignContactDeliveryLogRepository
{
    /**
     * @param  int  $perPage
     * @param  int  $page
     * @param  int|null  $companyId
     * @param  bool|null  $succeeded
     * @param  string|null  $campaign
     * @param  int|null  $consumerProductId
     * @param  array|null  $dateRange
     *
     * @return LengthAwarePaginator
     */
    public function listContactDeliveryLogs(
        int     $perPage,
        int     $page,
        ?int    $companyId = null,
        ?bool   $succeeded = null,
        ?string $campaign = null,
        ?int    $consumerProductId = null,
        ?array  $dateRange = null
    ): LengthAwarePaginator
    {
        $query = CompanyCampaignContactDeliveryLog::query();

        $campaignName = preg_match("/\D/", $campaign ?? '');
        if ($companyId || $campaignName)
            $query->join(CompanyCampaign::TABLE, CompanyCampaign::TABLE .'.'.CompanyCampaign::FIELD_ID, CompanyCampaignContactDeliveryLog::FIELD_CAMPAIGN_ID);

        if (isset($campaign)) {
            if ($campaignName)
                $query->where(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_NAME, 'LIKE', '%' . $campaign . '%');
            else
                $query->where(CompanyCampaignContactDeliveryLog::FIELD_CAMPAIGN_ID, $campaign);
        }

        if (isset($companyId)) {
            $query->where(CompanyCampaign::FIELD_COMPANY_ID, $companyId);
        }

        if (isset($succeeded)){
            $query->where(CompanyCampaignContactDeliveryLog::FIELD_SUCCESS, $succeeded);
        }

        if (isset($consumerProductId)) {
           $query->where(CompanyCampaignContactDeliveryLog::FIELD_CONSUMER_PRODUCT_ID, $consumerProductId);
        }

        if (isset($dateRange)) {
            $query->whereBetween(CompanyCampaignContactDeliveryLog::FIELD_CREATED_AT, [$dateRange['from'],  $dateRange['to']]);
        }

        $query->orderByDesc(CompanyCampaignContactDeliveryLog::TABLE .'.'. CompanyCampaignContactDeliveryLog::FIELD_CREATED_AT);

        return $query->paginate($perPage, ['*'], 'page', $page);
    }
}
