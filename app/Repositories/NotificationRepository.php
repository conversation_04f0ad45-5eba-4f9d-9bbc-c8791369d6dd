<?php

namespace App\Repositories;

use App\Enums\NotificationLinkType;
use App\Models\Notification;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Mo<PERSON>y\Matcher\Not;

class NotificationRepository
{
    const NOTIFICATION_PAGE_COUNT = 100;

    /**
     * Creates a notification
     *
     * @param int $userId
     * @param int $fromId
     * @param string $subject
     * @param string $body
     * @param int $type
     * @param string|null $link
     * @param NotificationLinkType|null $linkType
     * @return Notification
     */
    public function createNotification(
        int $userId,
        int $fromId,
        string $subject,
        string $body,
        int $type,
        ?string $link = null,
        ?NotificationLinkType $linkType = null,
        ?array $payload = null,
    ): Notification {
        return Notification::create(
            [
                Notification::FIELD_USER_ID   => $userId,
                Notification::FIELD_FROM_ID   => $fromId,
                Notification::FIELD_SUBJECT   => $subject,
                Notification::FIELD_BODY      => $body,
                Notification::FIELD_TYPE      => $type,
                Notification::FIELD_READ      => Notification::UNREAD,
                Notification::FIELD_LINK      => $link,
                Notification::FIELD_LINK_TYPE => $linkType,
                Notification::FIELD_PAYLOAD   => $payload ?? []
            ]
        );
    }

    /**
     * Returns the latest 100 notifications for the user.
     *
     * @param int $userId
     * @param int|null $offset
     * @return Collection
     */
    public function getNotificationsForUserPaginated(int $userId, ?int $offset): Collection
    {
        return Notification::query()->where(Notification::FIELD_USER_ID, $userId)->latest()->skip($offset)->take(self::NOTIFICATION_PAGE_COUNT)->get();
    }

    /**
     * @param int $userId
     * @return Builder
     */
    public function getUnreadNotificationsForUserQuery(int $userId): Builder
    {
        return Notification::query()
            ->where(Notification::FIELD_USER_ID, $userId)
            ->where(Notification::FIELD_READ, Notification::UNREAD);
    }

    public function getUnreadNotificationCount(int $userId): int
    {
        return Notification::query()
            ->where(Notification::FIELD_USER_ID, $userId)
            ->where(Notification::FIELD_READ, Notification::UNREAD)
            ->count();
    }

    public function getTotalNotificationCount(int $userId): int
    {
        return Notification::query()
            ->where(Notification::FIELD_USER_ID, $userId)
            ->count();
    }

    /**
     * Returns a notification by it's id.
     *
     * @param int $id
     * @return Notification|null
     */
    public function getNotification(int $id): ?Notification
    {
        return Notification::query()->find($id);
    }

    /**
     * Marks a notification as being read.
     *
     * @param int $id
     * @return bool
     */
    public function markAsRead(int $id): bool
    {
        $notification       = $this->getNotification($id);
        $notification->read = Notification::READ;

        return $notification->save();
    }
}
