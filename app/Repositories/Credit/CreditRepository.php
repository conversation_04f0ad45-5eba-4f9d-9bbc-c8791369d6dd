<?php

namespace App\Repositories\Credit;

use App\Models\Billing\Credit;
use App\Models\Billing\CreditType;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Facades\DB;

class CreditRepository
{

    /**
     * @param int $creditId
     * @return Credit
     */
    public function getCredit(int $creditId): Credit
    {
        /* @var Credit */
        return Credit::query()->findOrFail($creditId);
    }

    /**
     * @param int $companyId
     * @return Collection
     */
    public function getCompanyCredits(int $companyId): Collection
    {
        return Credit::query()
            ->where(Credit::FIELD_COMPANY_ID, $companyId)
            ->where(Credit::FIELD_REMAINING_VALUE, '>', 0)
            ->orderBy(Credit::FIELD_CREDIT_TYPE)
            ->orderBy(Credit::FIELD_EXPIRES_AT, 'desc')
            ->get();
    }

    /**
     * @param int $companyId
     * @param string|null $type
     * @return Collection
     */
    public function getAvailableActiveCredits(
        int $companyId,
        ?string $type = null
    ): Collection
    {
        return Credit::query()
            ->select([
                Credit::TABLE . '.' . Credit::FIELD_CREDIT_TYPE,
                CreditType::TABLE . '.' . CreditType::FIELD_NAME,
                DB::raw('SUM(' . Credit::TABLE . '.' . Credit::FIELD_REMAINING_VALUE . ') as balance')
            ])
            ->join(
                CreditType::TABLE,
                CreditType::TABLE . '.' . CreditType::FIELD_SLUG,
                Credit::TABLE . '.' . Credit::FIELD_CREDIT_TYPE
            )
            ->where(Credit::TABLE . '.' . Credit::FIELD_COMPANY_ID, $companyId)
            ->where(function ($query) {
                $query->where(Credit::TABLE . '.' . Credit::FIELD_EXPIRES_AT, '>', now())
                    ->orWhereNull(Credit::TABLE . '.' . Credit::FIELD_EXPIRES_AT);
            })
            ->having('balance', '>', 0)
            ->when($type, fn (Builder $builder) => $builder->where(Credit::TABLE . '.' . Credit::FIELD_CREDIT_TYPE, $type))
            ->groupBy(Credit::TABLE . '.' . Credit::FIELD_CREDIT_TYPE)
            ->orderByDesc(Credit::TABLE . '.' . Credit::FIELD_CREATED_AT)
            ->get();
    }

    /**
     * @param int $companyId
     * @param Carbon|null $expireDateReference
     * @param string|null $type
     * @return Collection
     */
    public function getEligibleCompanyCredits(
        int $companyId,
        ?Carbon $expireDateReference = null,
        ?string $type = null
    ): Collection
    {
        if (empty($expireDateReference)) {
            $expireDateReference = now();
        }

        /** @var Collection */
        return Credit::query()
            ->join(CreditType::TABLE, CreditType::TABLE . '.' . CreditType::FIELD_SLUG, Credit::TABLE . '.' . Credit::FIELD_CREDIT_TYPE)
            ->orderBy(CreditType::TABLE . '.' . CreditType::FIELD_CONSUMPTION_ORDER)
            ->where(Credit::TABLE . '.' . Credit::FIELD_COMPANY_ID, $companyId)
            ->where(Credit::TABLE . '.' . Credit::FIELD_REMAINING_VALUE, '>', 0)
            ->where(CreditType::TABLE . '.' . CreditType::FIELD_ACTIVE, true)
            ->when($type, fn ($query) => $query->where(Credit::TABLE . '.' . Credit::FIELD_CREDIT_TYPE, $type))
            ->where(function ($query) use ($expireDateReference) {
                $query->where(Credit::TABLE . '.' . Credit::FIELD_EXPIRES_AT, '>=', $expireDateReference)
                    ->orWhereNull(Credit::TABLE . '.' . Credit::FIELD_EXPIRES_AT);
            })
            ->get();
    }

}
