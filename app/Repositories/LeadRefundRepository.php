<?php

namespace App\Repositories;

use App\DTO\LeadRefund\LeadRefundItemPayload;
use App\Enums\LeadRefundItemStatus;
use App\Enums\LeadRefundStatus;
use App\Mail\LeadRefunds\LeadRefundRequested;
use App\Models\LeadRefundItem;
use App\Models\LeadRefund;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use App\Models\User;
use FacebookAds\Object\Lead;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Mail;

class LeadRefundRepository
{
    /**
     * @param int $companyId
     * @param int $authorId
     * @param float $total
     * @param Collection<LeadRefundItemPayload> $items
     * @param string $status
     * @return LeadRefund
     */
    public function createRequestRefund(
        int $companyId,
        int $authorId,
        float $total,
        Collection $items,
        string $status,
    ): LeadRefund
    {
        $refundRequest = new LeadRefund();

        $refundRequest->fill([
            LeadRefund::FIELD_REQUESTED_BY => $authorId,
            LeadRefund::FIELD_TOTAL        => $total,
            LeadRefund::FIELD_STATUS       => $status,
            LeadRefund::FIELD_COMPANY_ID   => $companyId,
        ]);

        $refundRequest->save();

        $this->createRequestRefundItems($refundRequest, $items);

        return $refundRequest;
    }

    /**
     * @param LeadRefund $leadRefund
     * @param Collection<LeadRefundItemPayload> $items
     * @return void
     */
    public function createRequestRefundItems(LeadRefund $leadRefund, Collection $items): void
    {
        foreach ($items as $item) {
            LeadRefundItem::query()->create([
                LeadRefundItem::FIELD_LEAD_REFUND_ID        => $leadRefund->{LeadRefundItem::FIELD_ID},
                LeadRefundItem::FIELD_PRODUCT_ASSIGNMENT_ID => $item->getProductAssignmentId(),
                LeadRefundItem::FIELD_VALUE                 => $item->getCost(),
                LeadRefundItem::FIELD_REFUND_REASON         => $item->getRefundReason(),
                LeadRefundItem::FIELD_REFUND_TYPE           => $item->getType(),
                LeadRefundItem::FIELD_STATUS                => LeadRefundItemStatus::PENDING->value,
            ]);
        }
    }

    /**
     * @param int|null $companyId
     * @param int|null $reviewedBy
     * @param int|null $requestedBy
     * @param string|null $status
     * @param int|null $leadIdLegacyId
     * @return Builder
     */
    public function getLeadRefundQuery(
        ?int $companyId = null,
        ?int $reviewedBy = null,
        ?int $requestedBy = null,
        ?string $status = null,
        ?int $leadIdLegacyId = null,
    ): Builder
    {
        return LeadRefund::query()
            ->when($companyId, fn($query) => $query->where(LeadRefund::FIELD_COMPANY_ID, $companyId))
            ->when($reviewedBy, fn($query) => $query->where(LeadRefund::FIELD_REVIEWED_BY, $reviewedBy))
            ->when($requestedBy, fn($query) => $query->where(LeadRefund::FIELD_REQUESTED_BY, $requestedBy))
            ->when($status, fn($query) => $query->where(LeadRefund::FIELD_STATUS, $status))
            ->when($leadIdLegacyId, function($query) use ($leadIdLegacyId) {
                return $query->whereHas(LeadRefund::RELATION_ITEMS, function (Builder $query) use ($leadIdLegacyId) {
                    return $query->whereHas(LeadRefundItem::RELATION_PRODUCT_ASSIGNMENT, function(Builder $query) use ($leadIdLegacyId) {
                        return $query->whereHas(ProductAssignment::RELATION_CONSUMER_PRODUCT, function (Builder $query) use ($leadIdLegacyId) {
                            return $query->whereHas(ConsumerProduct::RELATION_CONSUMER, function(Builder $query) use ($leadIdLegacyId) {
                                return $query->where(Consumer::FIELD_ID, $leadIdLegacyId)
                                    ->orWhere(Consumer::FIELD_LEGACY_ID, $leadIdLegacyId);
                            });
                        });
                    });
                });
            })
            ->orderByDesc(LeadRefund::FIELD_ID);
    }
}
