<?php

namespace App\Repositories\Legacy;

use App\Contracts\Repositories\SuperPremiumLimitsRepositoryContract;
use App\Models\Legacy\EloquentConfiguration;

class SuperPremiumLimitsRepository implements SuperPremiumLimitsRepositoryContract
{
    /**
     * @return array
     */
    public function get(): array
    {
        $configs = EloquentConfiguration::whereIn(EloquentConfiguration::NAME, [EloquentConfiguration::NAME_SUPER_PREMIUM_ELECTRIC_COST_MIN, EloquentConfiguration::NAME_SUPER_PREMIUM_BUDGET_DIVISION_AMOUNT, EloquentConfiguration::NAME_SUPER_PREMIUM_LEADS_PER_DIVISION])
            ->select(
                EloquentConfiguration::NAME,
                EloquentConfiguration::VALUE
            )
            ->pluck(EloquentConfiguration::VALUE, EloquentConfiguration::NAME);

        return [
            EloquentConfiguration::NAME_SUPER_PREMIUM_ELECTRIC_COST_MIN      => (int)$configs[EloquentConfiguration::NAME_SUPER_PREMIUM_ELECTRIC_COST_MIN] ?? 0,
            EloquentConfiguration::NAME_SUPER_PREMIUM_BUDGET_DIVISION_AMOUNT => (int)$configs[EloquentConfiguration::NAME_SUPER_PREMIUM_BUDGET_DIVISION_AMOUNT] ?? 0,
            EloquentConfiguration::NAME_SUPER_PREMIUM_LEADS_PER_DIVISION     => (int)$configs[EloquentConfiguration::NAME_SUPER_PREMIUM_LEADS_PER_DIVISION] ?? 0
        ];
    }
}
