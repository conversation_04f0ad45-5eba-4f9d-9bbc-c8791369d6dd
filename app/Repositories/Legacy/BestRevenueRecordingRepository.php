<?php

namespace App\Repositories\Legacy;

use App\Contracts\Repositories\BestRevenueRecordingRepositoryContract;
use App\Models\Legacy\BestRevenueScenarioRoofing;
use App\Models\Legacy\BestRevenueScenarioSolar;
use App\Models\Legacy\EloquentComment;
use App\Services\Legacy\APIConsumer;

class BestRevenueRecordingRepository implements BestRevenueRecordingRepositoryContract
{
    const API_BASE_ENDPOINT           = '/repositories/best-revenue-recording';
    const API_UPDATE_SOLAR_ENDPOINT   = self::API_BASE_ENDPOINT . '/update-solar';
    const API_UPDATE_ROOFING_ENDPOINT = self::API_BASE_ENDPOINT . '/update-roofing';

    /** @var APIConsumer $apiConsumer */
    protected APIConsumer $apiConsumer;

    /**
     * @param APIConsumer $apiConsumer
     */
    public function __construct(APIConsumer $apiConsumer)
    {
        $this->apiConsumer = $apiConsumer;
    }

    /**
     * @inheritDoc
     */
    public function updateSolar(string $zipCode, int $utilityId, bool $electricOver100, int $leadTypeId, int $revenue): ?BestRevenueScenarioSolar
    {
        $bestRevenueScenarioSolarId = $this->apiConsumer->patch(self::API_UPDATE_SOLAR_ENDPOINT,
            compact("zipCode", "utilityId", "electricOver100", "leadTypeId", "revenue")
        )->json(APIConsumer::RESPONSE_RESULT);

        return BestRevenueScenarioSolar::find($bestRevenueScenarioSolarId);
    }

    /**
     * @inheritDoc
     */
    public function updateRoofing(string $zipCode, int $leadTypeId, int $revenue): ?BestRevenueScenarioRoofing
    {
        $bestRevenueScenarioRoofingId = $this->apiConsumer->patch(self::API_UPDATE_ROOFING_ENDPOINT,
            compact("zipCode", "leadTypeId", "revenue")
        )->json(APIConsumer::RESPONSE_RESULT);

        return BestRevenueScenarioRoofing::find($bestRevenueScenarioRoofingId);
    }
}
