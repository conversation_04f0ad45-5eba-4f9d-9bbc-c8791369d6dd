<?php

namespace App\Repositories\Advertising;

use App\Models\GoogleAdsGeoTarget;

class GoogleAdsGeoTargetRepository
{
    /**
     * @param string $state
     * @param string $county
     * @return int|null
     */
    public function getCountyLocationId(string $state, string $county): ?int
    {
        return GoogleAdsGeoTarget::query()
            ->select(GoogleAdsGeoTarget::TABLE.'.'.GoogleAdsGeoTarget::FIELD_LOCATION_ID)
            ->join(
                GoogleAdsGeoTarget::TABLE . ' as g2',
                GoogleAdsGeoTarget::TABLE.'.'.GoogleAdsGeoTarget::FIELD_PARENT_ID,
                'g2.'.GoogleAdsGeoTarget::FIELD_CRITERIA_ID
            )
            ->where(GoogleAdsGeoTarget::TABLE.'.'.GoogleAdsGeoTarget::FIELD_NAME, $county)
            ->where(GoogleAdsGeoTarget::TABLE.'.'.GoogleAdsGeoTarget::FIELD_TARGET_TYPE, GoogleAdsGeoTarget::TARGET_TYPE_COUNTY)
            ->where('g2.'.GoogleAdsGeoTarget::FIELD_NAME, $state)
            ->first()?->{GoogleAdsGeoTarget::FIELD_LOCATION_ID};
    }

    /**
     * @param string $state
     * @return int|null
     */
    public function getStateLocationId(string $state): ?int
    {
        return GoogleAdsGeoTarget::query()
            ->select(GoogleAdsGeoTarget::TABLE.'.'.GoogleAdsGeoTarget::FIELD_LOCATION_ID)
            ->where(GoogleAdsGeoTarget::TABLE.'.'.GoogleAdsGeoTarget::FIELD_NAME, $state)
            ->where(GoogleAdsGeoTarget::TABLE.'.'.GoogleAdsGeoTarget::FIELD_TARGET_TYPE, GoogleAdsGeoTarget::TARGET_TYPE_STATE)
            ->first()?->{GoogleAdsGeoTarget::FIELD_LOCATION_ID};
    }
}
