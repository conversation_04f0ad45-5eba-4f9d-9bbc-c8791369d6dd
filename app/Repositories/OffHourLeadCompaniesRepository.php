<?php

namespace App\Repositories;

use App\Enums\CompanyConsolidatedStatus;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyConfiguration;
use App\Services\Budget\CampaignBudgetCalculationService;
use App\Services\Campaigns\CalculateBudgetService;
use Illuminate\Support\Collection;

class OffHourLeadCompaniesRepository
{

    /**
     * @param EloquentQuote $lead
     * @return Collection <int, LeadCampaign>
     */
    public function companyCampaignsAvailableForLead(EloquentQuote $lead): Collection
    {
        $companies = Company::query()
            ->leftJoin(CompanyConfiguration::TABLE, Company::TABLE.'.'.Company::FIELD_ID, CompanyConfiguration::TABLE.'.'.CompanyConfiguration::FIELD_COMPANY_ID)
            ->where(CompanyConfiguration::TABLE.'.'.CompanyConfiguration::FIELD_RECEIVE_OFF_HOUR_LEADS, true)
            ->where(Company::FIELD_CONSOLIDATED_STATUS, CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS)
            ->get();

        $eligibleCampaigns = LeadCampaign::query()
            ->selectRaw(LeadCampaign::TABLE.'.*')
            ->leftJoin(LeadCampaignLocation::TABLE, LeadCampaign::TABLE.'.'.LeadCampaign::ID, LeadCampaignLocation::TABLE.'.'.LeadCampaignLocation::LEAD_CAMPAIGN_ID)
            ->leftJoin(Location::TABLE, LeadCampaignLocation::TABLE.'.'.LeadCampaignLocation::LOCATION_ID, Location::TABLE.'.'.Location::ID)
            ->whereIn(LeadCampaign::COMPANY_ID, $companies->pluck(Company::FIELD_LEGACY_ID)->toArray())
            ->where(LeadCampaign::STATUS, LeadCampaign::STATUS_ACTIVE)
            ->where(Location::TABLE.'.'.Location::ZIP_CODE, $lead->address->zipcode)
            ->get()
            ->unique(LeadCampaign::ID);

        /** @var CampaignBudgetCalculationService $budgetService */
        $budgetService = app(CampaignBudgetCalculationService::class);
        $utilization = $budgetService->getCurrentUtilizationForCampaigns($eligibleCampaigns->pluck(LeadCampaign::ID)->toArray());

        // remove campaigns with no available budget
        $eligibleCampaigns = $eligibleCampaigns->filter(function($campaign) use ($utilization) {
            return $utilization[$campaign->id] < 100;
        });

        // return only one campaign per company
        return $eligibleCampaigns->unique(LeadCampaign::COMPANY_ID);
    }
}
