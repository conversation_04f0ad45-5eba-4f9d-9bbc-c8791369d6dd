<?php

namespace App\Repositories\Campaigns;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\CompanyCampaignFilter;

class CampaignFilterRepository
{
    /**
     * @param CompanyCampaign $companyCampaign
     * @param array $data
     *
     * @return CompanyCampaignFilter
     */
    public function createFilter(CompanyCampaign $companyCampaign, array $data): CompanyCampaignFilter
    {
        /** @var CompanyCampaignFilter */
        return $companyCampaign->filters()->create($data);
    }

    /**
     * @param CompanyCampaignFilter $companyCampaignFilter
     * @param array $data
     *
     * @return bool
     */
    public function updateFilter(CompanyCampaignFilter $companyCampaignFilter, array $data): bool
    {
        return $companyCampaignFilter->update($data);
    }
}
