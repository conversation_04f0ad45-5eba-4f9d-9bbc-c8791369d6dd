<?php

namespace App\Services;

use App\Enums\Calendar\DemoStatus;
use App\Models\Calendar\CalendarEvent;
use App\Models\Calendar\Demo;
use App\Models\Conference\Conference;
use App\Models\Conference\ConferenceParticipant;
use App\Models\Conference\ConferenceTranscript;
use App\Models\Conference\ConferenceTranscriptEntry;
use App\Repositories\Demo\DemoRepository;
use App\Services\Filterables\SalesOverview\DemoFilterableService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Str;

class DemoService
{
    public const string     WORD_DEMO_REGEX        = 'demo';
    public const int        TEN_MINUTES_IN_SECONDS = 600;

    public function __construct(protected DemoRepository $demoRepository)
    {

    }

    /**
     * @param CalendarEvent $calendarEvent
     * @return bool
     */
    public function checkIfCalendarEventIsDemo(CalendarEvent $calendarEvent): bool
    {
        $pattern = '/\b' . self::WORD_DEMO_REGEX . '\b/i';
        return Str::match($pattern, $calendarEvent->{CalendarEvent::FIELD_TITLE})
            || Str::match($pattern, $calendarEvent->{CalendarEvent::FIELD_DESCRIPTION});
    }

    /**
     * @param int $calendarEventId
     * @param string $status
     * @param int $userId
     * @param string|null $note
     * @return Demo
     */
    public function updateOrCreate(
        int $calendarEventId,
        string $status,
        int $userId,
        ?string $note = null,
    ): Demo
    {
        return $this->demoRepository->updateOrCreate(
            calendarEventId: $calendarEventId,
            status         : $status,
            userId         : $userId,
            note           : $note,
        );
    }

    /**
     * @param int|null $userId
     * @param array $filters
     * @param int|null $companyId
     *
     * @return Builder
     */
    public function getFilteredDemoQuery(?int $userId, array $filters, ?int $companyId = null): Builder
    {
        $filterableService = app(DemoFilterableService::class);
        $query = $this->getBaseDemoQuery(userId: $userId)
            ->select([Demo::TABLE . '.*']);

        if ($companyId) {
            $query->whereHas(Demo::RELATION_COMPANY, fn(Builder $query) => $query->where(Demo::FIELD_COMPANY_ID, $companyId));
        }

        return $filterableService->runQuery($filters, $query)
            ->orderBy(CalendarEvent::TABLE .'.'. CalendarEvent::FIELD_START_TIME, 'desc');
    }

    /**
     * @param int|null $userId
     * @param string|null $startDate
     * @param string|null $endDate
     * @return Builder
     */
    public function getListDemosQuery(
        ?int $userId = null,
        ?string $startDate = null,
        ?string $endDate = null
    ): Builder
    {
        return $this->getBaseDemoQuery($userId, $startDate, $endDate)
            ->where(Demo::TABLE . '.' . Demo::FIELD_STATUS, DemoStatus::COMPLETED)
            ->havingRaw('COUNT(' . ConferenceParticipant::TABLE . '.' . ConferenceParticipant::FIELD_ID . ') > 1')
            ->where(Conference::TABLE . '.' . Conference::FIELD_DURATION_IN_SECONDS, '>=', self::TEN_MINUTES_IN_SECONDS)
            ->orderByDesc(Demo::TABLE . '.' . Demo::FIELD_CREATED_AT);
    }

    protected function getBaseDemoQuery(
        ?int $userId = null,
        ?string $startDate = null,
        ?string $endDate = null
    ): Builder
    {
        return Demo::query()
            ->with([
                Demo::RELATION_CALENDAR_EVENT
                . '.' . CalendarEvent::RELATION_CONFERENCES
                . '.' . Conference::RELATION_PARTICIPANTS,
                Demo::RELATION_CALENDAR_EVENT
                . '.' . CalendarEvent::RELATION_CONFERENCES
                . '.' . Conference::RELATION_TRANSCRIPTS
                . '.' . ConferenceTranscript::RELATION_ENTRIES
                . '.' . ConferenceTranscriptEntry::RELATION_PARTICIPANT,
                Demo::RELATION_CALENDAR_EVENT
                . '.' . CalendarEvent::RELATION_ATTENDEES
            ])
            ->when(filled($userId), fn($query) => $query->where(Demo::TABLE . '.' . Demo::FIELD_USER_ID, $userId))
            ->join(CalendarEvent::TABLE, CalendarEvent::TABLE . '.' . CalendarEvent::FIELD_ID, Demo::TABLE . "." . Demo::FIELD_CALENDAR_EVENT_ID)
            ->join(Conference::TABLE, Conference::TABLE . '.' . Conference::FIELD_CALENDAR_EVENT_ID, CalendarEvent::TABLE . '.' . CalendarEvent::FIELD_ID)
            ->join(ConferenceParticipant::TABLE, ConferenceParticipant::TABLE . '.' . ConferenceParticipant::FIELD_CONFERENCE_ID, Conference::TABLE . '.' . Conference::FIELD_ID)
            ->when(filled($startDate), fn($query) => $query->where(Conference::TABLE . '.' . Conference::FIELD_START_TIME, '>=', $startDate))
            ->when(filled($endDate), fn($query) => $query->where(Conference::TABLE . '.' . Conference::FIELD_END_TIME, '<=', $endDate))
            ->groupBy(Conference::TABLE . '.' . Conference::FIELD_CALENDAR_EVENT_ID);
    }
}
