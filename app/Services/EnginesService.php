<?php

namespace App\Services;

use App\Http\Requests\Odin\v2\StoreConsumerRequest;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Http;

class EnginesService
{
    const int SECONDS_IN_MINUTE        = 60;
    const int MINUTES_IN_HOUR          = 60;
    const int HOURS_IN_DAY             = 24;
    const int EXPIRY_1_DAY             = self::SECONDS_IN_MINUTE * self::MINUTES_IN_HOUR * self::HOURS_IN_DAY;
    protected string $engineUrl;
    protected string $basePath = 'api/v1/builder/engines/';

    public function __construct()
    {
        $this->engineUrl = config('services.flow_engines.url');
    }

    public function getAvailableEngines(): Collection
    {
        return Cache::remember('engines', self::EXPIRY_1_DAY, function () {
            $response = Http::get($this->engineUrl.$this->basePath);
            logger()->error(collect(json_decode($response->getBody(), true)['data']['engines'])->pluck('name'));
            if($response->successful())
                return collect(json_decode($response->getBody(), true)['data']['engines'])->pluck('name');
            else
                return collect();
        });
    }

    public function getAvailableEngineOptions(): Collection
    {
        return $this->getAvailableEngines()->map(function (string $item) {
            return [
                'label' => $item,
                'value' => $item
            ];
        });
    }

    public function getEngineOutputsOptions(string $engineName): Collection
    {
        return Cache::remember($engineName.'-outputs', self::EXPIRY_1_DAY, function () use ($engineName) {
            $response = Http::get($this->engineUrl.$this->basePath.$engineName.'/get-available-outputs');
            if($response->getStatusCode() >= 200 && $response->getStatusCode() < 300)
            {
                return collect(json_decode($response->getBody(), true)['data'])->map(function (string $item) {
                    return [
                        'label' => $item,
                        'value' => $item
                    ];
                });
            }

            return collect();
        });
    }

    /**
     * @param array $payload
     * @return bool|string
     */
    public function checkEngineOutputExistsInConsumerPayload(array &$payload): bool|string
    {
        foreach ($this->getAvailableEngines() as $engine)
            if(Arr::has($payload, StoreConsumerRequest::CONSUMER_KEY .'.'. $engine) && $engine !== 'lead')
                return $engine;

        return false;
    }
}
