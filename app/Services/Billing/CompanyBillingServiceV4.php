<?php

namespace App\Services\Billing;

use App\Contracts\Services\PaymentGatewayServiceContract;
use App\DTO\Billing\PaymentMethods\PaymentMethodDTO;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Enums\Billing\PaymentMethodServices;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\CompanyPaymentMethod;
use App\Models\Billing\Invoice;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Repositories\Billing\BillingProfileRepository;
use App\Repositories\Billing\CompanyPaymentMethodRepository;
use App\Services\Billing\BillingProfile\BillingProfileService;
use App\Services\PaymentGateway\PaymentGatewayServiceFactory;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Collection;

class CompanyBillingServiceV4
{

    protected PaymentGatewayServiceContract $paymentGatewayService;

    public function __construct(
        protected BillingProfileService $billingProfileService,
        protected InvoiceService $invoiceService,
        protected InvoicePaymentService $invoicePaymentService,
        protected BillingProfileRepository $billingProfileRepository,
        protected CompanyPaymentMethodRepository $companyPaymentMethodRepository
    )
    {
        $this->paymentGatewayService = PaymentGatewayServiceFactory::make(PaymentMethodServices::STRIPE);
    }

    /**
     * @param string $invoiceId
     * @param Company $company
     * @param int $userId
     * @return array
     * @throws BindingResolutionException
     */
    public function payCustomerInvoice(string $invoiceId, Company $company, int $userId): array
    {
        /** @var Invoice $invoice */
        $invoice = Invoice::query()->where(Invoice::FIELD_ID, $invoiceId)->first();

        if (!$company->billingProfiles()->exists()) {
            $message = 'Company has not added a payment method.';
        } else {
            $this->invoicePaymentService->payInvoice(
                invoice   : $invoice,
                amount    : $invoice->getTotalOutstanding(),
                authorType: InvoiceEventAuthorTypes::COMPANY_USER,
                authorId  : $userId,
            );

            $message = 'Processing payment request';
        }

        // TODO
        return ['status' => 'paid', 'message' => $message];
    }

    /**
     * @param Company $company
     * @param CompanyUser $user
     * @param string $stripeToken
     * @param string|null $number
     * @param string|null $expiryMonth
     * @param string|null $expiryYear
     * @return bool
     */
    public function initialiseCustomerBilling(
        Company $company,
        CompanyUser $user,
        string $stripeToken,
        ?string $number = null,
        ?string $expiryMonth = null,
        ?string $expiryYear = null,
    ): bool
    {
        $customerCode = $this->companyPaymentMethodRepository
            ->getStripeClientCodeByCompanyId($company->{Company::FIELD_ID});

        $isNewCustomer = empty($customerCode);

        $customerCode = $isNewCustomer ? $this->createNewCustomer(
            company: $company,
            email  : $user->email
        ) : $customerCode;

        $paymentMethodCode = $this->createNewPaymentMethod($stripeToken);

        $this->addPaymentMethodToCustomer(
            customerCode : $customerCode,
            paymentMethod: $paymentMethodCode,
            default      : $isNewCustomer
        );

        $hasStripeBillingProfile = $this->billingProfileService->getBillingProfile(
            companyId    : $company->{Company::FIELD_ID},
            paymentMethod: PaymentMethodServices::STRIPE->value
        );

        if (!$hasStripeBillingProfile) {
            $this->billingProfileService->createProfileWithDefaultConfiguration(
                paymentMethod: PaymentMethodServices::STRIPE,
                companyId    : $company->id,
                default      : true,
                processAuto  : true,
                contact      : $user,
                createdById  : $user->id,
                createdByType: CompanyUser::class,
            );
        }

        $this->companyPaymentMethodRepository->create(
            companyId                      : $company->{Company::FIELD_ID},
            type                           : PaymentMethodServices::STRIPE,
            addedByType                    : $user::class,
            isDefault                      : $isNewCustomer,
            addedById                      : $user->id,
            paymentGatewayPaymentMethodCode: $paymentMethodCode,
            paymentGatewayClientCode       : $customerCode,
            expiryMonth                    : $expiryMonth,
            expiryYear                     : $expiryYear,
            number                         : $number,
        );

        $this->setDefaultPaymentMethod($paymentMethodCode);

        return true;
    }

    /**
     * @param Company $company
     * @param string $email
     * @return string
     */
    public function createNewCustomer(Company $company, string $email): string
    {
        return $this->paymentGatewayService->createCustomer($company, $email);
    }

    /**
     * @param string $token
     * @return string
     */
    public function createNewPaymentMethod(string $token): string
    {
        return $this->paymentGatewayService->createNewPaymentMethod($token);
    }

    /**
     * @param string $customerCode
     * @param string $paymentMethod
     * @param bool|null $default
     * @return void
     */
    public function addPaymentMethodToCustomer(string $customerCode, string $paymentMethod, ?bool $default = false): void
    {
        $this->paymentGatewayService->addPaymentMethod($customerCode, $paymentMethod);
        if ($default) $this->paymentGatewayService->setPaymentMethodPrimary($customerCode, $paymentMethod);
    }

    /**
     * @param string $customerId
     * @return Collection<PaymentMethodDTO>
     */
    public function getPaymentMethods(string $customerId): Collection
    {
        return collect($this->paymentGatewayService->getPaymentMethods($customerId));
    }

    /**
     * @param int $companyId
     * @return Collection<PaymentMethodDTO>
     */
    public function getAllCompanyStripePaymentMethods(int $companyId): Collection
    {
        $clientCode = $this->companyPaymentMethodRepository->getStripeClientCodeByCompanyId($companyId);

        if (empty($clientCode)) {
            return collect();
        }

        return $this->getPaymentMethods($clientCode);
    }

    /**
     * @param int $companyId
     * @return bool
     */
    public function hasAtLeastOneCardOnFile(int $companyId): bool
    {
        return CompanyPaymentMethod::query()
            ->where(CompanyPaymentMethod::FIELD_TYPE, PaymentMethodServices::STRIPE)
            ->where(CompanyPaymentMethod::FIELD_COMPANY_ID, $companyId)
            ->count() > 0;
    }

    /**
     * @param int $companyId
     *
     * @return bool
     */
    public function companyHasValidPaymentMethod(int $companyId): bool
    {
        if (CompanyPaymentMethod::query()
            ->where(CompanyPaymentMethod::FIELD_TYPE, PaymentMethodServices::MANUAL)
            ->where(CompanyPaymentMethod::FIELD_COMPANY_ID, $companyId)
            ->get()
            ->isNotEmpty()) {
            return true;
        }

        $paymentMethods = $this->getAllCompanyStripePaymentMethods($companyId);

        if ($paymentMethods->isEmpty()) {
            return false;
        }

        foreach ($paymentMethods as $paymentMethod) {
            if (!$paymentMethod->expired()) {
                return true;
            }
        }

        return false;
    }

    /**
     * @param CompanyPaymentMethod|string $companyPaymentMethod
     * @return bool
     */
    public function setDefaultPaymentMethod(CompanyPaymentMethod|string $companyPaymentMethod): bool
    {
        if (is_string($companyPaymentMethod)) {
            $companyPaymentMethod = $this->companyPaymentMethodRepository->getByExternalId($companyPaymentMethod);
        }

        $this->paymentGatewayService->setPaymentMethodPrimary(
            $companyPaymentMethod->{CompanyPaymentMethod::FIELD_PAYMENT_GATEWAY_CLIENT_CODE},
            $companyPaymentMethod->{CompanyPaymentMethod::FIELD_PAYMENT_GATEWAY_PAYMENT_METHOD_CODE}
        );

        CompanyPaymentMethod::query()
            ->where(CompanyPaymentMethod::FIELD_COMPANY_ID, $companyPaymentMethod->{CompanyPaymentMethod::FIELD_COMPANY_ID})
            ->whereNot(CompanyPaymentMethod::FIELD_ID, $companyPaymentMethod->{CompanyPaymentMethod::FIELD_ID})
            ->update([
                CompanyPaymentMethod::FIELD_IS_DEFAULT => false
            ]);

        $companyPaymentMethod->update([
            CompanyPaymentMethod::FIELD_IS_DEFAULT => true
        ]);

        return true;
    }

    /**
     * @param string $paymentMethodId
     * @return bool
     * @throws Exception
     */
    public function deletePaymentMethod(string $paymentMethodId): bool
    {
        $paymentMethod = CompanyPaymentMethod::query()
            ->where(CompanyPaymentMethod::FIELD_PAYMENT_GATEWAY_PAYMENT_METHOD_CODE, $paymentMethodId)
            ->firstOrFail();

        $hasAnotherCard = CompanyPaymentMethod::query()
            ->where(CompanyPaymentMethod::FIELD_COMPANY_ID, $paymentMethod->{CompanyPaymentMethod::FIELD_COMPANY_ID})
            ->where(CompanyPaymentMethod::FIELD_TYPE, PaymentMethodServices::STRIPE->value)
            ->whereNull(CompanyPaymentMethod::FIELD_DELETED_AT)
            ->whereNot(CompanyPaymentMethod::FIELD_PAYMENT_GATEWAY_PAYMENT_METHOD_CODE, $paymentMethodId)
            ->first();

        if (!$hasAnotherCard){
            throw new Exception('Cannot remove primary card');
        }

        $this->paymentGatewayService->deletePaymentMethod($paymentMethodId);

        BillingProfile::query()
            ->where(BillingProfile::FIELD_PAYMENT_METHOD_ID, $paymentMethod->id)
            ->update([
                BillingProfile::FIELD_PAYMENT_METHOD_ID => null
            ]);

        $paymentMethod->delete();

        return true;
    }

}
