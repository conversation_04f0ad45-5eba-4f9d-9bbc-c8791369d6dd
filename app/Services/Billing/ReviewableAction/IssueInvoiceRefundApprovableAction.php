<?php

namespace App\Services\Billing\ReviewableAction;

use App\Aggregates\InvoiceAggregateRoot;
use App\DTO\Billing\Refund\InvoiceRefundDTO;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Models\Billing\Invoice;
use App\Services\Billing\CreditService;
use App\Services\Billing\InvoiceRefunds\InvoiceRefundService;
use Illuminate\Contracts\Container\BindingResolutionException;

class IssueInvoiceRefundApprovableAction extends ReviewableAction
{
    public function __construct(protected InvoiceRefundService $invoiceRefundService)
    {

    }

    /**
     * @param array $arguments
     * @return void
     * @throws BindingResolutionException
     */
    public function onApproval(array $arguments): void
    {
        [
            "invoiceUuid"  => $invoiceUuid,
            "refundObject" => $refundObject,
            "authorType"   => $authorType,
            "authorId"     => $authorId,
        ] = $arguments;

        $invoiceAggregateRoot = InvoiceAggregateRoot::retrieve($invoiceUuid);

        $invoiceAggregateRoot->refund->requestRefund(
            invoiceUuid     : $invoiceUuid,
            invoiceRefundDTO: InvoiceRefundDTO::fromArray($refundObject),
            authorType      : $authorType,
            authorId        : $authorId,
        );

        $invoiceAggregateRoot->persist();
    }
}
