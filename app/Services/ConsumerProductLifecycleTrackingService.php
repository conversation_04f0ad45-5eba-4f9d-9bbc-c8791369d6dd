<?php

namespace App\Services;

use App\Campaigns\Modules\DataModels\AllocateData;
use App\Events\ConsumerProductLifecycleTracking\AllocationAttemptScheduled;
use App\Events\ConsumerProductLifecycleTracking\ApprovedToSell;
use App\Events\ConsumerProductLifecycleTracking\Created;
use App\Events\ConsumerProductLifecycleTracking\QueueUpdated;
use App\Events\ConsumerProductLifecycleTracking\StatusUpdated;
use App\Jobs\ConsumerProductLifecycleTracking\UpdateAllocationAttemptData;
use App\Models\LeadProcessor;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ProductAssignment;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;

class ConsumerProductLifecycleTrackingService
{
    const string CONCLUSION_FINAL_ATTEMPT_SCHEDULED = 'final_attempt_scheduled';
    const string CONCLUSION_RELEASED_BACK_TO_QUEUE  = 'released_back_to_queue';
    const string CONCLUSION_ALLOCATED               = 'allocated';
    const string CONCLUSION_UNSOLD                  = 'unsold';
    const string CONCLUSION_ZERO_CONTACT_REQUESTS   = 'zero_contact_requests';
    const string ATTEMPT_TO_SELL_NON_DIRECT_LEAD    = 'attempt_to_sell_non_direct_lead';

    /** @var int $consumerProductId */
    protected int $consumerProductId;

    /** @var string $currentJobUuid */
    protected string $currentJobUuid;

    /** @var int $currentJobAttemptNumber */
    protected int $currentJobAttemptNumber;

    /** @var array $attemptData */
    protected array $attemptData;

    /**
     * @param ConsumerProduct $consumerProduct
     * @return void
     */
    public static function created(ConsumerProduct $consumerProduct): void
    {
        Created::dispatch($consumerProduct->id, Carbon::now());
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @return void
     */
    public static function statusUpdated(ConsumerProduct $consumerProduct): void
    {
        $status = array_key_exists($consumerProduct->status, ConsumerProduct::STATUS_TEXT) ?
            ConsumerProduct::STATUS_TEXT[$consumerProduct->status] :
            $consumerProduct->status;
        StatusUpdated::dispatch($consumerProduct->id, $status, Carbon::now());
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @param LeadProcessor $processor
     * @param string $newQueue
     * @param string $reason
     * @param string|null $comment
     * @param bool|null $automatic
     * @return void
     */
    public static function queueUpdated(
        ConsumerProduct $consumerProduct,
        LeadProcessor $processor,
        string $newQueue,
        string $reason,
        ?string $comment,
        ?bool $automatic
    ): void
    {
        QueueUpdated::dispatch($consumerProduct->id, Carbon::now(), $processor->user?->name, $newQueue, $reason, $comment, $automatic);
    }

    /**
     * @param ConsumerProduct $consumerProduct
     * @return void
     */
    public static function approvedToSell(ConsumerProduct $consumerProduct): void
    {
        ApprovedToSell::dispatch($consumerProduct->id, Carbon::now());
    }

    /**
     * @param ConsumerProduct|null $consumerProduct
     * @param int|null $delayInSeconds
     * @return void
     */
    public static function allocationAttemptScheduled(?ConsumerProduct $consumerProduct, ?int $delayInSeconds = null): void
    {
        AllocationAttemptScheduled::dispatch(
            $consumerProduct?->id ?? self::fakeConsumerProductId(),
            Carbon::now(),
            $delayInSeconds
        );
    }

    /**
     * @return int
     */
    private static function fakeConsumerProductId(): int
    {
        return '999' . rand(0,1000);
    }

    /**
     * @return void
     */
    private function updateAttemptData(): void
    {
        UpdateAllocationAttemptData::dispatch($this->consumerProductId, $this->currentJobUuid, $this->currentJobAttemptNumber, $this->attemptData, Carbon::now());
    }

    /**
     * @param ConsumerProduct|null $consumerProduct
     * @param string|null $allocationJobUuid
     * @param int|null $attemptNumber
     * @return void
     */
    public function beginAllocationAttempt(?ConsumerProduct $consumerProduct, ?string $allocationJobUuid, ?int $attemptNumber = 0): void
    {
        $this->consumerProductId = $consumerProduct?->id ?? self::fakeConsumerProductId();
        $this->currentJobUuid = $allocationJobUuid ?? ('?_' . Str::uuid());
        $this->currentJobAttemptNumber = $attemptNumber;
        $this->attemptData = [
            'started_at' => Carbon::now()->toDateTimeString(),
            'completed_at' => null,
            'delay_to_tz_open' => null,
            'campaigns' => null,
            'conclusion' => null,
            'proposed_assignments' => null,
            'allocation_data' => null,
        ];
        $this->updateAttemptData();
    }

    /**
     * @param int $delayToTZOpen
     * @return void
     */
    public function delayToTimeZoneOpen(int $delayToTZOpen): void
    {
        $this->attemptData['delay_to_tz_open'] = $delayToTZOpen;
        $this->updateAttemptData();
    }

    /**
     * @param array $campaignIds
     * @return void
     */
    public function campaigns(array $campaignIds): void
    {
        if(!$this->attemptData['campaigns']) {
            $this->attemptData['campaigns'] = [];
        }
        $this->attemptData['campaigns']['count'] = count($campaignIds);
        $this->attemptData['campaigns']['ids'] = $campaignIds;
        $this->updateAttemptData();
    }

    /**
     * @param Collection $proposedAssignments
     * @return void
     */
    public function proposedAssignments(Collection $proposedAssignments): void
    {
        // todo: add more explicit data
        if(!$this->attemptData['proposed_assignments']) {
            $this->attemptData['proposed_assignments'] = [];
        }
        $this->attemptData['proposed_assignments']['count'] = $proposedAssignments->count();
        $this->updateAttemptData();
    }

    /**
     * @param string $conclusion
     * @return void
     */
    public function concludeAttempt(string $conclusion): void
    {
        $this->attemptData['conclusion'] = $conclusion;
        $this->attemptData['completed_at'] = Carbon::now()->toDateTimeString();
        $this->updateAttemptData();
    }

    /**
     * @param AllocateData $allocationData
     * @return void
     */
    public function allocationData(AllocateData $allocationData): void
    {
        if(!$this->attemptData['allocation_data']) {
            $this->attemptData['allocation_data'] = [];
        }

        $productAssignments = $allocationData->getProductAssignments();
        if($productAssignments->isEmpty()) {
            return;
        }

        $this->attemptData['allocation_data']['product_assignment_count'] = $productAssignments->count();
        $this->attemptData['allocation_data']['product_assignment_ids'] = $productAssignments->pluck(ProductAssignment::FIELD_ID)->toArray();
        $this->updateAttemptData();
    }

    /**
     * @param array $brsData
     * @return void
     */
    public function addBudgetSummaryToAllocationData(array $brsData): void
    {
        if(!$this->attemptData['allocation_data']) {
            $this->attemptData['allocation_data'] = [];
        }
        $this->attemptData['allocation_data']['brs_data'] = $brsData;
    }

    /**
     * @param int $campaignId
     * @param string $reason
     * @param bool $update
     *
     * @return void
     */
    public function addFilteredCampaign(int $campaignId, string $reason, bool $update = false): void
    {
        if(!$this->attemptData['allocation_data']) {
            $this->attemptData['allocation_data'] = [];
        }

        $this->attemptData['allocation_data']['excluded_campaigns'][] = ['campaign_id' => $campaignId, 'reason' => $reason];

        if ($update) {
            $this->updateAttemptData();
        }
    }
}
