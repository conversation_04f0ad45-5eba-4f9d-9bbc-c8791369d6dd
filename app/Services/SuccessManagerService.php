<?php

namespace App\Services;

use App\Enums\ActivityLog\ActivityLogDescription;
use App\Enums\ActivityLog\ActivityLogName;
use App\Models\Odin\Company;
use App\Models\SuccessManager;
use App\Models\SuccessManagerClient;
use App\Models\User;
use App\Repositories\ActivityLog\ActivityLogRepository;
use App\Repositories\SuccessManagerRepository;
use Illuminate\Support\Collection;

class SuccessManagerService
{
    public function __construct(
        protected ActivityLogRepository $activityLogRepository,
        protected SuccessManagerRepository $successManagerRepository,
    )
    {
    }

    /**
     * @return Collection
     */
    public function getSuccessManagersWithUserJoin(): Collection
    {
        return SuccessManager::query()
            ->join(User::TABLE, User::TABLE.'.'.User::FIELD_ID, '=',
                SuccessManager::TABLE.'.'.SuccessManager::FIELD_USER_ID)
            ->select([
                '*',
                User::TABLE.'.'.User::FIELD_NAME.' as '.User::TABLE.'.'.User::FIELD_NAME,
                SuccessManager::TABLE.'.'.SuccessManager::FIELD_ID.' as '.SuccessManager::TABLE.'.'.SuccessManager::FIELD_ID,
            ])
            ->get();
    }

    /**
     * @param ActivityLogDescription $updateReason
     * @param Company $company
     * @param int|null $successManagerId
     * @return SuccessManagerClient|null
     */
    public function updateCompanySuccessManager(
        ActivityLogDescription $updateReason,
        Company $company,
        ?int $successManagerId = null,
    ): ?SuccessManagerClient
    {
        /** @var Collection<SuccessManagerClient> $successManagerClients */
        $successManagerClients = $company->successManagerClients()
            ->where(SuccessManagerClient::FIELD_STATUS, '=', true)
            ->get();

        //if no success manager clients and no id given returns false, only returns true if there are other success managers different
        $otherManagerPresent = $successManagerClients->contains(function (SuccessManagerClient $successManagerClient) use ($successManagerId) {
            return $successManagerClient->success_manager_id !== $successManagerId;
        });

        $existingSuccessManagerClients = $successManagerClients->isNotEmpty();

        $noSuccessManagerClientToNoSuccessManager = (!$existingSuccessManagerClients && empty($successManagerId));

        $sameExistingAsNew = (!$otherManagerPresent && $existingSuccessManagerClients);

        if ($noSuccessManagerClientToNoSuccessManager || $sameExistingAsNew) {
            //nothing to update
            return null;
        }

        $oldSuccessManagers = $successManagerClients->pluck('success_manager_id')->unique()->toArray();

        $this->activityLogRepository->createActivityLog(
            logName    : ActivityLogName::COMPANY_SUCCESS_MANAGER_CHANGE->value,
            description: $updateReason->value,
            subjectType: $company::class,
            subjectId  : $company->id,
            properties : [
                'from_ids' => $oldSuccessManagers,
                'to_id'    => $successManagerId,
            ],
        );

        return $this->successManagerRepository->assignSuccessManager(
            $company->id,
            $successManagerId
        );
    }
}
