<?php

namespace App\Services\Filterables\Company;

use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\EloquentInvoice;
use App\Models\Odin\Company;
use App\Services\DatabaseHelperService;
use App\Services\Filterables\SelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;

class CompanyHasUnpaidInvoicesFilterable extends SelectFilterableOption
{
    public function __construct()
    {
        $this->model = Company::class;
        $this->name = "Has Unpaid Invoices";
        $this->id = 'company-has-unpaid-invoices';
        $this->withRelations = null;

        $this->options = [
            'Yes' => 'true',
        ];
    }

    /**
     * @param Builder $builder
     * @param mixed $value
     * @return Builder
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        if ($value) {
            return $builder->leftJoin(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoice::TABLE, function (JoinClause $joinClause) use ($value) {
                $joinClause->on(Company::TABLE . '.' . Company::FIELD_LEGACY_ID, '=', DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoice::TABLE . '.' . EloquentInvoice::COMPANY_ID);
            })->where(DatabaseHelperService::readOnlyDatabase() . '.' . EloquentInvoice::TABLE . '.' . EloquentInvoice::STATUS, '=', EloquentInvoice::VALUE_STATUS_INITIAL)
                ->where(EloquentInvoice::TIMESTAMP_PAYMENT_DUE, '<', time())
                ->where(EloquentInvoice::TABLE . '.' . EloquentInvoice::PROCESSING, '=', false);
        } else {
            return $builder;
        }
    }
}
