<?php

namespace App\Services\Filterables\Company;

use App\Models\Odin\Company;
use App\Services\Filterables\BaseFilterableService;
use Illuminate\Database\Eloquent\Builder;

class CompanyFilterableService extends BaseFilterableService
{
    const FILTERABLE_CATEGORY = 'company-search';

    protected array $filters = [
        CampaignStatusFilterable::class,
        CompanyAdminStatusFilterable::class,
        CompanySystemStatusFilterable::class,
        CompanySalesStatusFilterable::class,
        CompanyConsolidatedStatusFilterable::class,
        CompanyIndustryFilterable::class,
        CompanyOfficeLocationFilterable::class,
        CompanyAccountManagerFilterable::class,
        CompanySuccessManagerFilterable::class,
        CompanyAmountOfLeadsPurchasedFilterable::class,
        CompanyNeverExceedsBudgetFilterable::class,
        CompanyCadenceStatusFilterable::class,
        CompanyCadenceNameFilterable::class,
//        CompanyCampaignStatusFilterable::class, //todo: pending delete (using legacy campaign)
        CompanyCampaignTemporarilyPausedFilterable::class,
        CompanyCampaignBudgetFilterable::class,
        CompanyCampaignServiceAreaFilterable::class,
        CompanyLeadRejectionPercentageFilterable::class,
        CompanyDirectLeadsRejectionPercentageFilterable::class,
        CompanyEmployeeCountFilterable::class,
        CompanyEstimatedRevenueFilterable::class,
        CompanyGoogleRatingFilterable::class,
        CompanyGoogleReviewCountFilterable::class,
        CompanyConsumerRatingFilterable::class,
        CompanyConsumerReviewCountFilterable::class,
        CompanyPreScreenedStatusFilterable::class,
        CompanyLastContactedDateRangeFilterable::class,
        CompanyLastLeadPurchasedDateRangeFilterable::class,
        CompanyDecisionMakerFilterable::class,
        CompanyConfigurableFieldsFilterable::class,
        CompanyHasUnpaidInvoicesFilterable::class,
        CompanyIsEnergySageCustomerFilterable::class,
        CompanyPurchasingFromCompetitorFilterable::class,
        CompanyTimeZoneFilterable::class,
        CompanyEstimatedMonthlyPPCSpendFilterable::class,
        CompanyBusinessDevelopmentManagerFilterable::class,
        CompanyQrTop500Filterable::class,
        CompanyHasFilterEnabledCampaignsFilterable::class,
        CompanyHasBiddingDisabledCampaignsFilterable::class,
        CompanySalesDevelopmentRepresentativeFilterable::class,
        CompanyMissingRoleAssignmentsFilterable::class,
        CompanyFirstLeadPurchasedDateRangeFilterable::class,
    ];

    protected string $baseModel = Company::class;

    /**
     * @param array $results
     * @param Builder|null $baseQuery
     * @return Builder
     */
    public function runQuery(array $results, ?Builder $baseQuery = null): Builder
    {
        $query = $baseQuery ?? Company::query();
        $this->runFilterQueries($query, $results);

        return $query;
    }
}
