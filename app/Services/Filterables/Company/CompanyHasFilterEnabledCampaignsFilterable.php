<?php

namespace App\Services\Filterables\Company;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\CompanyCampaignFilter;
use App\Models\Odin\Company;
use App\Models\Odin\IndustryService;
use App\Services\Filterables\SelectFilterableOption;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Query\JoinClause;
use Illuminate\Support\Facades\DB;

class CompanyHasFilterEnabledCampaignsFilterable extends SelectFilterableOption
{
    public function __construct()
    {
        $this->model         = Company::class;
        $this->name          = "Has Filter Enabled Campaigns";
        $this->id            = 'company-has-filter-enabled-campaigns';
        $this->withRelations = null;

        $this->options = [
            'Yes' => 'yes',
        ];
    }

    /**
     * @param Builder $builder
     * @param mixed $value
     *
     * @return Builder
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        if ($value) {
            return $builder->whereExists(
                CompanyCampaign::query()
                    ->select(DB::raw(1))
                    ->join(IndustryService::TABLE, function (JoinClause $joinClause) {
                        $joinClause->on(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_SERVICE_ID, IndustryService::TABLE . '.' . IndustryService::FIELD_ID)
                            ->where(IndustryService::TABLE . '.' . IndustryService::FIELD_CAMPAIGN_FILTER_ENABLED, true);
                    })->join(
                        CompanyCampaignFilter::TABLE,
                        CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID,
                        CompanyCampaignFilter::TABLE . '.' . CompanyCampaignFilter::FIELD_COMPANY_CAMPAIGN_ID
                    )->whereColumn(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_COMPANY_ID, Company::TABLE . '.' . Company::FIELD_ID)
            );
        }

        return $builder;
    }
}
