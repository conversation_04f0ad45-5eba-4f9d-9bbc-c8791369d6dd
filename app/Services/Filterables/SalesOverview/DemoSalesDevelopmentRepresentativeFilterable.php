<?php

namespace App\Services\Filterables\SalesOverview;

use App\Models\Calendar\Demo;
use App\Models\User;
use App\Services\Filterables\MultiSelectFilterableOption;
use Exception;
use Illuminate\Database\Eloquent\Builder;

class DemoSalesDevelopmentRepresentativeFilterable extends MultiSelectFilterableOption
{
    public function __construct()
    {
        $this->model         = Demo::class;
        $this->name          = "Sales Development Representatives";
        $this->id            = 'demo-sales-development-representative';
        $this->withRelations = null;

        $this->options = User::salesDevelopmentRepresentativeRole()
            ->orderBy(User::FIELD_NAME)
            ->pluck(User::FIELD_ID, User::FIELD_NAME)
            ->toArray();
    }

    /**
     * @param Builder $builder
     * @param mixed $value
     *
     * @return Builder
     * @throws Exception
     */
    public function runQuery(Builder $builder, mixed $value): Builder
    {
        if (filled($value)) {
            return $builder->whereHas(
                Demo::RELATION_COMPANY . '.' . 'salesDevelopmentRepresentative',
                fn(Builder $query) => $query->whereIn(User::TABLE . '.' . User::FIELD_ID, $value));
        }

        return $builder;
    }
}
