<?php

namespace App\Services\Filterables\SalesOverview;

use App\Models\Calendar\Demo;
use App\Services\Filterables\BaseFilterableService;
use Illuminate\Database\Eloquent\Builder;

class DemoFilterableService extends BaseFilterableService
{
    const string FILTERABLE_CATEGORY = 'demo-search';

    protected string $baseModel = Demo::class;

    protected array $filters = [
        DemoDateFilterable::class,
        DemoSalesDevelopmentRepresentativeFilterable::class,
    ];

    public function runQuery(array $results, ?Builder $baseQuery = null): Builder
    {
        $query = $baseQuery ?? Demo::query();
        $this->runFilterQueries($query, $results);

        return $query;
    }
}
