<?php

namespace App\Services\Calendar\Google;

use App\DTO\Calendar\RecurrenceRuleDTO;

class GoogleRecurrenceRuleParser
{
    /**
     * @param string|null $rrule
     * @return RecurrenceRuleDTO|null
     */
    public function parse(?string $rrule = null): ?RecurrenceRuleDTO
    {
        $rule = new RecurrenceRuleDTO();

        try {
            if (empty($rrule)) {
                return null;
            }

            $ruleParts = explode(';', $rrule);


            foreach ($ruleParts as $part) {
                list($key, $value) = explode('=', $part);

                switch (strtoupper($key)) {
                    case 'FREQ':
                        $rule->setFrequency(strtoupper($value));
                        break;
                    case 'INTERVAL':
                        $rule->setFrequency((int)$value);
                        break;
                    case 'BYDAY':
                        $rule->setByDay(explode(',', strtoupper($value)));
                        break;
                    case 'BYMONTHDAY':
                        $rule->setByMonthDay(array_map('intval', explode(',', $value)));
                        break;
                    case 'BYMONTH':
                        $rule->setByMonth(array_map('intval', explode(',', $value)));
                        break;
                    case 'UNTIL':
                        $rule->setUntil($value);
                        break;
                    case 'COUNT':
                        $rule->setCount((int)$value);
                        break;
                }
            }

        } catch (\Exception $exception) {
            logger()->error($exception);
        }

        return $rule;
    }
}

?>
