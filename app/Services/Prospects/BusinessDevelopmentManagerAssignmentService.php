<?php

namespace App\Services\Prospects;

use App\Models\Action;
use App\Models\ActivityFeed;
use App\Models\Odin\Company;
use App\Models\Odin\ProductAssignment;
use App\Models\User;
use App\Repositories\ActionRepository;
use Illuminate\Contracts\Container\BindingResolutionException;
use Throwable;

class BusinessDevelopmentManagerAssignmentService
{
    const string STATUS_GOOD_TO_ASSIGN = 'good_to_assign';

    const string STATUS_ACTIVE_BUYER = 'active_buyer';

    const string STATUS_RECENT_BUSINESS_DEVELOPMENT_MANAGER_RELATED_ACTIVITY = 'recent_bdm_activity';

    const string STATUS_ACCOUNT_MANAGER_PRESENT = 'account_manager_present';

    const string STATUS_UNKNOWN_ISSUE = 'unknown_issue';

    const int LEAD_BUYER_RECENCY_THRESHOLD_DAYS = 90;

    const int BDM_ACTIVITY_RECENCY_THRESHOLD_DAYS = 42;

    const int ACTION_CATEGORY_OTHER = 4;

    /**
     * @throws BindingResolutionException|Throwable
     */
    public static function assignToCompany(int $userId, int $companyId, bool $isProspectDuplicate = false, ?string $reason = null): void
    {
        /** @var Company $company */
        $company = Company::findOrFail($companyId);

        $canAssignStatus = $isProspectDuplicate ? self::STATUS_GOOD_TO_ASSIGN : self::verifyIfCanBeAssigned($company);
        self::logAssignmentStatus($userId, $company, $canAssignStatus);

        if ($canAssignStatus !== self::STATUS_GOOD_TO_ASSIGN) {
            return;
        }

        /** @var User $user */
        $user = User::query()->findOrFail($userId);

        if ($user->hasRole('business-development-manager')) {
            if ($isProspectDuplicate && $company->businessDevelopmentManager) {
                self::logAssignmentStatus($userId, $company, self::STATUS_RECENT_BUSINESS_DEVELOPMENT_MANAGER_RELATED_ACTIVITY);
            } else {
                $company->assign($user)->asBusinessDevelopmentManager(false, $reason);
            }
        }

        if ($user->hasRole('sales-development-representative')) {
            if ($isProspectDuplicate && ($company->businessDevelopmentManager || $company->salesDevelopmentRepresentative)) {
                self::logAssignmentStatus($userId, $company, self::STATUS_RECENT_BUSINESS_DEVELOPMENT_MANAGER_RELATED_ACTIVITY);
            } else {
                $company->assign($user)->asSalesDevelopmentRepresentative();
            }
        }
    }

    private static function verifyIfCanBeAssigned(?Company $company): string
    {
        if (! $company) {
            return self::STATUS_UNKNOWN_ISSUE;
        }

        if ($company->accountManager) {
            return self::STATUS_ACCOUNT_MANAGER_PRESENT;
        }

        if (self::companyIsActiveBuyer($company)) {
            return self::STATUS_ACTIVE_BUYER;
        }

        if (self::companyHasRecentBusinessDevelopmentManagerRelatedActivity($company)) {
            return self::STATUS_RECENT_BUSINESS_DEVELOPMENT_MANAGER_RELATED_ACTIVITY;
        }

        return self::STATUS_GOOD_TO_ASSIGN;
    }

    private static function companyIsActiveBuyer(Company $company): bool
    {
        return ProductAssignment::query()->where(ProductAssignment::FIELD_COMPANY_ID, $company->id)
            ->where(ProductAssignment::FIELD_DELIVERED, true)
            ->where(ProductAssignment::FIELD_CHARGEABLE, true)
            ->whereDate(ProductAssignment::FIELD_DELIVERED_AT, '>=', now()->subDays(self::LEAD_BUYER_RECENCY_THRESHOLD_DAYS))
            ->exists();
    }

    private static function companyHasRecentBusinessDevelopmentManagerRelatedActivity(Company $company): bool
    {
        return $company->businessDevelopmentManager &&
               ActivityFeed::query()->where(ActivityFeed::FIELD_COMPANY_ID, $company->id)
                   ->whereDate(ActivityFeed::CREATED_AT, '>=', now()->subDays(self::BDM_ACTIVITY_RECENCY_THRESHOLD_DAYS))
                   ->exists();
    }

    /**
     * @throws BindingResolutionException
     */
    private static function logAssignmentStatus(int $userId, Company $company, string $canAssignStatus): void
    {
        /** @var ActionRepository $repository */
        $repository = app()->make(ActionRepository::class);

        [$subject, $message] = match ($canAssignStatus) {
            self::STATUS_GOOD_TO_ASSIGN => ['BDM Assigned', 'BDM successfully assigned via BDM dashboard'],
            self::STATUS_ACTIVE_BUYER => ['BDM Not Assigned', 'BDM was not automatically assigned because the company is actively purchasing leads'],
            self::STATUS_RECENT_BUSINESS_DEVELOPMENT_MANAGER_RELATED_ACTIVITY => ['BDM Not Assigned', 'BDM was not automatically assigned because their is an active BDM'],
            self::STATUS_ACCOUNT_MANAGER_PRESENT => ['BDM Not Assigned', 'BDM was not automatically assigned because their is an active Account Manager'],
            self::STATUS_UNKNOWN_ISSUE => ['BDM Not Assigned', 'Unknown issue'],
        };

        $repository->createAction($userId, $subject, $message, $company->id, Action::RELATION_TYPE_COMPANY, null, self::ACTION_CATEGORY_OTHER);
    }
}
