<?php


namespace App\Services\CompanyRegistration;

use App\Contracts\Services\CompanyRegistration\CompanyRegistrationServiceContract;
use App\Enums\ContractType;
use App\Enums\EventName;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\Odin\SolarConfigurableFields;
use App\Builders\Odin\CompanyBuilder;
use App\Http\Resources\Bundles\BundleCollection;
use App\Models\CompanyContract;
use App\Models\ContractKey;
use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentCompanyContact;
use App\Models\Odin\Address;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Website;
use App\Repositories\BundleManagement\BundleRepository;
use App\Repositories\ContractsRepository;
use App\Repositories\Odin\AddressRepository;
use App\Repositories\Odin\CompanyIndustryServiceRepository;
use App\Repositories\Odin\CompanyLocationRepository;
use App\Repositories\Odin\CompanyRepository;
use App\Repositories\Odin\IndustryCompanyRepository;
use App\Repositories\Odin\IndustryRepository;
use App\Services\CompanyContractService;
use App\Services\CompanyUserService;
use App\Services\ContractService;
use App\Services\Docusign\DocuSignService;
use Illuminate\Http\Client\Response;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Hash;
use App\Services\Legacy\APIConsumer;
use Illuminate\Contracts\Container\BindingResolutionException;
use App\Repositories\Legacy\CompanyRepository as LegacyCompanyRepository;
use Exception;
use Ramsey\Uuid\Uuid;

class CompanyRegistrationService implements CompanyRegistrationServiceContract
{
    const string REQUEST_STRIPE_TOKEN = 'stripe_token';
    const string REQUEST_EMAIL = 'email';
    const string REQUEST_USER_OBJECT = 'user';
    const string REQUEST_LEGACY_ID = 'legacy_id';
    const string REQUEST_USER_LEGACY_ID = 'user_legacy_id';
    const string REQUEST_REFERENCE = 'reference';
    const string REQUEST_COMPANY_OBJECT = 'company';
    const string REQUEST_COMPANY_ID = 'company_id';
    const string REQUEST_DAILY_LEAD_BUDGET = 'daily_lead_budget';
    const string REQUEST_CONTRACT_TYPE = 'contract_type';
    const string REQUEST_COMPANY_NAME = 'company_name';
    const string REQUEST_COMPANY_USER_NAME = 'company_user_name';
    const string REQUEST_COMPANY_USER_ROLE = 'company_user_role';
    const string REQUEST_COMPANY_ADDRESS = 'company_address';
    const string REQUEST_LEAD_CONTACT = 'lead_contact';
    const string REQUEST_LOCATION_OBJECT = 'location';
    const string REQUEST_SERVICE_AREA = 'radius';

    const string RESPONSE_STATUS_KEY = 'status';

    const string INTEGRATION_ROUTE_PAYMENT_METHOD_ADDED = '/company-registration/payment-method-added';
    const string INTEGRATION_ROUTE_GET_COMPANY_CONTRACT = '/company-registration/get-company-contract';

    /**
     * @param CompanyRepository $companyRepository
     * @param CompanyIndustryServiceRepository $serviceRepository
     * @param AddressRepository $addressRepository
     * @param CompanyLocationRepository $companyLocationRepository
     * @param CompanyRegistrationSyncService $companyRegistrationSyncService
     * @param APIConsumer $apiConsumer
     * @param LegacyCompanyRepository $legacyCompanyRepository
     * @param CompanyContractService $companyContractService
     * @param CompanyUserService $companyUserService
     * @param BundleRepository $bundleRepository
     * @param IndustryCompanyRepository $industryCompanyRepository
     */
    public function __construct(
        protected CompanyRepository                 $companyRepository,
        protected CompanyIndustryServiceRepository  $serviceRepository,
        protected AddressRepository                 $addressRepository,
        protected CompanyLocationRepository         $companyLocationRepository,
        protected CompanyRegistrationSyncService    $companyRegistrationSyncService,
        protected APIConsumer                       $apiConsumer,
        protected LegacyCompanyRepository           $legacyCompanyRepository,
        protected CompanyContractService            $companyContractService,
        protected CompanyUserService                $companyUserService,
        protected BundleRepository                  $bundleRepository,
        protected IndustryCompanyRepository         $industryCompanyRepository,
        protected ContractsRepository               $contractRepository,
        protected DocuSignService                   $docuSignService,
    ) {}

    /**
     * @inheritDoc
     */
    public function registerBaseCompany(string $name, string $entityName, ?string $watchDogId = null, ?string $website = null, ?string $registrationDomain = null): Company
    {
        /** @var Company $company */
        $company = Company::query()->create([
            Company::FIELD_NAME => $name,
            Company::FIELD_ENTITY_NAME => $entityName,
            Company::FIELD_ADMIN_LOCKED => true,
            Company::FIELD_STATUS => Company::STATUS_REGISTERING,
            Company::FIELD_WATCHDOG_ID => $watchDogId,
            Company::FIELD_WEBSITE => $website ?? "",
            Company::FIELD_REGISTRATION_DOMAIN => $registrationDomain
        ]);
        return $company;
    }

    /**
     * @inheritDoc
     */
    public function addCompanyUser(Company $company, string $firstName, string $lastName, string $email, string|null $password, string|null $department): CompanyUser
    {
        /** @var CompanyUser $companyUser */
        $companyUser = $company->users()->create([
            CompanyUser::FIELD_EMAIL => $email,
            CompanyUser::FIELD_FIRST_NAME => $firstName,
            CompanyUser::FIELD_LAST_NAME => $lastName,
            CompanyUser::FIELD_STATUS => CompanyUser::STATUS_ACTIVE,
            CompanyUser::FIELD_IS_CONTACT => false,
            CompanyUser::FIELD_CAN_LOG_IN => true,
            CompanyUser::FIELD_AUTHENTICATION_TYPE  => CompanyUser::AUTHENTICATION_TYPE_ADMIN2,
            CompanyUser::FIELD_DEPARTMENT           => $department,
        ]);

        $this->companyUserService->updatePassword($companyUser, $password);

        return $companyUser;
    }

    /**
     * @inheritDoc
     */
    public function getCompanyServiceNamesByIndustry(Company $company): Collection
    {
        $companyServices = $company->{Company::RELATION_SERVICES};

        $allServices = IndustryService::query()
            ->where(IndustryService::FIELD_SHOW_ON_REGISTRATION, true)
            ->get();

        $groupByIndustry = function (Collection $services): Collection {
            return $services
                ->groupBy(IndustryService::RELATION_INDUSTRY . '.' . Industry::FIELD_NAME)
                ->map(fn($industryGroup) => $industryGroup->map(fn(IndustryService $service) => [
                    'name' => $service->{IndustryService::FIELD_NAME},
                    'slug' => $service->{IndustryService::FIELD_SLUG},
                ]
                ));
        };

        return collect([
            'allServices' => $groupByIndustry($allServices),
            'activeServices' => $groupByIndustry($companyServices),
        ]);
    }

    /**
     * @inheritDoc
     */
    public function refreshCompanyServicesBySlugs(Company $company, array $slugs): int
    {
        $newServices = $this->serviceRepository->getIndustryServicesBySlug($slugs);
        $newServiceIds = $newServices->pluck(IndustryService::FIELD_ID);

        $company->{Company::RELATION_SERVICES}->each(function (IndustryService $service) use (&$company, &$newServiceIds) {
            if (!$newServiceIds->contains($service->{IndustryService::FIELD_ID})) {
                $this->serviceRepository->removeCompanyService($company, $service);
            }
        });

        /** @var Collection|null $result */
        $result = $this->serviceRepository->setupServicesForCompany(
            company: $company,
            industryServiceIds: $newServiceIds->toArray()
        );

        return is_null($result) ? 0 : $result->filter()?->count();
    }

    /**
     * @inheritDoc
     */
    public function refreshCompanyIndustriesByNames(Company $company, array $industryNames): int
    {
        $industryRepository = app()->make(IndustryRepository::class);
        $industryCompanyRepository = app()->make(IndustryCompanyRepository::class);

        /** @var Collection<Industry> $requestedIndustries */
        $requestedIndustries = $industryRepository->getIndustriesByNames($industryNames);
        $company->{Company::RELATION_INDUSTRIES}()->detach();

        $this->industryCompanyRepository->setCompanyTypeInLegacy(
            company: $company,
            industryIds: $requestedIndustries->pluck(Industry::FIELD_ID)->toArray()
        );

        return $requestedIndustries->reduce(fn(int $output, Industry $industry) => $industryCompanyRepository->addIndustryCompany($industry->{Industry::FIELD_ID}, $company->{Company::FIELD_ID})
            ? $output + 1
            : $output
            , 0);
    }

    /**
     * @param Company $company
     * @return array
     * @throws BindingResolutionException
     */
    public function doCompanyRegistrationChecks(Company $company): array
    {
        $apiConsumer = app()->make(APIConsumer::class);
        $paymentStatusApiEndpoint = '/repositories/companies/companies-payment-method-statuses';

        $firstUserEmail = $company->{Company::RELATION_USERS}
            ->where(CompanyUser::FIELD_CAN_LOG_IN, CompanyUser::USER_ALLOWED_TO_LOGIN)
            ->where(CompanyUser::FIELD_STATUS, 1)
            ->first()
            ?->{CompanyUser::FIELD_EMAIL};

        $userHint = $firstUserEmail
            ? substr($firstUserEmail, 0, 3) . "XXXXXX" . substr($firstUserEmail, -5)
            : null;

        $hasActivePaymentMethod = $apiConsumer->post($paymentStatusApiEndpoint, [$company->{Company::FIELD_ID}])[0] ?? false;
        $allowLeadsWithoutPayment = $company->{Company::RELATION_DATA}[SolarConfigurableFields::ALLOW_LEAD_SALES_WITHOUT_CC->value] ?? false;

        $domain = getWebsiteDomain($company->{Company::FIELD_WEBSITE});

        return [
            'canPurchaseLeads' => $hasActivePaymentMethod || $allowLeadsWithoutPayment,
            'hasActiveUser' => !!$firstUserEmail,
            'userHint' => $userHint,
            'domain' => $domain
        ];

    }

    /**
     * Get clean domain from URL
     * @param string $url
     * @return string
     */
    public function getDomain(string $url): string
    {
        $url = trim($url);

        if (!preg_match("/^https?:\/\//", $url)) {
            $url = 'http://' . $url;
        }
        $urlParts = parse_url($url);

        return preg_replace('/^www\./', '', $urlParts['host']);
    }

    /**
     * @param string $companyName
     * @param int $limit
     * @return Collection
     */
    public function checkSimilarCompanies(string $companyName, mixed $companyWebsite = null, int $limit = 20): Collection
    {
        $results = CompanyBuilder::query()
            ->forCompanyName($companyName)
            ->orForWebsite($companyWebsite)
            ->getQuery()
            ->limit($limit)
            ->get();
        return $results->map(fn(Company $company) => [
            'id' => $company->{Company::FIELD_ID},
            'name' => $company->{Company::FIELD_NAME},
            'website' => $company->{Company::FIELD_WEBSITE},
            'city' => $company->{Company::RELATION_LOCATIONS}?->first()?->{CompanyLocation::RELATION_ADDRESS}?->{Address::FIELD_CITY} ?? '',
            'state' => $company->{Company::RELATION_LOCATIONS}?->first()?->{CompanyLocation::RELATION_ADDRESS}?->{Address::FIELD_STATE} ?? '',
        ]);
    }

    /**
     * @inheritDoc
     */
    public function addLocationToCompany(Company $company, array $addressData): ?CompanyLocation
    {
        $newAddress = $this->addressRepository->createAddressFromAttributes($addressData);
        if ($newAddress->{Address::FIELD_ID}) {
            $officeName = $addressData[CompanyLocation::FIELD_NAME] ?? "{$newAddress->{Address::FIELD_CITY}} Office";

            return $this->companyLocationRepository->updateOrCreateCompanyLocation([
                CompanyLocation::FIELD_COMPANY_ID => $company->{Company::FIELD_ID},
                CompanyLocation::FIELD_ADDRESS_ID => $newAddress->{Address::FIELD_ID},
                CompanyLocation::FIELD_PHONE => $addressData[CompanyLocation::FIELD_PHONE],
                CompanyLocation::FIELD_NAME => $officeName,
            ]);
        }
        return null;
    }

    /**
     * @inheritDoc
     */
    public function addServiceAreaToCompany(Company $company, CompanyLocation $companyLocation, ?int $serviceArea = null): bool
    {
        $transformedData = $this->companyRegistrationSyncService->transformForLegacy(Address::class, $companyLocation->address->toArray());
        $transformedData[EloquentAddress::PHONE] = $companyLocation->{CompanyLocation::FIELD_PHONE} ?? '5555555555'; // Phone cannot be null in legacy table
        $transformedData[self::REQUEST_SERVICE_AREA] = $serviceArea;

        return $this->companyRegistrationSyncService->syncChangesToLegacy(
            EventName::REGISTRATION_ADD_COMPANY_LOCATION,
            $company->{Company::FIELD_REFERENCE},
            [self::REQUEST_LOCATION_OBJECT => $transformedData]
        );
    }

    /**
     * @inheritDoc
     */
    public function addPaymentMethod(Company $company, array $requestData): Response
    {
        $companyData = [
            self::REQUEST_REFERENCE => $company->{Company::FIELD_REFERENCE},
            self::REQUEST_STRIPE_TOKEN => $requestData[self::REQUEST_STRIPE_TOKEN],
        ];
        $userData = [
            self::REQUEST_LEGACY_ID => $requestData[self::REQUEST_USER_LEGACY_ID],
            self::REQUEST_EMAIL => $requestData[self::REQUEST_EMAIL],
        ];
        //TODO: update this to the new Admin2 route
        return $this->apiConsumer->post(self::INTEGRATION_ROUTE_PAYMENT_METHOD_ADDED, [
            self::REQUEST_COMPANY_OBJECT => $companyData,
            self::REQUEST_USER_OBJECT => $userData,
        ]);
    }

    /**
     * @inheritDoc
     */
    public function updateCompanyDetails(Company $company, array $requestData): bool
    {
        $result = $this->companyRepository->updateCompanyModelAndPayloadById(
            $company->{Company::FIELD_ID},
            collect($requestData),
            [Company::FIELD_WEBSITE, Company::FIELD_LINK_TO_LOGO, Company::FIELD_STATUS],
            [GlobalConfigurableFields::SALES_EMAIL->value, GlobalConfigurableFields::YEAR_STARTED_BUSINESS->value, GlobalConfigurableFields::TECH_SUPPORT_EMAIL->value, GlobalConfigurableFields::DESCRIPTION->value]
        );
        if ($result) {
            $legacyData = $this->companyRegistrationSyncService->transformForLegacy(Company::class, $requestData);
            $this->companyRegistrationSyncService->syncChangesToLegacy(
                EventName::COMPANY_BASIC_DETAILS_UPDATED,
                $company->{Company::FIELD_REFERENCE},
                ['data' => $legacyData],
            );
            $this->companyRegistrationSyncService->syncChangesToLegacy(
                EventName::COMPANY_CONFIGURABLE_FIELDS_UPDATED,
                $company->{Company::FIELD_REFERENCE},
                ['data' => $legacyData],
            );
        }
        return $result;
    }

    /**
     * @inheritDoc
     */
    public function setLeadBudgetOfCreatedCompany(string $companyReference, int $budget): bool
    {
        $response = $this->legacyCompanyRepository->setLeadBudgetOfCreatedCompany($companyReference, [
            self::REQUEST_DAILY_LEAD_BUDGET => $budget
        ]);

        if (!$response || !key_exists('status', $response) || !$response['status']) return false;

        return true;
    }

    /**
     * @inheritDoc
     */
    public function setLeadContactOfCreatedCompany(Company $company, array $requestData): bool
    {
        /** @var CompanyUser $companyUser */
        $companyUser = CompanyUser::query()
            ->where(CompanyUser::FIELD_COMPANY_ID, $company->{Company::FIELD_ID})
            ->where(CompanyUser::FIELD_EMAIL, $requestData[CompanyUser::FIELD_EMAIL])
            ->where(CompanyUser::FIELD_IS_CONTACT, true)
            ->first();

        if (!$companyUser) {
            /** @var CompanyUser $companyUser */
            $companyUser = CompanyUser::query()->create([
                CompanyUser::FIELD_COMPANY_ID => $company->{Company::FIELD_ID},
                CompanyUser::FIELD_EMAIL => $requestData[CompanyUser::FIELD_EMAIL],
                CompanyUser::FIELD_FIRST_NAME => $requestData[CompanyUser::FIELD_FIRST_NAME],
                CompanyUser::FIELD_LAST_NAME => $requestData[CompanyUser::FIELD_LAST_NAME],
                CompanyUser::FIELD_NOTES => $requestData[CompanyUser::FIELD_NOTES],
                CompanyUser::FIELD_STATUS => CompanyUser::STATUS_ACTIVE,
                CompanyUser::FIELD_REFERENCE => Uuid::uuid4()->toString(),
                CompanyUser::FIELD_CAN_LOG_IN => CompanyUser::USER_NOT_ALLOWED_TO_LOGIN,
                CompanyUser::FIELD_IS_CONTACT => true
            ]);
        }

        $response = $this->legacyCompanyRepository->setLeadContactOfCreatedCompany(
            $company->{Company::FIELD_REFERENCE},
            [
                self::REQUEST_LEAD_CONTACT => [
                    EloquentCompanyContact::FIELD_FIRST_NAME => $companyUser->{CompanyUser::FIELD_FIRST_NAME},
                    EloquentCompanyContact::FIELD_LAST_NAME => $companyUser->{CompanyUser::FIELD_LAST_NAME},
                    EloquentCompanyContact::FIELD_EMAIL => $companyUser->{CompanyUser::FIELD_EMAIL},
                    EloquentCompanyContact::FIELD_PHONE => $companyUser->{CompanyUser::FIELD_CELL_PHONE} ?: $companyUser->{CompanyUser::FIELD_OFFICE_PHONE},
                    self::REQUEST_REFERENCE => $companyUser->{CompanyUser::FIELD_REFERENCE}
                ]
            ]
        );

        if (!$response || !key_exists('status', $response) || !$response['status']) return false;

        return true;
    }

    /**
     * @inheritDoc
     * @throws Exception
     */
    public function getCompanyContract(Company $company, CompanyUser $user, ContractKey $contractKey, Website $website, ?string $ip, ?string $appId): array
    {
        $contract = $this->contractRepository->getActiveContractForWebsiteAndKey($website->id, $contractKey->id);

        if(!$contract) throw new Exception('There is no active contract for website:'.$website->name.' and key:'.$contractKey->name);

        $embeddedSignUrl = $this->docuSignService->getEmbeddedSignUrl($user, $contract);

        return [
            'status'            => true,
            'contract_sign_url' => urlencode($embeddedSignUrl)
        ];
    }

    /**
     * @inheritDoc
     */
    public function acceptCompanyContract(Company $company, CompanyUser $user, CompanyContract $contract): bool
    {
        if ($this->companyContractService->agreeToContract($company, $user, $contract)) {
            $this->companyContractService->cleanUpRedundantContracts($contract);
            return true;
        } else {
            return false;
        }
    }

    /**
     * @param Company $company
     * @return void
     */
    public function markCompanyAsPending(Company $company): void
    {
        $company->update([Company::FIELD_STATUS => Company::STATUS_PENDING_APPROVAL]);
    }

}
