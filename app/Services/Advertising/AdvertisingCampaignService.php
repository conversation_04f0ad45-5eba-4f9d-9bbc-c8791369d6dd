<?php

namespace App\Services\Advertising;

use App\DataModels\Advertising\AccountCampaigns;
use App\DataModels\Advertising\Campaign;
use App\DataModels\Advertising\CampaignLocations;
use App\DataModels\Advertising\DashboardTargetLocation;
use App\Enums\Advertising\AdvertisingPlatform;
use App\Enums\Odin\Industry;
use App\Jobs\RecordMonitoringLog;
use App\Models\AdvertisingAccount;
use App\Models\AdvertisingCampaign;
use App\Models\AdvertisingCampaignAutomationParameter;
use App\Models\AdvertisingCampaignHistoryLog;
use App\Models\EstimatedRevenuePerLeadByLocation;
use App\Models\Legacy\Location;
use App\Models\LockedAdvertisingCampaignLocation;
use App\Repositories\AvailableCompanyByLocationRepository;
use App\Repositories\EstimatedRevenuePerLeadByLocationRepository;
use App\Services\Advertising\Campaigns\AdvertisingServiceFactory;
use App\Services\DatabaseHelperService;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Mail\Message;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Throwable;

class AdvertisingCampaignService
{
    const CAMPAIGN_AUTOMATION_PARAMETERS = 'automation_parameters';
    const CAMPAIGN_AUTOMATED = 'automated';
    const CAMPAIGN_LOCATIONS = 'locations';
    const CAMPAIGN_RUN_INTERVAL = 'run_interval';

    /**
     * @param EstimatedRevenuePerLeadByLocationRepository $estimatedRevenuePerLeadByLocationRepository
     * @param AvailableCompanyByLocationRepository $availableCompanyByLocationRepository
     */
    public function __construct(
        private readonly EstimatedRevenuePerLeadByLocationRepository $estimatedRevenuePerLeadByLocationRepository,
        private readonly AvailableCompanyByLocationRepository $availableCompanyByLocationRepository
    )
    {

    }

    /**
     * @return Collection
     */
    public function all(): Collection
    {
        return AdvertisingCampaign::all()->keyBy(AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID);
    }

    /**
     * @param string $platform
     * @param string $platformCampaignId
     * @return AdvertisingCampaign|Model
     */
    public function getByPlatformCampaignId(string $platform, string $platformCampaignId): AdvertisingCampaign|Model
    {
        return AdvertisingCampaign::query()
                ->where(AdvertisingCampaign::FIELD_PLATFORM, $platform)
                ->where(AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID, $platformCampaignId)
                ->first();
    }

    /**
     * @param string $platform
     * @return Collection
     */
    public function getByPlatform(string $platform): Collection
    {
        return AdvertisingCampaign::query()
                ->where(AdvertisingCampaign::FIELD_PLATFORM, $platform)
                ->get();
    }

    /**
     * @param Collection $platformCampaigns
     * @param string $platform
     * @param $accountId
     * @return Collection
     */
    public function attachAutomationStatusToPlatformCampaigns(Collection &$platformCampaigns, string $platform, $accountId): Collection
    {
        $advertisingCampaigns = AdvertisingCampaign::query()
                                    ->where(AdvertisingCampaign::FIELD_PLATFORM, $platform)
                                    ->where(AdvertisingCampaign::FIELD_PLATFORM_ACCOUNT_ID, $accountId)
                                    ->get()
                                    ->keyBy(AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID)
                                    ->toArray();

        foreach($platformCampaigns as $platformCampaignId => $campaign) {
            $campaign[AdvertisingCampaign::FIELD_PLATFORM] = $platform;
            $campaign[AdvertisingCampaign::FIELD_PLATFORM_ACCOUNT_ID] = $accountId;
            $campaign[AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID] = $platformCampaignId;
            $campaign[self::CAMPAIGN_AUTOMATED] = !empty($advertisingCampaigns[$platformCampaignId]);
            $campaign[AdvertisingCampaign::FIELD_RUN_INTERVAL_SECS] = $campaign[self::CAMPAIGN_AUTOMATED] ? $advertisingCampaigns[$platformCampaignId][AdvertisingCampaign::FIELD_RUN_INTERVAL_SECS] : 0;
            $campaign[AdvertisingCampaign::FIELD_RUN_INTERVAL_DISPLAY_UNIT] = $campaign[self::CAMPAIGN_AUTOMATED] ? $advertisingCampaigns[$platformCampaignId][AdvertisingCampaign::FIELD_RUN_INTERVAL_DISPLAY_UNIT] : AdvertisingCampaign::RUN_INTERVAL_HOURS;

            $platformCampaigns->put($platformCampaignId, $campaign);
        }

        return $platformCampaigns;
    }

    /**
     * @param Collection $campaigns
     * @param string $platform
     * @param $accountId
     * @return bool
     */
    public function saveCampaignAutomationStatuses(Collection $campaigns, string $platform, $accountId): bool
    {
        DB::transaction(function() use ($campaigns, $platform, $accountId) {
            foreach($campaigns as $platformCampaignId => $campaign) {
                $advertisingCampaign = AdvertisingCampaign::query()->firstOrNew([
                    AdvertisingCampaign::FIELD_PLATFORM => $platform,
                    AdvertisingCampaign::FIELD_PLATFORM_ACCOUNT_ID => $accountId,
                    AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID => $platformCampaignId
                ]);

                $currentlyAutomated = !empty($advertisingCampaign->{AdvertisingCampaign::FIELD_ID});

                if($campaign[self::CAMPAIGN_AUTOMATED]
                && !empty($campaign[AdvertisingCampaign::FIELD_RUN_INTERVAL_SECS])
                && in_array($campaign[AdvertisingCampaign::FIELD_RUN_INTERVAL_DISPLAY_UNIT], [AdvertisingCampaign::RUN_INTERVAL_HOURS, AdvertisingCampaign::RUN_INTERVAL_WEEKS, AdvertisingCampaign::RUN_INTERVAL_MINUTES], true)
                && !empty($campaign[self::CAMPAIGN_AUTOMATION_PARAMETERS])) {
                    $priorRunInterval = $advertisingCampaign->{AdvertisingCampaign::FIELD_RUN_INTERVAL_SECS};
                    $priorRunIntervalUnit = $advertisingCampaign->{AdvertisingCampaign::FIELD_RUN_INTERVAL_DISPLAY_UNIT};

                    $advertisingCampaign->{AdvertisingCampaign::FIELD_RUN_INTERVAL_SECS} = (int) $campaign[AdvertisingCampaign::FIELD_RUN_INTERVAL_SECS];
                    $advertisingCampaign->{AdvertisingCampaign::FIELD_RUN_INTERVAL_DISPLAY_UNIT} = $campaign[AdvertisingCampaign::FIELD_RUN_INTERVAL_DISPLAY_UNIT];

                    $advertisingCampaign->save();

                    $currentRunInterval = $advertisingCampaign->{AdvertisingCampaign::FIELD_RUN_INTERVAL_SECS};
                    $currentRunIntervalUnit = $advertisingCampaign->{AdvertisingCampaign::FIELD_RUN_INTERVAL_DISPLAY_UNIT};

                    if(!$currentlyAutomated
                    || $priorRunInterval !== $currentRunInterval
                    || $priorRunIntervalUnit !== $currentRunIntervalUnit) {
                        $verb = $currentlyAutomated ? "updated" : "activated";

                        AdvertisingLoggingService::createLogEntry(
                            $platform,
                            $accountId,
                            $platformCampaignId,
                            "Automation {$verb}. Interval: {$currentRunInterval} secs. Unit: $currentRunIntervalUnit",
                            $currentlyAutomated ? AdvertisingCampaignHistoryLog::TYPE_AUTOMATION_UPDATED : AdvertisingCampaignHistoryLog::TYPE_AUTOMATION_ACTIVATED
                        );
                    }
                }
                else {
                    $advertisingCampaign->delete();

                    if($currentlyAutomated) {
                        AdvertisingLoggingService::createLogEntry($platform, $accountId, $platformCampaignId, "Automation deactivated", AdvertisingCampaignHistoryLog::TYPE_AUTOMATION_DEACTIVATED);
                    }
                }
            }
        });

        return true;
    }

    /**
     * @param Collection $platformCampaigns
     * @param string $platform
     * @param $accountId
     * @return Collection
     */
    public function attachAutomationParametersToPlatformCampaigns(Collection &$platformCampaigns, string $platform, $accountId): Collection
    {
        $campaignAutomationParams = AdvertisingCampaignAutomationParameter::query()
                                        ->where(AdvertisingCampaignAutomationParameter::FIELD_PLATFORM, $platform)
                                        ->where(AdvertisingCampaignAutomationParameter::FIELD_PLATFORM_ACCOUNT_ID, $accountId)
                                        ->get()
                                        ->groupBy(AdvertisingCampaignAutomationParameter::FIELD_PLATFORM_CAMPAIGN_ID)
                                        ->toArray();

        foreach($platformCampaigns as $platformCampaignId => $campaign) {
            $campaign[self::CAMPAIGN_AUTOMATION_PARAMETERS] = $campaignAutomationParams[$platformCampaignId] ?? [];

            $platformCampaigns->put($platformCampaignId, $campaign);
        }

        return $platformCampaigns;
    }

    /**
     * @param Collection $campaigns
     * @param string $platform
     * @param $accountId
     * @return bool
     */
    public function saveAutomationParameters(Collection $campaigns, string $platform, $accountId): bool
    {
        $existingCampaignIds = AdvertisingCampaign::query()
                                    ->where(AdvertisingCampaign::FIELD_PLATFORM, $platform)
                                    ->where(AdvertisingCampaign::FIELD_PLATFORM_ACCOUNT_ID, $accountId)
                                    ->pluck(AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID)
                                    ->toArray();

        DB::transaction(function() use ($campaigns, $platform, $accountId, $existingCampaignIds) {
            foreach($campaigns as $platformCampaignId => $campaign) {
                $hasExistingParameters = AdvertisingCampaignAutomationParameter::query()
                                            ->where(AdvertisingCampaignAutomationParameter::FIELD_PLATFORM, $platform)
                                            ->where(AdvertisingCampaignAutomationParameter::FIELD_PLATFORM_ACCOUNT_ID, $accountId)
                                            ->where(AdvertisingCampaignAutomationParameter::FIELD_PLATFORM_CAMPAIGN_ID, $platformCampaignId)
                                            ->exists();

                if(in_array($platformCampaignId, $existingCampaignIds)) {
                    $updatedParameterIds = [];

                    foreach($campaign[self::CAMPAIGN_AUTOMATION_PARAMETERS] as $automationParam) {
                        $adParam = AdvertisingCampaignAutomationParameter::query()->firstOrNew([
                            AdvertisingCampaignAutomationParameter::FIELD_PLATFORM => $platform,
                            AdvertisingCampaignAutomationParameter::FIELD_PLATFORM_ACCOUNT_ID => $accountId,
                            AdvertisingCampaignAutomationParameter::FIELD_PLATFORM_CAMPAIGN_ID => $platformCampaignId,
                            AdvertisingCampaignAutomationParameter::FIELD_TYPE => $automationParam[AdvertisingCampaignAutomationParameter::FIELD_TYPE],
                            AdvertisingCampaignAutomationParameter::FIELD_STRATEGY => $automationParam[AdvertisingCampaignAutomationParameter::FIELD_STRATEGY],
                            AdvertisingCampaignAutomationParameter::FIELD_PARAMETER => $automationParam[AdvertisingCampaignAutomationParameter::FIELD_PARAMETER],
                            AdvertisingCampaignAutomationParameter::FIELD_OPERATOR => $automationParam[AdvertisingCampaignAutomationParameter::FIELD_OPERATOR],
                            AdvertisingCampaignAutomationParameter::FIELD_THRESHOLD => $automationParam[AdvertisingCampaignAutomationParameter::FIELD_THRESHOLD],
                            AdvertisingCampaignAutomationParameter::FIELD_THRESHOLD_TYPE => $automationParam[AdvertisingCampaignAutomationParameter::FIELD_THRESHOLD_TYPE]
                        ]);

                        $new = empty($adParam->{AdvertisingCampaignAutomationParameter::FIELD_ID});

                        $adParam->save();

                        $updatedParameterIds[] = $adParam->{AdvertisingCampaignAutomationParameter::FIELD_ID};

                        if($new) {
                            $createdParamInfo = sprintf(
                                "%s %s %s %s %s %s",
                                $adParam->{AdvertisingCampaignAutomationParameter::FIELD_TYPE},
                                $adParam->{AdvertisingCampaignAutomationParameter::FIELD_STRATEGY},
                                $adParam->{AdvertisingCampaignAutomationParameter::FIELD_PARAMETER},
                                $adParam->{AdvertisingCampaignAutomationParameter::FIELD_OPERATOR},
                                $adParam->{AdvertisingCampaignAutomationParameter::FIELD_THRESHOLD},
                                $adParam->{AdvertisingCampaignAutomationParameter::FIELD_THRESHOLD_TYPE}
                            );

                            AdvertisingLoggingService::createLogEntry($platform, $accountId, $platformCampaignId, "Automation parameter created: $createdParamInfo", AdvertisingCampaignHistoryLog::TYPE_INFO);
                        }
                    }

                    $deleteParameters = AdvertisingCampaignAutomationParameter::query()
                                            ->where(AdvertisingCampaignAutomationParameter::FIELD_PLATFORM, $platform)
                                            ->where(AdvertisingCampaignAutomationParameter::FIELD_PLATFORM_ACCOUNT_ID, $accountId)
                                            ->where(AdvertisingCampaignAutomationParameter::FIELD_PLATFORM_CAMPAIGN_ID, $platformCampaignId)
                                            ->whereNotIn(AdvertisingCampaignAutomationParameter::FIELD_ID, $updatedParameterIds)
                                            ->get();

                    AdvertisingCampaignAutomationParameter::query()
                        ->where(AdvertisingCampaignAutomationParameter::FIELD_PLATFORM, $platform)
                        ->where(AdvertisingCampaignAutomationParameter::FIELD_PLATFORM_ACCOUNT_ID, $accountId)
                        ->where(AdvertisingCampaignAutomationParameter::FIELD_PLATFORM_CAMPAIGN_ID, $platformCampaignId)
                        ->whereNotIn(AdvertisingCampaignAutomationParameter::FIELD_ID, $updatedParameterIds)
                        ->delete();

                    foreach($deleteParameters as $deletedParameter) {
                        $deletedParamInfo = sprintf(
                            "%s %s %s %s %s %s",
                            $deletedParameter->{AdvertisingCampaignAutomationParameter::FIELD_TYPE},
                            $deletedParameter->{AdvertisingCampaignAutomationParameter::FIELD_STRATEGY},
                            $deletedParameter->{AdvertisingCampaignAutomationParameter::FIELD_PARAMETER},
                            $deletedParameter->{AdvertisingCampaignAutomationParameter::FIELD_OPERATOR},
                            $deletedParameter->{AdvertisingCampaignAutomationParameter::FIELD_THRESHOLD},
                            $deletedParameter->{AdvertisingCampaignAutomationParameter::FIELD_THRESHOLD_TYPE}
                        );

                        AdvertisingLoggingService::createLogEntry($platform, $accountId, $platformCampaignId, "Automation parameter deleted: $deletedParamInfo", AdvertisingCampaignHistoryLog::TYPE_INFO);
                    }
                }
                else if($hasExistingParameters) {
                    AdvertisingCampaignAutomationParameter::query()
                        ->where(AdvertisingCampaignAutomationParameter::FIELD_PLATFORM, $platform)
                        ->where(AdvertisingCampaignAutomationParameter::FIELD_PLATFORM_ACCOUNT_ID, $accountId)
                        ->where(AdvertisingCampaignAutomationParameter::FIELD_PLATFORM_CAMPAIGN_ID, $platformCampaignId)
                        ->delete();

                    AdvertisingLoggingService::createLogEntry($platform, $accountId, $platformCampaignId, "All automation parameters deleted", AdvertisingCampaignHistoryLog::TYPE_INFO);
                }
            }
        });

        return true;
    }

    /**
     * @param Collection $campaigns
     * @param string $platform
     * @param $accountId
     * @return bool
     */
    public function saveLockedCampaignLocations(Collection $campaigns, string $platform, $accountId): bool
    {
        $existingCampaignIds = AdvertisingCampaign::query()
            ->where(AdvertisingCampaign::FIELD_PLATFORM, $platform)
            ->where(AdvertisingCampaign::FIELD_PLATFORM_ACCOUNT_ID, $accountId)
            ->whereIn(AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID, $campaigns->keys()->toArray())
            ->pluck(AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID)
            ->toArray();

        DB::transaction(function() use ($campaigns, $platform, $accountId, $existingCampaignIds) {
            LockedAdvertisingCampaignLocation::query()
                ->where(LockedAdvertisingCampaignLocation::FIELD_PLATFORM, $platform)
                ->where(LockedAdvertisingCampaignLocation::FIELD_PLATFORM_ACCOUNT_ID, $accountId)
                ->whereIn(LockedAdvertisingCampaignLocation::FIELD_PLATFORM_CAMPAIGN_ID, $campaigns->keys()->toArray())
                ->delete();

            foreach($existingCampaignIds as $campaignId) {
                $insertRows = [];
                $campaign = $campaigns[$campaignId];

                foreach($campaign[self::CAMPAIGN_LOCATIONS] as $listType => $campaignLocations) {
                    foreach($campaignLocations as $campaignLocation) {
                        $hasLocked = in_array(
                            $campaignLocation[DashboardTargetLocation::LOCKED],
                            [LockedAdvertisingCampaignLocation::TARGETED_INCLUDE, LockedAdvertisingCampaignLocation::TARGETED_EXCLUDE],
                            true
                        );

                        if($hasLocked
                            && $campaignLocation[DashboardTargetLocation::LOCATION_ID] > 0) {
                            $insertRows[] = [
                                LockedAdvertisingCampaignLocation::FIELD_PLATFORM => $platform,
                                LockedAdvertisingCampaignLocation::FIELD_PLATFORM_ACCOUNT_ID => $accountId,
                                LockedAdvertisingCampaignLocation::FIELD_PLATFORM_CAMPAIGN_ID => $campaignId,
                                LockedAdvertisingCampaignLocation::FIELD_LOCATION_ID => $campaignLocation[DashboardTargetLocation::LOCATION_ID],
                                LockedAdvertisingCampaignLocation::FIELD_TARGETED => $campaignLocation[DashboardTargetLocation::LOCKED]
                            ];

                            if(count($insertRows) > 500) {
                                LockedAdvertisingCampaignLocation::query()->insert($insertRows);

                                $insertRows = [];
                            }
                        }
                    }
                }

                if(!empty($insertRows)) {
                    LockedAdvertisingCampaignLocation::query()->insert($insertRows);
                }
            }
        });

        return true;
    }

    /**
     * @param string $platform
     * @param string $accountId
     * @return bool
     */
    public function deleteAccountAutomationParameters(string $platform, string $accountId): bool
    {
        AdvertisingCampaignAutomationParameter::query()
            ->where(AdvertisingCampaignAutomationParameter::FIELD_PLATFORM, $platform)
            ->where(AdvertisingCampaignAutomationParameter::FIELD_PLATFORM_ACCOUNT_ID, $accountId)
            ->delete();

        return true;
    }

    /**
     * @param string $platform
     * @param string $accountId
     * @return bool
     */
    public function deleteAccountAutomationStatus(string $platform, string $accountId): bool
    {
        AdvertisingCampaign::query()
            ->where(AdvertisingCampaign::FIELD_PLATFORM, $platform)
            ->where(AdvertisingCampaign::FIELD_PLATFORM_ACCOUNT_ID, $accountId)
            ->delete();

        return true;
    }

    /**
     * @param string $platform
     * @param array $retryCampaigns
     * @return AccountCampaigns|null
     * @throws Exception
     */
    public function getAutomatedCampaignsWithParametersByPlatform(string $platform, array $retryCampaigns = []): ?AccountCampaigns
    {
        if(!empty($retryCampaigns)) {
            $accountCampaignsParameters = AdvertisingCampaign::query()
                ->leftJoin(AdvertisingCampaignAutomationParameter::TABLE, function($join) {
                    $join
                        ->on(AdvertisingCampaign::TABLE.'.'.AdvertisingCampaign::FIELD_PLATFORM, '=', AdvertisingCampaignAutomationParameter::TABLE.'.'.AdvertisingCampaignAutomationParameter::FIELD_PLATFORM)
                        ->on(AdvertisingCampaign::TABLE.'.'.AdvertisingCampaign::FIELD_PLATFORM_ACCOUNT_ID, '=', AdvertisingCampaignAutomationParameter::TABLE.'.'.AdvertisingCampaignAutomationParameter::FIELD_PLATFORM_ACCOUNT_ID)
                        ->on(AdvertisingCampaign::TABLE.'.'.AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID, '=', AdvertisingCampaignAutomationParameter::TABLE.'.'.AdvertisingCampaignAutomationParameter::FIELD_PLATFORM_CAMPAIGN_ID);
                })
                ->where(AdvertisingCampaign::TABLE.'.'.AdvertisingCampaign::FIELD_PLATFORM, $platform)
                ->whereIn(AdvertisingCampaign::TABLE.'.'.AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID, collect($retryCampaigns)->flatten()->toArray())
                ->whereIn(AdvertisingCampaign::TABLE.'.'.AdvertisingCampaign::FIELD_PLATFORM_ACCOUNT_ID, array_keys($retryCampaigns))
                ->get()
                ->groupBy(AdvertisingCampaign::FIELD_PLATFORM_ACCOUNT_ID);
        }
        else {
            $accountCampaignsParameters = AdvertisingCampaign::query()
                ->leftJoin(AdvertisingCampaignAutomationParameter::TABLE, function($join) {
                    $join
                        ->on(AdvertisingCampaign::TABLE.'.'.AdvertisingCampaign::FIELD_PLATFORM, '=', AdvertisingCampaignAutomationParameter::TABLE.'.'.AdvertisingCampaignAutomationParameter::FIELD_PLATFORM)
                        ->on(AdvertisingCampaign::TABLE.'.'.AdvertisingCampaign::FIELD_PLATFORM_ACCOUNT_ID, '=', AdvertisingCampaignAutomationParameter::TABLE.'.'.AdvertisingCampaignAutomationParameter::FIELD_PLATFORM_ACCOUNT_ID)
                        ->on(AdvertisingCampaign::TABLE.'.'.AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID, '=', AdvertisingCampaignAutomationParameter::TABLE.'.'.AdvertisingCampaignAutomationParameter::FIELD_PLATFORM_CAMPAIGN_ID);
                })
                ->where(AdvertisingCampaign::TABLE.'.'.AdvertisingCampaign::FIELD_PLATFORM, $platform)
                ->whereRaw(sprintf(
                    "%s >= %s + %s",
                    "UNIX_TIMESTAMP()",
                    AdvertisingCampaign::FIELD_LAST_RUN_TIMESTAMP,
                    AdvertisingCampaign::FIELD_RUN_INTERVAL_SECS
                ))
                ->get()
                ->groupBy(AdvertisingCampaign::FIELD_PLATFORM_ACCOUNT_ID);
        }

        if($accountCampaignsParameters->isEmpty()) {
            return null;
        }

        $accountCampaigns = new AccountCampaigns();
        foreach($accountCampaignsParameters as $accountId => $campaigns) {
            foreach($campaigns as $campaign) {
                $campaignId = $campaign[AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID];

                if(empty($accountCampaigns->get($accountId)->get($campaignId))) {
                    $campaignDataModel = new Campaign(
                        new AdvertisingCampaign([
                            AdvertisingCampaign::FIELD_PLATFORM => $platform,
                            AdvertisingCampaign::FIELD_PLATFORM_ACCOUNT_ID => $accountId,
                            AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID => $campaignId
                        ]),
                        collect(),
                        collect(),
                        collect()
                    );

                    $accountCampaigns->setAccountCampaign($accountId, $campaignId, $campaignDataModel);
                }

                $automationParameter = new AdvertisingCampaignAutomationParameter([
                    AdvertisingCampaignAutomationParameter::FIELD_TYPE => $campaign[AdvertisingCampaignAutomationParameter::FIELD_TYPE],
                    AdvertisingCampaignAutomationParameter::FIELD_STRATEGY => $campaign[AdvertisingCampaignAutomationParameter::FIELD_STRATEGY],
                    AdvertisingCampaignAutomationParameter::FIELD_PARAMETER => $campaign[AdvertisingCampaignAutomationParameter::FIELD_PARAMETER],
                    AdvertisingCampaignAutomationParameter::FIELD_OPERATOR => $campaign[AdvertisingCampaignAutomationParameter::FIELD_OPERATOR],
                    AdvertisingCampaignAutomationParameter::FIELD_THRESHOLD => $campaign[AdvertisingCampaignAutomationParameter::FIELD_THRESHOLD],
                    AdvertisingCampaignAutomationParameter::FIELD_THRESHOLD_TYPE => $campaign[AdvertisingCampaignAutomationParameter::FIELD_THRESHOLD_TYPE]
                ]);

                $accountCampaigns->addAutomationParameter($accountId, $campaignId, $automationParameter);
            }
        }

        return $accountCampaigns;
    }

    /**
     * Decides which campaign locations should be excluded/included
     * $accountCampaigns is a list of platform campaigns along with their location target ID's
     * $accountCampaigns is obtained from AdvertisingServiceContract::getAutomatedCampaignsWithLocations()
     *
     * @param int $logId
     * @param AccountCampaigns $accountCampaigns
     * @param string $platform
     * @return CampaignLocations
     * @throws Exception
     */
    public function determineCampaignLocationsToUpdate(int $logId, AccountCampaigns $accountCampaigns, string $platform): CampaignLocations
    {
        $locationIds = [];
        foreach($accountCampaigns as $accountId => $campaigns) {
            $locationIds[$accountId] = [];

            foreach($campaigns as $campaign) {
                $campaignLocationIds = $campaign->locations->values()->toArray();

                AdvertisingLoggingService::writeLog(
                    "Initial campaign locations",
                    [
                        "id" => $logId,
                        "platform" => $platform,
                        "account_id" => (string) $accountId,
                        "campaign_id" => (string) $campaign->{AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID},
                        "locations" => $campaignLocationIds
                    ]
                );

                $locationIds[$accountId] = array_merge($locationIds[$accountId], $campaignLocationIds);
            }
        }
        unset($campaignLocationIds);

        AdvertisingLoggingService::writeLog(
            "Gathered campaign location ids",
            [
                "id" => $logId,
                "platform" => $platform
            ]
        );

        $countyLocationIds = Location::query()
            ->where(Location::TYPE, Location::TYPE_COUNTY)
            ->distinct()
            ->pluck(Location::ID)
            ->toArray();

        AdvertisingLoggingService::writeLog(
            "Obtained county locations reference list",
            [
                "id" => $logId,
                "platform" => $platform
            ]
        );

        $accounts = app(AdvertisingAccountService::class)->getAccounts($platform)->pluck(AdvertisingAccount::FIELD_INDUSTRY, AdvertisingAccount::FIELD_PLATFORM_ACCOUNT_ID);

        AdvertisingLoggingService::writeLog(
            "Obtained advertising account industries",
            [
                "id" => $logId,
                "platform" => $platform
            ]
        );

        $lockedLocations = $this->getLockedCountyLocationsForPlatform($platform);

        AdvertisingLoggingService::writeLog("Locked locations found", ["id" => $logId, "platform" => $platform, "locations" => $lockedLocations->toArray()]);

        $platformAdvertisingService = AdvertisingServiceFactory::make($platform);

        $campaignLocations = new CampaignLocations();
        $campaignLocations->setAdditionalParameters($accountCampaigns->getCampaignsAdditionalParameters()->recursiveToArray());

        foreach($accountCampaigns as $accountId => &$campaigns) {
            $excludeLocations = [];
            $industry = $accounts->get($accountId);
            $hasRevenueLocationsByZip = $this->estimatedRevenuePerLeadByLocationRepository->getAvailableRevenueByCountyZip($industry, $locationIds[$accountId]);

            $campaignCountyLocationIds = array_intersect($locationIds[$accountId], $countyLocationIds);

            $hasRevenueLocationsByCounty = $this->availableCompanyByLocationRepository->getAvailableRevenueByCounty(Industry::fromSlug(strtolower($industry)), $campaignCountyLocationIds);

            AdvertisingLoggingService::writeLog("Obtained revenue information per location", ["id" => $logId, "platform" => $platform, "account_id" => (string) $accountId]);

            foreach($campaigns as $campaignId => &$campaign) {
                $excludeLocations[$campaignId] = [];
                $campaignCountyLocationIds = array_intersect($campaign->{Campaign::LOCATIONS}->values()->toArray(), $countyLocationIds);

                foreach($campaign->{Campaign::AUTOMATION_PARAMETERS} as $automationParameter) {
                    $automationParameterExcludedIds = $this->determineExcludedLocationsByAutomationParameter(
                        $automationParameter,
                        $campaignCountyLocationIds,
                        $hasRevenueLocationsByZip,
                        $hasRevenueLocationsByCounty
                    );

                    AdvertisingLoggingService::writeLog(
                        "Excluded locations for parameter",
                        [
                            "id" => $logId,
                            "platform" => $platform,
                            "account_id" => (string) $accountId,
                            "campaign_id" => (string) $campaignId,
                            "automation_parameter" => $automationParameter->toArray(),
                            "excluded_locations" => $automationParameterExcludedIds
                        ]
                    );

                    $excludeLocations[$campaignId] = array_merge($excludeLocations[$campaignId], $automationParameterExcludedIds);
                }

                $lockedCampaignLocations = $lockedLocations->get($accountId)?->get($campaignId);
                $lockedIncludedLocations = $lockedCampaignLocations?->where(LockedAdvertisingCampaignLocation::FIELD_TARGETED, true)->pluck(LockedAdvertisingCampaignLocation::FIELD_LOCATION_ID)->toArray() ?? [];
                $lockedExcludedLocations = $lockedCampaignLocations?->where(LockedAdvertisingCampaignLocation::FIELD_TARGETED, false)->pluck(LockedAdvertisingCampaignLocation::FIELD_LOCATION_ID)->toArray() ?? [];

                $excludeLocations[$campaignId] = array_diff(array_merge($excludeLocations[$campaignId], $lockedExcludedLocations), $lockedIncludedLocations);

                AdvertisingLoggingService::writeLog(
                    "Excluded locations after accounting for locked locations",
                    [
                        "id" => $logId,
                        "platform" => $platform,
                        "account_id" => (string) $accountId,
                        "campaign_id" => (string) $campaignId,
                        "excluded_locations" => $excludeLocations[$campaignId]
                    ]
                );
            }

            $platformAdvertisingService->populateCampaignLocationsToUpdate($accountId, $campaignLocations, $campaigns, $excludeLocations, $logId);
        }

        return $campaignLocations;
    }

    /**
     * @param AdvertisingCampaignAutomationParameter $automationParameter
     * @param array $campaignCountyLocationIds
     * @param Collection $hasRevenueLocationsByZip
     * @param Collection $hasRevenueLocationsByCounty
     * @return array
     */
    private function determineExcludedLocationsByAutomationParameter(
        AdvertisingCampaignAutomationParameter $automationParameter,
        array $campaignCountyLocationIds,
        Collection $hasRevenueLocationsByZip,
        Collection $hasRevenueLocationsByCounty
    ): array
    {
        $excludeLocations = [];

        $compareOperatorsFunc = function($a, $b, $op) {
            return match($op) {
                AdvertisingCampaignAutomationParameter::OPERATOR_EQUAL => $a == $b,
                AdvertisingCampaignAutomationParameter::OPERATOR_GREATER_THAN => $a > $b,
                AdvertisingCampaignAutomationParameter::OPERATOR_GREATER_THAN_EQUAL => $a >= $b,
                AdvertisingCampaignAutomationParameter::OPERATOR_LESS_THAN => $a < $b,
                AdvertisingCampaignAutomationParameter::OPERATOR_LESS_THAN_EQUAL => $a <= $b,
                AdvertisingCampaignAutomationParameter::OPERATOR_NOT_EQUAL => $a != $b
            };
        };

        $automationType = $automationParameter->{AdvertisingCampaignAutomationParameter::FIELD_TYPE};
        $automationStrategy = $automationParameter->{AdvertisingCampaignAutomationParameter::FIELD_STRATEGY};
        $operator = $automationParameter->{AdvertisingCampaignAutomationParameter::FIELD_OPERATOR};
        $threshold = (int) $automationParameter->{AdvertisingCampaignAutomationParameter::FIELD_THRESHOLD};
        $thresholdType = $automationParameter->{AdvertisingCampaignAutomationParameter::FIELD_THRESHOLD_TYPE};

        if($automationType === AdvertisingCampaignAutomationParameter::AUTOMATION_TYPE_LOCATION) {
            //County level
            if($automationParameter->{AdvertisingCampaignAutomationParameter::FIELD_PARAMETER} === AdvertisingCampaignAutomationParameter::PARAMETER_ZIPCODES_WITH_REVENUE) {
                $campaignCounties = $hasRevenueLocationsByZip->only($campaignCountyLocationIds);

                if($thresholdType === AdvertisingCampaignAutomationParameter::THRESHOLD_TYPE_AMOUNT) {
                    $testFunc = function(array $zipcodes) use ($compareOperatorsFunc, $operator, $threshold) {
                        return $compareOperatorsFunc(array_sum($zipcodes), $threshold, $operator);
                    };
                }
                else if($thresholdType === AdvertisingCampaignAutomationParameter::THRESHOLD_TYPE_PERCENT) {
                    $testFunc = function(array $zipcodes) use ($compareOperatorsFunc, $operator, $threshold) {
                        return $compareOperatorsFunc(array_sum($zipcodes) / count($zipcodes) * 100, $threshold, $operator);
                    };
                }

                foreach($campaignCounties as $countyLocationId => $countyZips) {
                    if($testFunc($countyZips->pluck('has_revenue')->toArray())) {
                        $excludeLocations[] = $countyLocationId;
                    }
                }
            }//Zipcode level
            else if($automationParameter->{AdvertisingCampaignAutomationParameter::FIELD_PARAMETER} === AdvertisingCampaignAutomationParameter::PARAMETER_ZIPCODE_HAS_REVENUE) {
                $campaignCounties = $hasRevenueLocationsByZip->only($campaignCountyLocationIds);

                if($thresholdType === AdvertisingCampaignAutomationParameter::THRESHOLD_TYPE_DOLLARS) {
                    $metricField = EstimatedRevenuePerLeadByLocation::FIELD_ESTIMATED_REVENUE;
                }
                else if($thresholdType === AdvertisingCampaignAutomationParameter::THRESHOLD_TYPE_AVAILABLE_COMPANIES) {
                    $metricField = EstimatedRevenuePerLeadByLocation::FIELD_AVAILABLE_COMPANIES;
                }

                foreach($campaignCounties as $countyLocationId => $countyZips) {
                    foreach($countyZips as $zipCodeLocationId => $zipCode) {
                        if($compareOperatorsFunc($zipCode[$metricField], $threshold, $operator)) {
                            $excludeLocations[] = $zipCodeLocationId;
                        }
                    }
                }
            }
            else if ($automationParameter->{AdvertisingCampaignAutomationParameter::FIELD_PARAMETER} === AdvertisingCampaignAutomationParameter::PARAMETER_COMPANIES_IN_COUNTY) {
                $revenueLocationsByCounty = $hasRevenueLocationsByCounty->only($campaignCountyLocationIds);

                if ($thresholdType === AdvertisingCampaignAutomationParameter::THRESHOLD_TYPE_AVAILABLE_COMPANIES) {
                    $testFunc = function (int $companiesCount) use ($compareOperatorsFunc, $operator, $threshold) {
                        return $compareOperatorsFunc($companiesCount, $threshold, $operator);
                    };
                }

                foreach ($revenueLocationsByCounty as $countyLocationId => $revenueLocation) {
                    if ($testFunc($revenueLocation[AvailableCompanyByLocationRepository::COMPANIES_COUNT])) {
                        $excludeLocations[] = $countyLocationId;
                    }
                }
            }
        }
        else if($automationParameter->{AdvertisingCampaignAutomationParameter::FIELD_TYPE} === AdvertisingCampaignAutomationParameter::AUTOMATION_TYPE_BUDGET) {
            if ($automationParameter->{AdvertisingCampaignAutomationParameter::FIELD_PARAMETER} === AdvertisingCampaignAutomationParameter::PARAMETER_UNLIMITED_BUDGETS_AMOUNT) {
                $revenueLocationsByCounty = $hasRevenueLocationsByCounty->only($campaignCountyLocationIds);

                if ($thresholdType === AdvertisingCampaignAutomationParameter::THRESHOLD_TYPE_AMOUNT) {
                    $testFunc = function (int $unlimitedBudgets) use ($compareOperatorsFunc, $operator, $threshold) {
                        return $compareOperatorsFunc($unlimitedBudgets, $threshold, $operator);
                    };
                }

                foreach ($revenueLocationsByCounty as $countyLocationId => $revenueLocation) {
                    if ($testFunc($revenueLocation[AvailableCompanyByLocationRepository::UNLIMITED_BUDGETS])) {
                        $excludeLocations[] = $countyLocationId;
                    }
                }
            }
        }

        return array_unique($excludeLocations);
    }

    /**
     * @param string $platform
     * @return Collection
     */
    public function getLockedCountyLocationsForPlatform(string $platform): Collection
    {
        return LockedAdvertisingCampaignLocation::query()
                    ->join(DatabaseHelperService::readOnlyDatabase().'.'.Location::TABLE, function($join) {
                        $join->on(
                            DatabaseHelperService::readOnlyDatabase().'.'.Location::TABLE.'.'.Location::ID,
                            '=',
                            DatabaseHelperService::database().'.'.LockedAdvertisingCampaignLocation::TABLE.'.'.LockedAdvertisingCampaignLocation::FIELD_LOCATION_ID
                        );
                    })
                    ->where(LockedAdvertisingCampaignLocation::FIELD_PLATFORM, $platform)
                    ->where(Location::TYPE, Location::TYPE_COUNTY)
                    ->select([
                        LockedAdvertisingCampaignLocation::FIELD_PLATFORM_ACCOUNT_ID,
                        LockedAdvertisingCampaignLocation::FIELD_PLATFORM_CAMPAIGN_ID,
                        LockedAdvertisingCampaignLocation::FIELD_LOCATION_ID,
                        LockedAdvertisingCampaignLocation::FIELD_TARGETED
                    ])
                    ->get()
                    ->groupBy([LockedAdvertisingCampaignLocation::FIELD_PLATFORM_ACCOUNT_ID, LockedAdvertisingCampaignLocation::FIELD_PLATFORM_CAMPAIGN_ID]);
    }

    /**
     * @return bool
     * @throws Throwable
     */
    public function sendNotificationEmailForFailedAdvertisingCampaigns(): bool
    {
        try {
            $twentyFourHoursInSecs = Carbon::SECONDS_PER_MINUTE * Carbon::MINUTES_PER_HOUR * Carbon::HOURS_PER_DAY;
            $oneWeekInSecs = $twentyFourHoursInSecs * Carbon::DAYS_PER_WEEK;

            $adCampaigns = AdvertisingCampaign::query()
                                ->select([
                                    AdvertisingCampaign::FIELD_PLATFORM,
                                    AdvertisingCampaign::FIELD_PLATFORM_ACCOUNT_ID,
                                    AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID
                                ])
                                ->where(AdvertisingCampaign::FIELD_LAST_RUN_TIMESTAMP, '>', 0)
                                ->where(function($where) use ($twentyFourHoursInSecs, $oneWeekInSecs) {
                                    $where
                                        ->where(function($subWhere) use ($twentyFourHoursInSecs) {
                                            $subWhere
                                                ->whereRaw(sprintf(
                                                    "UNIX_TIMESTAMP() >= %s + %s",
                                                    AdvertisingCampaign::FIELD_LAST_RUN_TIMESTAMP,
                                                    $twentyFourHoursInSecs
                                                ))
                                                ->where(AdvertisingCampaign::FIELD_PLATFORM, AdvertisingPlatform::GOOGLE->value);
                                        })
                                        ->orWhere(function($subWhere) use ($oneWeekInSecs) {
                                            $subWhere
                                                ->whereRaw(sprintf(
                                                    "UNIX_TIMESTAMP() >= %s + %s",
                                                    AdvertisingCampaign::FIELD_LAST_RUN_TIMESTAMP,
                                                    $oneWeekInSecs
                                                ))
                                                ->where(AdvertisingCampaign::FIELD_PLATFORM, AdvertisingPlatform::META->value);
                                        });
                                })
                                ->get()
                                ->groupBy([AdvertisingCampaign::FIELD_PLATFORM, AdvertisingCampaign::FIELD_PLATFORM_ACCOUNT_ID])
                                ->toArray();

            if(!empty($adCampaigns)) {
                $message = '';
                foreach($adCampaigns as $platform => $accountCampaigns) {
                    $displayName = AdvertisingPlatform::displayName($platform);
                    $hierarchyNames = AdvertisingPlatform::hierarchyNames($platform);

                    $message .= "{$displayName} automation has stalled for the following {$hierarchyNames[0]}\n\n";

                    foreach($accountCampaigns as $account => $campaigns) {
                        $message .= sprintf(
                            "%s: %s -> %s: %s\n",
                            ucwords(Str::singular($hierarchyNames[0])),
                            $account,
                            ucwords($hierarchyNames[1]),
                            implode(', ', array_column($campaigns, AdvertisingCampaign::FIELD_PLATFORM_CAMPAIGN_ID))
                        );
                    }

                    $message .= "\n";
                }

                $recipients = explode(',', config('services.ads.notification_emails'));
                foreach($recipients as $recipient) {
                    Mail::raw(
                        $message,
                        function(Message $message) use ($recipient) {
                            $message
                                ->to(trim($recipient))
                                ->subject("SolarReviews: Ads Automation Stalled");
                        }
                    );
                }
            }

            return true;
        }
        catch(Throwable $e) {
            $errMsg = __METHOD__.": ".substr($e->getMessage(), 0, 255);

            AdvertisingLoggingService::writeLog(
                $errMsg,
                [
                    'trace' => base64_encode(gzcompress($e->getTraceAsString()))
                ]
            );

            throw $e;
        }
    }
}
