<?php

namespace App\Services\Odin;


use App\Jobs\TagUserJob;
use App\Models\Action;
use App\Models\User;

class ActionService
{

    /***
     * @param Action $action
     * @param array $taggedUserIds
     * @return void
     */
    public function tagUsers(Action $action, array $taggedUserIds): void
    {
        $users = User::query()->whereIn(User::FIELD_ID, $taggedUserIds)->get();

        foreach ($users as $user) {
            TagUserJob::dispatch($action, $user);
        }
    }
}
