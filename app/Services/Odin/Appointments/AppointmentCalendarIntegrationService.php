<?php

namespace App\Services\Odin\Appointments;

use App\Enums\CalendarPlatform;
use App\Enums\HttpMethod;
use App\Enums\Odin\QualityTier as QualityTierEnum;
use App\Http\Controllers\API\ClientDashboard\AppointmentCalendarIntegrationController;
use App\Models\ClientToken;
use App\Models\ClientTokenService;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\ProductCampaignSchedule;
use App\Repositories\Odin\CompanyRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Http;
use Throwable;

class AppointmentCalendarIntegrationService
{
    public function __construct(

    ) {

    }

    /**
     * @param CompanyUser $companyUser
     * @return string
     * @throws Throwable
     */
    public function registerUserForScheduling(CompanyUser $companyUser): string
    {
        $schedulingUserRef = $this->sendRequest(
            HttpMethod::METHOD_POST,
            "integration/register-user",
            [
                "name" => $companyUser->completeName(),
                "email" => $companyUser->{CompanyUser::FIELD_EMAIL},
                "company_reference" => $companyUser->{CompanyUser::RELATION_COMPANY}->{Company::FIELD_REFERENCE},
                "company_name" => $companyUser->{CompanyUser::RELATION_COMPANY}->{Company::FIELD_NAME}
            ]
        )['user_reference'];

        $companyUser->{CompanyUser::FIELD_SCHEDULING_USER_REFERENCE} = $schedulingUserRef;

        $companyUser->save();

        return $schedulingUserRef;
    }

    /**
     * @param CalendarPlatform $platform
     * @param CompanyUser $companyUser
     * @return string
     * @throws Throwable
     */
    public function getOauthAuthorizationUri(CalendarPlatform $platform, CompanyUser $companyUser): string
    {
        $this->checkSchedulingUserRegistered($companyUser);

        return $this->sendRequest(
            HttpMethod::METHOD_GET,
            "integration/{$platform->value}/{$companyUser->{CompanyUser::FIELD_SCHEDULING_USER_REFERENCE}}/oauth/authorization-uri"
        )['uri'];
    }

    /**
     * @param CalendarPlatform $platform
     * @param array $params
     * @return bool
     */
    public function handleOauthRedirect(CalendarPlatform $platform, array $params): bool
    {
        try {
            $this->sendRequest(
                HttpMethod::METHOD_GET,
                "integration/{$platform->value}/oauth-redirect",
                $params
            );

            return true;
        }
        catch(Throwable $e) {
            logger()->error($e);
        }

        return false;
    }

    /**
     * @param int $companyId
     * @param CompanyUser $companyUser
     * @return array
     * @throws Throwable
     */
    public function getAllCompanyCalendars(int $companyId, CompanyUser $companyUser): array
    {
        $this->checkSchedulingUserRegistered($companyUser);

        $teamReference = Company::query()
            ->findOrFail($companyId)
            ->{Company::FIELD_REFERENCE};

        $calendars = $this->sendRequest(HttpMethod::METHOD_GET, "integration/{$companyUser->{CompanyUser::FIELD_SCHEDULING_USER_REFERENCE}}/team-calendars/{$teamReference}")["calendars"];

        $productCampaignSchedules = ProductCampaignSchedule::query()
            ->whereIn(ProductCampaignSchedule::FIELD_SCHEDULE_ID, array_column($calendars, 'id'))
            ->get()
            ->mapToGroups(function($pcs) {
                 return [$pcs[ProductCampaignSchedule::FIELD_SCHEDULE_ID] => $pcs[ProductCampaignSchedule::FIELD_PRODUCT_CAMPAIGN_ID]];
            })
            ->toArray();

        foreach($calendars as &$calendar) {
            $calendar["campaigns"] = $productCampaignSchedules[$calendar["id"]] ?? [];
        }

        return $calendars;
    }

    /**
     * @param CompanyUser $companyUser
     * @param string $name
     * @param string $timezone
     * @param int $onlineDuration
     * @param int $inHomeDuration
     * @param int $inHomeBufferBefore
     * @param int $inHomeBufferAfter
     * @param int $onlineBufferBefore
     * @param int $onlineBufferAfter
     * @param bool $inHomeSameDayAppointments
     * @param bool $onlineSameDayAppointments
     * @param int $overlappingEventsAllowed
     * @return int
     * @throws Throwable
     */
    public function createStaticCalendar(
        CompanyUser $companyUser,
        string $name,
        string $timezone,
        int $onlineDuration,
        int $inHomeDuration,
        int $inHomeBufferBefore,
        int $inHomeBufferAfter,
        int $onlineBufferBefore,
        int $onlineBufferAfter,
        bool $inHomeSameDayAppointments,
        bool $onlineSameDayAppointments,
        int $overlappingEventsAllowed
    ): int
    {
        $this->checkSchedulingUserRegistered($companyUser);

        $params = [
            AppointmentCalendarIntegrationController::REQUEST_NAME => $name,
            AppointmentCalendarIntegrationController::REQUEST_TIMEZONE => $timezone,
            AppointmentCalendarIntegrationController::REQUEST_ONLINE_DURATION => $onlineDuration,
            AppointmentCalendarIntegrationController::REQUEST_IN_HOME_DURATION => $inHomeDuration,
            AppointmentCalendarIntegrationController::REQUEST_IN_HOME_BUFFER_AFTER => $inHomeBufferAfter,
            AppointmentCalendarIntegrationController::REQUEST_IN_HOME_BUFFER_BEFORE => $inHomeBufferBefore,
            AppointmentCalendarIntegrationController::REQUEST_ONLINE_BUFFER_AFTER => $onlineBufferAfter,
            AppointmentCalendarIntegrationController::REQUEST_ONLINE_BUFFER_BEFORE => $onlineBufferBefore,
            AppointmentCalendarIntegrationController::REQUEST_IN_HOME_SAME_DAY_APPOINTMENTS => $inHomeSameDayAppointments,
            AppointmentCalendarIntegrationController::REQUEST_ONLINE_SAME_DAY_APPOINTMENTS => $onlineSameDayAppointments,
            AppointmentCalendarIntegrationController::REQUEST_OVERLAPPING_EVENTS_ALLOWED => $overlappingEventsAllowed
        ];

        return $this->sendRequest(
            HttpMethod::METHOD_POST,
            "integration/{$companyUser->{CompanyUser::FIELD_SCHEDULING_USER_REFERENCE}}/static-calendar",
            $params
        )['calendar_id'];
    }

    /**
     * @param int $calendarId
     * @param CompanyUser $companyUser
     * @return array
     * @throws Throwable
     */
    public function getCalendarById(int $calendarId, CompanyUser $companyUser): array
    {
        $this->checkSchedulingUserRegistered($companyUser);

        return $this->sendRequest(
            HttpMethod::METHOD_GET,
            "integration/{$companyUser->{CompanyUser::FIELD_SCHEDULING_USER_REFERENCE}}/calendar/{$calendarId}"
        )['calendar'];
    }

    /**
     * @param int $calendarId
     * @param CompanyUser $companyUser
     * @param string $name
     * @param string $timezone
     * @param int $onlineDuration
     * @param int $inHomeDuration
     * @param int $inHomeBufferBefore
     * @param int $inHomeBufferAfter
     * @param int $onlineBufferBefore
     * @param int $onlineBufferAfter
     * @param bool $inHomeSameDayAppointments
     * @param bool $onlineSameDayAppointments
     * @param int $overlappingEventsAllowed
     * @param string $platform
     * @param array $items
     * @param array $overrideItems
     * @return bool
     */
    public function saveCalendar(
        int $calendarId,
        CompanyUser $companyUser,
        string $name,
        string $timezone,
        int $onlineDuration,
        int $inHomeDuration,
        int $inHomeBufferBefore,
        int $inHomeBufferAfter,
        int $onlineBufferBefore,
        int $onlineBufferAfter,
        bool $inHomeSameDayAppointments,
        bool $onlineSameDayAppointments,
        int $overlappingEventsAllowed,
        string $platform = '',
        array $items = [],
        array $overrideItems = []
    ): bool
    {
        try {
            $this->checkSchedulingUserRegistered($companyUser);

            $params = [
                AppointmentCalendarIntegrationController::REQUEST_NAME => $name,
                AppointmentCalendarIntegrationController::REQUEST_TIMEZONE => $timezone,
                AppointmentCalendarIntegrationController::REQUEST_ONLINE_DURATION => $onlineDuration,
                AppointmentCalendarIntegrationController::REQUEST_IN_HOME_DURATION => $inHomeDuration,
                AppointmentCalendarIntegrationController::REQUEST_IN_HOME_BUFFER_AFTER => $inHomeBufferAfter,
                AppointmentCalendarIntegrationController::REQUEST_IN_HOME_BUFFER_BEFORE => $inHomeBufferBefore,
                AppointmentCalendarIntegrationController::REQUEST_ONLINE_BUFFER_AFTER => $onlineBufferAfter,
                AppointmentCalendarIntegrationController::REQUEST_ONLINE_BUFFER_BEFORE => $onlineBufferBefore,
                AppointmentCalendarIntegrationController::REQUEST_IN_HOME_SAME_DAY_APPOINTMENTS => $inHomeSameDayAppointments,
                AppointmentCalendarIntegrationController::REQUEST_ONLINE_SAME_DAY_APPOINTMENTS => $onlineSameDayAppointments,
                AppointmentCalendarIntegrationController::REQUEST_PLATFORM => $platform,
                AppointmentCalendarIntegrationController::REQUEST_ITEMS => $items,
                AppointmentCalendarIntegrationController::REQUEST_OVERRIDE_ITEMS => $overrideItems,
                AppointmentCalendarIntegrationController::REQUEST_OVERLAPPING_EVENTS_ALLOWED => $overlappingEventsAllowed
            ];

            $this->sendRequest(
                HttpMethod::METHOD_PATCH,
                "integration/{$companyUser->{CompanyUser::FIELD_SCHEDULING_USER_REFERENCE}}/calendar/{$calendarId}",
                $params
            );

            return true;
        }
        catch(Throwable $e) {
            logger()->error($e);
        }

        return false;
    }

    /**
     * @param int $calendarId
     * @param CompanyUser $companyUser
     * @return bool
     */
    public function deleteCalendar(int $calendarId, CompanyUser $companyUser): bool
    {
        try {
            $this->checkSchedulingUserRegistered($companyUser);

            $this->sendRequest(
                HttpMethod::METHOD_DELETE,
                "integration/{$companyUser->{CompanyUser::FIELD_SCHEDULING_USER_REFERENCE}}/calendar/{$calendarId}"
            );

            ProductCampaignSchedule::query()
                ->where(ProductCampaignSchedule::FIELD_SCHEDULE_ID, $calendarId)
                ->delete();

            return true;
        }
        catch(Throwable $e) {
            logger()->error($e);
        }

        return false;
    }

    /**
     * @param Collection $productCampaignIds
     * @param Carbon $appointmentDate
     * @param QualityTierEnum $appointmentType
     * @param Carbon $approvalDate
     * @return array
     * @throws Throwable
     */
    public function getAvailabilityForProductCampaigns(Collection $productCampaignIds, Carbon $appointmentDate, QualityTierEnum $appointmentType, Carbon $approvalDate): array
    {
        if(!in_array($appointmentType, [QualityTierEnum::ONLINE, QualityTierEnum::IN_HOME], true)) {
            throw new Exception("Invalid appointment type");
        }

        $campaignAvailabilities = array_fill_keys($productCampaignIds->toArray(), 0);

        $campaignSchedules = ProductCampaignSchedule::query()
            ->whereIn(ProductCampaignSchedule::FIELD_PRODUCT_CAMPAIGN_ID, $productCampaignIds)
            ->distinct()
            ->get()
            ->mapToGroups(function($schedule) {
                return [$schedule[ProductCampaignSchedule::FIELD_PRODUCT_CAMPAIGN_ID] => $schedule[ProductCampaignSchedule::FIELD_SCHEDULE_ID]];
            });

        if($campaignSchedules->isEmpty()) {
            return $campaignAvailabilities;
        }

        $scheduleAvailabilities = $this->sendRequest(
            HttpMethod::METHOD_GET,
            "integration/schedules-availability",
            [
                "appointment_date" => $appointmentDate->format("Y-m-d H:i:s"),
                "timezone_offset" => $appointmentDate->format("P"),
                "schedule_ids" => $campaignSchedules->collapse()->unique()->toArray(),
                "appointment_type" => $appointmentType->value,
                "approval_date" => $approvalDate->format("Y-m-d H:i:s")
            ]
        )['availabilities'];

        foreach($campaignAvailabilities as $campaignId => $campaignAvailability) {
            foreach($campaignSchedules->get($campaignId) ?? [] as $scheduleId) {
                if(!empty($scheduleAvailabilities[$scheduleId])) {
                    $campaignAvailabilities[$campaignId] = $scheduleId;

                    break;
                }
            }
        }

        return $campaignAvailabilities;
    }

    /**
     * @param int $productCampaignId
     * @param array $calendarIds
     * @return bool
     * @throws Throwable
     */
    public function updateProductCampaignSchedules(int $productCampaignId, array $calendarIds = []): bool
    {
        try {
            DB::beginTransaction();

            $filteredCalendarIds = array_unique(array_filter($calendarIds));

            $entriesToDelete = ProductCampaignSchedule::query()
                ->where(ProductCampaignSchedule::FIELD_PRODUCT_CAMPAIGN_ID, $productCampaignId)
                ->whereNotIn(ProductCampaignSchedule::FIELD_SCHEDULE_ID, $filteredCalendarIds)
                ->get();

            foreach($entriesToDelete as $toDelete) {
                $toDelete->delete();
            }

            foreach($filteredCalendarIds as $calendarId) {
                ProductCampaignSchedule::query()->updateOrCreate([
                    ProductCampaignSchedule::FIELD_PRODUCT_CAMPAIGN_ID => $productCampaignId,
                    ProductCampaignSchedule::FIELD_SCHEDULE_ID => $calendarId
                ]);
            }

            DB::commit();

            return true;
        }
        catch(Throwable $e) {
            DB::rollBack();

            $errLocation = $e->getFile().'. Line: '.$e->getLine().'. ';

            AppointmentService::writeAppointmentLog(
                $errLocation.substr($e->getMessage(), 0, 255),
                [
                    "product_campaign_id" => $productCampaignId,
                    "calendar_ids" => $calendarIds,
                    "trace" => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ]
            );

            throw $e;
        }
    }

    /**
     * @param int $calendarId
     * @param QualityTierEnum $eventType
     * @param string $eventName
     * @param Carbon $eventDateTime
     * @param string $consumerName
     * @param string $consumerEmail
     * @param int $productAssignmentId
     * @return bool
     * @throws Throwable
     */
    public function addEventToCalendar(
        int $calendarId,
        QualityTierEnum $eventType,
        string $eventName,
        Carbon $eventDateTime,
        string $consumerName,
        string $consumerEmail,
        int $productAssignmentId
    ): bool
    {
        try {
            $this->sendRequest(
                HttpMethod::METHOD_POST,
                "integration/event/{$calendarId}",
                [
                    "event_type" => $eventType->value,
                    "event_name" => $eventName,
                    "event_date" => $eventDateTime->format('Y-m-d H:i:s'),
                    "event_timezone" => $eventDateTime->format("P"),
                    "event_invitee_name" => $consumerName,
                    "event_invitee_email" => $consumerEmail,
                    "event_product_assignment_id" => $productAssignmentId
                ]
            );

            return true;
        }
        catch(Throwable $e) {
            $errLocation = $e->getFile().'. Line: '.$e->getLine().'. ';

            AppointmentService::writeAppointmentLog(
                $errLocation.substr($e->getMessage(), 0, 255),
                [
                    "trace" => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ]
            );

            throw $e;
        }
    }

    /**
     * @param int $calendarId
     * @param string $startDate
     * @param string $endDate
     * @param CompanyUser $companyUser
     * @return array
     * @throws Throwable
     */
    public function getEventsForCalendarInDateRange(int $calendarId, string $startDate, string $endDate, CompanyUser $companyUser): array
    {
        $this->checkSchedulingUserRegistered($companyUser);

        return $this->sendRequest(
            HttpMethod::METHOD_GET,
            "integration/{$companyUser->{CompanyUser::FIELD_SCHEDULING_USER_REFERENCE}}/calendar-events-date-range/{$calendarId}",
            [
                "start_date" => $startDate,
                "end_date" => $endDate
            ]
        )["calendar_events"];
    }

    /**
     * @param array $scheduleIds
     * @param CompanyUser $companyUser
     * @return bool
     * @throws Throwable
     */
    public function checkCalendarsHaveTimeSlots(
        array $scheduleIds,
        CompanyUser $companyUser
    ): bool
    {
        $this->checkSchedulingUserRegistered($companyUser);

        return $this->sendRequest(
            HttpMethod::METHOD_GET,
            "integration/{$companyUser->{CompanyUser::FIELD_SCHEDULING_USER_REFERENCE}}/calendars-have-time-slots",
            [
                "schedule_ids" => $scheduleIds
            ]
        )['has_time_slots'];
    }

    /**
     * @param CompanyUser $companyUser
     * @return CompanyUser
     * @throws Throwable
     */
    private function checkSchedulingUserRegistered(CompanyUser &$companyUser): CompanyUser
    {
        if(empty($companyUser->{CompanyUser::FIELD_SCHEDULING_USER_REFERENCE})) {
            $this->registerUserForScheduling($companyUser);

            $companyUser->refresh();
        }

        return $companyUser;
    }

    /**
     * @param HttpMethod $method
     * @param string $path
     * @param array $params
     * @return array
     * @throws Throwable
     */
    protected function sendRequest(HttpMethod $method, string $path, array $params = []): array
    {
        try {
            $JWT = $this->authenticateWithSchedulingService();

            $client = Http::withHeaders(['X-CLIENT-BEARER' => $JWT])
                ->timeout(120)
                ->baseUrl(config('services.scheduling.base_api_url'))
                ->throw();

            if(!App::isProduction()) {
                logger()->info("Scheduling API call");
                logger()->info($method->value." ".config('services.scheduling.base_api_url')."/".$path);
                logger()->info($params);
            }

            AppointmentService::writeAppointmentLog(
                "Scheduling API request: {$method->value} {$path}",
                [
                    "path" => $path,
                    "params" => $params
                ],
                DB::transactionLevel() > 0
            );

            switch($method) {
                case HttpMethod::METHOD_GET:
                    $res = $client->get($path, $params);
                    break;
                case HttpMethod::METHOD_POST:
                    $res = $client->post($path, $params);
                    break;
                case HttpMethod::METHOD_PATCH:
                    $res = $client->patch($path, $params);
                    break;
                case HttpMethod::METHOD_DELETE:
                    $res = $client->delete($path, $params);
                    break;
                default:
                    throw new Exception("Invalid method");
            }

            AppointmentService::writeAppointmentLog(
                "Scheduling API response: {$method->value} {$path}",
                [
                    "path" => $path,
                    "params" => $params,
                    "response" => $res->json()
                ],
                DB::transactionLevel() > 0
            );

            if(!$res->successful() || !$res->json("status")) {
                $responseData = json_encode($res->json());

                throw new Exception(__METHOD__." - Error: {$responseData}");
            }

            return $res->json("data");
        }
        catch(Throwable $e) {
            $errLocation = $e->getFile().'. Line: '.$e->getLine().'. ';

            AppointmentService::writeAppointmentLog(
                "Appt calendar integration http: ".$errLocation.substr($e->getMessage(), 0, 255),
                [
                    "path" => $path,
                    "params" => $params,
                    "trace" => base64_encode(gzcompress($e->getTraceAsString(), 9))
                ],
                DB::transactionLevel() > 0
            );

            throw $e;
        }
    }

    /**
     * @return string
     * @throws RequestException
     */
    private function authenticateWithSchedulingService(): string
    {
        $serviceId = ClientTokenService::where(ClientTokenService::FIELD_SERVICE_KEY, '=', ClientTokenService::SCHEDULING_API_SERVICE_KEY)->first()?->{ClientTokenService::FIELD_ID};

        if($serviceId === null) {
            throw new Exception("Service ID not found");
        }

        $clientToken = ClientToken::where(ClientToken::FIELD_SERVICE_ID, '=', $serviceId)->first();

        if(empty($clientToken)) {
            $authenticate = true;
        }
        else {
            $authenticate = ($clientToken->{ClientToken::FIELD_UPDATED_AT}?->timestamp ?? 0) + $clientToken->{ClientToken::FIELD_EXPIRES_IN} <= Carbon::now()->timestamp;
        }

        if($authenticate) {
            try {
                $response = Http::post(config('services.scheduling.base_api_url')."/auth-token", ["admin_client_secret" => config('services.scheduling.client_secret')]);

                if(!$response->successful()) {
                    $response->throw();
                }
            }
            catch(Exception $e) {
                logger()->error($e);

                $errLocation = $e->getFile().'. Line: '.$e->getLine().'. ';

                AppointmentService::writeAppointmentLog(
                    $errLocation."Scheduling API auth err: ".substr($e->getMessage(), 0, 255),
                    [
                        "trace" => base64_encode(gzcompress($e->getTraceAsString(), 9))
                    ]
                );

                throw $e;
            }

            $clientToken = ClientToken::firstOrNew([
                ClientToken::FIELD_SERVICE_ID => $serviceId
            ]);

            $now = Carbon::now();

            $clientToken->{ClientToken::FIELD_CLIENT_TOKEN} = $response["data"]["token"];
            $clientToken->{ClientToken::FIELD_EXPIRES_IN} = $response["data"]["expires_in"];
            $clientToken->{ClientToken::FIELD_UPDATED_AT} = $now;

            if(empty($clientToken->{ClientToken::FIELD_ID})) {
                $clientToken->{ClientToken::FIELD_CREATED_AT} = $now;
            }

            $clientToken->save();
        }

        return $clientToken->{ClientToken::FIELD_CLIENT_TOKEN};
    }
}
