<?php

namespace App\Services\Odin\ProductProcessing;

use App\Models\LeadProcessingQueueConfiguration;
use App\Models\LeadProcessingTeam;
use App\Models\LeadProcessor;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ServiceProduct;
use App\Repositories\Odin\ProductProcessing\ProductProcessingAffiliateQueueRepository;
use App\Repositories\Odin\ProductProcessing\ProductProcessingAgedQueueRepository;
use App\Repositories\Odin\ProductProcessing\ProductProcessingQueueRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use App\Enums\Odin\Product as ProductEnum;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Facades\DB;

class ProductQueueStatisticsService
{
    const QUEUE_INITIAL         = 'initial';
    const QUEUE_PENDING_REVIEW  = 'pending_review';
    const QUEUE_UNDER_REVIEW    = 'under_review';

    const STATUS_ALLOCATED  = 'allocated';
    const STATUS_UNSOLD     = 'no_companies';
    const STATUS_CANCELLED  = 'cancelled';

    const MAX_DAYS_DISPLAY = [
        self::STATUS_ALLOCATED  => 1,
        self::STATUS_UNSOLD     => 1,
        self::STATUS_CANCELLED  => 1
    ];

    protected int $productId;

    protected ?Industry $industry;

    /**
     * @param ProductProcessingQueueRepository $queueRepository
     */
    public function __construct(
        protected ProductProcessingQueueRepository $queueRepository,
    )
    {
        $this->productId = Product::query()
            ->where(Product::FIELD_NAME, ProductEnum::LEAD)
            ->firstOrFail()
            ->{Product::FIELD_ID};

        $this->industry = Industry::query()->firstOrFail();
    }

    /**
     * @param int $industryId
     * @return void
     */
    public function setIndustry(int $industryId): void
    {
        if ($industryId === 0) {
            $this->industry = null;
        }
        else {
            $this->industry = $this->getIndustry($industryId);
        }
    }

    /**
     * @param LeadProcessingQueueConfiguration $queue
     * @param int[]|null $utcOffsets
     * @param bool $addConstraints
     *
     * @return int
     * @throws Exception
     */
    protected function getLeadCount(LeadProcessingQueueConfiguration $queue, ?array $utcOffsets = null, bool $addConstraints = false): int
    {
        $query = $this->queueRepository->getQueryForQueue(
            $queue,
            !empty($utcOffsets),
            $utcOffsets ?? [-5],
            0,
            $this->industry ? collect([$this->industry]) : null,
            false,
            $addConstraints,
            null,
            !($this->industry)
        );

        if ($addConstraints) {
            $query->limit(null); //reset limit
        }

        return $this->getCountForQuery($query);
    }

    /**
     * @return int
     * @throws Exception
     */
    public function getInitialCount(): int
    {
        $queue = $this->getQueue(self::QUEUE_INITIAL);

        return $this->getLeadCount($queue);
    }

    /**
     * @return int
     * @throws Exception
     */
    public function getPendingReviewCount(): int
    {
        $queue = $this->getQueue(self::QUEUE_PENDING_REVIEW);

        return $this->getLeadCount($queue);
    }

    /**
     * @param int[]|null $utcOffsets
     * @param bool $addConstraints
     *
     * @return int
     * @throws Exception
     */
    public function getUnderReviewCount(?array $utcOffsets = null, bool $addConstraints = false): int
    {
        $queue = $this->getQueue(self::QUEUE_UNDER_REVIEW);

        return $this->getLeadCount($queue, $utcOffsets, $addConstraints);
    }

    /**
     * @return int
     */
    public function getAllocatedCount(): int
    {
        return $this->getStatusCount(self::MAX_DAYS_DISPLAY[self::STATUS_ALLOCATED], ConsumerProduct::STATUS_ALLOCATED);
    }

    /**
     * @return int
     */
    public function getCancelledCount(): int
    {
        return $this->getStatusCount(self::MAX_DAYS_DISPLAY[self::STATUS_CANCELLED], ConsumerProduct::STATUS_CANCELLED);
    }

    /**
     * @return int
     */
    public function getUnsoldCount(): int
    {
        return $this->getStatusCount(self::MAX_DAYS_DISPLAY[self::STATUS_UNSOLD], ConsumerProduct::STATUS_UNSOLD);
    }

    /**
     * @param int[]|null $utcOffsets
     *
     * @return int
     */
    public function getAgedCount(?array $utcOffsets = null): int
    {
        $processor = LeadProcessor::query()->where(LeadProcessor::FIELD_USER_ID, auth()->user()->id)->first();

        if (!$processor || !$processor->team?->primaryQueue) {
            $processor = LeadProcessor::query()->whereHas(LeadProcessor::RELATION_LEAD_PROCESSING_TEAM . '.' . LeadProcessingTeam::RELATION_PRIMARY_QUEUE)->first();
        }

        /** @var ProductProcessingAgedQueueRepository $repository */
        $repository = app(ProductProcessingAgedQueueRepository::class);
        $result = DB::select(
            $repository->getQueryForAgedQueue(
                processor: $processor,
                industriesOverwrite: $this->industry ? [$this->industry->id] : Industry::query()->pluck(Industry::FIELD_ID)->toArray(),
                utcOffsets: $utcOffsets
            )->select([])->selectRaw('COUNT(*) AS total')->toRawSql()
        );

        return $result[0]?->total ?? 0;
    }

    /**
     * @param int[]|null $utcOffsets
     *
     * @return int
     */
    public function getAffiliateCount(?array $utcOffsets = null): int
    {
        $processor = LeadProcessor::query()->where(LeadProcessor::FIELD_USER_ID, auth()->user()->id)->first();

        if (!$processor || !$processor->team?->primaryQueue) {
            $processor = LeadProcessor::query()->whereHas(LeadProcessor::RELATION_LEAD_PROCESSING_TEAM . '.' . LeadProcessingTeam::RELATION_PRIMARY_QUEUE)->first();
        }

        /** @var ProductProcessingAffiliateQueueRepository $repository */
        $repository = app(ProductProcessingAffiliateQueueRepository::class);
        $result = DB::select(
            $repository->getQueryForAffiliateQueue(
                processor: $processor,
                industriesOverwrite: $this->industry ? [$this->industry->id] : Industry::query()->pluck(Industry::FIELD_ID)->toArray(),
                utcOffsets: $utcOffsets
            )->select([])->selectRaw('COUNT(*) AS total')->toRawSql()
        );

        return $result[0]?->total ?? 0;
    }

    /**
     * @param int $maxDays
     * @param int $consumerProductStatus
     * @return int
     */
    protected function getStatusCount(int $maxDays, int $consumerProductStatus): int
    {
        $query = ConsumerProduct::query()
            ->with([ConsumerProduct::RELATION_SERVICE_PRODUCT, ConsumerProduct::RELATION_INDUSTRY_SERVICE])
            ->where(ConsumerProduct::CREATED_AT, '>=', Carbon::now()->subDays($maxDays))
            ->where(ConsumerProduct::FIELD_STATUS, $consumerProductStatus)
            ->whereHas(ConsumerProduct::RELATION_SERVICE_PRODUCT, fn (Builder $query) =>
                $query->where(ServiceProduct::FIELD_PRODUCT_ID, $this->productId)
            )->when($this->industry, fn(Builder $query) =>
                $query->whereHas(ConsumerProduct::RELATION_INDUSTRY_SERVICE, fn (Builder $query) =>
                    $query->where(IndustryService::FIELD_INDUSTRY_ID, $this->industry->id)
                )
            )->groupBy(ConsumerProduct::FIELD_ID);

        return $this->getCountForQuery($query);
    }

    /**
     * @param Builder $query
     * @return int
     */
    protected function getCountForQuery(Builder $query): int
    {
        return $query->selectRaw('COUNT(*) as count')->get()->count();
    }

    /**
     * @param string $primaryStatus
     * @return LeadProcessingQueueConfiguration
     */
    protected function getQueue(string $primaryStatus): LeadProcessingQueueConfiguration
    {
        /** @var LeadProcessingQueueConfiguration $queue */
        $queue = LeadProcessingQueueConfiguration::query()
            ->where(LeadProcessingQueueConfiguration::FIELD_PRIMARY_STATUS, $primaryStatus)
            ->firstOrFail();
        return $queue;
    }

    /**
     * @param int $industryId
     * @return Industry
     */
    protected function getIndustry(int $industryId): Industry
    {
        /** @var Industry $industry */
        $industry = Industry::query()
            ->findOrFail($industryId);
        return $industry;
    }


}
