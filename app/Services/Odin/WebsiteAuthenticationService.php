<?php

namespace App\Services\Odin;

use App\Models\Odin\Website;
use Illuminate\Contracts\Container\BindingResolutionException;

class WebsiteAuthenticationService
{
    /**
     * Checks whether a website is authenticated.
     *
     * @return bool
     * @throws BindingResolutionException
     */
    public function isWebsiteAuthenticated(): bool
    {
        /** @var Website $website */
        $website = app()->make(Website::class);

        return $website->exists;
    }

    /**
     * Returns the authenticated website.
     *
     * @return Website|null
     * @throws BindingResolutionException
     */
    public function getAuthenticatedWebsite(): ?Website
    {
        if(!$this->isWebsiteAuthenticated()) return null;

        /** @var Website $website */
        $website = app()->make(Website::class);

        return $website;
    }
}
