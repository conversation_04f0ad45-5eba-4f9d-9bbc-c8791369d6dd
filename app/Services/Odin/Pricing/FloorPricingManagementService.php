<?php

namespace App\Services\Odin\Pricing;

use App\Builders\Pricing\BasePricingBuilder;
use App\Contracts\Services\FloorPricingManagementServiceContract;
use App\DataModels\Odin\ConfigurableFieldDataModel;
use App\Enums\LoweredFloorPricePolicy;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\Product;
use App\Enums\Odin\Region;
use App\Http\Controllers\API\FloorPricing\FloorPricingController;
use App\Jobs\CalculateCompanyCampaignLowBidFlagForFloorPriceChangeJob;
use App\Jobs\CleanUpRedundantBidPricesJob;
use App\Models\Legacy\Location;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\ProductCountyFloorPrice;
use App\Models\Odin\ProductStateFloorPrice;
use App\Models\Odin\PropertyType;
use App\Models\Odin\QualityTier;
use App\Models\Odin\ServiceProduct;
use App\Models\SaleType;
use App\Repositories\Odin\ProductProcessing\FloorPricingRepository;
use App\Enums\Odin\QualityTier as QualityTierEnum;
use App\Enums\Odin\SaleTypes as SaleTypeEnum;
use App\Transformers\Odin\FloorPricingManagementTransformer;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

/**
 * Service for managing state & county Floor prices
 */
class FloorPricingManagementService implements FloorPricingManagementServiceContract
{
    const string SALE_TYPE_KEY = 'sale_type_key';

    public function __construct(
        protected FloorPricingRepository            $floorPricingRepository,
        protected FloorPricingManagementTransformer $floorPricingTransformer,
        protected LoweredFloorPricePolicyService    $loweredFloorPricePolicyService,
        protected FloorPriceChangesService          $floorPriceChangesService,
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function getStatePrices(int $serviceProductId, int $qualityTierId, int $propertyTypeId, bool $checkMissingPrices = false): array
    {
        $statePrices = $this->floorPricingRepository->getAllStates(
            serviceProductId: $serviceProductId,
            qualityTierId   : $qualityTierId,
            propertyTypeId  : $propertyTypeId,
        );

        return $this->floorPricingTransformer->transformStatePrices($statePrices->get(), $serviceProductId, $checkMissingPrices);
    }

    /**
     * @inheritDoc
     */
    public function getCountyPrices(int $serviceProductId, int $qualityTierId, int $propertyTypeId, int $stateLocationId): array
    {
        $countyPrices = $this->floorPricingRepository->getAllCountiesByState(
            serviceProductId: $serviceProductId,
            qualityTierId   : $qualityTierId,
            propertyTypeId  : $propertyTypeId,
            stateLocationId : $stateLocationId,
            countiesOnly    : true,
        );

        $statePrices = BasePricingBuilder::query()
            ->forState($stateLocationId)
            ->forServiceProduct($serviceProductId)
            ->forQualityTier($qualityTierId)
            ->forPropertyType($propertyTypeId);

        return $this->floorPricingTransformer->transformCountyPrices($statePrices->get(), $countyPrices->get(), $serviceProductId, $stateLocationId, $qualityTierId, $propertyTypeId);
    }

    /**
     * @inheritDoc
     */
    public function updateScopedNationalPrices(array $priceUpdates, int $serviceProductId, int $qualityTierId, int $propertyTypeId): array
    {
        $this->deleteScopedPrices($serviceProductId, $qualityTierId, $propertyTypeId);

        $stateIds       = $this->getStateLocationIds();
        $saleTypeKeyMap = array_flip($this->getSaleTypeMapForServiceProduct($serviceProductId));
        $insertData     = [];

        foreach ($stateIds as $stateId) {
            foreach (array_keys($saleTypeKeyMap) as $saleTypeKey) {
                $price        = $priceUpdates[$saleTypeKey] ?? 0;
                $insertData[] = [
                    ProductStateFloorPrice::FIELD_STATE_LOCATION_ID  => $stateId,
                    ProductStateFloorPrice::FIELD_SALE_TYPE_ID       => $saleTypeKeyMap[$saleTypeKey],
                    ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID   => $propertyTypeId,
                    ProductStateFloorPrice::FIELD_QUALITY_TIER_ID    => $qualityTierId,
                    ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID => $serviceProductId,
                    ProductStateFloorPrice::FIELD_PRICE              => $price,
                ];
            }
        }

        ProductStateFloorPrice::query()
            ->insert($insertData);

        dispatch(new CleanUpRedundantBidPricesJob($serviceProductId, $propertyTypeId, $qualityTierId));
        $this->dispatchCalculateLowBidJob($serviceProductId);

        return $this->getStatePrices($serviceProductId, $qualityTierId, $propertyTypeId);
    }

    /**
     * @inheritDoc
     * @throws Exception
     */
    public function updateScopedStatePrices(
        array  $priceUpdates,
        int    $stateLocationId,
        int    $serviceProductId,
        int    $qualityTierId,
        int    $propertyTypeId,
        ?array $loweredPricePolicy = null
    ): array
    {
        $saleTypeIdMap = $this->getSaleTypeMapForServiceProduct($serviceProductId);

        $scopedPrices     = BasePricingBuilder::query()
            ->forState($stateLocationId)
            ->forServiceProduct($serviceProductId)
            ->forQualityTier($qualityTierId)
            ->forPropertyType($propertyTypeId)
            ->get();
        $scopedPriceArray = $scopedPrices->reduce(function ($output, ProductStateFloorPrice $price) {
            $output[$price->sale_type_id] = $price->price;
            return $output;
        }, []);

        $loweredPrices = $this->getLoweredPrices($scopedPriceArray, $priceUpdates, $serviceProductId);
        $policyType    = $loweredPrices
            ? $this->validateLoweredPricePolicy($loweredPricePolicy)
            : null;
        if ($loweredPrices && !$policyType)
            throw new Exception("Invalid Lowered Price Policy supplied, cannot update prices");

        DB::beginTransaction();

        $increasedStatePriceIds    = [];
        $increasedStateLocationIds = [];
        $initialPriceUpdate        = [];

        try {
            $scopedPrices->each(function (ProductStateFloorPrice $statePrice) use (&$saleTypeIdMap, &$priceUpdates, &$increasedStatePriceIds, &$increasedStateLocationIds, &$initialPriceUpdate) {
                $saleTypeKey = $saleTypeIdMap[$statePrice->sale_type_id] ?? null;
                if ($saleTypeKey) {
                    $newPrice                         = $priceUpdates[$saleTypeKey] ?? null;
                    $oldPrice                         = $statePrice->price;
                    $initialPriceUpdate[$saleTypeKey] = $oldPrice;
                    if ($newPrice !== $oldPrice) {
                        $statePrice->update([ProductStateFloorPrice::FIELD_PRICE => $newPrice]);
                        if ($newPrice > $oldPrice) {
                            $increasedStatePriceIds[]    = $statePrice->id;
                            $increasedStateLocationIds[] = $statePrice->state_location_id;
                        }
                    }
                }
            });

            $this->floorPriceChangesService->formatAndDispatchFloorPriceUpdate(
                initialPrices: $initialPriceUpdate,
                newPrices: $priceUpdates,
                locationId: $stateLocationId,
                serviceProductId: $serviceProductId,
                region: Region::STATE,
            );

            $this->removeRedundantCountyFloorPrices($increasedStatePriceIds, $serviceProductId, $qualityTierId, $propertyTypeId);

            if ($increasedStateLocationIds)
                dispatch(new CleanUpRedundantBidPricesJob($serviceProductId, $propertyTypeId, $qualityTierId, $increasedStateLocationIds));

            if ($loweredPrices && !$this->loweredFloorPricePolicyService->handlePolicySideEffects(
                    policy                : $policyType,
                    policyFields          : $loweredPricePolicy,
                    serviceProductId      : $serviceProductId,
                    qualityTierId         : $qualityTierId,
                    propertyTypeId        : $propertyTypeId,
                    stateLocationId       : $stateLocationId,
                    currentPriceCollection: $loweredPrices,
                )) {
                throw new Exception("Failed to update Lowered Price Policy side-effects");
            }
        } catch (Exception $e) {
            DB::rollBack();
            logger()->error($e->getMessage());

            throw new Exception("Failed to update Floor Pricing");
        }

        $this->dispatchCalculateLowBidJob($serviceProductId);

        DB::commit();

        return $this->getStatePrices($serviceProductId, $qualityTierId, $propertyTypeId);
    }

    /**
     * @inheritDoc
     */
    public function updateScopedCountyPrices(
        array  $priceUpdates,
        int    $stateLocationId,
        int    $countyLocationId,
        int    $serviceProductId,
        int    $qualityTierId,
        int    $propertyTypeId,
        ?array $loweredPricePolicy = null,
        bool   $allowUpdateInheritedPrice = false
    ): array
    {
        $saleTypeIdMap = $this->getSaleTypeMapForServiceProduct($serviceProductId);

        $countyPrices = BasePricingBuilder::query()
            ->withCounties(true)
            ->forState($stateLocationId)
            ->forCounty($countyLocationId)
            ->forServiceProduct($serviceProductId)
            ->forQualityTier($qualityTierId)
            ->forPropertyType($propertyTypeId)
            ->aliasPriceColumns()
            ->get()
            ->reduce(function ($output, ProductCountyFloorPrice|ProductStateFloorPrice $countyPrice) {
                if ($countyPrice->sale_type_id) $output[$countyPrice->sale_type_id] = $countyPrice->{BasePricingBuilder::ALIAS_COUNTY_FLOOR_PRICE};
                return $output;
            }, []);

        $loweredPrices = $this->getLoweredPrices($countyPrices, $priceUpdates, $serviceProductId);
        $policyType    = $loweredPrices
            ? $this->validateLoweredPricePolicy($loweredPricePolicy)
            : null;
        if ($loweredPrices && !$policyType)
            throw new Exception("Invalid Lowered Price Policy supplied, cannot update prices");

        $statePrices = BasePricingBuilder::query()
            ->forState($stateLocationId)
            ->forServiceProduct($serviceProductId)
            ->forQualityTier($qualityTierId)
            ->forPropertyType($propertyTypeId)
            ->get()
            ->reduce(function ($output, ProductStateFloorPrice $statePrice) {
                $output[$statePrice->sale_type_id] = [
                    ProductStateFloorPrice::FIELD_PRICE => $statePrice->price,
                    ProductStateFloorPrice::FIELD_ID    => $statePrice->id,
                    self::SALE_TYPE_KEY                 => $statePrice->saleType->key,
                ];
                return $output;
            }, []);

        $pricesUpdated             = false;
        $increasedStateLocationIds = [];

        DB::beginTransaction();

        try {
            foreach ($priceUpdates as $saleTypeKey => $newPrice) {
                $saleTypeId = array_flip($saleTypeIdMap)[$saleTypeKey] ?? null;
                if ($saleTypeId) {
                    $oldPrice = $countyPrices[$saleTypeId] ?? null;
                    if ($newPrice !== $oldPrice) {
                        if ($newPrice > $oldPrice)
                            $increasedStateLocationIds[] = $stateLocationId;

                        $statePrice = ($statePrices[$saleTypeId] ?? [])[ProductStateFloorPrice::FIELD_PRICE] ?? null;
                        if ($statePrice && $newPrice === $statePrice) {
                            ProductCountyFloorPrice::query()
                                ->where([
                                    ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID => $countyLocationId,
                                    ProductCountyFloorPrice::FIELD_STATE_LOCATION_ID  => $stateLocationId,
                                    ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID => $serviceProductId,
                                    ProductCountyFloorPrice::FIELD_SALE_TYPE_ID       => $saleTypeId,
                                    ProductCountyFloorPrice::FIELD_QUALITY_TIER_ID    => $qualityTierId,
                                    ProductCountyFloorPrice::FIELD_PROPERTY_TYPE_ID   => $propertyTypeId
                                ])->first()
                                ?->delete();
                        } else {
                            ProductCountyFloorPrice::query()
                                ->updateOrCreate([
                                    ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID => $countyLocationId,
                                    ProductCountyFloorPrice::FIELD_STATE_LOCATION_ID  => $stateLocationId,
                                    ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID => $serviceProductId,
                                    ProductCountyFloorPrice::FIELD_SALE_TYPE_ID       => $saleTypeId,
                                    ProductCountyFloorPrice::FIELD_QUALITY_TIER_ID    => $qualityTierId,
                                    ProductCountyFloorPrice::FIELD_PROPERTY_TYPE_ID   => $propertyTypeId,
                                ], [
                                    ProductCountyFloorPrice::FIELD_PRICE => $newPrice,
                                ]);

                            $pricesUpdated = true;
                        }
                    }
                }
            }

            $this->floorPriceChangesService->dispatchCountyPriceUpdateNotification(
                statePrices: $statePrices,
                countyPrices: $countyPrices,
                priceUpdates: $priceUpdates,
                serviceProductId: $serviceProductId,
                countyLocationId: $countyLocationId
            );

            if ($increasedStateLocationIds)
                dispatch(new CleanUpRedundantBidPricesJob($serviceProductId, $propertyTypeId, $qualityTierId, $increasedStateLocationIds));

            if ($loweredPrices && !$this->loweredFloorPricePolicyService->handlePolicySideEffects(
                    policy                : $policyType,
                    policyFields          : $loweredPricePolicy,
                    serviceProductId      : $serviceProductId,
                    qualityTierId         : $qualityTierId,
                    propertyTypeId        : $propertyTypeId,
                    stateLocationId       : $stateLocationId,
                    currentPriceCollection: $loweredPrices,
                    countyLocationId      : $countyLocationId,
                )) {
                throw new Exception("Failed to update Lowered Price Policy side-effects");
            }
        } catch (Exception $e) {
            DB::rollBack();
            logger()->error($e->getMessage());

            throw new Exception("Failed to update Floor Pricing");
        }

        $this->dispatchCalculateLowBidJob($serviceProductId);

        DB::commit();

        return $pricesUpdated
            ? $this->getCountyPrices($serviceProductId, $qualityTierId, $propertyTypeId, $stateLocationId)
            : [];
    }

    /**
     * @inheritDoc
     */
    public function initialisePricingForServiceProduct(int $serviceProductId): bool
    {
        $defaultPricing = $this->floorPricingRepository->getDefaultFloorPricing(true);
        if (!$defaultPricing) return false;

        $stateIds = $this->getStateLocationIds();

        $qualityTiers = QualityTierEnum::byServiceProductId($serviceProductId);
        $saleTypes    = SaleTypeEnum::byServiceProductId($serviceProductId);
        $productName  = ServiceProduct::query()->find($serviceProductId)->product->name;

        $qualityTierIds  = QualityTier::query()
            ->whereIn(QualityTier::FIELD_NAME, $qualityTiers)
            ->pluck(QualityTier::FIELD_ID)
            ->toArray();
        $saleTypeIds     = SaleType::query()
            ->whereIn(SaleType::FIELD_NAME, $saleTypes)
            ->pluck(SaleType::FIELD_ID)
            ->toArray();
        $propertyTypeIds = PropertyType::query()
            ->pluck(PropertyType::FIELD_ID)
            ->toArray();

        $this->deleteAllPricesForServiceProductId($serviceProductId);

        $insertData = [];

        foreach ($stateIds as $stateId) {
            foreach ($qualityTierIds as $qualityTierId) {
                $qualityTierName = QualityTier::query()->find($qualityTierId)->name;
                foreach ($propertyTypeIds as $propertyTypeId) {
                    foreach ($saleTypeIds as $saleTypeId) {
                        $saleTypeName = SaleType::query()->find($saleTypeId)->name;
                        $price        = $this->getNestedPrice($defaultPricing, [$productName, $qualityTierName, $saleTypeName]);
                        $insertData[] = [
                            ProductStateFloorPrice::FIELD_STATE_LOCATION_ID  => $stateId,
                            ProductStateFloorPrice::FIELD_SALE_TYPE_ID       => $saleTypeId,
                            ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID   => $propertyTypeId,
                            ProductStateFloorPrice::FIELD_QUALITY_TIER_ID    => $qualityTierId,
                            ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID => $serviceProductId,
                            ProductStateFloorPrice::FIELD_PRICE              => $price,
                        ];
                    }
                }
            }
        }

        return DB::table(ProductStateFloorPrice::TABLE)
            ->insert($insertData);
    }

    /**
     * @inheritDoc
     */
    public function getDefaultPricingTable(): array
    {
        $products             = Product::cases();
        $defaultPricingConfig = $this->floorPricingRepository->getDefaultFloorPricing(true);
        $output               = [];

        foreach ($products as $product) {
            $output[$product->value] = [];
            $qualityTiers            = QualityTierEnum::byProductAndIndustry($product, IndustryEnum::SOLAR);
            $saleTypes               = SaleTypeEnum::byProductAndIndustry($product, IndustryEnum::SOLAR);
            foreach ($qualityTiers as $qualityTier) {
                $output[$product->value][$qualityTier] = [];
                foreach ($saleTypes as $saleType) {
                    $output[$product->value][$qualityTier][$saleType] = [
                        'price' => $this->getNestedPrice($defaultPricingConfig, [$product->value, $qualityTier, $saleType]) ?? 0,
                    ];
                }
            }
        }

        return $output;
    }

    /**
     * @inheritDoc
     */
    public function saveDefaultPricingTable(array $data, int $userId): bool
    {
        $payload = ConfigurableFieldDataModel::fromArray($data);

        return $this->floorPricingRepository->setDefaultFloorPricing($payload, $userId);
    }

    /**
     * @param int $productId
     * @return array
     */
    public function getExportableIndustryServices(int $productId): array
    {
        $filteredServiceProductIds = ServiceProduct::query()
            ->where(ServiceProduct::FIELD_PRODUCT_ID, $productId)
            ->pluck(ServiceProduct::FIELD_ID)
            ->toArray();

        $industryServicesWithPriceEntries = ProductStateFloorPrice::query()
            ->distinct(ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID)
            ->whereIn(ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID, $filteredServiceProductIds)
            ->pluck(ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID)
            ->toArray();

        $services = IndustryService::query()
            ->with(IndustryService::RELATION_INDUSTRY)
            ->whereIn(IndustryService::FIELD_ID, $industryServicesWithPriceEntries)
            ->get();

        $industries = $services->reduce(function ($output, IndustryService $industryService) {
            if (!($output->search(fn(Industry $industry) => $industry->id === $industryService->industry_id) > -1)) {
                $output->push($industryService->industry);
            }
            return $output;
        }, collect());

        return [$industries, $services];
    }

    /**
     * @inheritDoc
     */
    public function importAllPricesFromServiceProduct(int $fromServiceProductId, int $toServiceProductId): bool
    {
        $this->deleteAllPricesForServiceProductId($toServiceProductId);

        $statePrices = BasePricingBuilder::query()
            ->forServiceProduct($toServiceProductId)
            ->get()
            ->map(function (ProductStateFloorPrice $price) use ($toServiceProductId) {
                $price->service_product_id = $toServiceProductId;
                return $price;
            })->toArray();

        if (ProductStateFloorPrice::query()->insert($statePrices)) {
            DB::beginTransaction();
            try {
                ProductCountyFloorPrice::query()
                    ->select([
                        ProductCountyFloorPrice::FIELD_STATE_LOCATION_ID,
                        ProductCountyFloorPrice::FIELD_COUNTY_LOCATION_ID,
                        ProductCountyFloorPrice::FIELD_SALE_TYPE_ID,
                        ProductCountyFloorPrice::FIELD_PROPERTY_TYPE_ID,
                        ProductCountyFloorPrice::FIELD_QUALITY_TIER_ID,
                        ProductCountyFloorPrice::FIELD_PRICE,
                    ])->where(ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID, $fromServiceProductId)
                    ->chunk(2500, function (Collection $chunk) use ($toServiceProductId) {
                        $insertData = $chunk->map(function (ProductCountyFloorPrice $price) use ($toServiceProductId) {
                            $price->service_product_id = $toServiceProductId;
                            return $price;
                        })->toArray();

                        ProductCountyFloorPrice::query()
                            ->insert($insertData);
                    });
            } catch (Exception $e) {
                logger()->error($e->getMessage());
                DB::rollBack();

                return false;
            }

            DB::commit();

            return true;
        }

        return false;
    }

    /**
     * @inheritDoc
     */
    public function repairPricesForServiceProduct(int $serviceProductId): array
    {
        $errorMessage         = null;
        $result               = false;
        $defaultPricingConfig = $this->floorPricingRepository->getDefaultFloorPricing(true);

        if (!$defaultPricingConfig)
            $errorMessage = "Default pricing must be set up to repair floor prices.";
        else {
            $validQualityTierMap = $this->getQualityTierMapForServiceProduct($serviceProductId);
            $validSaleTypeMap    = $this->getSaleTypeMapForServiceProduct($serviceProductId, SaleTypeEnum::RETURN_TYPE_NAME);
            $propertyTypeIds     = PropertyType::query()->pluck(PropertyType::FIELD_ID)->toArray();
            $stateIds            = $this->getStateLocationIds();
            $productName         = ServiceProduct::query()->find($serviceProductId)->product->name;

            $repairInserts     = [];
            $failedRepairCount = 0;

            foreach ($stateIds as $stateId) {
                $currentStatePrices = ProductStateFloorPrice::query()
                    ->select([
                        ProductStateFloorPrice::FIELD_STATE_LOCATION_ID,
                        ProductStateFloorPrice::FIELD_SALE_TYPE_ID,
                        ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID,
                        ProductStateFloorPrice::FIELD_QUALITY_TIER_ID,
                        ProductStateFloorPrice::FIELD_PRICE,
                    ])
                    ->where([
                        ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID => $serviceProductId,
                        ProductStateFloorPrice::FIELD_STATE_LOCATION_ID  => $stateId,
                    ])->get();
                foreach (array_keys($validQualityTierMap) as $qualityTierId) {
                    foreach ($propertyTypeIds as $propertyTypeId) {
                        foreach (array_keys($validSaleTypeMap) as $saleTypeId) {
                            $priceExists = $currentStatePrices->where(ProductStateFloorPrice::FIELD_QUALITY_TIER_ID, $qualityTierId)
                                ->where(ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID, $propertyTypeId)
                                ->where(ProductStateFloorPrice::FIELD_SALE_TYPE_ID, $saleTypeId)
                                ->first();

                            if ($priceExists?->price > 0) continue;

                            $defaultPrice = $this->getNestedPrice($defaultPricingConfig, [$productName, $validQualityTierMap[$qualityTierId], $validSaleTypeMap[$saleTypeId]]);
                            if ($defaultPrice > 0) {
                                $repairInserts[] = [
                                    ProductStateFloorPrice::FIELD_STATE_LOCATION_ID  => $stateId,
                                    ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID => $serviceProductId,
                                    ProductStateFloorPrice::FIELD_SALE_TYPE_ID       => $saleTypeId,
                                    ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID   => $propertyTypeId,
                                    ProductStateFloorPrice::FIELD_QUALITY_TIER_ID    => $qualityTierId,
                                    ProductStateFloorPrice::FIELD_PRICE              => $defaultPrice,
                                ];
                            } else
                                $failedRepairCount++;
                        }
                    }
                }
            }

            if ($repairInserts) {
                $result = ProductStateFloorPrice::query()
                    ->upsert($repairInserts, [], [ProductStateFloorPrice::FIELD_PRICE]);

                if ($failedRepairCount)
                    $errorMessage = count($repairInserts) . " prices were successfully updated, but $failedRepairCount failed. Are all price types set up in default pricing configuration?";
            } else {
                $errorMessage = $failedRepairCount
                    ? "All $failedRepairCount missing prices failed to update. Are all price types set up in default pricing configuration?"
                    : "No missing prices were found, nothing to repair.";
            }
        }

        return [
            $result,
            $errorMessage,
        ];
    }

    /**
     * @param array $targetArray
     * @param array $nestedKeys
     * @return float
     */
    protected function getNestedPrice(array &$targetArray, array $nestedKeys): float
    {
        $target = $targetArray;
        foreach ($nestedKeys as $key) {
            if (array_key_exists($key, $target))
                $target = $target[$key];
            else
                return 0;
        }

        return $target['price'] ?? 0;
    }

    /**
     * Remove county bids which are equal to their state bid. Supply a compare price to return affected count with no delete.
     *
     * @param array $statePriceIds
     * @param int $serviceProductId
     * @param int $qualityTierId
     * @param int $propertyTypeId
     * @param float|null $dryRunPrice
     * @return int
     */
    protected function removeRedundantCountyFloorPrices(array $statePriceIds, int $serviceProductId, int $qualityTierId, int $propertyTypeId, ?float $dryRunPrice = null): int
    {
        $baseQuery = ProductCountyFloorPrice::query()
            ->select(ProductCountyFloorPrice::FIELD_ID)
            ->join(ProductStateFloorPrice::TABLE, ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_STATE_LOCATION_ID, '=', ProductCountyFloorPrice::TABLE . '.' . ProductCountyFloorPrice::FIELD_STATE_LOCATION_ID)
            ->whereIn(ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_ID, $statePriceIds)
            ->where(ProductCountyFloorPrice::TABLE . '.' . ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID, $serviceProductId)
            ->where(ProductCountyFloorPrice::TABLE . '.' . ProductCountyFloorPrice::FIELD_QUALITY_TIER_ID, $qualityTierId)
            ->where(ProductCountyFloorPrice::TABLE . '.' . ProductCountyFloorPrice::FIELD_PROPERTY_TYPE_ID, $propertyTypeId);

        if ($dryRunPrice) {
            return $baseQuery->where(ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_PRICE, '=', $dryRunPrice)
                ->count();
        } else {
            return $baseQuery->whereColumn(ProductStateFloorPrice::TABLE . '.' . ProductStateFloorPrice::FIELD_PRICE, '=', ProductCountyFloorPrice::TABLE . '.' . ProductCountyFloorPrice::FIELD_PRICE)
                ->delete();
        }
    }

    /**
     * TODO: implement formulas if required
     *
     * @param int $serviceProductId
     * @return bool
     */
    public static function shouldUseFormulaPricing(int $serviceProductId): bool
    {
        return false;
    }

    /**
     * @param int $serviceProductId
     * @return void
     */
    public function deleteAllPricesForServiceProductId(int $serviceProductId): void
    {
        ProductStateFloorPrice::query()
            ->where(ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID, $serviceProductId)
            ->delete();

        ProductCountyFloorPrice::query()
            ->where(ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID, $serviceProductId)
            ->delete();
    }

    /**
     * @param int $serviceProductId
     * @param int $qualityTierId
     * @param int $propertyTypeId
     * @return void
     */
    public function deleteScopedPrices(int $serviceProductId, int $qualityTierId, int $propertyTypeId): void
    {
        ProductStateFloorPrice::query()
            ->where([
                ProductStateFloorPrice::FIELD_SERVICE_PRODUCT_ID => $serviceProductId,
                ProductStateFloorPrice::FIELD_QUALITY_TIER_ID    => $qualityTierId,
                ProductStateFloorPrice::FIELD_PROPERTY_TYPE_ID   => $propertyTypeId,
            ])->delete();

        ProductCountyFloorPrice::query()
            ->where([
                ProductCountyFloorPrice::FIELD_SERVICE_PRODUCT_ID => $serviceProductId,
                ProductCountyFloorPrice::FIELD_QUALITY_TIER_ID    => $qualityTierId,
                ProductCountyFloorPrice::FIELD_PROPERTY_TYPE_ID   => $propertyTypeId,
            ])->delete();
    }

    /**
     * @return array
     */
    protected function getStateLocationIds(): array
    {
        return Location::query()
            ->where(Location::TYPE, Location::TYPE_STATE)
            ->pluck(Location::ID)
            ->toArray();
    }

    /**
     * @param int $serviceProductId
     * @param string|null $stringReturnType
     * @return array
     */
    protected function getSaleTypeMapForServiceProduct(int $serviceProductId, ?string $stringReturnType = SaleTypeEnum::RETURN_TYPE_KEY): array
    {
        $validSaleTypes = SaleTypeEnum::byServiceProductId($serviceProductId, $stringReturnType);
        $compareColumn  = $stringReturnType === SaleTypeEnum::RETURN_TYPE_NAME
            ? SaleType::FIELD_NAME
            : SaleType::FIELD_KEY;

        return collect($validSaleTypes)->mapWithKeys(fn(string $saleTypeKey) => [
            (SaleType::query()->where($compareColumn, $saleTypeKey)->first()?->id ?? 0) => $saleTypeKey,
        ])->toArray();
    }

    /**
     * @param int $serviceProductId
     * @return array
     */
    protected function getQualityTierMapForServiceProduct(int $serviceProductId): array
    {
        $validQualityTiers = QualityTierEnum::byServiceProductId($serviceProductId);

        return collect($validQualityTiers)->mapWithKeys(fn(string $qualityTypeName) => [
            (QualityTier::query()->where(QualityTier::FIELD_NAME, $qualityTypeName)->first()?->id ?? 0) => $qualityTypeName,
        ])->toArray();
    }

    /**
     * @param array $currentPrices
     * @param array $proposedUpdates
     * @param int $serviceProductId
     * @return array|null
     */
    protected function getLoweredPrices(array $currentPrices, array $proposedUpdates, int $serviceProductId): ?array
    {
        $output = [];
        if (!$currentPrices || !$proposedUpdates)
            return null;

        $saleTypeMap = $this->getSaleTypeMapForServiceProduct($serviceProductId);
        foreach ($currentPrices as $saleTypeId => $price) {
            $newPrice = $proposedUpdates[$saleTypeMap[$saleTypeId]] ?? null;
            if ($newPrice && $newPrice < $price)
                $output[$saleTypeId] = $price;
        }

        return $output ?: null;
    }

    /**
     * @param array $loweredPricePolicy
     * @return LoweredFloorPricePolicy|null
     */
    protected function validateLoweredPricePolicy(array $loweredPricePolicy): ?LoweredFloorPricePolicy
    {
        $policyType = LoweredFloorPricePolicy::tryfrom($loweredPricePolicy[FloorPricingController::REQUEST_LOWERED_PRICE_POLICY_TYPE]);
        if ($policyType) {
            $requiredFields = $policyType->getRequiredPolicyFields();
            foreach ($requiredFields as $requiredField) {
                if (!array_key_exists($requiredField, $loweredPricePolicy)) {
                    return null;
                }
            }
            return $policyType;
        }

        return null;
    }

    /**
     * @param int $serviceProductId
     * @return void
     */
    private function dispatchCalculateLowBidJob(int $serviceProductId): void
    {
        /** @var ServiceProduct $serviceProduct */
        $serviceProduct = ServiceProduct::query()
            ->findOrFail($serviceProductId);
        $industry       = $serviceProduct
            ->service
            ->industry;

        CalculateCompanyCampaignLowBidFlagForFloorPriceChangeJob::dispatch($industry);
    }
}
