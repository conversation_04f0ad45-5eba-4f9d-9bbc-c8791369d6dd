<?php

namespace App\Services\Odin\Campaigns;

use App\ConsumerProcessing\Services\ConsumerProjectProcessingService;
use App\Contracts\ProductAssignment\MultiProductAssignmentStrategyContract;
use App\DataModels\ProductAssignment\ProposedProductAssignment;
use App\Models\Odin\ConsumerProduct;

class FutureCampaignAvailabilityService
{
    public function __construct(
        protected ConsumerProjectProcessingService $consumerProjectProcessingService,
        protected MultiProductAssignmentStrategyContract $assignmentStrategyContract
    ) {}

    /**
     * @param ConsumerProduct $consumerProduct
     *
     * @return int
     */
    public function companiesWithAvailableBudgetCount(ConsumerProduct $consumerProduct): int
    {
        $consumerProject = $this->consumerProjectProcessingService->prepareConsumerProject($consumerProduct->consumer, $consumerProduct->address);
        $potentialCampaigns = $this->consumerProjectProcessingService->getAvailableCampaigns($consumerProject);

        if ($potentialCampaigns->isEmpty()) {
            return 0;
        }

        return $this->assignmentStrategyContract->calculate(
            $consumerProject,
            $potentialCampaigns,
            $this->consumerProjectProcessingService->getPotentialProductTypes($consumerProject),
            []
        )
            ->filter(fn(ProposedProductAssignment $proposedProductAssignment) => !$proposedProductAssignment->isExistingAssignment)
            ->count();
    }
}
