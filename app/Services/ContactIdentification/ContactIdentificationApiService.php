<?php

namespace App\Services\ContactIdentification;

use App\Enums\ContactIdentification\ContactType;
use App\Enums\ContactIdentification\IdentifiableModelType;
use App\Enums\ContactIdentification\IdentificationStatus;
use App\Enums\ContactIdentification\SearchableFieldType;
use App\Http\Resources\ContactIdentification\IdentifiedContactResource;
use App\Models\ContactIdentification\IdentifiedContact;
use App\Models\ContactIdentification\PossibleContact;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Model;

class ContactIdentificationApiService
{
    /**
     * @param ContactType $contactRelationType
     * @param int $contactRelationId
     * @param string $identifierValue
     * @param SearchableFieldType $identifierFieldType
     * @param $entity
     * @return IdentifiedContact
     */
    public function identifyAndNominateContact(
        ContactType $contactRelationType,
        int $contactRelationId,
        string $identifierValue,
        SearchableFieldType $identifierFieldType,
        Model $entity
    ): IdentifiedContact
    {
        /** @var IdentifiedContact $identifiedContact */
        $identifiedContact = IdentifiedContact::query()->firstOrCreate([
            IdentifiedContact::FIELD_IDENTIFIER_VALUE       => $identifierValue,
            IdentifiedContact::FIELD_IDENTIFIER_FIELD_TYPE  => $identifierFieldType->value,
        ], [
            IdentifiedContact::FIELD_IDENTIFICATION_STATUS  => IdentificationStatus::SINGLE_RESULT,
        ]);

        /** @var PossibleContact $possibleContact */
        $possibleContact = PossibleContact::query()->updateOrCreate([
            PossibleContact::FIELD_RELATION_ID           => $contactRelationId,
            PossibleContact::FIELD_RELATION_TYPE         => $contactRelationType->getModelClass(),
            PossibleContact::FIELD_IDENTIFIED_CONTACT_ID => $identifiedContact->id,
        ]);

        $this->nominateContact($identifiedContact, $possibleContact);

        $entity->update([
            $entity->getFieldIdentifiedContactIdField() => $identifiedContact->id,
        ]);

        return $identifiedContact;
    }

    /**
     * @param IdentifiedContact $identifiedContact
     * @param PossibleContact $possibleContact
     * @return bool
     */
    public function nominateContact(
        IdentifiedContact $identifiedContact,
        PossibleContact $possibleContact,
    ): bool
    {
        $identifiedContact->update([
            IdentifiedContact::FIELD_NOMINATED_CONTACT_ID => $possibleContact->id,
            IdentifiedContact::FIELD_IDENTIFICATION_STATUS => IdentificationStatus::SINGLE_RESULT,
        ]);

        return true;
    }
}
