<?php

namespace App\Services;

use App\Models\Phone;

class CallForwardingService
{

    /**
     * @param string $originToNumber
     * @return string|null
     */
    public function getForwardingNumber(string $originToNumber): ?string
    {
        return Phone::where(Phone::FIELD_PHONE, $originToNumber)
                    ->first()
                    ?->primaryUser()
                    ?->callForwarding
                    ?->forward_to_number;
    }
}
