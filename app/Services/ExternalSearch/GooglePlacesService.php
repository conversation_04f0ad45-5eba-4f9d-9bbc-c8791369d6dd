<?php

namespace App\Services\ExternalSearch;

use App\Enums\CompanyConsolidatedStatus;
use App\Models\Odin\Company;
use App\Models\USZipCode;
use App\Repositories\Odin\CompanyRepository;
use App\Services\CompanyDiscoveryService;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;

class GooglePlacesService
{
    const BASE_API_URI          = 'https://maps.googleapis.com/maps/api/place';
    const NEARBY_SEARCH_API_URI = '/nearbysearch/json'; // https://developers.google.com/maps/documentation/places/web-service/search-nearby
    const PLACE_DETAILS_API_URI = '/details/json'; // https://developers.google.com/maps/documentation/places/web-service/details

    const API_FIELD_KEY      = 'key';
    const API_FIELD_RADIUS   = 'radius';
    const API_FIELD_TYPE     = 'type';
    const API_FIELD_KEYWORD  = 'keyword';
    const API_FIELD_LOCATION = 'location';
    const API_FIELD_PLACE_ID = 'place_id';
    const API_FIELD_FIELDS   = 'fields';

    const API_KEY_RESULT             = 'result';
    const API_KEY_RESULTS            = 'results';
    const API_KEY_WEBSITE            = 'website';
    const API_KEY_PLACE_ID           = 'place_id';
    const API_KEY_BUSINESS_STATUS    = 'business_status';
    const API_KEY_NEARBY_LATITUDE    = 'geometry.location.lat';
    const API_KEY_NEARBY_LONGITUDE   = 'geometry.location.lng';
    const API_KEY_PHONE_NUMBER       = 'formatted_phone_number';
    const API_KEY_USER_RATINGS_TOTAL = 'user_ratings_total';
    const API_KEY_RATING             = 'rating';

    const BUSINESS_STATUS_CLOSED_PERMANENTLY = "CLOSED_PERMANENTLY";

    const API_KEY_STREET_NUMBER = 'street_number';
    const API_KEY_STREET        = 'route';
    const API_KEY_CITY          = 'locality';
    const API_KEY_STATE         = 'administrative_area_level_1';
    const API_KEY_COUNTRY       = 'country';
    const API_KEY_ZIP_CODE      = 'postal_code';

    const DEFAULT_RADIUS = 8000;
    const COUNTY_RADIUS  = 16000;

    const PLACE_DETAILS_REQUEST_FIELDS = 'address_component,business_status,name,formatted_phone_number,website,user_ratings_total,rating'; // https://developers.google.com/maps/documentation/places/web-service/details

    /**
     * These are based on previous searches done and should eventually be database driven
     */
    const INDUSTRY_SEARCH_PARAMS = [
        GooglePlacesService::API_FIELD_TYPE => [
            'solar' => null,
            'roofing' => 'roofing_contractor',
        ],
        GooglePlacesService::API_FIELD_KEYWORD => [
            'solar' => 'solar installer',
            'roofing' => null,
        ]
    ];

    /** @var CompanyRepository $companyRepository */
    protected CompanyRepository $companyRepository;

    /**
     * @param CompanyRepository $companyRepository
     */
    public function __construct(CompanyRepository $companyRepository)
    {
        $this->companyRepository = $companyRepository;
    }

    /**
     * @param string $placeId
     * @return array|null
     */
    public function getCompanyDetailsByPlaceId(string $placeId): ?array
    {
        $url = self::BASE_API_URI . self::PLACE_DETAILS_API_URI;

        $requestArray = [
            self::API_FIELD_KEY      => config('services.google.company_discovery.places_api_key'),
            self::API_FIELD_FIELDS   => self::PLACE_DETAILS_REQUEST_FIELDS,
            self::API_FIELD_PLACE_ID => $placeId
        ];

        $response = Http::get($url, $requestArray);

        return Arr::get($response->json(),self::API_KEY_RESULT);
    }

    /**
     * @param float $latitude
     * @param float $longitude
     * @param array $options
     * @return array|null
     */
    public function getCompaniesInLocation(float $latitude, float $longitude, array $options): ?array
    {
        $url = self::BASE_API_URI . self::NEARBY_SEARCH_API_URI;

        $requestArray = array_merge($options, [
            self::API_FIELD_LOCATION => "{$latitude},{$longitude}",
        ]);

        $response = Http::get($url, $requestArray);

        return Arr::get($response->json(),self::API_KEY_RESULTS);
    }

    /**
     * @param string $zipCode
     * @param string $industry
     * @param bool $isCountySearch
     * @return array|null
     */
    public function getResultsByZipCodeAndIndustry(string $zipCode, string $industry, bool $isCountySearch = false): ?array
    {
        /** @var USZipCode $zipCode */
        $zipCode = USZipCode::query()->where(USZipCode::FIELD_ZIP_CODE, $zipCode)->first();
        $options = $this->getNearbySearchRequestArray($industry, $isCountySearch);

        $companies = [];

        if($zipCode instanceof USZipCode) {
            $companies = $this->getCompaniesInLocation($zipCode->latitude, $zipCode->longitude, $options);
        }

        if($companies){
            $placeIds = Arr::pluck($companies, 'place_id');
            $accountManagers = $this->companyRepository->getCompaniesAndAccountManagersByPlaceIdIfExists($placeIds);

            $companies = Arr::map($companies, function ($value, $key) use ($accountManagers) {
                $accountManager = $accountManagers->where('place_id',Arr::get($value, 'place_id'))->first();
                if($accountManager) {
                    $data = [
                        'account_manager' => $accountManager->account_manager_name,
                        'website' => $accountManager->website,
                        'company_status' => CompanyConsolidatedStatus::label($accountManager->company_consolidated_status),
                    ];
                } else {
                    //** todo: Enable auto import of new companies after round robin script has been implemented */
                    // dispatch(new ProcessGooglePlaceJob($value));

                    $data = [
                        'account_manager' => null,
                        'website' => null,
                        'company_status' => null,
                    ];
                }
                return array_merge($value, $data);
            });
        }

        return $companies;
    }

    /**
     * @param string $industry
     * @param bool $isCountySearch
     * @return array
     */
    private function getNearbySearchRequestArray(string $industry, bool $isCountySearch): array
    {
        $options = [
            self::API_FIELD_KEY    => config('services.google.company_discovery.places_api_key'),
            self::API_FIELD_RADIUS   => $isCountySearch ? self::COUNTY_RADIUS : self::DEFAULT_RADIUS
        ];

        $industry = strtolower($industry);

        if (self::INDUSTRY_SEARCH_PARAMS[self::API_FIELD_TYPE][$industry]) {
            $options = array_merge($options, [
                self::API_FIELD_TYPE => self::INDUSTRY_SEARCH_PARAMS[self::API_FIELD_TYPE][$industry]
            ]);
        }

        if (self::INDUSTRY_SEARCH_PARAMS[self::API_FIELD_KEYWORD][$industry]) {
            $options = array_merge($options, [
                self::API_FIELD_KEYWORD => self::INDUSTRY_SEARCH_PARAMS[self::API_FIELD_KEYWORD][$industry]
            ]);
        }

        return $options;
    }
}
