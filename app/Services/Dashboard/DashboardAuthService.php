<?php

namespace App\Services\Dashboard;

use App\Enums\ActioningUserType;
use App\Enums\Company\CompanyAdminStatus;
use App\Http\Controllers\DashboardAPI\AuthenticationController;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Models\User;
use App\Services\Dashboard\JWT\DashboardJWTService;
use App\Services\Dashboard\JWT\DashboardTokenModel;
use Exception;
use Illuminate\Auth\AuthenticationException;
use Illuminate\Database\Query\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Spatie\Activitylog\Facades\CauserResolver;

/**
 * Registered as a singleton for helping w/ getting the user & the shadower.
 */
class DashboardAuthService
{
    protected ?int                 $userId     = null;
    protected ?int                 $shadowerId = null;
    protected ?DashboardTokenModel $token      = null;

    public function __construct(protected DashboardJWTService $service) {}

    /**
     * @param int|null $userId
     * @return DashboardAuthService
     */
    public function setUserId(?int $userId): DashboardAuthService
    {
        $this->userId = $userId;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getUserId(): ?int
    {
        return $this->userId;
    }

    /**
     * @param int|null $shadowerId
     * @return DashboardAuthService
     */
    public function setShadowerId(?int $shadowerId): DashboardAuthService
    {
        $this->shadowerId = $shadowerId;
        return $this;
    }

    /**
     * @return int|null
     */
    public function getShadowerId(): ?int
    {
        return $this->shadowerId;
    }

    /**
     * @param DashboardTokenModel|null $token
     * @return DashboardAuthService
     */
    public function setToken(?DashboardTokenModel $token): DashboardAuthService
    {
        $this->token = $token;
        return $this;
    }

    /**
     * @return DashboardTokenModel|null
     */
    public function getToken(): ?DashboardTokenModel
    {
        return $this->token;
    }

    /**
     * Set the actioning user for the request session for tracking campaign changes
     *
     * @return void
     */
    public function setActioningUser(): void
    {
        $shadowerId = $this->getShadowerId();
        if ($shadowerId) {
            /** @var User $user */
            $user     = User::query()->find($shadowerId);
            $userType = ActioningUserType::ADMIN2_SHADOW_USER;
            $legacyId = $user->legacy_user_id;
        }
        else {
            /** @var CompanyUser $user */
            $user = CompanyUser::query()->find($this->getUserId());
            $userType = ActioningUserType::COMPANY_USER;
            $legacyId = $user->legacy_id;
        }

        if ($user) {
            Auth::setActioningUserType($userType);
            Auth::setActioningUserId($user->id);
            Auth::setActioningUserLegacyId($legacyId);

            CauserResolver::setCauser($user);
        }
    }

    /**
     * Authenticates a user.
     *
     * @throws AuthenticationException
     */
    public function authenticate(string $email, string $password): string
    {
        $user = $this->getCompanyUserByEmail($email);

        if (!$user) {
            $migratingUser = $this->getMigratingCompanyUserByEmail($email);
            if ($migratingUser instanceof CompanyUser) {
                return AuthenticationController::AUTH_MIGRATE_PREFIX . AuthenticationController::AUTH_MIGRATE_USER;
            }
            else if ($migratingUser > 0) {
                return AuthenticationController::AUTH_MIGRATE_PREFIX . AuthenticationController::AUTH_MIGRATE_ERROR;
            }
        }

        if(!$user)
            throw new AuthenticationException("Invalid Credentials.");

        if(!$this->doCredentialsMatch($email, $password, $user))
            throw new AuthenticationException("Invalid Credentials.");

        if($user->company) {
            switch($user->company->admin_status) {
                case CompanyAdminStatus::ARCHIVED:
                    throw new AuthenticationException("Your account is not active.");

                case CompanyAdminStatus::COLLECTIONS:
                    throw new AuthenticationException("Your account is suspended.");
            }
        }

        return $this->service->generate($user->id);
    }

    /**
     * Handles authenticating a specific user, typically via a token.
     *
     * @param CompanyUser $user
     * @return string
     * @throws AuthenticationException
     */
    public function authenticateUser(CompanyUser $user, ?User $shadower = null): string
    {
        if($user->company) {
            switch($user->company->admin_status) {
                case CompanyAdminStatus::ARCHIVED:
                    throw new AuthenticationException("Your account is not active.");

                case CompanyAdminStatus::COLLECTIONS:
                    throw new AuthenticationException("Your account is suspended.");
            }
        }

        return $shadower
            ? $this->service->generateShadowToken($user->id, $shadower->id)
            : $this->service->generate($user->id);
    }

    /**
     * @param string $email
     * @param string $token
     * @return CompanyUser
     * @throws Exception
     */
    public function getUserForPasswordReset(string $email, string $token): CompanyUser
    {
        $passwordReset = $this->getPasswordResetTable()
            ->where('email', $email)->latest()
            ->first();
        $validToken = $passwordReset && Hash::check($token, $passwordReset->token);

        if (!$validToken)
            throw new Exception("Cannot reset the password for this user.");

        $expiryMinutes = config('auth.passwords.company_users.expire', 60);
        $validExpiry = $passwordReset->created_at > now()->subMinutes($expiryMinutes);
        if (!$validExpiry)
            throw new Exception("The password reset token has expired");

        $user = $this->getCompanyUserByEmail($email);

        if (!$user) {
            $migratingUser = $this->getMigratingCompanyUserByEmail($email);
            if ($migratingUser instanceof CompanyUser) {
                $user = $migratingUser;
            }
            else if ($migratingUser > 0) {
                throw new Exception("Cannot reset the password for this user at this time. <NAME_EMAIL> for assistance.");
            }
        }
        if(!$user)
            throw new Exception("Could not locate the requested user.");

        return $user;
    }

    /**
     * @param string $email
     * @return void
     */
    public function cleanUpPasswordResets(string $email): void
    {
        $this->getPasswordResetTable()
            ->where('email', $email)
            ->delete();
    }

    protected function doCredentialsMatch(string $email, string $password, CompanyUser $user): bool
    {
        return Hash::check($password, $user->password);
    }

    /**
     * @param string $email
     * @return CompanyUser|null
     */
    protected function getCompanyUserByEmail(string $email): ?CompanyUser
    {
        /** @var CompanyUser|null */
        return CompanyUser::query()
            ->where(CompanyUser::FIELD_EMAIL, $email)
            ->where(CompanyUser::FIELD_STATUS, '=', CompanyUser::STATUS_ACTIVE)
            ->where(CompanyUser::FIELD_CAN_LOG_IN, '=', 1)
            ->where(CompanyUser::FIELD_AUTHENTICATION_TYPE, CompanyUser::AUTHENTICATION_TYPE_ADMIN2)
            ->latest()
            ->first();
    }

    /**
     * Attempt to find a CompanyUser who is migrating from a legacy dashboard
     * We can't be sure of their Admin2.0 status, and email addresses are not unique
     * So we'll try a variety of constraints to narrow down to the correct CompanyUser
     * Returns a single CompanyUser on success, otherwise an integer representing the number of matches
     *
     * 0 should be handled as any other non-existent user
     * Higher number indicates dupe email addresses in the DB which need to be manually addressed
     *
     * @param string $email
     * @return CompanyUser|int
     */
    protected function getMigratingCompanyUserByEmail(string $email): CompanyUser|int {
        $migratingUser = 0;

        $basePotentialUsers = CompanyUser::query()
            ->where(CompanyUser::FIELD_EMAIL, $email)
            ->where(CompanyUser::FIELD_AUTHENTICATION_TYPE, CompanyUser::AUTHENTICATION_TYPE_LEGACY);
        if ($basePotentialUsers->count() === 1)
            $migratingUser = $basePotentialUsers->first();
        else if ($basePotentialUsers->count() > 1) {
            $potentialUsers = (clone $basePotentialUsers)->where(CompanyUser::FIELD_CAN_LOG_IN, CompanyUser::USER_ALLOWED_TO_LOGIN);
            if (!$potentialUsers->count())
                $potentialUsers = $basePotentialUsers->where(CompanyUser::FIELD_IS_CONTACT, false);

            if ($potentialUsers->count() === 1)
                $migratingUser = $potentialUsers->first();
            else if ($potentialUsers->count() > 1) {
                $potentialUsers->where(CompanyUser::FIELD_STATUS, CompanyUser::STATUS_ACTIVE);
                if ($potentialUsers->count() === 1) {
                    $migratingUser = $potentialUsers->first();
                }
                else if ($potentialUsers->count() > 1) {
                    if ($this->allCompanyNamesAreSimilar($potentialUsers->get())) {
                        // The lowest company id should be the solar company if this email address is attached to multiple companies
                        $migratingUser = $potentialUsers->orderBy(CompanyUser::FIELD_COMPANY_ID)->first();
                    }
                    else
                        $migratingUser = $potentialUsers->count();
                }
            }
        }

        return $migratingUser;
    }

    /**
     * Ensure this email isn't attached to multiple companies
     * Matches only on the first word in the name, to avoid a returning negative for companies currently split into two for different dashboard
     * e.g. Awesome Solar Inc, Awesome Roofing Inc
     *
     * @param Collection $users
     * @return bool
     */
    private function allCompanyNamesAreSimilar(Collection $users): bool {
        $firstFirstWord = null;
        foreach ($users as $user) {
            $companyName = trim($user->company->name);
            $firstWord = preg_replace("/[^0-z].*/", "", $companyName);
            if ($firstFirstWord === null)
                $firstFirstWord = $firstWord;
            else
                if ($firstFirstWord !== $firstWord) return false;;
        }

        return true;
    }

    /**
     * @return Builder
     */
    private function getPasswordResetTable(): Builder
    {
        return DB::table(config('auth.passwords.company_users.table', 'password_resets'));
    }
}
