<?php

namespace App\Services\Campaigns;

use App\Enums\Campaigns\BudgetAverageCostGroupBy;
use App\Enums\Campaigns\CampaignStatus;
use App\Enums\Campaigns\Modules\Budget\BudgetType;
use App\Enums\Campaigns\PauseReason;
use App\Enums\Timezone;
use App\Jobs\UnpauseFutureCampaignJob;
use App\Mediators\CampaignMediator;
use App\Models\Campaigns\CampaignReactivation;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\IndustryService;
use App\Repositories\Campaigns\Modules\Budget\BudgetRepository;
use App\Repositories\Odin\Campaigns\CompanyCampaignRepository;
use App\Services\OutreachCadence\TimeZoneHelperService;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;

class CompanyCampaignService
{
    const string PAYLOAD_STATUSES               = 'statuses';
    const string PAYLOAD_REASONS                = 'reasons';
    const string FIELD_SORT_BY_NAME             = 'campaign_name';
    const string FIELD_SORT_BY_SPENT_TODAY      = 'spend_today';
    const string FIELD_SORT_BY_AVG_DAILY_SPEND  = 'avg_daily_spend';
    const string FIELD_SORT_BY_DAILY_BUDGET     = 'daily_budget';
    const string FIELD_SORT_BY_BUDGET_USAGE     = 'budget_usage';
    const string FIELD_SORT_BY_MAX_BUDGET_USAGE = 'max_budget_usage';
    const string BUDGET_ID                      = 'budget_id';
    const string BUDGET_TYPE                    = 'budget_type';
    const string BUDGET_VALUE                   = 'budget_value';
    const string CAMPAIGN_ID                    = 'campaign_id';
    const string CAMPAIGN_REFERENCE             = 'campaign_reference';
    const string CAMPAIGN_PAUSED_AT             = 'campaign_paused_at';
    const string CAMPAIGN_NAME                  = 'campaign_name';
    const string BUDGET_DISPLAY_NAME            = 'budget_display_name';
    const string CAMPAIGN_STATUS                = 'campaign_status';
    const string SPEND_TODAY                    = 'spend_today';
    const string AVERAGE_DAILY_SPEND            = 'average_daily_spend';
    const string BUDGET_USAGE_TODAY             = 'budget_usage_today';
    const string MAXIMUM_BUDGET_USAGE           = 'maximum_budget_usage';
    const string BUDGET_SPEND                   = 'budget_spend';
    const string AVERAGE_SPEND                  = 'average_spend';
    const string BUDGET_USAGE                   = 'budget_usage';

    const array SORT_BY_FIELD_MAP = [
        self::FIELD_SORT_BY_NAME             => 'campaign_name',
        self::FIELD_SORT_BY_SPENT_TODAY      => 'spend_today',
        self::FIELD_SORT_BY_AVG_DAILY_SPEND  => 'average_daily_spend',
        self::FIELD_SORT_BY_DAILY_BUDGET     => 'budget_type',
        self::FIELD_SORT_BY_BUDGET_USAGE     => 'budget_usage_today',
        self::FIELD_SORT_BY_MAX_BUDGET_USAGE => 'maximum_budget_usage',
    ];

    public function __construct(
        protected CampaignMediator          $campaignMediator,
        protected CompanyCampaignRepository $campaignRepository,
        protected BudgetRepository          $budgetRepository,
    )
    {
    }

    /**
     * @return array
     */
    public function getCampaignStatusConfig(): array
    {
        return [
            self::PAYLOAD_STATUSES => CampaignStatus::displayNames(),
            self::PAYLOAD_REASONS  => PauseReason::displayNames(),
        ];
    }

    /**
     * Currently, Pause reasons only exist on the reactivation model, so we create one even if the campaign is permanently paused
     * @param CompanyCampaign $campaign
     * @param CampaignStatus $newStatus
     * @param CampaignStatus $oldStatus
     * @param string $reason
     * @param ?Carbon $reactivateAt
     * @return bool
     * @throws Exception
     */
    public function pauseCampaign(CompanyCampaign $campaign, CampaignStatus $newStatus, CampaignStatus $oldStatus, string $reason, ?Carbon $reactivateAt = null): bool
    {
        if ($this->updateCampaignStatus($campaign, $newStatus, $oldStatus)) {
            if ($newStatus === CampaignStatus::PAUSED_TEMPORARILY && !$reactivateAt) {
                throw new Exception("A valid reactivation date must be supplied for a temporary pause.");
            }

            $reactivateTimestamp = $reactivateAt ?? null;

            CampaignReactivation::query()
                ->withTrashed()
                ->updateOrCreate([
                    CampaignReactivation::FIELD_CAMPAIGN_ID => $campaign->id
                ], [
                    CampaignReactivation::FIELD_REACTIVATE_AT => $reactivateTimestamp,
                    CampaignReactivation::FIELD_REASON        => $reason,
                    CampaignReactivation::FIELD_DELETED_AT    => null,
                    Model::CREATED_AT                         => now(),
                ]);

            if ($newStatus === CampaignStatus::PAUSED_TEMPORARILY) {
                UnpauseFutureCampaignJob::dispatch($campaign->id)->delay($reactivateTimestamp);
            }

            return true;
        }

        return false;
    }

    /**
     * @param CompanyCampaign $campaign
     * @param CampaignStatus $oldStatus
     * @return bool
     * @throws BindingResolutionException
     */
    public function unpauseCampaign(CompanyCampaign $campaign, CampaignStatus $oldStatus): bool
    {
        if ($this->updateCampaignStatus($campaign, CampaignStatus::ACTIVE, $oldStatus)) {
            $campaign->reactivation()->delete();

            return true;
        }

        return false;
    }

    /**
     * @param CompanyCampaign $campaign
     * @param CampaignStatus $newStatus
     * @param CampaignStatus $oldStatus
     * @return bool
     * @throws BindingResolutionException
     */
    public function updateCampaignStatus(CompanyCampaign $campaign, CampaignStatus $newStatus, CampaignStatus $oldStatus): bool
    {
        if ($this->campaignRepository->changeStatus($campaign, $newStatus, $oldStatus)) {
            return $this->campaignMediator->statusChange($campaign, $oldStatus, $newStatus);
        }

        return false;
    }

    /**
     * @param CompanyCampaign $campaign
     * @return bool
     * @throws BindingResolutionException
     */
    public function deleteCampaign(CompanyCampaign $campaign): bool
    {
        $this->campaignMediator->delete($campaign);

        return !$campaign->exists || $this->campaignRepository->delete($campaign);
    }

    /**
     * @return void
     */
    public function reactivateTemporarilyPausedCampaigns(): void
    {
        CompanyCampaign::query()
            ->select(CompanyCampaign::TABLE . '.*')
            ->join(
                CampaignReactivation::TABLE,
                CampaignReactivation::TABLE . '.' . CampaignReactivation::FIELD_CAMPAIGN_ID,
                '=',
                CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_ID
            )
            ->where(CompanyCampaign::FIELD_STATUS, CampaignStatus::PAUSED_TEMPORARILY)
            ->whereNull(CampaignReactivation::TABLE . '.' . CampaignReactivation::FIELD_DELETED_AT)
            ->whereNotNull(CampaignReactivation::TABLE . '.' . CampaignReactivation::FIELD_REACTIVATE_AT)
            ->where(CampaignReactivation::TABLE . '.' . CampaignReactivation::FIELD_REACTIVATE_AT, '<=', now())
            ->each(fn(CompanyCampaign $companyCampaign) => $this->unpauseCampaign($companyCampaign, $companyCampaign->status));
    }

    /**
     * @param CompanyCampaign $campaign
     * @param bool $enable
     * @return bool
     * @throws Exception
     */
    public function toggleCustomFloorPricing(CompanyCampaign $campaign, bool $enable): bool
    {
        if ($enable) {
            $industryEnabled = $campaign->service->industry->industryConfiguration?->allow_custom_floor_prices ?? false;
            if (!$industryEnabled)
                throw new Exception("This campaign's industry does not support custom floor prices.");
        }

        $campaign->uses_custom_floor_prices = $enable;

        return $campaign->save();
    }

    /**
     * Disable ALL custom floor pricing on campaigns in this Industry. This is called when an Industry has the flag disabled in its configuration
     *
     * @param int $industryId
     * @return bool
     */
    public function disableCustomFloorPricingForIndustry(int $industryId): bool
    {
        return CompanyCampaign::query()
            ->join(IndustryService::TABLE, IndustryService::TABLE . '.' . IndustryService::FIELD_ID, '=', CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_SERVICE_ID)
            ->where(IndustryService::FIELD_INDUSTRY_ID, $industryId)
            ->where(CompanyCampaign::TABLE . '.' . CompanyCampaign::FIELD_USES_CUSTOM_FLOOR_PRICES, true)
            ->update([
                CompanyCampaign::FIELD_USES_CUSTOM_FLOOR_PRICES => false,
            ]);
    }

    /**
     * @param int $companyId
     * @param array $filters
     * @return LengthAwarePaginator
     */
    public function getCampaignOverviewPaginated(int $companyId, array $filters): LengthAwarePaginator
    {
        $campaigns = $this->campaignRepository->getCampaignOverview(companyId: $companyId);

        $budgetIds = $campaigns->pluck(self::BUDGET_ID)->unique()->values()->toArray();

        $budgetSpend = $this->budgetRepository->getBudgetSpend(budgetIds: $budgetIds);

        $averageDailySpend = $this->budgetRepository->getAverageBudgetSpend($budgetIds, now()->subMonth(), BudgetAverageCostGroupBy::DAY);

        $budgetUsage = $this->budgetRepository->getTodayBudgetUsage($campaigns, $budgetSpend);

        $formatted = $this->mergeCampaignOverviewData($campaigns, $budgetSpend, $averageDailySpend, $budgetUsage);

        if (isset($filters['sortBy'])) {
            if ($filters['sortBy'] !== self::FIELD_SORT_BY_DAILY_BUDGET) {
                $formatted = $formatted->sortBy(self::SORT_BY_FIELD_MAP[$filters['sortBy']], SORT_NATURAL, $filters['sortDir'] === 'desc');
            } else {//filtering by daily budget should prioritise unlimited budget highest, then most leads, then money or the opposite if
                $formatted = $formatted->sortBy(function ($campaign) use ($filters) {
                    if ($filters['sortDir'] === 'desc') {
                        if ($campaign[self::BUDGET_TYPE] === BudgetType::NO_LIMIT->value) {
                            return 0;
                        } else {
                            if ($campaign[self::BUDGET_TYPE] === BudgetType::TYPE_DAILY_UNITS->value) {
                                return 1;
                            } else {
                                return 2;
                            }
                        }
                    } else {
                        if ($campaign[self::BUDGET_TYPE] === BudgetType::TYPE_DAILY_SPEND->value) {
                            return 0;
                        } else {
                            if ($campaign[self::BUDGET_TYPE] === BudgetType::TYPE_DAILY_UNITS->value) {
                                return $campaign[self::BUDGET_VALUE];
                            } else {
                                return PHP_INT_MAX;
                            }
                        }
                    }
                });
            }
        }

        return self::paginateCollection($formatted, 10, $filters['page']);
    }

    /**
     * @param int $companyId
     * @return array
     */
    public function getCompanyBudgetUsage(int $companyId): array
    {
        $activeCampaigns = $this->campaignRepository->getCampaignOverview($companyId)->where('campaign_status', '=', CampaignStatus::ACTIVE->value);

        $unlimited = $activeCampaigns->where('budget_type', '=', BudgetType::NO_LIMIT->value)->isNotEmpty();

        $budgetIds = $activeCampaigns->pluck(self::BUDGET_ID)->unique()->values()->toArray();

        $budgetSpend = $this->budgetRepository->getBudgetSpend(budgetIds: $budgetIds);

        $budgetUsage = $this->budgetRepository->getTodayBudgetUsage($activeCampaigns, $budgetSpend);

        $formatted = $this->formatCompanyBudgetData($activeCampaigns, $budgetUsage);

        return [
            'unlimited' => $unlimited,
            'average'   => $formatted->filter(fn($campaign) => $campaign[self::BUDGET_TYPE] !== BudgetType::NO_LIMIT->value)->avg('budget_usage_today'),
            'data'      => $formatted,
        ];
    }

    public function formatCompanyBudgetData(Collection $campaigns, Collection $budgetUsage): Collection
    {
        return $campaigns->map(function ($campaign) use ($budgetUsage) {
            $budgetUsageToday = $budgetUsage->first(fn($item) => $item[self::BUDGET_ID] === $campaign[self::BUDGET_ID])[self::BUDGET_USAGE] ?? 0;
            return [
                self::CAMPAIGN_ID        => $campaign[self::CAMPAIGN_ID],
                self::CAMPAIGN_NAME      => $campaign[self::CAMPAIGN_NAME],
                self::BUDGET_USAGE_TODAY => $budgetUsageToday,
                self::BUDGET_TYPE        => $campaign[self::BUDGET_TYPE],
            ];
        })->groupBy(self::CAMPAIGN_ID)->map(function ($grouped, $id) {
            return [
                self::CAMPAIGN_ID        => $id,
                self::CAMPAIGN_NAME      => $grouped->first()[self::CAMPAIGN_NAME],
                self::BUDGET_USAGE_TODAY => $grouped->avg(self::BUDGET_USAGE_TODAY),
                self::BUDGET_TYPE        => $grouped->first()[self::BUDGET_TYPE],
            ];
        })->values();
    }

    /**
     * @param Collection $campaigns
     * @param Collection $todayBudgetSpend
     * @param Collection $averageBudgetSpend
     * @param Collection $budgetUsage
     * @return Collection
     */
    public function mergeCampaignOverviewData(
        Collection $campaigns,
        Collection $todayBudgetSpend,
        Collection $averageBudgetSpend,
        Collection $budgetUsage,
    ): Collection
    {
        return $campaigns->map(function ($campaign) use ($todayBudgetSpend, $averageBudgetSpend, $budgetUsage) {
            $spendToday        = $todayBudgetSpend->first(fn($item) => $item[self::BUDGET_ID] === $campaign[self::BUDGET_ID])[self::BUDGET_SPEND] ?? 0;
            $averageDailySpend = $averageBudgetSpend->first(fn($item) => $item[self::BUDGET_ID] === $campaign[self::BUDGET_ID])[self::AVERAGE_SPEND] ?? 0;
            $budgetUsageToday  = $budgetUsage->first(fn($item) => $item[self::BUDGET_ID] === $campaign[self::BUDGET_ID])[self::BUDGET_USAGE] ?? 0;

            return [
                self::CAMPAIGN_ID          => $campaign[self::CAMPAIGN_ID],
                self::CAMPAIGN_REFERENCE   => $campaign[self::CAMPAIGN_REFERENCE],
                self::BUDGET_ID            => $campaign[self::BUDGET_ID],
                self::CAMPAIGN_NAME        => $campaign[self::CAMPAIGN_NAME],
                self::BUDGET_DISPLAY_NAME  => $campaign[self::BUDGET_DISPLAY_NAME],
                self::BUDGET_TYPE          => $campaign[self::BUDGET_TYPE],
                self::CAMPAIGN_STATUS      => $campaign[self::CAMPAIGN_STATUS],
                self::CAMPAIGN_PAUSED_AT   => $campaign[self::CAMPAIGN_PAUSED_AT],
                self::SPEND_TODAY          => $spendToday,
                self::AVERAGE_DAILY_SPEND  => $averageDailySpend,
                self::BUDGET_USAGE_TODAY   => $budgetUsageToday,
                self::MAXIMUM_BUDGET_USAGE => $campaign[self::MAXIMUM_BUDGET_USAGE],
            ];
        });
    }

    /**
     * Will turn your collection into a length aware paginator. Only use if results to be displayed will be affected by
     * what you need to add to the collection before paginating.
     *
     * @param Collection $results
     * @param int|null $perPage
     * @param int|null $pageNumber
     * @return LengthAwarePaginator
     */
    public static function paginateCollection(Collection $results, ?int $perPage = null, ?int $pageNumber = null): LengthAwarePaginator
    {
        $totalItemCount = $results->count();

        return new LengthAwarePaginator($results->forPage($pageNumber, $perPage), $totalItemCount, $perPage, $pageNumber);
    }

    /**
     * @param CompanyCampaign $campaign
     * @param Carbon $reactivateRequest
     * @return Carbon
     */
    private function getUnpauseTimestamp(CompanyCampaign $campaign, Carbon $reactivateRequest): Carbon
    {
        $timezone = TimeZoneHelperService::getCompanyTimezone($campaign->company);

        return $reactivateRequest->startOfDay()->subHours($timezone->value)->addMinute();
    }
}
