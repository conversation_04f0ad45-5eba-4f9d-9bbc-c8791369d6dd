<?php

namespace App\Services\ProductPricing\BestRevenueScenario;

use App\DataModels\Odin\LeadCampaignBudgetUsage;
use App\DataModels\Odin\Prices\BRSCampaignPrices;
use App\DataModels\Odin\Prices\BudgetUsageData;
use App\DataModels\Odin\Prices\FilteredBRSPrices;
use App\DataModels\Odin\Prices\PotentialBRSCampaigns;
use App\DataModels\Odin\Prices\SaleTypeLimits;
use App\Enums\Odin\BudgetCategory;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Enums\Odin\Product as ProductEnum;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\PropertyType;
use App\Enums\Odin\QualityTier;
use App\Enums\Odin\StateAbbreviation;
use App\Exceptions\DebugException;
use App\Models\ComputedRejectionStatistic;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadSalesType;
use App\Models\Odin\Company;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAppointment;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\ProductCampaignBudget;
use App\Models\Odin\ServiceProduct;
use App\Models\SaleType;
use App\Repositories\Legacy\DatabaseLocationRepository;
use App\Repositories\Odin\Campaigns\OdinCampaignRepository;
use App\Repositories\Odin\ProductCampaignBudgetRepository;
use App\Repositories\Odin\ProductCampaignRepository;
use App\Services\Odin\Campaigns\OdinCampaignAvailabilityService;
use App\Services\Odin\Campaigns\OdinProductCampaignBudgetUsageCalculationService;
use App\Services\ProductPricing\ProductPricingService;
use Exception;
use Throwable;

class OdinBestRevenueScenarioService extends BestRevenueScenarioService
{
    public function __construct(
        ProductPricingService                     $productPricingService,
        ProductCampaignBudgetRepository           $productCampaignBudgetRepository,
        protected OdinCampaignAvailabilityService $campaignAvailabilityService,
        protected ProductCampaignRepository       $productCampaignRepository,
        protected OdinCampaignRepository          $odinCampaignRepository
    )
    {
        parent::__construct($productPricingService, $productCampaignBudgetRepository);
    }

    /** @inheritDoc */
    public function getPotentialCampaigns(int $consumerProductId, ?array $excludedCompanyIds = [], ?int $overrideSaleTypeId = null): ?PotentialBRSCampaigns
    {
        /** @var ConsumerProduct $consumerProduct */
        $consumerProduct = ConsumerProduct::query()->findOrFail($consumerProductId);

        $product = $consumerProduct->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::RELATION_PRODUCT}->{Product::FIELD_NAME};
        $industry = $consumerProduct->{ConsumerProduct::RELATION_SERVICE_PRODUCT}->{ServiceProduct::RELATION_SERVICE}->{IndustryService::RELATION_INDUSTRY}->{Industry::FIELD_NAME};
        $legacyLead = $consumerProduct->{ConsumerProduct::RELATION_CONSUMER}->{Consumer::RELATION_LEGACY_LEAD};

        $leadTypeId = $industry === IndustryEnum::ROOFING->value ? $legacyLead->{EloquentQuote::ROOFING_LEAD_TYPE_ID} : $legacyLead->{EloquentQuote::LEAD_TYPE_ID};

        // TODO: Define premium for odin products.
        if($product === ProductEnum::LEAD->value) {
            $qualityTier = $leadTypeId === 2 ? QualityTier::PREMIUM : QualityTier::STANDARD;
        }
        else if($product === ProductEnum::APPOINTMENT->value) {
            $qualityTier = $consumerProduct->{ConsumerProduct::RELATION_APPOINTMENT}->{ProductAppointment::APPOINTMENT_TYPE};
        }
        else {
            throw new Exception("Invalid product");
        }

        // TODO: Define premium for odin products.
        $potentialBrsCampaigns = new PotentialBRSCampaigns(
            serviceProductId: $consumerProduct->service_product_id,
            product: ProductEnum::tryFrom($consumerProduct->serviceProduct->product->name),
            industry: $consumerProduct->serviceProduct->service->industry->name,
            propertyType: PropertyType::tryFrom(ucfirst(strtolower($consumerProduct->consumerProductData->payload[GlobalConfigurableFields::PROPERTY_TYPE->value] ?? PropertyType::RESIDENTIAL->value))),
            qualityTier: $qualityTier,
            stateAbbreviation: StateAbbreviation::tryFrom($consumerProduct->address->state),
            countyKey: app(DatabaseLocationRepository::class)->getZipCode($consumerProduct->address->zip_code)->county_key
        );

        $campaigns = $this->campaignAvailabilityService->getAvailableCampaignsForProduct($consumerProduct, $excludedCompanyIds, $overrideSaleTypeId);

        foreach ($campaigns as $campaign) {
            $potentialBrsCampaigns->addCampaign(
                $campaign["sale_type_id"],
                $this->productCampaignRepository->findProductCampaignByLegacyParentIdOrFail($campaign["campaign_id"])->id,
                $campaign["company_id"],
                $campaign[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY],
                $campaign[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID]
            );
        }

        return $potentialBrsCampaigns;
    }

    /** @inheritDoc */
    public function filterOverBudgetCampaigns(BRSCampaignPrices $BRSCampaignPrices, int $consumerProductId): ?FilteredBRSPrices
    {
        if ($BRSCampaignPrices->product === ProductEnum::LEAD) {
            return $this->filterOverBudgetLeadCampaigns($BRSCampaignPrices);
        }

        $productCampaignIds = $BRSCampaignPrices->getCampaignIds()->collapse()->unique()->toArray();

        $budgetUsageData = $this->productCampaignBudgetRepository->getProductCampaignBudgetUsage($productCampaignIds, $BRSCampaignPrices->qualityTier);

        $filteredBrsPrices = new FilteredBRSPrices(
            $BRSCampaignPrices->qualityTier
        );

        $noLimitLabel = ucwords(str_replace('_', ' ', ProductCampaignBudget::VALUE_TYPE_NO_LIMIT));

        $productBudgetUnits = [LeadCampaign::DISPLAY_BUDGET_UNIT_LEAD, ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_LEADS, ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_APPOINTMENTS];

        foreach($BRSCampaignPrices as $saleTypeId => $campaignPrices) {
            $budgetCategory = match($saleTypeId) {
                self::EXCLUSIVE_SALES_TYPE_ID, self::DUO_SALES_TYPE_ID, self::TRIO_SALES_TYPE_ID, self::QUAD_SALES_TYPE_ID => BudgetCategory::VERIFIED,
                self::UNVERIFIED_SALES_TYPE_ID => BudgetCategory::UNVERIFIED,
                self::EMAIL_ONLY_SALES_TYPE_ID => BudgetCategory::EMAIL_ONLY
            };

            foreach($campaignPrices as $campaignId => $campaignInfo) {
                $campaignBudgetUsageData = $budgetUsageData->getBudgetUsageData($campaignId);

                if($campaignBudgetUsageData->isEmpty()) {
                    continue;
                }

                $budgetData = $campaignBudgetUsageData->get(BudgetUsageData::BUDGET_INFO)->get($budgetCategory->value);
                $companyData = $campaignBudgetUsageData->get(BudgetUsageData::COMPANY_INFO);

                if(empty($budgetData->get(BudgetUsageData::BUDGET_UNIT))) {
                    continue;
                }
                else if(in_array($budgetData->get(BudgetUsageData::BUDGET_UNIT),  [LeadCampaign::DISPLAY_BUDGET_UNIT_UNLIMITED, ProductCampaignBudget::VALUE_TYPE_NO_LIMIT], true)) {
                    $filteredBrsPrices->setPrice(
                        $saleTypeId,
                        $campaignId,
                        $campaignInfo[BRSCampaignPrices::COMPANY_ID],
                        $budgetData[BudgetUsageData::CAMPAIGN_BUDGET_ID],
                        $campaignInfo[BRSCampaignPrices::PRICE],
                        $campaignInfo[BRSCampaignPrices::UNRECTIFIED_PRICE],
                        $campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY],
                        $campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID],
                        0,
                        $noLimitLabel,
                        $campaignInfo[BRSCampaignPrices::SCHEDULE_ID] ?? 0
                    );

                    continue;
                }

                $dailyBudget = $budgetData->get(BudgetUsageData::DAILY_BUDGET);
                $budgetUnit = $budgetData->get(BudgetUsageData::BUDGET_UNIT);

                $estimatedPrice = in_array($budgetUnit, $productBudgetUnits, true) ? 1 : $campaignInfo[BRSCampaignPrices::UNRECTIFIED_PRICE];

                $saleTypeBudgetSpent = $budgetData->get(BudgetUsageData::BUDGET_SPENT);

                $budgetSpent = $saleTypeBudgetSpent + ($companyData->get(BudgetUsageData::NEVER_EXCEED_BUDGET) ? $estimatedPrice : 0);
                $usagePercentage = round($budgetSpent / $budgetData->get(BudgetUsageData::BUDGET_TIMEFRAME_DAYS) * 100 / $dailyBudget, 2);

                if($usagePercentage <= $budgetData->get(BudgetUsageData::MAX_BUDGET_USAGE)) {
                    $displayUnit = 'Dollars';

                    if(in_array($budgetUnit,  [LeadCampaign::DISPLAY_BUDGET_UNIT_LEAD, ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_LEADS], true)) {
                        $displayUnit = 'Leads';
                    }
                    else if($budgetUnit === ProductCampaignBudget::VALUE_TYPE_AVG_DAILY_APPOINTMENTS) {
                        $displayUnit = 'Appointments';
                    }

                    $filteredBrsPrices->setPrice(
                        $saleTypeId,
                        $campaignId,
                        $campaignInfo[BRSCampaignPrices::COMPANY_ID],
                        $budgetData[BudgetUsageData::CAMPAIGN_BUDGET_ID],
                        $campaignInfo[BRSCampaignPrices::PRICE],
                        $campaignInfo[BRSCampaignPrices::UNRECTIFIED_PRICE],
                        $campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY],
                        $campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID],
                        $usagePercentage,
                        "{$dailyBudget} {$displayUnit}",
                        $campaignInfo[BRSCampaignPrices::SCHEDULE_ID] ?? 0
                    );
                }
            }
        }

        return $filteredBrsPrices;
    }

    protected function filterOverBudgetLeadCampaigns(BRSCampaignPrices $BRSCampaignPrices): ?FilteredBRSPrices
    {
        $productCampaignIds = $BRSCampaignPrices->getCampaignIds()->collapse()->unique()->toArray();

        /** @var OdinProductCampaignBudgetUsageCalculationService $budgetCalculationService */
        $budgetCalculationService = app()->make(OdinProductCampaignBudgetUsageCalculationService::class);

        $budgetCalculationService->calculateProductCampaignBudgetUsage($productCampaignIds);

        $filteredBrsPrices = new FilteredBRSPrices(
            $BRSCampaignPrices->qualityTier
        );

        $noLimitLabel = ucwords(str_replace('_', ' ', ProductCampaignBudget::VALUE_TYPE_NO_LIMIT));

        foreach($BRSCampaignPrices as $saleTypeId => $campaignPrices) {
            foreach($campaignPrices as $campaignId => $campaignInfo) {
                if (!$budgetCalculationService->getBudgetType($campaignId, $saleTypeId))
                    continue;

                if ($budgetCalculationService->isBudgetUnlimited($campaignId, $saleTypeId)) {
                    $filteredBrsPrices->setPrice(
                        $saleTypeId,
                        $campaignId,
                        $campaignInfo[BRSCampaignPrices::COMPANY_ID],
                        0,
                        $campaignInfo[BRSCampaignPrices::PRICE],
                        $campaignInfo[BRSCampaignPrices::UNRECTIFIED_PRICE],
                        $campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY],
                        $campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID],
                        0,
                        $noLimitLabel
                    );

                    continue;
                }

                $dailyBudget = $budgetCalculationService->getDailyBudgetForSaleType($campaignId, $saleTypeId);

                if (!$dailyBudget) continue;

                $maxBudgetUsage = $budgetCalculationService->getMaxBudgetUsage($campaignId, $saleTypeId) ?? LeadCampaignBudgetUsage::MAX_BUDGET_USAGE;

                $estimatedPrice = $budgetCalculationService->isBudgetTypeLead($campaignId, $saleTypeId) ? 1 : $campaignInfo[BRSCampaignPrices::UNRECTIFIED_PRICE];

                $saleTypeBudgetSpent = $budgetCalculationService->getBudgetSpent($campaignId, $saleTypeId);

                $budgetSpent = $saleTypeBudgetSpent + ($budgetCalculationService->getNeverExceedBudgetStatus($campaignId, $saleTypeId) ? $estimatedPrice : 0);

                $usagePercentage = round($budgetSpent / $budgetCalculationService->getCalculationDays($campaignId, $saleTypeId) * 100 / $dailyBudget, 2);

                if($usagePercentage <= $maxBudgetUsage) {
                    $displayUnit = 'Dollars';

                    if($budgetCalculationService->isBudgetTypeLead($campaignId, $saleTypeId)) {
                        $displayUnit = 'Leads';
                    }

                    $filteredBrsPrices->setPrice(
                        $saleTypeId,
                        $campaignId,
                        $campaignInfo[BRSCampaignPrices::COMPANY_ID],
                        0,
                        $campaignInfo[BRSCampaignPrices::PRICE],
                        $campaignInfo[BRSCampaignPrices::UNRECTIFIED_PRICE],
                        $campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY],
                        $campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID],
                        $usagePercentage,
                        "{$dailyBudget} {$displayUnit}"
                    );
                }
            }
        }

        return $filteredBrsPrices;
    }

    public function getSaleTypeLimits(ConsumerProduct $product): SaleTypeLimits
    {
        //todo: use SalesType Model instead of legacy model
        $saleTypeLimits = new SaleTypeLimits();
        $maxRequests = $product->contact_requests > 0 ? $product->contact_requests : ConsumerProduct::DEFAULT_CONTACT_REQUEST_FOR_MULTI_INDUSTRY_LEAD;

        foreach(LeadSalesType::query()->pluck(LeadSalesType::SALE_LIMIT, LeadSalesType::ID)->toArray() as $id => $limit) {
            if($limit <= $maxRequests)
                $saleTypeLimits->setLimit($id, $limit);
        }

        return $saleTypeLimits;
    }

    /** @inheritDoc */
    public function investigateAllocationFailure(Company $company, ConsumerProduct $consumerProduct, ?ProductCampaign $productCampaign = null, ?array $excludedCompanyIds = []): string
    {
        try {
            $availableCampaigns = $this->campaignAvailabilityService->getAvailableCampaignsForProduct($consumerProduct, $excludedCompanyIds, null, $company, $productCampaign, true);

            $campaignNames = ProductCampaign::query()
                ->whereIn(ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID, $availableCampaigns->pluck("campaign_id"))
                ->select([
                    ProductCampaign::FIELD_ID,
                    ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID,
                    ProductCampaign::FIELD_NAME
                ])
                ->get()
                ->keyBy(ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID)
                ->toArray();

            $saleTypes = SaleType::query()->pluck(SaleType::FIELD_NAME, SaleType::FIELD_ID)->toArray();

            $legacyCompanyId = $company->{Company::FIELD_LEGACY_ID};

            $potentialBrsCampaigns = [];
            foreach ($availableCampaigns as $campaign) {
                if ($campaign["company_id"] === $legacyCompanyId) {
                    $campaignInfo = $campaignNames[$campaign["campaign_id"]];

                    $potentialBrsCampaigns[] = sprintf(
                        "Sale Type: %s - Campaign: %s",
                        $saleTypes[$campaign["sale_type_id"]],
                        "{$campaignInfo[ProductCampaign::FIELD_ID]} ({$campaignInfo[ProductCampaign::FIELD_NAME]})"
                    );
                }
            }

            $allocationFailureReason = !empty($potentialBrsCampaigns) ? "Potential allocations found;".implode(';', $potentialBrsCampaigns) : "Problem retrieving potential campaigns";
        }
        catch (DebugException $e) {
            $allocationFailureReason = $e->getMessage();
        }
        catch (Throwable $e) {
            report($e);

            $allocationFailureReason = 'Error investigating allocation failures';
        }

        return $allocationFailureReason;
    }
}
