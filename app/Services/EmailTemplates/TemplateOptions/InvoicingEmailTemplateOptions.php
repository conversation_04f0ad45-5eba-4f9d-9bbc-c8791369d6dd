<?php

namespace App\Services\EmailTemplates\TemplateOptions;

use App\Enums\EmailTemplateType;
use App\Services\EmailTemplates\TemplateAttachment\InvoicePdfAttachment;
use App\Services\Shortcode\ShortcodeImplementation\InvoiceEmailShortcodeUseCase;

class InvoicingEmailTemplateOptions extends BaseEmailTemplateOptions
{
    protected array $attachments = [
        InvoicePdfAttachment::class
    ];

    /**
     * @return EmailTemplateType
     */
    static function getType(): EmailTemplateType
    {
        return EmailTemplateType::BILLING;
    }

    /**
     * @return array
     */
    public function getShortcodes(): array
    {
        return InvoiceEmailShortcodeUseCase::getShortcodesList();
    }
}
