<?php

namespace App\Services;

use App\Enums\LeadRefundItemChargeRefundStatus;
use App\Enums\LeadRefundStatus;
use App\Models\LeadRefund;
use App\Models\LeadRefundItemRefund;

class BillingLegacyAdminEventService
{
    /**
     * @param array $data
     * @return void
     */
    public function handleChangeRefunded(array $data): void
    {
        [
            "charge_id"      => $chargeId,
            "refund_id"      => $refundId,
            "lead_refund_id" => $leadRefundId,
            "status"         => $status,
            "amount"         => $amount,
        ] = $data;

        LeadRefundItemRefund::query()
            ->where(LeadRefundItemRefund::FIELD_EXTERNAL_REFUND_ID, $refundId)
            ->where(LeadRefundItemRefund::FIELD_EXTERNAL_CHARGE_ID, $chargeId)
            ->update([
                LeadRefundItemRefund::FIELD_STATUS => LeadRefundItemChargeRefundStatus::REFUNDED->value
            ]);

        $charges = LeadRefundItemRefund::query()
            ->where(LeadRefundItemRefund::FIELD_LEAD_REFUND_ID, $leadRefundId)
            ->get();

        $totalRefunded = $charges
            ->filter(fn (LeadRefundItemRefund $item) => $item->status === LeadRefundItemChargeRefundStatus::REFUNDED)
            ->count();

        if ($charges->count() === $totalRefunded) {
            LeadRefund::query()
                ->where(LeadRefund::FIELD_ID, $leadRefundId)
                ->update([
                    LeadRefund::FIELD_STATUS => LeadRefundStatus::REFUNDED
                ]);
        }
    }
}
