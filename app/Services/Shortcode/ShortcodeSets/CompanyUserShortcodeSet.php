<?php

namespace App\Services\Shortcode\ShortcodeSets;

use App\Services\Shortcode\Shortcodes\CompanyUser\CompanyUserNameShortcode;
use Illuminate\Support\Collection;

class CompanyUserShortcodeSet extends ShortcodeSet
{
    const string DEFAULT_KEY = 'company-user';

    /**
     * @inheritDoc
     */
    public function listShortcodes(): Collection
    {
        return collect([
            new CompanyUserNameShortcode(),
        ]);
    }
}