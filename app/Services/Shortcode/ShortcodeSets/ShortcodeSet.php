<?php

namespace App\Services\Shortcode\ShortcodeSets;

use App\Services\Shortcode\Shortcodes\GenericShortcodeContract;
use App\Services\Shortcode\Shortcodes\ModelFieldShortcode;
use Illuminate\Support\Collection;

abstract class ShortcodeSet implements ShortcodeSetContract
{
    protected array $modelFields;

    /**
     * @return Collection
     */
    public function listShortcodes(): Collection
    {
        return collect();
    }

    /**
     * @param mixed $value
     * @return array
     */
    public function compile(mixed $value): array
    {
        return $this->getShortcodes()
            ->mapWithKeys(function (GenericShortcodeContract $shortcode) use ($value) {
                return [$shortcode->getKey() => $shortcode->getValue($value)];
            })->toArray();
    }

    /**
     * @return Collection
     */
    public function getShortcodes(): Collection
    {
        $modelShortcodes = collect();

        if (isset($this->modelFields)) {
            $modelShortcodes = collect($this->modelFields)->map(function (string $field) {
                return new ModelFieldShortcode($field);
            });
        }

        return $this->listShortcodes()->merge($modelShortcodes);
    }

    /**
     * @return Collection
     */
    public function getShortcodeKeys(): Collection
    {
        return $this->getShortcodes()->map(fn(GenericShortcodeContract $shortcode) => $shortcode->getKey());
    }

    /**
     * @return Collection
     */
    public function getShortcodeOptions(): Collection
    {
        return $this->getShortcodes()->map(fn(GenericShortcodeContract $shortcode) => [
            'label' => $shortcode->getLabel(),
            'key'   => $shortcode->getKey()
        ]);
    }
}
