<?php

namespace App\Services\Shortcode\Shortcodes\Prospecting;

use App\Models\Prospects\NewBuyerProspect;
use App\Services\Shortcode\Shortcodes\GenericShortcodeContract;

class DecisionMakerFirstNameShortcode implements GenericShortcodeContract
{

    /**
     * @inheritDoc
     */
    public function getLabel(): string
    {
        return 'Decision Maker First Name';
    }

    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return 'decision_maker_first_name';
    }

    /**
     * @param NewBuyerProspect $input
     *
     * @return string
     */
    public function getValue(mixed $input): string
    {
        return $input->decision_maker_first_name ?? '';
    }
}
