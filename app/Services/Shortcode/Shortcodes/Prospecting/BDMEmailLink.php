<?php

namespace App\Services\Shortcode\Shortcodes\Prospecting;

use App\Models\User;
use App\Services\Shortcode\Shortcodes\GenericShortcodeContract;

class BDMEmailLink implements GenericShortcodeContract
{
    /**
     * @inheritDoc
     */
    public function getLabel(): string
    {
        return 'Email Link';
    }

    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return 'email-link';
    }

    /**
     * @param User $input
     *
     * @return mixed
     */
    public function getValue(mixed $input): string
    {
        return $this->makeEmailLink($input->email);
    }

    /**
     * @param string $email
     * @param string|null $label
     * @return string
     */
    protected function makeEmailLink(string $email, ?string $label = null): string
    {
        $label = $label ?? $email;

        return "<a href='mailto:{$email}'>{$label}</a>";
    }
}
