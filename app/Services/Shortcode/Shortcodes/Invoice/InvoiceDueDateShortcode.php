<?php

namespace App\Services\Shortcode\Shortcodes\Invoice;

use App\Helpers\CarbonHelper;
use App\Models\Billing\Invoice;
use App\Services\Shortcode\Shortcodes\GenericShortcodeContract;

class InvoiceDueDateShortcode implements GenericShortcodeContract
{
    public function getLabel(): string
    {
        return "Due Date";
    }

    public function getKey(): string
    {
        return "due_date";
    }

    /**
     * @param Invoice $input
     * @return string
     */
    public function getValue(mixed $input): string
    {
        return CarbonHelper::parse($input->{Invoice::FIELD_DUE_AT})->toFormat(
            format: CarbonHelper::FORMAT_DATE
        );
    }
}
