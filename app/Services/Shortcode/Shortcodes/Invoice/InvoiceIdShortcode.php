<?php

namespace App\Services\Shortcode\Shortcodes\Invoice;

use App\Models\Billing\Invoice;
use App\Services\Shortcode\Shortcodes\GenericShortcodeContract;
use Illuminate\Support\Number;

class InvoiceIdShortcode implements GenericShortcodeContract
{
    public function getLabel(): string
    {
        return "Id";
    }

    public function getKey(): string
    {
        return "id";
    }

    /**
     * @param Invoice $input
     * @return string
     */
    public function getValue(mixed $input): string
    {
        return $input->{Invoice::FIELD_ID};
    }
}
