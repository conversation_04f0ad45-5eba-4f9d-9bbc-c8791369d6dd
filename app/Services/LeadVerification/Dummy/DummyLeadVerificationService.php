<?php

namespace App\Services\LeadVerification\Dummy;

use App\Models\Legacy\EloquentQuote;
use App\Services\LeadVerification\LeadVerificationResult;
use App\Services\LeadVerification\LeadVerificationServiceInterface;

class DummyLeadVerificationService implements LeadVerificationServiceInterface
{

    /**
     * Verifies a given lead
     *
     * @param EloquentQuote $lead
     * @return array
     */
    public function verifyLead(EloquentQuote $lead): array
    {
        $result                   = new LeadVerificationResult();
        $result->driver           = "Dummy LeadVerification";
        $result->confidence       = rand(1, 500);
        $result->phone_is_valid   = rand(0, 100) < 50;
        $result->phone_name_match = rand(0, 100) < 50;
        $result->address_is_valid = rand(0, 100) < 50;
        $result->email_is_valid   = rand(0, 100) < 50;
        $result->ip_is_valid      = rand(0, 100) < 50;
        $lead->saveLeadVerificationDetails(collect($result->toArray()));

        return $result->toArray();
    }
}
