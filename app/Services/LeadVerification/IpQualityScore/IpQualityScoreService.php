<?php

namespace App\Services\LeadVerification\IpQualityScore;

use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentAddress;
use App\Services\LeadVerification\********************************;
use GuzzleHttp\Client;
use GuzzleHttp\Promise;

class IpQualityScoreService implements ********************************
{
    /** @inheritDoc */
    public function verifyLead(EloquentQuote $lead): array
    {
        $guzzleClient = new Client(['base_uri' => config('services.ip_quality_score.base_url')]);

        $apiKey = config('services.ip_quality_score.api_key');

        $email = $lead->{EloquentQuote::USER_EMAIL};
        $phone = $lead->{EloquentQuote::RELATION_ADDRESS}?->trimmedPhoneNumber();
        $ipAddress = $lead->{EloquentQuote::IP_ADDRESS};
        $country = $lead->{EloquentQuote::RELATION_ADDRESS}?->{EloquentAddress::COUNTRY};
        $firstName = $lead->{EloquentQuote::FIRST_NAME};
        $lastName = $lead->{EloquentQuote::LAST_NAME};
        $city = $lead->{EloquentQuote::RELATION_ADDRESS}?->{EloquentAddress::CITY};
        $state = $lead->{EloquentQuote::RELATION_ADDRESS}?->{EloquentAddress::STATE};
        $zipCode = $lead->{EloquentQuote::RELATION_ADDRESS}?->{EloquentAddress::ZIP_CODE};

        $responses = Promise\Utils::unwrap([
            "email" => $guzzleClient->getAsync("email/$apiKey/$email"),
            "phone" => $guzzleClient->getAsync("phone/$apiKey/$phone?country[]=US"),
            "transaction" => $guzzleClient->getAsync("ip/$apiKey/$ipAddress?strictness=0&billing_email=$email&billing_phone=$phone&billing_country=$country&billing_first_name=$firstName&billing_last_name=$lastName&billing_city=$city&billing_region=$state&billing_postcode=$zipCode")
        ]);

        $emailData = json_decode($responses["email"]->getBody()->getContents(), true);
        $phoneData = json_decode($responses["phone"]->getBody()->getContents(), true);
        $transactionData = json_decode($responses["transaction"]->getBody()->getContents(), true);

        return [
            'email' => $emailData,
            'phone' => $phoneData,
            'transaction' => $transactionData
        ];
    }
}
