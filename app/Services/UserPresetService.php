<?php

namespace App\Services;

use App\Services\Filterables\DateRangeFilterableOption;
use Carbon\Carbon;
use Illuminate\Support\Arr;

class UserPresetService
{
    /**
     * Transform a static date to a delta from now, so it is still meaningful when retrieved from preset data
     * @param array $dateRangeFilterArray
     * @return array
     */
    public function transformDateToDelta(array $dateRangeFilterArray): array
    {
        if (Arr::has($dateRangeFilterArray, [DateRangeFilterableOption::DATE_RANGE_FROM, DateRangeFilterableOption::DATE_RANGE_TO, DateRangeFilterableOption::DATE_RANGE_PRESET])
            && $dateRangeFilterArray[DateRangeFilterableOption::DATE_RANGE_PRESET] === null) {

                $startDate = new Carbon($dateRangeFilterArray[DateRangeFilterableOption::DATE_RANGE_FROM]);
                $endDate = new Carbon($dateRangeFilterArray[DateRangeFilterableOption::DATE_RANGE_TO]);
                $dateRangeFilterArray[DateRangeFilterableOption::DATE_RANGE_FROM] = (int) now()->diffInMilliseconds($startDate, true);
                $dateRangeFilterArray[DateRangeFilterableOption::DATE_RANGE_TO] = (int) now()->diffInMilliseconds($endDate, true);
        }

        return $dateRangeFilterArray;
    }

    /**
     * Transform a delta millisecond time from now back into a static date for frontend
     * @param array $dateRangePresetArray
     * @return array
     */
    public function transformDeltaToDate(array $dateRangePresetArray): array
    {
        if (Arr::has($dateRangePresetArray, [DateRangeFilterableOption::DATE_RANGE_FROM, DateRangeFilterableOption::DATE_RANGE_TO, DateRangeFilterableOption::DATE_RANGE_PRESET])
            && $dateRangePresetArray[DateRangeFilterableOption::DATE_RANGE_PRESET] === null
            && is_int($dateRangePresetArray[DateRangeFilterableOption::DATE_RANGE_FROM])
            && is_int($dateRangePresetArray[DateRangeFilterableOption::DATE_RANGE_TO])) {

                $startDate = now()->subMilliseconds($dateRangePresetArray[DateRangeFilterableOption::DATE_RANGE_FROM]);
                $endDate = now()->subMilliseconds($dateRangePresetArray[DateRangeFilterableOption::DATE_RANGE_TO]);
                $dateRangePresetArray[DateRangeFilterableOption::DATE_RANGE_FROM] = $startDate;
                $dateRangePresetArray[DateRangeFilterableOption::DATE_RANGE_TO] = $endDate;
        }

        return $dateRangePresetArray;
    }
}
