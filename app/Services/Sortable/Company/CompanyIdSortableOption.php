<?php

namespace App\Services\Sortable\Company;

use App\Models\Odin\Company;
use App\Services\Sortable\BaseSortableOption;
use Illuminate\Database\Eloquent\Builder;

class CompanyIdSortableOption extends BaseSortableOption
{
    protected string $id = 'id';

    public function appendToQuery(Builder $existingQuery, string $direction): void
    {
        $existingQuery->orderBy(Company::TABLE.'.id', $direction);
    }
}
