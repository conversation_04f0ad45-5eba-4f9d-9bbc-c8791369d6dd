<?php

namespace App\Services\MarketingCampaign\Sending;

use App\DTO\EmailService\OutgoingEmailDTO;
use App\DTO\MarketingCampaign\MarketingSMS;
use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\MarketingCampaignConsumerStatus;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Enums\Odin\StateAbbreviation;
use App\Models\MarketingCampaign;
use App\Models\MarketingCampaignConsumer;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Services\MarketingCampaign\MarketingLogService;
use Carbon\Carbon;
use Carbon\CarbonTimeZone;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Support\Arr;

class MarketingCampaignSendingService
{
    const int SEND_CHUNK_SIZE = 50;

    /**
     * @param MarketingCampaign $campaign
     * @return void
     */
    public function prepareSend(MarketingCampaign $campaign): void
    {
        $campaignType = $campaign->type->getClass();

        $sendingConfiguration = $campaignType->getSendingConfiguration(campaign: $campaign);

        $targetCount = 0;

        MarketingCampaignConsumer::query() //todo: move query into builder
        ->where(MarketingCampaignConsumer::FIELD_MARKETING_CAMPAIGN_ID, $campaign->id)
            ->with(MarketingCampaignConsumer::RELATION_CONSUMER . '.' . Consumer::RELATION_CONSUMER_PRODUCT)
            ->whereIn(MarketingCampaignConsumer::FIELD_STATUS, MarketingCampaignConsumerStatus::sendable())
            ->whereNull(MarketingCampaignConsumer::FIELD_SENT_AT)
            ->chunkById(self::SEND_CHUNK_SIZE, function (Collection $chunk) use ($campaign, $campaignType, $sendingConfiguration, &$targetCount) {
                $groupedBy = $chunk->mapToGroups(function (MarketingCampaignConsumer $marketingCampaignConsumer) use ($campaign, $campaignType, $sendingConfiguration) {

                    /** @var ConsumerProduct $consumerProduct */
                    $consumerProduct = $marketingCampaignConsumer->consumer->consumerProducts()->first();

                    $dto = $campaignType->prepareMarketingCampaignConsumerToSend(
                        campaignConsumer: $marketingCampaignConsumer,
                        sendingConfiguration: $sendingConfiguration
                    );

                    $marketingCampaignConsumer->update([
                        MarketingCampaignConsumer::FIELD_STATUS => $dto
                            ? MarketingCampaignConsumerStatus::QUEUED
                            : MarketingCampaignConsumerStatus::ERROR
                    ]);

                    return [
                        StateAbbreviation::timeZone($consumerProduct->address->stateLocation?->state_abbr ?? '') => $dto
                    ];
                })->map(fn($group) => $group->filter());

                foreach ($groupedBy as $timezone => $timezoneUsers) {
                    $timezoneUsers = collect($timezoneUsers);

                    $targetCount = $targetCount + $timezoneUsers->count();

                    if ($timezoneUsers->isNotEmpty()) {
                        /** @var OutgoingEmailDTO|MarketingSMS $timezoneUser */
                        foreach ($timezoneUsers as $timezoneUser) {
                            $delay = $this->calculateSendDelay($campaign, new CarbonTimeZone($timezone));

                            $campaignType->dispatch(campaign: $campaign, dto: $timezoneUser, delay: $delay);

                            MarketingLogService::log(
                                message: 'Send Queued',
                                namespace: MarketingLogType::MARKETING_CAMPAIGN_SENT,
                                level: LogLevel::INFO,
                                context: [
                                    'contact'               => $timezoneUser instanceof OutgoingEmailDTO
                                        ? $timezoneUser->getToEmail()
                                        : $timezoneUser->getToPhone(),
                                    'timezone'              => $timezone,
                                    'send_at_utc'           => $delay->clone()->utc()->format('Y-m-d H:i:s'),
                                    'send_at_target_time'   => $delay->clone()->tz($timezone)->format('Y-m-d H:i:s'),
                                ],
                                relations: [
                                    [
                                        'id'    => $timezoneUser instanceof OutgoingEmailDTO
                                            ? $timezoneUser->getRelationId()
                                            : $timezoneUser->getMarketingConsumerId(),
                                        'class' => MarketingCampaignConsumer::class
                                    ]
                                ],
                            );
                        }

                        MarketingLogService::log(
                            message: 'Marketing Campaign chunk send dispatched',
                            namespace: MarketingLogType::MARKETING_CAMPAIGN_SENT,
                            level: LogLevel::INFO,
                            context: [
                                'marketing_campaign_id' => $campaign->id,
                                'chunk_count'           => $timezoneUsers->count(),
                            ],
                            relations: [$campaign],
                        );
                    }
                }

            });
    }

    /**
     * @param MarketingCampaign $marketingCampaign
     * @param CarbonTimeZone $carbonTimeZone
     * @return Carbon
     */
    public function calculateSendDelay(
        MarketingCampaign $marketingCampaign,
        CarbonTimeZone    $carbonTimeZone
    ): Carbon
    {

        $type = $marketingCampaign->type;

        if ($type->delaySend()) {
            $configuration = $marketingCampaign->{MarketingCampaign::FIELD_CONFIGURATION};

            $sendTime = Arr::get($configuration, 'send_time');

            $start = Arr::get($sendTime, 'start');

            $hours = Arr::get($start, 'hours');
            $minutes = Arr::get($start, 'minutes');
            $seconds = Arr::get($start, 'seconds');

            $startCarbon = now();

            $startCarbon = $startCarbon->setTimezone($carbonTimeZone)
                ->startOfDay()
                ->addHours($hours)
                ->addMinutes($minutes)
                ->addSeconds($seconds);

            $end = Arr::get($sendTime, 'end');
            $hours = Arr::get($end, 'hours');
            $minutes = Arr::get($end, 'minutes');
            $seconds = Arr::get($end, 'seconds');

            $endCarbon = now();

            $endCarbon = $endCarbon->setTimezone($carbonTimeZone)
                ->startOfDay()
                ->addHours($hours)
                ->addMinutes($minutes)
                ->addSeconds($seconds);

            $delay = rand($startCarbon->unix(), $endCarbon->unix());

            $delayInTimezone = Carbon::parse($delay, $carbonTimeZone);

            while ($delayInTimezone->isPast()) {
                $delayInTimezone->addDay();
            }

            return $delayInTimezone;
        } else {
            return now();
        }
    }

    public function nextSendTime(): Carbon
    {
        $timezone = StateAbbreviation::timeZone(StateAbbreviation::NY->value);
        $start = '11:00:00';
        $end = '21:00:00';

        $start = Carbon::parse($start, $timezone)->addDay();
        $end = Carbon::parse($end, $timezone)->addDay();

        $randomTimestamp = rand($start->timestamp, $end->timestamp);

        return Carbon::createFromTimestamp($randomTimestamp, $timezone);
    }
}
