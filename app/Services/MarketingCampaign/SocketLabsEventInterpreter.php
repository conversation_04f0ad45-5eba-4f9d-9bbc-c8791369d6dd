<?php

namespace App\Services\MarketingCampaign;

use App\Contracts\Marketing\MarketingEventInterpreterContract;
use App\DTO\EmailService\OutgoingEmailDTO;
use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Enums\MarketingCampaigns\SocketLabsEmailMarketingEvents;
use App\Services\MarketingCampaign\Events\MarketingEvent;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;

class SocketLabsEventInterpreter implements MarketingEventInterpreterContract
{
    protected string $secretKey;

    public function __construct()
    {
        $this->secretKey = config('services.marketing.email.socket_labs.secret_key');
    }

    public function interpret(Request $request): ?MarketingEvent
    {
        $content = $request->all();

        $data = Arr::get($content, 'Data', []);
        $meta = Arr::get($data, 'Meta', []);

        //convert {'key' => $key, 'value' => $value} into [$key => $value]
        $formatted = collect($meta)->mapWithKeys(function ($value) {
            return [$value['Key'] => $value['Value']];
        });

        if ($this->secretKey !== $request->input('key')) {
            MarketingLogService::log(
                message: "Request Secret Key does not match",
                namespace: MarketingLogType::WEBHOOK,
                level: LogLevel::ERROR,
                context: $request->all(),
            );

            return null;
        }

        $marketingCampaignConsumerId = $formatted->get(OutgoingEmailDTO::EMAIL_META_MCC_ID_KEY);

        if (empty($marketingCampaignConsumerId)) {
            MarketingLogService::log(
                message: "Unable to identify Marketing Campaign Consumer",
                namespace: MarketingLogType::WEBHOOK,
                level: LogLevel::ERROR,
                context: $request->all(),
            );

            return null;
        }

        $eventType = SocketLabsEmailMarketingEvents::tryFrom($request->input('Type'));

        return $eventType?->matchEvent(
            requestData: $content,
            meta: $formatted
        );
    }
}
