<?php


namespace App\Services\PubSub\PubSubMiddleware;

use App\Contracts\PubSubMiddlewareContract;
use App\Enums\Odin\PubSubOrigin;
use App\Models\Odin\Company;
use Closure;
use Illuminate\Support\Collection;

class ConvertLegacyCompanyId implements PubSubMiddlewareContract
{
    const REQUEST_COMPANY_ID = 'company_id';
    const REQUEST_LEGACY_ID = 'legacy_id';
    const REQUEST_COMPANY_REFERENCE = 'company_reference';
    const REQUEST_COMPANY_NAME = 'company_name';

    /**
     * @inheritDoc
     */
    public function handle(array $event, Closure $next): mixed
    {
        if ($event["origin"] === PubSubOrigin::PUBSUB_ORIGIN_LEGACY->value && array_key_exists(self::REQUEST_COMPANY_ID, $event)) {
            if(empty($event[self::REQUEST_COMPANY_ID])) {
                $this->handleFailedSearch(null, $event);
            } else {
                $event = $this->processLegacyCompanyId($event);
            }
        }
        return $next($event);
    }

    /**
     * @param array $event
     * @return array
     */
    private function processLegacyCompanyId(array $event): array
    {
        $legacyId = $event[self::REQUEST_COMPANY_ID];

        $companies      = $this->getCompanyByAttribute(Company::FIELD_LEGACY_ID, $legacyId);
        $companiesFound = $companies->count();

        // Fallbacks if legacy search fails with either 0 results, or multiple results
        if ($companiesFound !== 1) {
            logger()->warning("There were $companiesFound companies found with the legacy id '$legacyId'.");

            if (array_key_exists(self::REQUEST_COMPANY_REFERENCE, $event) && !empty($event[self::REQUEST_COMPANY_REFERENCE])) {
                $companies = $companiesFound === 0
                    ? $this->getCompanyByAttribute(Company::FIELD_REFERENCE, $event[self::REQUEST_COMPANY_REFERENCE])
                    : $this->tryFilteringBy(Company::FIELD_REFERENCE, $event[self::REQUEST_COMPANY_REFERENCE], $companies);
            }
            if ($companies->count() !== 1 && array_key_exists(self::REQUEST_COMPANY_NAME, $event) && !empty($event[self::REQUEST_COMPANY_NAME])) {
                $companies = $companiesFound === 0
                    ? $this->getCompanyByAttribute(Company::FIELD_NAME, $event[self::REQUEST_COMPANY_NAME])
                    : $this->tryFilteringBy(Company::FIELD_NAME, $event[self::REQUEST_COMPANY_NAME], $companies);
            }
        }

        if ($companies->count() === 1) {
            $this->attachUpdatedData($event, $companies->first());
        } else {
            $this->handleFailedSearch($legacyId, $event);
        }

        return $event;
    }

    /**
     * @param array $event
     * @param Company $company
     * @return void
     */
    private function attachUpdatedData(array &$event, Company $company): void
    {
        $event[self::REQUEST_COMPANY_ID] = $company->{Company::FIELD_ID};
        $event[self::REQUEST_LEGACY_ID] = $company->{Company::FIELD_LEGACY_ID};
        $event[self::REQUEST_COMPANY_REFERENCE] = $company->{Company::FIELD_REFERENCE};
    }

    /**
     * @param string $column
     * @param mixed $value
     * @param Collection<Company> $companies
     * @return Collection<Company>
     */
    private function tryFilteringBy(string $column, mixed $value, Collection $companies): Collection
    {
        return $companies->filter(fn(Company $company) => $company->$column === $value);
    }

    /**
     * @param string $column
     * @param mixed  $value
     * @return Collection<Company>
     */
    private function getCompanyByAttribute(string $column, mixed $value): Collection
    {
        /** @var Collection $companies */
        return Company::query()
            ->where($column, $value)
            ->get();
    }

    /**
     * @param ?int $legacyId
     * @param array $event
     * @return void
     */
    private function handleFailedSearch(?int $legacyId, array $event = []): void
    {
        $eventName = $event['event'] ?? 'Unknown';
        $eventCategory = $event['type'] ?? 'Unknown';
        if (!$legacyId) {
            logger()->error("Received PubSub event from Legacy with company_id key, but no value - Category '$eventCategory', EventName: '$eventName'");
        } else {
            logger()->error("Received PubSub event from Legacy with a company_id: '$legacyId' - could not find an A2 company with a matching legacy_id - Category '$eventCategory', EventName: '$eventName'");
        }
    }
}
