<?php

namespace App\Services;
use App\Repositories\CompanyCampaignContactDeliveryLogRepository;
use Illuminate\Pagination\LengthAwarePaginator;

class CompanyCampaignContactDeliveryLogService
{
    public function __construct(
        protected CompanyCampaignContactDeliveryLogRepository $companyCampaignContactDeliveryLogRepository
    )
    {}

    /**
     * @param  int  $perPage
     * @param  int  $page
     * @param  int|null  $companyId
     * @param  bool|null  $succeeded
     * @param  string|null  $campaign
     * @param  int|null  $consumerProductId
     * @param  array|null  $dateRange
     *
     * @return LengthAwarePaginator
     */
    public function listContactDeliveryLogs(
        int     $perPage,
        int     $page,
        ?int    $companyId = null,
        ?bool   $succeeded = null,
        ?string $campaign = null,
        ?int    $consumerProductId  = null,
        ?array  $dateRange = null

    ): LengthAwarePaginator
    {
        return $this->companyCampaignContactDeliveryLogRepository->listContactDeliveryLogs(
            perPage             :    $perPage,
            page                :       $page,
            companyId           :  $companyId,
            succeeded           :  $succeeded,
            campaign            :   $campaign,
            consumerProductId   : $consumerProductId,
            dateRange           : $dateRange
        );
    }
}

