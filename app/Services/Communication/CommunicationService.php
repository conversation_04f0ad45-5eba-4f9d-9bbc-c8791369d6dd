<?php

namespace App\Services\Communication;

use App\Contracts\Services\Communication\CommunicationContract;
use App\Enums\CommunicationRelationTypes;
use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Enums\NotificationLinkType;
use App\Enums\NotificationPayloadKeys;
use App\Enums\TemplateManagement\TemplateType;
use App\Factories\Workflows\WorkflowPayloadFactory;
use App\Jobs\DispatchPubSubEvent;
use App\Models\Call;
use App\Models\CallRecording;
use App\Models\Notification;
use App\Models\Odin\CompanyLocation;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Phone;
use App\Models\Sales\Task;
use App\Models\Template;
use App\Models\Text;
use App\Models\User;
use App\Repositories\CommunicationRepository;
use App\Repositories\IdentificationRepository;
use App\Repositories\Odin\CompanyLocationRepository;
use App\Repositories\Odin\CompanyUserRepository;
use App\Repositories\Odin\ConsumerProductRepository;
use App\Repositories\Odin\ConsumerRepository;
use App\Repositories\TaskRepository;
use App\Services\Broadcasting\PusherNotificationBroadcaster;
use App\Services\NotificationService;
use App\Services\Odin\ContactIdentification\ContactIdentificationService;
use App\Transformers\LeadProcessing\CommunicationTransformer;
use App\Workflows\Shortcodes\WorkflowShortcodeService;
use App\Workflows\WorkflowEvent;
use Exception;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Pagination\LengthAwarePaginator;
use Throwable;

class CommunicationService
{

    public function __construct(
        protected CommunicationRepository       $communicationRepository,
        protected IdentificationRepository      $identificationRepository,
        protected TaskRepository                $taskRepository,
        protected CommunicationContract|null    $service,
        protected NotificationService           $notificationService,
        protected PusherNotificationBroadcaster $broadcaster,
        protected CommunicationTransformer      $leadCommunicationTransformer,
        protected ContactIdentificationService  $contactIdentificationService,
        protected ConsumerProductRepository     $consumerProductRepository,
        protected CompanyUserRepository         $companyUserRepository,
        protected ConsumerRepository            $consumerRepository,
        protected CompanyLocationRepository     $companyLocationRepository,
    )
    {
    }


    public function recordVoicemail(string $from, string $to, string $callSid, string $voicemailLink, float $duration): bool
    {
        $user     = $this->communicationRepository->getUserByNumber($to);
        $identity = $this->identificationRepository->identifyCaller($from);

        if ($user === null)
            return false;

        return $this->communicationRepository->saveVoicemail(
                $from,
                $user->id,
                $callSid,
                $voicemailLink,
                $identity["name"],
                $identity["relation_type"],
                $identity["relation_id"],
                $duration
            ) !== null;
    }

    /**
     * @param User $user
     * @param string $contactPhoneNumber
     * @param string $body
     * @param string|null $relType
     * @param int|null $relId
     * @return \Illuminate\Http\JsonResponse
     * @throws Throwable
     */
    public function deliverSMSFromUser(
        User   $user,
        string $contactPhoneNumber,
        string $body,
        string $relType = null,
        int    $relId = null
    ): \Illuminate\Http\JsonResponse
    {
        $phone = $user->primaryPhone();
        throw_if(!$phone, \Exception::class, 'User has no assigned phone');
        try {
            $status = $this->service->sendSMS($phone->phone, $contactPhoneNumber, $body);
        } catch (Exception $e) {
            $this->notificationService->createNotificationForUser(
                $user->id,
                0,
                "SMS to \"" . $contactPhoneNumber . "\" Failed to Send",
                "Error: " . $e->getMessage(), 0
            );

            return response()->json([
                "status" => false,
                "error"  => $e->getMessage()
            ]);
        }

        // log message
        $this->communicationRepository->updateOrCreateOutboundSMS(
            $this->service?->getServiceName() ?? 'Unknown',
            $status,
            $phone->id,
            $contactPhoneNumber,
            $body,
            $relType,
            $relId
        );

        return response()->json([
            "status" => true
        ]);
    }

    /**
     * @param Template $template
     * @param string $from
     * @param string $to
     * @param array $data Key-value pairs for shortcode replacement in the template. Example: ['company_id' => 1517, 'campaign_id' => '123']
     *
     * @return void
     * @throws Exception
     */
    public function sendSmsWithTemplate(Template $template, string $from, string $to, array $data): void
    {
        if ($template->type !== TemplateType::SMS) {
            throw new Exception('Invalid SMS template');
        }

        /** @var WorkflowShortcodeService $shortcodeService */
        $shortcodeService = app(WorkflowShortcodeService::class);

        $this->service?->sendSMS(
            from: $from,
            to: $to,
            body: $shortcodeService->handle($template->content, WorkflowPayloadFactory::create(
                WorkflowEvent::fromArray([
                    'event_category' => EventCategory::GENERIC->value,
                    'event_name' => EventName::GENERIC_EVENT->value,
                    'event_data' => $data
                ])
            ))
        );
    }

    /**
     * @throws Throwable
     */
    public function receiveInboundSMS(
        string $serviceName,
        string $reference,
        string $toNumber,
        string $fromNumber,
        string $body,
        ?array $mediaArray = null
    ): void
    {
        /** @var Phone $phone */
        $phone = $this->communicationRepository->getPhoneFromPhoneNumber($toNumber);
        throw_if(!$phone, \Exception::class, 'No such phone found');
        $identity = $this->identificationRepository->identifyCaller($fromNumber);

        //dispatch an internal event
        $this->dispatchEvent($phone, $fromNumber, $identity, $body);

        // log message
        $text = $this->communicationRepository->updateOrCreateInboundSMS(
            $serviceName,
            $reference,
            $fromNumber,
            $phone->id,
            $body
        );

        if (!empty($mediaArray))
            $this->communicationRepository->createMediaForText($text, $mediaArray);

        if ($identity['relation_type'] === NotificationLinkType::LEAD->value) {
            /** @var Task|null $associatedTask */
            $associatedTask = $this->taskRepository->getAssociatedTasksForLead($identity['relation_id'])->first();
            if (!empty($associatedTask) && $associatedTask->assigned_user_id === $phone->primaryUser()?->id) {
                $link_id   = $associatedTask->{Task::FIELD_ID};
                $link_type = NotificationLinkType::TASK;
            }
        }

        if ($phone->primaryUser()?->id) {
            $this->notificationService->createNotificationForUser(
                $phone->primaryUser()?->id,
                $identity['relation_id'] ?? 0,
                'Message From ' . $identity['name'] . " ({$identity['relation_id']})",
                $body,
                Notification::IDENTITY_TYPE_MAPPING[$identity['relation_type']] ?? Notification::TYPE_DEFAULT,
                $link_id ?? null,
                $link_type ?? null,
                [
                    NotificationPayloadKeys::PHONE_NUMBER->value => $fromNumber,
                    NotificationPayloadKeys::SMS->value          => true,
                    NotificationPayloadKeys::NAME->value         => $identity['name']
                ]
            );

            $this->notificationService->broadcastEvent("new-message", ["private-notifications-{$phone->primaryUser()?->id}"], $this->leadCommunicationTransformer->transformText($text));
            $this->notificationService->broadcastEvent("on-new-sms-message", ["private-users.{$phone->primaryUser()?->id}.communication"], $this->leadCommunicationTransformer->transformText($text));
        }
    }

    /**
     * @param string $callSid
     * @param string $recordingSid
     * @param string $recordingUrl
     * @param int $recordingDuration
     * @return CallRecording|null
     */
    public function recordCall(string $callSid, string $recordingSid, string $recordingUrl, int $recordingDuration): ?CallRecording
    {
        $call = $this->communicationRepository->getCallByExternalReference($callSid);
        // Currently, external reference reflects the client call only, which means there will be no record in the case of a forwarded call.
        // todo: possible solution: if $call is null, find the client call using to and from numbers, taking recency into consideration (last hour?).
        // Then we can either clone the call, or save the recording to the existing call, and update the call status to 'answered'.
        if (!$call) {
            return null;
        } // for now, forwarded calls are not recorded
        return $this->communicationRepository->saveCallRecording($call->id, $recordingSid, $recordingDuration, $recordingUrl);
    }

    /**
     * @param string $serviceName
     * @param string $userPhone
     * @param string $contactPhone
     * @param string $externalReference
     * @param string $callResult
     * @param string|null $relType
     * @param int|null $relId
     * @return Call
     */
    public function updateOrCreateOutboundCall(
        string $serviceName,
        string $userPhone,
        string $contactPhone,
        string $externalReference,
        string $callResult,
        string $relType = null,
        int    $relId = null,
    )
    {
        if (is_null($relType) || is_null($relId)) {
            $identity = $this->identificationRepository->identifyCaller($contactPhone);
            $relType  = $identity['relation_type'];
            $relId    = $identity['relation_id'];
        }

        return $this->communicationRepository->updateOrCreateOutboundCall(
            $serviceName,
            $userPhone,
            $contactPhone,
            $externalReference,
            $callResult,
            $relType,
            $relId
        );
    }

    /**
     * @param string $serviceName
     * @param string $userPhone
     * @param string $contactPhone
     * @param string $externalReference
     * @param string $callResult
     * @return int
     */
    public function updateOrCreateInboundCall(
        string $serviceName,
        string $userPhone,
        string $contactPhone,
        string $externalReference,
        string $callResult): int
    {
        $identity = $this->identificationRepository->identifyCaller($contactPhone);

        $callLogId = $this->communicationRepository->updateOrCreateInboundCall(
            $serviceName,
            $userPhone,
            $contactPhone,
            $externalReference,
            $callResult,
            $identity['relation_type'],
            $identity['relation_id']
        );

        if (in_array($callResult, [Call::RESULT_BUSY, Call::RESULT_MISSED, Call::RESULT_VOICEMAIL], true)) {
            ;
            $name       = $contactPhone !== $identity['name'] ? $identity['name'] : 'Unknown';
            $relationId = !empty($identity['relation_id']) ? "({$identity['relation_id']})" : "";

            switch ($callResult) {
                case Call::RESULT_MISSED:
                case Call::RESULT_BUSY:
                    $subject = "Missed call from {$identity['name']}";
                    $body    = "Missed call from {$contactPhone} - {$name} {$relationId}";
                    break;
                case Call::RESULT_VOICEMAIL:
                    $subject = "Voicemail from {$identity['name']}";
                    $body    = "Voicemail from {$contactPhone} - {$name} {$relationId}";
                    break;
            }

            $userId = Call::query()
                ->with(Call::RELATION_PHONE)
                ->findOrFail($callLogId)
                ->{Call::RELATION_PHONE}
                ?->primaryUser()
                ?->{User::FIELD_ID} ?? 0;

            if ($identity['relation_type'] === NotificationLinkType::LEAD->value) {
                /** @var Task|null $associatedTask */
                $associatedTask = $this->taskRepository->getAssociatedTasksForLead($identity['relation_id'])->first();
                if (!empty($associatedTask) && $associatedTask->assigned_user_id === $userId) {
                    $link_id   = $associatedTask->{Task::FIELD_ID};
                    $link_type = NotificationLinkType::TASK;
                }
            }

            $this->notificationService->createNotificationForUser(
                $userId,
                $identity['relation_id'] ?? 0,
                $subject,
                $body,
                Notification::IDENTITY_TYPE_MAPPING[$identity['relation_type']] ?? Notification::TYPE_DEFAULT,
                $link_id ?? null,
                $link_type ?? null
            );
        }

        return $callLogId;
    }

    /**
     * @param Phone $phone
     * @param string $fromNumber
     * @param array $identity
     * @param string $smsBody
     *
     * @return void
     */
    protected function dispatchEvent(Phone $phone, string $fromNumber, array $identity, string $smsBody): void
    {
        DispatchPubSubEvent::dispatch(EventCategory::INTERNAL, EventName::INBOUND_SMS, [
            'sms-for'  => $phone->primaryUser()?->id,
            'sms-body' => $smsBody,
            'sms-from' => array_merge(['phone' => $fromNumber], $identity)
        ]);
    }

    /**
     * Retrieve additional data the log to enable users to identify the contact
     * Call -> call recording
     * Text -> all messages
     */
    public function getLogFurtherData(int $id, string $type): ?array
    {
        return $this->communicationRepository->getLogFurtherData($id, $type);
    }

    /**
     * Identify contact by phone number
     * @param User $user
     * @param string $phone
     * @return array
     */
    public function identifyContactByPhoneNumber(User $user, string $phone): array
    {
        $userPrimaryPhoneId = $user->primaryPhone()->id;

        $possibleContacts = $this->contactIdentificationService->identify($userPrimaryPhoneId, $phone);

        $isLeadProcessor = $user->leadProcessor()->first();

        $identifiedContact = null;

        if (count($possibleContacts) > 0) {
            // Lead first if user is lead processor
            usort($possibleContacts, function ($contact) use ($isLeadProcessor) {
                $isLead = $this->contactIdentificationService->checkIfContactIsLead($contact);

                if ($isLeadProcessor) return $isLead ? -1 : 1;
                else return $isLead ? 1 : 0;
            });

            $identifiedContact = $possibleContacts[0];
        }

        return [
            'possibleContacts'  => $possibleContacts,
            'identifiedContact' => $identifiedContact
        ];
    }


    /**
     * Update logs relation between system entities
     * @param array $payload
     * @return void
     */
    public function updateCallEntityRelation(array $payload): void
    {
        // TODO - Create generic class and use enum
        if ($payload['type'] === 'call') {
            // Find model and update it to trigger observer
            // If we update using where(id) observer is not triggered
            $call = Call::query()->findOrFail($payload['id']);

            $call->update([
                Call::FIELD_RELATION_TYPE => $payload[Call::FIELD_RELATION_TYPE] ?? null,
                Call::FIELD_RELATION_ID   => $payload[Call::FIELD_RELATION_ID] ?? null,
                Call::FIELD_NOTE          => $payload[Call::FIELD_NOTE] ?? null,
            ]);
        }

        if ($payload['type'] === 'text') {
            $text = Text::query()->findOrFail($payload['id']);

            // Update all messages sent/received to/from the same contact
            Text::query()
                ->where([
                    Text::FIELD_OTHER_NUMBER => $text->{Text::FIELD_OTHER_NUMBER},
                    Text::FIELD_PHONE_ID     => $text->{Text::FIELD_PHONE_ID},
                ])
                ->update([
                    Text::FIELD_RELATION_TYPE => $payload[Call::FIELD_RELATION_TYPE] ?? null,
                    Text::FIELD_RELATION_ID   => $payload[Call::FIELD_RELATION_ID] ?? null,
                    Text::FIELD_NOTE          => $payload[Call::FIELD_NOTE] ?? null,
                ]);
        }
    }

    public function getContactRelation(CommunicationRelationTypes $relType, int $relId): Model|ConsumerProduct|null
    {
        return match ($relType) {
            CommunicationRelationTypes::CONSUMER_PRODUCT => $this->consumerProductRepository->getConsumerProductById($relId),
            CommunicationRelationTypes::COMPANY_USER     => $this->companyUserRepository->getCompanyUserById($relId),
            CommunicationRelationTypes::LEAD,
            CommunicationRelationTypes::CONSUMERS        => $this->consumerRepository->findOrFail($relId),
            CommunicationRelationTypes::COMPANY_LOCATIONS,
            CommunicationRelationTypes::COMPANY_LOCATION,
            CommunicationRelationTypes::COMPANY,
            CommunicationRelationTypes::LEGACY_COMPANY   => $this->companyLocationRepository->findOrFail($relId),
            default                                      => null
        };
    }

    public function getRedirectUrl(CommunicationRelationTypes $relType, int $relId): ?string
    {
        $relation = $this->getContactRelation($relType, $relId);
        $appUrl   = config('app.url');

        return match ($relation::class) {
            ConsumerProduct::class => "$appUrl/consumer-product/?consumer_id=" . $relation->consumer_id,
            CompanyUser::class, CompanyLocation::class => "$appUrl/companies/" . $relation->company_id,
            Consumer::class => "$appUrl/consumer-product/?consumer_id=" . $relation->id,
            default => null,
        };
    }
}
