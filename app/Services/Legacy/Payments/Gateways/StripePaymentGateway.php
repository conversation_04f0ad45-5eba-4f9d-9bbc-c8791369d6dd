<?php

namespace App\Services\Legacy\Payments\Gateways;

use App\Contracts\Legacy\Payments\CreditCardPaymentMethodContract;
use App\Contracts\Legacy\Payments\PaymentGatewayAccountRepositoryContract;
use App\Contracts\Legacy\Payments\PaymentGatewayContract;
use App\Services\Legacy\Exceptions\PaymentException;
use App\Services\Legacy\Payments\CreditCardPaymentMethod;
use Exception;
use Illuminate\Support\Arr;
use Stripe\Card;
use Stripe\Customer;
use Stripe\Exception\ApiErrorException;
use Stripe\PaymentIntent;
use Stripe\PaymentMethod;
use Stripe\Stripe;
use Stripe\StripeClient;

class StripePaymentGateway implements PaymentGatewayContract
{
    public function __construct(protected string $secretKey, protected string $publishableKey, protected PaymentGatewayAccountRepositoryContract $account) {
        Stripe::setApiKey($this->secretKey);
    }

    public function id(): string
    {
        return PaymentGateways::SHORT_CODE_STRIPE;
    }

    public function name(): string
    {
        return PaymentGateways::DISPLAY_NAME_STRIPE;
    }

    public function paymentMethods(): array
    {
        $stripeClient = new StripeClient($this->secretKey);

        $paymentMethods = collect();
        $customer = $this->getCustomer();

        $paymentMethodResponse = $stripeClient->paymentMethods->all([
            'type' => 'card',
            'customer' => $customer->id,
        ]);

        $paymentMethodInstances = Arr::get($paymentMethodResponse, 'data');

        if(!$paymentMethodInstances) {
            return $paymentMethods->toArray();
        }

        //populate credit card payment method instances
        /** @var Card $stripePaymentMethodInstance */
        foreach ($paymentMethodInstances as $stripePaymentMethodInstance) {
            if(!($stripePaymentMethodInstance instanceof PaymentMethod)) {
                $stripePaymentMethodType = get_class($stripePaymentMethodInstance);
                throw new PaymentException("Unable to handle payment method of type '{$stripePaymentMethodType}'");
            }

            $card = $stripePaymentMethodInstance->card;
            $billingDetails = $stripePaymentMethodInstance->billing_details;
            $address = $billingDetails->offsetGet('address') ?? [];

            $id = $stripePaymentMethodInstance->offsetGet('id');
            $expiryMonth = str_pad($card->offsetGet('exp_month'), 2, '0', STR_PAD_LEFT);
            $expiryYear = substr($card->offsetGet('exp_year'),2,2);

            $type = ucwords($card->offsetGet('funding')) . " Card";
            $brand = $card->offsetGet('brand');
            $name = $billingDetails->offsetGet('name') ?? 'no-name';

            $paymentMethod = new CreditCardPaymentMethod(
                $this,
                $id,
                $card->offsetGet('last4'),
                $brand,
                $expiryMonth,
                $expiryYear,
                $type,
                $card->offsetGet('status')
            );

            $paymentMethod
                ->setName($name)
                ->setAddressLineOne($address->offsetGet('line1'))
                ->setAddressLineTwo($address->offsetGet('line2'))
                ->setCity($address->offsetGet('city'))
                ->setState($address->offsetGet('state'))
                ->setZipCode($address->offsetGet('postal_code'))
                ->setCountry($address->offsetGet('country'))
                ->setDefault($customer->default_source === $stripePaymentMethodInstance->offsetGet('id'));

            $paymentMethods->put($id, $paymentMethod);
        }

        return $paymentMethods->toArray();
    }

    /**
     * @param float $totalInCents
     * @param string $chargeId
     * @param array|null $meta
     * @return string
     * @throws ApiErrorException
     */
    public function makeRefundRequest(
        float $totalInCents,
        string $chargeId,
        ?array $meta = [],
    ): string
    {
        $stripeClient = new StripeClient($this->secretKey);

        $refund = $stripeClient->refunds->create([
            'charge'   => $chargeId,
            'amount'   => $totalInCents,
            'metadata' => $meta
        ]);

        return $refund['id'];
    }

    public function hasAccount(): bool
    {
        return !!$this->account->getAccountId();
    }

    public function deletePaymentMethod(string $id): void
    {
        if (str_contains($id, 'card')) {
            $card = $this->getCard($id);
            $card->delete();
        } else if (str_contains($id, 'pm')) {
            $paymentMethod = \Stripe\PaymentMethod::retrieve($id);
            $paymentMethod->detach();
        } else {
            throw new Exception('Payment method not supported id: ' . $id);
        }
    }


    public function setDefaultPaymentMethod(string $id): void
    {
        $customer = $this->getCustomer();

        try {
            Customer::update($customer->id, ['default_source' => $id]);
        } catch (\Exception $exception) {
            throw new PaymentException('Failed to save default payment method');
        }
    }

    public function updatePaymentMethod(CreditCardPaymentMethodContract $method): void
    {
        $customer = $this->getCustomer();
        $card = $this->getCard($method->id());

        $response = $this->executeRequest(function () use ($card, $customer, $method) {
            Customer::updateSource($customer->id, $card->id,
                [
                    'exp_month'       => $method->expiryMonth(),
                    'exp_year'        => $method->expiryYear(),
                    'name'            => $method->name(),
                    'address_line1'   => $method->addressLineOne(),
                    'address_line2'   => $method->addressLineTwo(),
                    'address_city'    => $method->city(),
                    'address_state'   => $method->state(),
                    'address_zip'     => $method->zipCode(),
                    'address_country' => $method->country(),
                ]);
        });

        if (!($response instanceof \Stripe\StripeObject)) {
            throw new PaymentException('Failed to update card.');
        }
    }

    protected function getCustomer(): ?Customer
    {
        if (!$this->hasAccount())
            throw new PaymentException("Account does not exist.");

        $customer = $this->executeRequest(function () {
            return Customer::retrieve($this->account->getAccountId());
        });

        if (!$customer)
            throw new PaymentException('Account missing.');

        if (!empty($customer->deleted))
            throw new PaymentException('Account has been deleted.');

        return $customer;
    }

    protected function getCard(string $id): ?Card
    {
        $response = Customer::retrieveSource(
            $this->account->getAccountId(),
            $id
        );

        if (!$response instanceof \Stripe\Card)
            throw new PaymentException('Card does not exist.');

        if (!empty($response->deleted))
            throw new PaymentException('Card record exists but has already been deleted.');

        return $response;
    }

    public function createCustomer(string $email, string $token): Customer
    {
        $customer = $this->executeRequest(function () use ($email, $token) {
            return Customer::create([
                'email'       => $email,
                'source'      => $token,
                'metadata'    => [
                    'Solar Reviews Company Id' => $this->account->getCompanyId(),
                ],
                'description' => $this->account->getCompanyName(),
            ]);
        });

        if (!$customer) {
            throw new PaymentException('Failed to create customer account');
        }

        $this->account->setAccountId($customer->id);

        return $customer;
    }

    public function addCard(string $token): Card
    {
        $stripeClient = new StripeClient($this->secretKey);

        return $stripeClient->customers->createSource($this->account->getAccountId(), [
            "source" => $token,
        ]);
    }

    protected function executeRequest(callable $apiRequest): mixed
    {
        try {
            return $apiRequest();
        } catch (\Stripe\Exception\AuthenticationException $e) {
            // Supplied Credentials could not be authenticated.
            throw $this->makePaymentException($e, "Authentication Error");
        } catch (\Stripe\Exception\InvalidRequestException $e) {
            return null;
        } catch (\Stripe\Exception\CardException $e) {
            throw $this->makePaymentException($e, "Payment Method Error");
        } catch (\Stripe\Exception\ApiErrorException $e) {
            // Library could not connect to Stripe servers.
            throw $this->makePaymentException($e, "API Connection Error");
        }
    }

    public function validateCard(string $cardId): bool
    {
        try {
            $paymentIntent = PaymentIntent::create([
                'amount'               => 50, //$.50
                'currency'             => 'usd',
                'payment_method_types' => ['card'],
                'capture_method'       => 'manual',
                'payment_method'       => $cardId,
                'description'          => 'SolarReviews card validation',
                'confirm'              => true,
                'customer'             => $this->account->getAccountId()
            ]);
        } catch (\Exception $e) {
            // TODO: Create History.
            //            $createHistoryFactory->makeCommand(
            //                $this->paymentGatewayAccount->getCompanyId(),
            //                0,
            //                EloquentHistory::REL_TYPE_CARD_VALIDATION_EXCEPTION,
            //                $this->paymentGatewayAccount->getCompanyId(),
            //                EloquentHistory::TYPE_EXCEPTION,
            //                $e->getMessage())->execute();
            return false;
        }

        try {
            $paymentIntent->cancel();
        } catch (\Exception $e) {
            // TODO: History
            //            $createHistoryFactory->makeCommand(
            //                $this->paymentGatewayAccount->getCompanyId(),
            //                0,
            //                EloquentHistory::REL_TYPE_CARD_VALIDATION_EXCEPTION,
            //                $this->paymentGatewayAccount->getCompanyId(),
            //                EloquentHistory::TYPE_EXCEPTION,
            //                $e->getMessage())->execute();
            //
            //            log_exception_error($e);
        }

        return true;
    }

    protected function makePaymentException(\Stripe\Exception\ApiErrorException $exception, $type = null): PaymentException
    {
        return new PaymentException(($type ?: "Error") . ": {$exception->getMessage()}", 0, null, [
            "Request ID" => $exception->getRequestId(),
        ]);
    }

    /**
     * Retrieves and counts the number of payment methods (cards) associated with the customer.
     *
     * @return int
     */
    public function getPaymentMethodsCount(): int
    {
        $stripeClient = new StripeClient($this->secretKey);

        try {
            $customer = $this->getCustomer();
            $paymentMethodResponse = $stripeClient->paymentMethods->all([
                'type'     => 'card',
                'customer' => $customer->id,
            ]);

            $paymentMethodInstances = Arr::get($paymentMethodResponse, 'data', []);
            return count($paymentMethodInstances);
        } catch (\Exception $e) {
            return 0;
        }
    }
}
