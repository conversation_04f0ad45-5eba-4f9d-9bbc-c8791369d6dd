<?php

namespace App\Http\Controllers\DashboardAPI\V4;

use App\Campaigns\Delivery\CRM\Enums\CRMType;
use App\Campaigns\Delivery\CRM\Enums\WebformPrefill;
use App\Campaigns\Modules\Bidding\ProductBiddingModule;
use App\Campaigns\Modules\DashboardModule;
use App\Enums\Campaigns\CampaignStatus;
use App\Enums\Campaigns\CampaignType;
use App\Enums\Campaigns\CRMFieldReplacerKey;
use App\Enums\Campaigns\CustomCampaignBudgetType;
use App\Enums\Odin\Industry as IndustryEnum;
use App\Enums\Odin\Grouping;
use App\Enums\Odin\Product as ProductEnum;
use App\Http\Requests\CompanyCampaigns\SaveCompanyCampaignRequest;
use App\Http\Requests\CompanyCampaigns\StoreCompanyCRMTemplateRequest;
use App\Http\Resources\CompanyCampaign\BaseCompanyCampaignSummaryResource;
use App\Http\Resources\Dashboard\v4\CompanyCampaignCRMDelivererResource;
use App\Http\Resources\Dashboard\v4\DashboardCompanyCampaignSummaryResource;
use App\Http\Resources\Dashboard\v4\CompanyCRMTemplateResource;
use App\Models\Campaigns\CampaignReactivation;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModule;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleCRM;
use App\Models\Campaigns\Modules\Delivery\CompanyCRMTemplate;
use App\Models\Odin\Company;
use App\Models\Odin\Industry as IndustryModel;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product as ProductModel;
use App\Http\Controllers\DashboardAPI\BaseDashboardApiController;
use App\Mediators\CampaignMediator;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\PropertyType;
use App\Registries\CampaignRegistry;
use App\Repositories\Odin\Campaigns\CompanyCampaignRepository;
use App\Services\Campaigns\CampaignHelpService;
use App\Services\Campaigns\CompanyCampaignService;
use App\Services\Campaigns\CRMIntegrations\CRMDelivererService;
use App\Services\Companies\ZipCodeExceptionService;
use App\Services\CompanyCRMTemplateService;
use App\Services\Dashboard\DashboardAuthService;
use App\Services\Dashboard\JWT\DashboardJWTService;
use App\Services\Odin\ProductStatistics\ProductStatisticsService;
use Carbon\Carbon;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;
use Illuminate\Support\Str;
use Psr\Container\ContainerExceptionInterface;
use Psr\Container\NotFoundExceptionInterface;

class CompanyCampaignController extends BaseDashboardApiController
{
    const string RESPONSE_STATUS                 = 'status';
    const string RESPONSE_CAMPAIGNS              = 'campaigns';
    const string RESPONSE_CAMPAIGN               = 'campaign';
    const string RESPONSE_CONFIG_DATA            = 'config_data';
    const string RESPONSE_PRODUCT                = 'product';
    const string RESPONSE_REFERENCE              = 'reference';
    const string RESPONSE_MODULES                = 'modules';
    const string RESPONSE_WIZARD                 = 'wizard';
    const string RESPONSE_ID                     = 'id';
    const string RESPONSE_PRODUCT_CONFIGURATIONS = 'product_configurations';
    const string RESPONSE_STATUS_CONFIGURATION   = 'status_configuration';
    const string RESPONSE_CRM_CONFIGURATIONS     = 'crm_configurations';
    const string RESPONSE_CRM_TEMPLATES          = 'crm_templates';
    const string RESPONSE_CRM_DELIVERER          = 'crm_deliverer';
    const string RESPONSE_CRM_DELIVERERS         = 'crm_deliverers';
    const string RESPONSE_CRM_SHORTCODES         = 'crm_shortcodes';
    const string RESPONSE_CRM_PREFILLS           = 'crm_prefills';
    const string RESPONSE_ZIP_CODES              = 'zip_codes';
    const string RESPONSE_MESSAGE                = 'message';
    const string RESPONSE_ZIP_CODE_EXCEPTIONS    = 'zip_code_exceptions';
    const string RESPONSE_CAMPAIGN_TYPE          = 'campaign_type';
    const string RESPONSE_CAMPAIGN_CONFIGURATION = 'campaign_configuration';
    const string RESPONSE_STATISTICS             = 'campaign_statistics';
    const string RESPONSE_REPLACER_INSTRUCTIONS  = 'replacer_instructions';
    const string RESPONSE_REPLACER_EXAMPLES      = 'replacer_examples';

    const string REQUEST_PAYLOAD             = 'payload';
    const string REQUEST_PRODUCT             = 'product';
    const string REQUEST_SCHEDULES           = 'schedules';
    const string REQUEST_MODULES             = 'modules';
    const string REQUEST_NEW_CAMPAIGN        = 'new';
    const string REQUEST_REFERENCES          = 'references';
    const string REQUEST_CRM_TYPE_ID         = 'crm_type_id';
    const string REQUEST_METHOD_NAME         = 'method_name';
    const string REQUEST_CRM_DELIVERER_ID    = 'crm_deliverer_id';
    const string REQUEST_INDUSTRY_SLUG       = 'industry_slug';
    const string REQUEST_UUIDS               = 'uuids';
    const string REQUEST_ALL_PRODUCTS        = 'all_products';
    const string REQUEST_TEMPLATE_ID         = 'template_id';
    const string REQUEST_IS_TEMPLATE         = 'is_template';

    const string FLAG_USE_V2 = 'v2';

    public function __construct(
        Request                                    $request,
        DashboardAuthService                       $authService,
        DashboardJWTService                        $jwtService,
        private readonly CampaignMediator          $campaignMediator,
        private readonly CompanyCampaignRepository $companyCampaignRepository,
        protected CompanyCampaignService           $companyCampaignService,
    )
    {
        parent::__construct($request, $authService, $jwtService);
    }

    /**
     * @param DashboardModule $dashboardModule
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getCampaignDetail(DashboardModule $dashboardModule): JsonResponse
    {
        $campaignReference = $this->request->route('campaignReference', null);

        $campaign = $this->companyCampaignRepository->findOrFailByReference($campaignReference);

        $transformedCampaign = $dashboardModule->transform($campaign);

        /** @var Collection $modules */
        $modules = $transformedCampaign->get('modules');
        $moduleKeys = $modules->values()->toArray();

        $transformedCampaign = $transformedCampaign->mergeRecursive(
            $this->campaignMediator->transformSingle(
                $campaign,
                $moduleKeys,
            )
        );

        return $this->formatResponse([
            self::RESPONSE_STATUS                 => !empty($transformedCampaign),
            self::RESPONSE_CAMPAIGN               => $transformedCampaign->toArray(),
            self::RESPONSE_CAMPAIGN_CONFIGURATION => $this->companyCampaignRepository->getConfigurationByCampaignType($campaign->type),
        ]);
    }

    /**
     * @param CampaignRegistry $campaignRegistry
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getNewCampaignConfiguration(CampaignRegistry $campaignRegistry): JsonResponse
    {
        $product = ProductEnum::tryFrom($this->getProduct()->name ?? '');
        $industry = IndustryEnum::tryFrom($this->getService()?->industry->name ?? '');
        $serviceKey = $this->getService()->slug;
        $budgetKey = CustomCampaignBudgetType::tryFrom($this->request->get('customBudgetKey', ''));

        $campaignType = $campaignRegistry->getCampaignMappingType($product, $industry, $serviceKey, $budgetKey);
        $campaignDefinition = $campaignRegistry->getCampaignDefinition($campaignType);

        return $this->formatResponse([
            self::RESPONSE_STATUS                 => !!$campaignDefinition,
            self::RESPONSE_CAMPAIGN               => [CompanyCampaign::FIELD_TYPE => $campaignType],
            self::RESPONSE_CAMPAIGN_CONFIGURATION => $this->companyCampaignRepository->getConfigurationByCampaignType($campaignType),
        ]);
    }

    /**
     * Returns the campaigns for a given company.
     *
     * @param ZipCodeExceptionService $zipCodeExceptionService
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getCampaigns(
        string $productKey,
        int $companyId,
        string $industry,
        string $service,
        ZipCodeExceptionService $zipCodeExceptionService
    ): JsonResponse
    {
        $allProducts = $this->request->get(self::REQUEST_ALL_PRODUCTS, false);
        $company = $this->getCompany();
        $campaigns = $this->getCompany()
            ->futureCampaigns()
            ->where(CompanyCampaign::FIELD_SERVICE_ID, $this->getService()?->id)
            ->when(!$allProducts, fn(\Illuminate\Database\Eloquent\Builder $query) =>
                $query->where(CompanyCampaign::FIELD_PRODUCT_ID, $this->getProduct()?->id))
            ->get();

        return $this->formatResponse([
            self::RESPONSE_STATUS              => true,
            self::RESPONSE_PRODUCT             => $this->getProduct()->{ProductModel::FIELD_NAME},
            self::RESPONSE_CAMPAIGNS           => DashboardCompanyCampaignSummaryResource::collection($campaigns),
            self::RESPONSE_ZIP_CODE_EXCEPTIONS => $zipCodeExceptionService->getCompanyExceptionsKeyedByState($company->id),
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getZipCodes(): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_STATUS => true,
            self::RESPONSE_ZIP_CODES => $this->companyCampaignRepository->getCampaignZipCodes(
                $this->request->get(self::REQUEST_UUIDS, [])
            )
        ]);
    }

    /**
     * Handles returning the modules and wizard slides for the frontend.
     *
     * @param CampaignRegistry $registry
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getModuleConfigurations(CampaignRegistry $registry): JsonResponse
    {
        $product = ProductEnum::tryFrom($this->getProduct()?->name ?? "") ?? ProductEnum::LEAD;
        $industry = IndustryEnum::tryFrom($this->getService()->industry->name);
        $campaignType = CampaignType::tryFrom($this->request->get(CompanyCampaign::FIELD_TYPE, -1))
            ?? $registry->getCampaignMappingType($product, $industry, $this->getService()->name);

        $campaignDefinition = $registry->getCampaignDefinition($campaignType);

        return $this->formatResponse([
            self::RESPONSE_STATUS  => isset($campaignDefinition),
            self::RESPONSE_CAMPAIGN_TYPE => $campaignType->value,
            self::RESPONSE_MODULES => isset($campaignDefinition) ? $this->campaignMediator->getFrontendModuleConfiguration($campaignDefinition)->values() : [],
            self::RESPONSE_WIZARD  => isset($campaignDefinition) ? $this->campaignMediator->getFrontEndWizardConfiguration($campaignDefinition)->values() : [],
        ]);
    }

    /**
     * @param string $productKey
     * @param int $companyId
     * @param string $industry
     * @param string $service
     * @param string $campaignReference
     * @param SaveCompanyCampaignRequest $saveCompanyCampaignRequest
     *
     * @return JsonResponse
     * @throws BindingResolutionException
     * @throws ContainerExceptionInterface
     * @throws NotFoundExceptionInterface
     * @throws Exception
     */
    public function saveCampaign(
        string $productKey,
        int $companyId,
        string $industry,
        string $service,
        string $campaignReference,
        SaveCompanyCampaignRequest $saveCompanyCampaignRequest
    ): JsonResponse
    {
        $data = $saveCompanyCampaignRequest->safe()->only([
            CompanyCampaign::FIELD_TYPE,
            CompanyCampaign::FIELD_STATUS,
            CompanyCampaign::FIELD_NAME,
            CompanyCampaign::FIELD_ZIP_CODE_TARGETED,
            SaveCompanyCampaignRequest::REQUEST_PROPERTY_TYPES,
            self::REQUEST_PAYLOAD
        ]);

        /** @var Company $company */
        $company = Company::query()->findOrFail($companyId);
        $unrestrictedZipCodes = $company->configuration?->unrestricted_zip_code_targeting ?? false;

        $campaign = $campaignReference === self::REQUEST_NEW_CAMPAIGN
            ? null
            : $this->companyCampaignRepository->findOrFailByReference($campaignReference);

        if (!$unrestrictedZipCodes && $data[CompanyCampaign::FIELD_ZIP_CODE_TARGETED]) {

            /** @var ZipCodeExceptionService $zipCodeService */
            $zipCodeService = app(ZipCodeExceptionService::class);
            if (!$zipCodeService->companyHasExceptions($companyId))
                throw new Exception("This company cannot create zip code targeted campaigns.");
        }

        if ($campaign?->bidding_disabled && !empty(Arr::get($data, self::REQUEST_PAYLOAD . '.' . ProductBiddingModule::PAYLOAD_PARENT_KEY . '.' . ProductBiddingModule::PAYLOAD_LOCATION_BIDS))) {
            throw new Exception("Bidding is disabled for this campaign");
        }

        $productId = ProductModel::query()
            ->where(ProductModel::FIELD_NAME, $productKey)
            ->firstOrFail()
            ->id;
        $propertyTypes = PropertyType::query()
            ->whereIn(PropertyType::FIELD_NAME, $data[SaveCompanyCampaignRequest::REQUEST_PROPERTY_TYPES])
            ->pluck(PropertyType::FIELD_ID)
            ->toArray();

        /** @var IndustryService $industryService */
        $industryService = IndustryService::query()
            ->where(IndustryService::FIELD_SLUG, $service)
            ->whereHas(IndustryService::RELATION_INDUSTRY, function($has) use ($industry) {
                $has->where(IndustryModel::FIELD_SLUG, $industry);
            })
            ->firstOrFail();

        $isFutureCampaignEnabled = !!$industryService->industry->industryConfiguration?->future_campaigns_active;
        if (!$isFutureCampaignEnabled) {
            return $this->formatResponse([
                self::RESPONSE_STATUS   => false,
                self::RESPONSE_MESSAGE  => "Cannot create this Campaign type for the service '$industryService->name'. Please reload the page and try again.",
            ]);
        }

        $campaign = $this->companyCampaignRepository->updateOrCreate(
            $company,
            $productId,
            $industryService->id,
            $data[CompanyCampaign::FIELD_NAME],
            CampaignStatus::from($data[CompanyCampaign::FIELD_STATUS]),
            $propertyTypes,
            $campaign,
            $unrestrictedZipCodes || ($data[CompanyCampaign::FIELD_ZIP_CODE_TARGETED] ?? false),
        );

        $fullPayload = collect($data[self::REQUEST_PAYLOAD])->merge(['saved_by_company_user_id' => $this->getUser()?->id]);

        return $this->formatResponse([
            self::RESPONSE_STATUS => $this->campaignMediator->save($campaign, $fullPayload),
            self::RESPONSE_ID => $campaign->{CompanyCampaign::FIELD_ID}
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getCRMConfigurations(CampaignHelpService $campaignHelpService): JsonResponse
    {
        $company = $this->getCompany();
        $configurations = CRMType::getAllFieldConfigurations();
        $templates = $company->crmTemplates;
        $prefills = WebformPrefill::getAllPrefillData();
        ksort($prefills);
        $referenceExamples = $campaignHelpService->getCrmFieldReplacerExamples();
        $referenceInstructions = $campaignHelpService->getFieldReplacerInstructions();

        return $this->formatResponse([
            self::RESPONSE_STATUS                => !!$configurations,
            self::RESPONSE_CRM_CONFIGURATIONS    => $configurations,
            self::RESPONSE_CRM_TEMPLATES         => CompanyCRMTemplateResource::collection($templates),
            self::RESPONSE_CRM_SHORTCODES        => CRMFieldReplacerKey::getAllKeysAndNames(),
            self::RESPONSE_CRM_PREFILLS          => $prefills,
            self::RESPONSE_REPLACER_INSTRUCTIONS => $referenceInstructions,
            self::RESPONSE_REPLACER_EXAMPLES     => $referenceExamples,
        ]);
    }

    /**
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getProductConfigurations(): JsonResponse
    {
        $company = $this->getCompany();
        $industrySlug = $this->request->get(self::REQUEST_INDUSTRY_SLUG, "");
        $industry = IndustryEnum::tryFromSlug($industrySlug);

        $productConfig = $this->companyCampaignRepository->getProductConfigData($company, $industry);
        $statusConfig = $this->companyCampaignService->getCampaignStatusConfig();

        return $this->formatResponse([
            self::RESPONSE_STATUS                 => true,
            self::RESPONSE_PRODUCT_CONFIGURATIONS => $productConfig,
            self::RESPONSE_STATUS_CONFIGURATION   => $statusConfig,
        ]);
    }

    /**
     * @return JsonResponse
     * @throws Exception
     */
    public function pauseCampaigns(): JsonResponse
    {
        $validated = $this->request->validate([
            self::REQUEST_REFERENCES                                => ['array'],
            CompanyCampaign::FIELD_STATUS                           => ['numeric', 'required'],
            CampaignReactivation::FIELD_REASON                      => ['string', 'required'],
            CampaignReactivation::FIELD_REACTIVATE_AT               => ['array', 'nullable'],
            CampaignReactivation::FIELD_REACTIVATE_AT . 'timestamp' => ['date', 'nullable'],
            CampaignReactivation::FIELD_REACTIVATE_AT . 'offset'    => ['number', 'nullable'],
        ]);

        $newStatus = CampaignStatus::from($validated[CompanyCampaign::FIELD_STATUS]);
        $reactivate = $validated[CampaignReactivation::FIELD_REACTIVATE_AT] ?? [];

        if ($newStatus === CampaignStatus::PAUSED_TEMPORARILY) {
            $reactivateAt = new Carbon($reactivate['timestamp'] ?? null);
            $reactivateAt->shiftTimezone($reactivate['offset'] ?? 0);
            $reactivateAt->setTimezone('utc');

            if (!$reactivateAt || $reactivateAt < now()) {
                throw new Exception("A valid Reactivation date must be supplied for a temporary pause.");
            }
        }
        else {
            $reactivateAt = null;
        }

        $campaigns = collect();
        foreach ($validated[self::REQUEST_REFERENCES] as $reference) {
            $campaigns->push($this->companyCampaignRepository->findOrFailByReference($reference));
        }

        $campaigns->each(function (CompanyCampaign $campaign) use ($newStatus, $validated, $reactivateAt) {
            $oldStatus = $campaign->status;
            $this->companyCampaignService->pauseCampaign($campaign, $newStatus, $oldStatus, $validated[CampaignReactivation::FIELD_REASON], $reactivateAt);
        });

        return $this->formatResponse([
            self::RESPONSE_STATUS   => true,
        ]);
    }

    /**
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function unpauseCampaigns(): JsonResponse
    {
        $validated = $this->request->validate([
            self::REQUEST_REFERENCES => ['array'],
        ]);

        $campaigns = collect();
        foreach ($validated[self::REQUEST_REFERENCES] as $reference) {
            $campaigns->push($this->companyCampaignRepository->findOrFailByReference($reference));
        }

        $campaigns->each(function (CompanyCampaign $campaign) use ($validated) {
            $oldStatus = $campaign->status;
            $this->companyCampaignService->unpauseCampaign($campaign, $oldStatus);
        });

        return $this->formatResponse([
            self::RESPONSE_STATUS   => true,
        ]);
    }

    /**
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function deleteCampaign(): JsonResponse
    {
        $campaign = $this->companyCampaignRepository->findOrFailByReference($this->request->route('campaignReference'));

        $success = $this->companyCampaignService->deleteCampaign($campaign);

        return $this->formatResponse([
            self::RESPONSE_STATUS   => $success,
        ]);
    }

    /**
     * @param CRMDelivererService $CRMDelivererService
     * @return JsonResponse
     * @throws Exception
     */
    public function executeCrmMethod(CRMDelivererService $CRMDelivererService): JsonResponse
    {
        $crmType = CRMType::tryFrom($this->request->get(self::REQUEST_CRM_TYPE_ID));
        $methodName = $this->request->get(self::REQUEST_METHOD_NAME);
        $delivererId = $this->request->get(self::REQUEST_CRM_DELIVERER_ID);
        $payload = $this->request->get(self::REQUEST_PAYLOAD, []);
        $isTemplate = $this->request->get(self::REQUEST_IS_TEMPLATE, false);

        if ((!$crmType && !$delivererId) || !$methodName)
            throw new Exception("Bad CRM Interactable call - no CRM type or missing method.");

        $result = $CRMDelivererService->executeInteractableMethod($methodName, $payload, $crmType, $delivererId, $isTemplate);

        return $this->formatResponse([
            self::RESPONSE_STATUS           => !!$result,
            self::RESPONSE_CRM_DELIVERER    => $result,
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getCrmImportOptions(): JsonResponse
    {
        $companyId = $this->getCompany()->id;
        $campaignReference = $this->request->get(CompanyCampaign::FIELD_REFERENCE, '');

        $crmOptions = CompanyCampaignDeliveryModuleCRM::query()
            ->join(CompanyCampaignDeliveryModule::TABLE, CompanyCampaignDeliveryModule::TABLE .'.'. CompanyCampaignDeliveryModule::FIELD_ID, '=', CompanyCampaignDeliveryModuleCRM::FIELD_MODULE_ID)
            ->join(CompanyCampaign::TABLE, CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_ID, '=', CompanyCampaignDeliveryModule::FIELD_CAMPAIGN_ID)
            ->where(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_COMPANY_ID, $companyId)
            ->where(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_REFERENCE, '!=', $campaignReference)
            ->whereNull(CompanyCampaignDeliveryModuleCRM::FIELD_TEMPLATE_ID)
            ->whereNull(CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_DELETED_AT)
            ->select([CompanyCampaignDeliveryModuleCRM::TABLE . '.*', CompanyCampaign::TABLE .'.'. CompanyCampaign::FIELD_NAME . ' as campaign_name'])
            ->get();

        return $this->formatResponse([
            self::RESPONSE_STATUS         => true,
            self::RESPONSE_CRM_DELIVERERS => CompanyCampaignCRMDelivererResource::collection($crmOptions),
        ]);
    }

    /**
     * @param StoreCompanyCRMTemplateRequest $request
     * @param CompanyCRMTemplateService $templateService
     * @param CompanyCampaignRepository $companyCampaignRepository
     * @return JsonResponse
     */
    public function saveCrmTemplate(StoreCompanyCRMTemplateRequest $request, CompanyCRMTemplateService $templateService, CompanyCampaignRepository $companyCampaignRepository): JsonResponse
    {
        $company = $this->getCompany();
        $templatePayload = $request->safe()->toArray();
        $templatePayload[CompanyCRMTemplate::FIELD_COMPANY_ID] = $company->id;
        $syncCampaigns = $templatePayload[StoreCompanyCRMTemplateRequest::PAYLOAD_SYNC_CAMPAIGNS] ?? false;

        $success = $templateService->saveCompanyCRMTemplate($templatePayload, $syncCampaigns);
        $templates = $success
            ? $company->crmTemplates
            : collect();

        return $this->formatResponse([
            self::RESPONSE_STATUS        => $success,
            self::RESPONSE_CRM_TEMPLATES => CompanyCRMTemplateResource::collection($templates),
        ]);
    }

    /**
     * @param CompanyCRMTemplateService $templateService
     * @return JsonResponse
     */
    public function deleteCrmTemplate(CompanyCRMTemplateService $templateService): JsonResponse
    {
        $templateId = $this->request->get(self::REQUEST_TEMPLATE_ID);
        /** @var CompanyCRMTemplate $template */
        $template = CompanyCRMTemplate::query()->findOrFail($templateId);
        $success = $templateService->deleteCompanyCRMTemplate($template);
        $templates = $success
            ? $this->getCompany()->crmTemplates
            : collect();

        return $this->formatResponse([
            self::RESPONSE_STATUS   => $success,
            self::RESPONSE_CRM_TEMPLATES => CompanyCRMTemplateResource::collection($templates),
        ]);
    }

    /**
     * @param ZipCodeExceptionService $zipCodeExceptionService
     * @return JsonResponse
     */
    public function validateTargetedZipCodes(ZipCodeExceptionService $zipCodeExceptionService): JsonResponse
    {
        $this->request->validate([ self::RESPONSE_ZIP_CODES => ['array'] ]);
        $zipCodeIds = $this->request->get(self::RESPONSE_ZIP_CODES);
        $companyId = $this->request->route('companyId');
        $validatedZipCodes = $zipCodeExceptionService->validateZipCodeLocationIdsForCompany($companyId, $zipCodeIds);

        return $this->formatResponse([
            self::RESPONSE_STATUS    => !!$validatedZipCodes,
            self::RESPONSE_ZIP_CODES => $validatedZipCodes
        ]);
    }

    /**
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getCampaignStatistics(): JsonResponse
    {
        $company = $this->getCompany();
        $serviceProduct = $this->getServiceProduct();

        if (!$company || !$serviceProduct)
            return $this->formatResponse([
                self::RESPONSE_STATUS => false,
            ]);

        /** @var ProductStatisticsService $statisticsService */
        $statisticsService = app(ProductStatisticsService::class);

        $stats = $statisticsService->getCampaignListStatistics(
            $company->id,
            $serviceProduct->id,
        );

        return $this->formatResponse([
            self::RESPONSE_STATUS     => !!$stats,
            self::RESPONSE_STATISTICS => $stats
        ]);
    }
}
