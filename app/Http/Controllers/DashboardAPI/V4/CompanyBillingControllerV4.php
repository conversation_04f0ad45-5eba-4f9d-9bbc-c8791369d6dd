<?php

namespace App\Http\Controllers\DashboardAPI\V4;

use App\Enums\Billing\PaymentMethodServices;
use App\Http\Controllers\DashboardAPI\BaseDashboardApiController;
use App\Http\Requests\Dashboard\AddBillingProfileRequestV4;
use App\Http\Requests\Dashboard\InvoiceRequestV4;
use App\Http\Requests\Dashboard\SearchInvoicesRequestV4;
use App\Http\Resources\Billing\BillingProfileResource;
use App\Http\Resources\Dashboard\v4\BillingSummaryResourceV4;
use App\Http\Resources\Dashboard\v4\InvoiceResourceV4;
use App\Http\Resources\Dashboard\v4\PaymentMethodResource;
use App\Models\Billing\BillingProfile;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyUser;
use App\Repositories\Billing\CompanyBillingRepository;
use App\Services\Billing\BillingProfile\BillingProfileService;
use App\Services\Billing\CompanyBillingServiceV4;
use App\Services\Billing\CompanyInvoiceService;
use App\Services\Billing\CreditService;
use App\Services\Billing\InvoiceService;
use App\Services\Dashboard\DashboardAuthService;
use App\Services\Dashboard\JWT\DashboardJWTService;
use App\Services\Odin\ProductAssignmentService;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Validation\ValidationException;

class CompanyBillingControllerV4 extends BaseDashboardApiController
{
    const string STATUS      = 'status';
    const string INVOICES    = 'invoices';
    const string TOTAL       = 'total';
    const string LIMIT       = 'limit';
    const string OFFSET      = 'offset';
    const string CONFIG_DATA = 'config_data';
    const string MESSAGE     = 'message';
    const string SUMMARY     = 'summary';
    const string PAID        = 'paid';

    public function __construct(
        Request $request,
        DashboardAuthService $authService,
        DashboardJWTService $jwtService,
        protected InvoiceService $invoiceService,
        protected CreditService $creditService,
        protected ProductAssignmentService $productAssignmentService,
        protected BillingProfileService $billingProfileService,
        protected CompanyBillingServiceV4 $billingServiceV4,
        protected CompanyInvoiceService $companyInvoiceService,
        protected CompanyBillingRepository $companyBillingRepository,
    )
    {
        parent::__construct($request, $authService, $jwtService);
    }

    /**
     * @param SearchInvoicesRequestV4 $request
     * @return JsonResponse
     */
    public function getCompanyInvoices(SearchInvoicesRequestV4 $request): JsonResponse
    {
        $validated = $request->validated();

        $companyId = $this->getUser()->{CompanyUser::FIELD_COMPANY_ID};

        $canPayInvoices = $this->billingServiceV4->hasAtLeastOneCardOnFile($companyId);

        $invoices = $this->companyInvoiceService
            ->getInvoicesCrossSystem(
                companyId: $this->getUser()->{CompanyUser::FIELD_COMPANY_ID},
                status   : Arr::get($validated, SearchInvoicesRequestV4::STATUS),
                startDate: Arr::get($validated, SearchInvoicesRequestV4::START_DATE),
                endDate  : Arr::get($validated, SearchInvoicesRequestV4::END_DATE),
                invoiceId: Arr::get($validated, SearchInvoicesRequestV4::INVOICE_ID),
            )
            ->paginate(
                perPage: Arr::get($validated, SearchInvoicesRequestV4::PER_PAGE, 25),
                page   : Arr::get($validated, SearchInvoicesRequestV4::PAGE, 1),
            );

        return $this->formatResponse([
            self::STATUS      => true,
            self::INVOICES    => InvoiceResourceV4::collection($invoices),
            self::TOTAL       => $invoices->total(),
            self::LIMIT       => $invoices->perPage(),
            self::OFFSET      => ($invoices->currentPage() - 1) * $invoices->perPage(),
            self::CONFIG_DATA => [
                'can_pay_invoices' => $canPayInvoices,
            ]
        ]);
    }

    /**
     * @param InvoiceRequestV4 $request
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function payInvoice(InvoiceRequestV4 $request): JsonResponse
    {
        $validated = $request->validated();
        $userId = $this->getUser()->id;
        $company = $this->getCompanyOrFail();

        $canPayInvoices = $this->billingServiceV4->hasAtLeastOneCardOnFile($company->id);

        if (!$canPayInvoices) {
            throw ValidationException::withMessages([
                'message' => 'No credit available on file. Unable to process invoice payment.'
            ]);
        }


        ['status' => $status, 'message' => $message] = $this->billingServiceV4
            ->payCustomerInvoice($validated[InvoiceRequestV4::INVOICE_ID], $company, $userId);

        return $this->formatResponse([
            self::STATUS  => $status,
            self::MESSAGE => $message,
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getBillingSummary(): JsonResponse
    {
        $company = $this->getCompanyOrFail();

        $creditBalances = $this->creditService->getCreditBalances($company->id);

        $productAssignments = $this->productAssignmentService->getUninvoicedProductAssignments($company);

        return $this->formatResponse([
            self::STATUS  => true,
            self::MESSAGE => 'Summary info received successfully',
            self::SUMMARY => new BillingSummaryResourceV4(collect([
                BillingSummaryResourceV4::FIELD_CREDIT_DATA  => $creditBalances,
                BillingSummaryResourceV4::FIELD_PRODUCT_DATA => $productAssignments,
            ]))
        ]);
    }

    /**
     * @param AddBillingProfileRequestV4 $request
     * @return JsonResponse
     * @throws Exception
     */
    public function initialiseCustomerBilling(AddBillingProfileRequestV4 $request): JsonResponse
    {
        $validated = $request->validated();

        $company = $this->getCompanyOrFail();

        /** @var CompanyUser $user */
        $user = $this->getUser();

        $status = $this->billingServiceV4->initialiseCustomerBilling(
            company    : $company,
            user       : $user,
            stripeToken: Arr::get($validated, AddBillingProfileRequestV4::STRIPE_TOKEN),
            number     : Arr::get($validated, AddBillingProfileRequestV4::FIELD_NUMBER),
            expiryMonth: Arr::get($validated, AddBillingProfileRequestV4::FIELD_EXPIRY_MONTH),
            expiryYear : Arr::get($validated, AddBillingProfileRequestV4::FIELD_EXPIRY_YEAR),
        );

        return $this->formatResponse([
            self::STATUS => $status,
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getPaymentMethods(): JsonResponse
    {
        $company = $this->getCompanyOrFail();

        $paymentMethods = $this->billingServiceV4->getAllCompanyStripePaymentMethods(
            companyId: $company->id
        );

        return $this->formatResponse([
            self::STATUS => true,
            'methods'    => new PaymentMethodResource(collect($paymentMethods))
        ]);

    }

    /**
     * @return JsonResponse
     */
    public function deletePaymentMethod(): JsonResponse
    {
        $cardId = $this->request->route('cardId');

        $status = $this->billingServiceV4->deletePaymentMethod($cardId);

        return $this->formatResponse([
            self::STATUS => $status
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function makePaymentMethodPrimary(): JsonResponse
    {
        $cardId = $this->request->route('cardId');

        $status = $this->billingServiceV4->setDefaultPaymentMethod($cardId);

        return $this->formatResponse([
            self::STATUS => $status
        ]);
    }

    /**
     * @param Company $company
     * @return JsonResponse
     */
    public function getBillingVersion(Company $company): JsonResponse
    {
        return $this->formatResponse([
            'version' => $this->companyBillingRepository->getBillingVersion(company: $company)
        ]);
    }

    /**
     * @param Company $company
     * @return JsonResponse
     */
    public function getDefaultBillingProfile(Company $company): JsonResponse
    {
        $billingProfile = BillingProfile::query()
            ->where(BillingProfile::FIELD_COMPANY_ID, $company->id)
            ->where(BillingProfile::FIELD_DEFAULT, true)
            ->first();

        if (!$billingProfile) {
            $billingProfile = $this->billingProfileService->createProfileWithDefaultConfiguration(
                paymentMethod: PaymentMethodServices::STRIPE,
                companyId    : $company->id,
                default      : true,
            );
        }

        return $this->formatResponse([
            'default_billing_profile' => new BillingProfileResource($billingProfile)
        ]);
    }
}
