<?php

namespace App\Http\Controllers\DashboardAPI;

use App\Enums\EventCategory;
use App\Enums\EventName;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Http\Requests\Dashboard\StoreCompanyLicenseRequest;
use App\Http\Resources\Dashboard\LicenseResource;
use App\Models\License;
use App\Models\Odin\Company;
use App\Repositories\Odin\CompanyRepository;
use App\Http\Resources\Dashboard\CompanyMediaAssetResource;
use App\Repositories\Odin\LicenseRepository;
use App\Services\Companies\CompanyProfileService;
use App\Services\Workflows\WorkflowEventService;
use App\Workflows\Shortcodes\EventValueNameShortcode;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Exception;
use Illuminate\Validation\ValidationException;
use App\Services\Dashboard\DashboardAuthService;
use App\Services\Dashboard\JWT\DashboardJWTService;
use Illuminate\Http\Request;

class CompanyProfileController extends BaseDashboardApiController
{
    const string REQUEST_COMPANY_MEDIA_ASSETS   = 'media_files';
    const string REQUEST_STATUS_KEY             = 'status';
    const string REQUEST_LOGO_STATUS_KEY        = 'logo';
    const string REQUEST_LICENSE_KEY            = 'license';
    const string REQUEST_LICENSES_KEY           = 'licenses';
    const string REQUEST_LICENSE_ID_KEY         = 'licenseId';
    const string REQUEST_URL_KEY                = 'url';

    /**
     * @param Request $request
     * @param DashboardAuthService $authService
     * @param DashboardJWTService $jwtService
     * @param WorkflowEventService $workflowEventService
     */
    public function __construct(
        Request                         $request,
        DashboardAuthService            $authService,
        DashboardJWTService             $jwtService,
        protected WorkflowEventService  $workflowEventService,
    )
    {
        parent::__construct($request, $authService, $jwtService);
    }

    /**
     * @param CompanyProfileService $service
     * @return JsonResponse
     */
    public function getCompanyLogo(CompanyProfileService $service): JsonResponse
    {
        $company = $this->getCompany();

        return $this->formatResponse([
            self::REQUEST_STATUS_KEY        => true,
            self::REQUEST_LOGO_STATUS_KEY   => $service->getCompanyLogoURL($company)
        ]);
    }

    /**
     * @param CompanyProfileService $service
     * @return JsonResponse
     * @throws Exception
     */
    public function updateCompanyLogo(CompanyProfileService $service): JsonResponse
    {
        $company = $this->getCompany();
        $this->validateLogo();

        $logo = $this->request->file(self::REQUEST_LOGO_STATUS_KEY);

        $updatedLogoUrl = $service->updateCompanyLogo($company, $logo);

        if(!is_null($updatedLogoUrl)) {
            /** Trigger workflow event for company logo update here */
            $this->triggerWorkflowEvent(
                $this->workflowEventService,
                EventCategory::COMPANIES,
                EventName::PROFILE_MEDIA_LOGO_UPDATED
            );
        }

        return $this->formatResponse([
            self::REQUEST_STATUS_KEY        => true,
            self::REQUEST_LOGO_STATUS_KEY   => $updatedLogoUrl
        ]);
    }

    /**
     * @param CompanyProfileService $service
     * @return JsonResponse
     */
    public function getCompanyMediaAssets(CompanyProfileService $service): JsonResponse
    {
        $company = $this->getCompany();
        if($company === null)
            throw new ModelNotFoundException();

        return $this->formatResponse([
            self::REQUEST_STATUS_KEY            => true,
            self::REQUEST_COMPANY_MEDIA_ASSETS  => CompanyMediaAssetResource::collection($company->companyMediaAssets),
        ]);
    }

    /**
     * @param CompanyProfileService $service
     * @return JsonResponse
     * @throws Exception
     */
    public function uploadCompanyMediaAssets(CompanyProfileService $service): JsonResponse
    {
        $company = $this->getCompany();
        if($company === null)
            throw new ModelNotFoundException();

        $this->validateMediaAssets();
        $files = $this->request->file(self::REQUEST_COMPANY_MEDIA_ASSETS);

        $newAssets = $service->uploadCompanyMediaAssets($company, $files);

        if (!$newAssets->isEmpty()) {
            /** Trigger workflow event for company asset added here */
            $this->triggerWorkflowEvent(
                $this->workflowEventService,
                EventCategory::COMPANIES,
                EventName::PROFILE_MEDIA_IMAGES_CREATED
            );
        }

        return $this->formatResponse([
            self::REQUEST_STATUS_KEY            => !!$newAssets,
            self::REQUEST_COMPANY_MEDIA_ASSETS  => CompanyMediaAssetResource::collection($newAssets)
        ]);
    }

    /**
     * @param CompanyProfileService $service
     * @return JsonResponse
     */
    public function addCompanyYoutubeAsset(CompanyProfileService $service): JsonResponse
    {
        $company = $this->getCompany();
        if($company === null)
            throw new ModelNotFoundException();

        $this->request->validate([
            self::REQUEST_URL_KEY => 'regex:/^(https:\/\/)?(www\.)?youtube\.com\/[^=]*=[0-z-]+/i'
        ]);
        $newAsset = $service->addYoutubeLink($company, $this->request->get(self::REQUEST_URL_KEY));

        if (!!$newAsset) {
            /** Trigger workflow event for company asset added here */
            $this->triggerWorkflowEvent(
                $this->workflowEventService,
                EventCategory::COMPANIES,
                EventName::PROFILE_YOUTUBE_LINK_ADDED,
                [
                    EventName::PROFILE_YOUTUBE_LINK_ADDED->getNewValueKey() => $this->request->get(self::REQUEST_URL_KEY)
                ]
            );
        }

        return $this->formatResponse([
            self::REQUEST_STATUS_KEY            => !!$newAsset,
            self::REQUEST_COMPANY_MEDIA_ASSETS  => new CompanyMediaAssetResource($newAsset)
        ]);
    }

    /**
     * @param CompanyProfileService $service
     * @return JsonResponse
     */
    public function deleteCompanyMediaAsset(CompanyProfileService $service): JsonResponse
    {
        $company = $this->getCompany();
        if($company === null)
            throw new ModelNotFoundException();

        $assetId = $this->request->route('assetId');

        $success = $service->deleteCompanyMediaAsset($company, $assetId);

        if ($success) {
            /** Trigger workflow event for media delete here */
            $this->triggerWorkflowEvent(
                $this->workflowEventService,
                EventCategory::COMPANIES,
                EventName::PROFILE_MEDIA_IMAGES_DELETED
            );
        }

        return $this->formatResponse([
            self::REQUEST_STATUS_KEY => $success,
        ]);
    }

    /**
     * @return void
     * @throws ValidationException
     */
    protected function validateLogo(): void
    {
        $this->validate($this->request, [self::REQUEST_LOGO_STATUS_KEY => 'required|file|image|max:10000']);
    }

    /**
     * @param CompanyRepository $companyRepository
     * @return JsonResponse
     */
    public function updateBasicInfo(CompanyRepository $companyRepository): JsonResponse
    {
        $company = $this->getCompany();

        if($company === null)
            throw new ModelNotFoundException();

        $validated = $this->request->validate([
            Company::FIELD_WEBSITE                                  => 'string|max:64|nullable',
            GlobalConfigurableFields::DESCRIPTION->value            => 'string|nullable',
            GlobalConfigurableFields::YEAR_STARTED_BUSINESS->value  => 'numeric|nullable'
        ]);

        /** Grab old values for description and year started for workflow {old_value}/{new_value} keys */
        $oldDescription = $companyRepository->getCompanyDataByKey($company, GlobalConfigurableFields::DESCRIPTION->value);
        $oldYearStarted = $companyRepository->getCompanyDataByKey($company, GlobalConfigurableFields::YEAR_STARTED_BUSINESS->value);

        $success = $companyRepository->updateCompanyModelAndPayloadById(
            $company->id,
            collect($validated),
            [Company::FIELD_WEBSITE],
            [GlobalConfigurableFields::DESCRIPTION->value, GlobalConfigurableFields::YEAR_STARTED_BUSINESS->value]
        );

        if ($success) {
            /** Trigger workflow event for description update here */
            if (array_key_exists(GlobalConfigurableFields::DESCRIPTION->value, $validated)) {
                $newDescription = $validated[GlobalConfigurableFields::DESCRIPTION->value];
                $this->triggerWorkflowEvent(
                    $this->workflowEventService,
                    EventCategory::COMPANIES,
                    EventName::PROFILE_DESCRIPTION_UPDATED,
                    [
                        EventName::PROFILE_DESCRIPTION_UPDATED->getOldValueKey() => $oldDescription,
                        EventName::PROFILE_DESCRIPTION_UPDATED->getNewValueKey() => $newDescription
                    ]
                );
            }

            /** Trigger workflow event for years in business update here */
            if (array_key_exists(GlobalConfigurableFields::YEAR_STARTED_BUSINESS->value, $validated)) {
                $newYearStarted = $validated[GlobalConfigurableFields::YEAR_STARTED_BUSINESS->value];
                $this->triggerWorkflowEvent(
                    $this->workflowEventService,
                    EventCategory::COMPANIES,
                    EventName::PROFILE_YEAR_STARTED_UPDATED,
                    [
                        EventName::PROFILE_YEAR_STARTED_UPDATED->getOldValueKey() => $oldYearStarted,
                        EventName::PROFILE_YEAR_STARTED_UPDATED->getNewValueKey() => $newYearStarted
                    ]
                );
            }
        }

        return $this->formatResponse([ self::REQUEST_STATUS_KEY => $success ]);
    }

    /**
     * @return JsonResponse
     */
    public function getCompanyLicenses(): JsonResponse
    {
        $company = $this->getCompany();
        if($company === null)
            throw new ModelNotFoundException();

        return $this->formatResponse([
            self::REQUEST_STATUS_KEY    => true,
            self::REQUEST_LICENSES_KEY  => LicenseResource::collection($company->licenses),
        ]);
    }

    /**
     * @param StoreCompanyLicenseRequest $request
     * @param LicenseRepository $licenseRepository
     * @return JsonResponse
     */
    public function createCompanyLicense(StoreCompanyLicenseRequest $request, LicenseRepository $licenseRepository): JsonResponse
    {
        $company = $this->getCompany();
        if($company === null)
            throw new ModelNotFoundException();

        $newLicense = $licenseRepository->createLicense($company, $request->safe()->toArray());

        /** Trigger workflow event for company license creation here */
        $this->triggerWorkflowEvent(
            $this->workflowEventService,
            EventCategory::COMPANIES,
            EventName::PROFILE_LICENSE_CREATED,
            [
                EventValueNameShortcode::VALUE_NAME_KEY => $newLicense[License::FIELD_NAME]
            ]
        );

        return $this->formatResponse([
            self::REQUEST_STATUS_KEY    => !!$newLicense,
            self::REQUEST_LICENSE_KEY   => new LicenseResource($newLicense),
        ]);
    }

    /**
     * @param StoreCompanyLicenseRequest $request
     * @param LicenseRepository $licenseRepository
     * @return JsonResponse
     */
    public function updateCompanyLicense(StoreCompanyLicenseRequest $request, LicenseRepository $licenseRepository): JsonResponse
    {
        $company = $this->getCompany();
        if($company === null)
            throw new ModelNotFoundException();

        $licenseId = $this->request->route(self::REQUEST_LICENSE_ID_KEY);

        /** @var License $oldLicense */
        $oldLicense = $company->licenses()->findOrFail($licenseId);

        $updatedLicense = $licenseRepository->updateLicense($company, $licenseId, $request->safe()->toArray());

        $updates = $updatedLicense->getChanges();
        unset($updates[Model::UPDATED_AT]);

        if (count($updates) !== 0) {
            /** Trigger workflow event for company license update here */
            $this->triggerWorkflowEvent(
                $this->workflowEventService,
                EventCategory::COMPANIES,
                EventName::PROFILE_LICENSE_UPDATED,
                [
                    EventValueNameShortcode::VALUE_NAME_KEY => $updatedLicense[License::FIELD_NAME],
                    EventName::PROFILE_LICENSE_UPDATED->getOldValueKey() => $this->buildDiffString(array_intersect_key($oldLicense->toArray(), $updates)),
                    EventName::PROFILE_LICENSE_UPDATED->getNewValueKey() => $this->buildDiffString($updates)
                ]
            );
        }

        return $this->formatResponse([
            self::REQUEST_STATUS_KEY    => !!$updatedLicense,
            self::REQUEST_LICENSE_KEY   => new LicenseResource($updatedLicense),
        ]);
    }

    /**
     * @param LicenseRepository $licenseRepository
     * @return JsonResponse
     */
    public function deleteCompanyLicense(LicenseRepository $licenseRepository): JsonResponse
    {
        $company = $this->getCompany();
        if($company === null)
            throw new ModelNotFoundException();
        $licenseId = $this->request->route(self::REQUEST_LICENSE_ID_KEY);
        $oldLicense = $company->licenses()->findOrFail($licenseId);

        $success = $licenseRepository->deleteLicense($company, $licenseId);

        if ($success) {
            /** Trigger workflow event for company license deletion here */
            $this->triggerWorkflowEvent(
                $this->workflowEventService,
                EventCategory::COMPANIES,
                EventName::PROFILE_LICENSE_DELETED,
                [
                    EventValueNameShortcode::VALUE_NAME_KEY => $oldLicense[License::FIELD_NAME]
                ]
            );
        }

        return $this->formatResponse([
            self::REQUEST_STATUS_KEY => $success,
        ]);
    }

    /**
     * @return void
     */
    protected function validateMediaAssets(): void
    {
        $this->request->validate([
           self::REQUEST_COMPANY_MEDIA_ASSETS.'.*'  => 'file|image|max:8000000'
        ]);
    }
}
