<?php

namespace App\Http\Controllers\DashboardAPI;

use App\Enums\ContractKeys;
use App\Enums\ContractType;
use App\Events\CompanyRegistration\V3\ContractAccepted;
use App\Http\Requests\Dashboard\AcceptCompanyContractRequest;
use App\Http\Resources\Dashboard\CompanyProgressResource;
use App\Http\Resources\Dashboard\CompanyResource;
use App\Models\CompanyContract;
use App\Models\ContractKey;
use App\Models\Odin\Company;
use App\Models\Odin\Website;
use App\Models\User;
use App\Repositories\CompanyContractRepository;
use App\Repositories\Odin\CompanyIndustryServiceRepository;
use App\Services\CompanyContractService;
use App\Services\CompanyRegistration\CompanyRegistrationService;
use App\Services\HelperService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Collection;
use Illuminate\Validation\UnauthorizedException;
use Exception;

class CompanyController extends BaseDashboardApiController
{
    const string REQUEST_FIELD_ORIGIN               = 'origin';
    const string REQUEST_FIELD_CONTRACT_KEY         = 'contract_type';
    const string REQUEST_FIELD_APP_ID               = 'contract_app_id';
    const string REQUEST_FIELD_CONTRACT_REFERENCE   = 'contract_reference';

    /**
     * Handles returning a company by id.
     *
     * @param int $companyId
     * @return JsonResponse
     */
    public function get(int $companyId): JsonResponse
    {
        if ($this->getUser()->company_id !== $companyId)
            throw new UnauthorizedException("You do not belong to this company.");

        /** @var Company $company */
        $company = Company::query()->findOrFail($companyId);

        return $this->formatResponse((new CompanyResource($company))->toArray($this->request));
    }

    public function getProfileProgress(int $companyId): JsonResponse
    {
        $company = $this->getCompany();

        if($company === null)
            throw new ModelNotFoundException();

        return $this->formatResponse((new CompanyProgressResource($company))->toArray($this->request));
    }

    /**
     * @param CompanyIndustryServiceRepository $serviceRepository
     * @return JsonResponse
     * @throws Exception
     */
    public function updateCompanyIndustryServices(CompanyIndustryServiceRepository $serviceRepository): JsonResponse
    {
        $company = $this->getCompany();
        if($company === null)
            throw new ModelNotFoundException();

        $selectedServiceIds = $this->request->get('services') ?? [];

        /** @var Collection|null $results */
        $results = $serviceRepository->setupServicesForCompany(
            company            : $company,
            industryServiceIds : $selectedServiceIds
        );

        $companyResource = (new CompanyResource($company->refresh()))->toArray($this->request);

        return $this->formatResponse([
            'status'    => $results->filter()?->count() > 0,
            'updated_services'  => [
                'industries'    => $companyResource['industries'],
                'services'      => $companyResource['services'],
            ],
        ]);
    }

    /**
     * @param CompanyContractRepository $contractRepository
     * @param CompanyContractService $companyContractService
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function getCompanyContractForCompany(CompanyContractRepository $contractRepository, CompanyContractService $companyContractService): JsonResponse
    {
        $company = $this->getCompany();
        $user = $this->getUser();
        if($company === null || $user === null)
            throw new ModelNotFoundException();

        /** @var Website $website */
        $website = Website::query()->where(Website::FIELD_ABBREVIATION, ContractType::FIXR)->firstOrFail();
        /** @var ContractKey $contractKey */
        $contractKey = ContractKey::query()->where(ContractKey::FIELD_KEY, ContractKeys::TERMS_AND_CONDITIONS)->firstOrFail();

        $contract = $contractRepository->getValidCompanyUserContractByType(user: $user, contractKey: $contractKey, website: $website);

        return $this->formatResponse([
            'status'        => !!$contract,
            'contract_url'  => $companyContractService->getSignedContractFileUrl($contract?->{CompanyContract::FIELD_FILE_URL}),
            'message'       => !$contract ? "The current user has not agreed to a contract" : null
        ]);
    }

    /**
     * @param CompanyRegistrationService $companyRegistrationService
     * @param HelperService $helperService
     * @return JsonResponse
     * @throws ApiException
     */
    public function getNewContractForCompany(CompanyRegistrationService $companyRegistrationService, HelperService $helperService): JsonResponse
    {
        $company = $this->getCompany();
        if($company === null)
            throw new ModelNotFoundException();

        $user = $this->getUser();
        $ip = $helperService->getIpFromRequest($this->request);

        /** @var Website $website */
        $website = Website::query()->where(Website::FIELD_ABBREVIATION, $this->request->get(self::REQUEST_FIELD_ORIGIN) ?? 'fixr')->firstOrFail();
        /** @var ContractKey $contractKey */
        $contractKey = ContractKey::query()->where(ContractKey::FIELD_KEY,  $this->request->get(self::REQUEST_FIELD_CONTRACT_KEY) ?? ContractKeys::TERMS_AND_CONDITIONS)->firstOrFail();
        $appId = $this->request->get(self::REQUEST_FIELD_APP_ID);

        return $this->formatResponse(
            $companyRegistrationService->getCompanyContract(
                company: $company,
                user: $user,
                contractKey: $contractKey,
                website: $website,
                ip: $ip,
                appId: $appId
            ));
    }

    /**
     * @param CompanyContractRepository $companyContractRepository
     * @param CompanyContractService $companyContractService
     * @param AcceptCompanyContractRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function agreeToContractForCompany(CompanyContractRepository $companyContractRepository, CompanyContractService $companyContractService, AcceptCompanyContractRequest $request): JsonResponse
    {
        $company = $this->getCompany();
        if($company === null)
            throw new ModelNotFoundException();

        $user = $this->getUser();

        $data = $request->safe()->all();

        $contractUuid = $data[self::REQUEST_FIELD_CONTRACT_REFERENCE];

        $contract = $companyContractRepository->findCompanyContractByUuidOrFail($contractUuid);

        if ($companyContractService->agreeToContract($company, $user, $contract)) {
            ContractAccepted::dispatch($company, $user);

            return $this->formatResponse([
                'status' => true
            ]);
        } else
            throw new Exception('Contract not accepted');
    }

}
