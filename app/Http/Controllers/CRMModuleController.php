<?php

namespace App\Http\Controllers;

use App\Campaigns\Delivery\CRM\CRMIntegrationTestService;
use App\Http\Controllers\API\APIController;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryModuleCRM;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\JsonResponse;
use Illuminate\Validation\ValidationException;

class CRMModuleController extends APIController
{
    const string TEST_DATA = 'test_data';

    /**
     * @param int $companyId
     * @param int $crmModuleId
     * @param CRMIntegrationTestService $integrationTestService
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function testCrmIntegration(int $companyId, int $crmModuleId, CRMIntegrationTestService $integrationTestService): JsonResponse
    {
        $this->validate($this->request, [
            self::TEST_DATA => 'array'
        ]);

        /** @var CompanyCampaignDeliveryModuleCRM $companyCampaignDeliveryModuleCrm */
        $companyCampaignDeliveryModuleCrm = CompanyCampaignDeliveryModuleCRM::query()->findOrFail($crmModuleId);

        if ($companyCampaignDeliveryModuleCrm->module->campaign->company_id !== $companyId) {
            throw new ModelNotFoundException('Invalid CRM Integration');
        }

        return $this->formatResponse([
            'test_result' => $integrationTestService->sendTestLead(
                $companyCampaignDeliveryModuleCrm,
                $this->request->get(self::TEST_DATA, [])
            )
        ]);
    }
}
