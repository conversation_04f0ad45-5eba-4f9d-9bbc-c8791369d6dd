<?php

namespace App\Http\Controllers\Billing;

use App\Enums\Billing\InvoiceReportsGrouping;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Billing\CompaniesOverviewReportRequest;
use App\Http\Requests\Billing\CreditMovementReportRequest;
use App\Http\Requests\Billing\Reports\GetAgedInvoicesReportRequest;
use App\Http\Requests\Billing\Reports\GetInvoiceBalanceReportRequest;
use App\Http\Requests\Billing\Reports\GetRevenueReportRequest;
use App\Http\Requests\Billing\Reports\GetReceivableInvoicesReportRequest;
use App\Http\Resources\Billing\Reports\AgedInvoicesReportResource;
use App\Http\Resources\Billing\Reports\CompaniesOverviewReportResource;
use App\Http\Resources\Billing\Reports\CreditMovementReportResource;
use App\Http\Resources\Billing\Reports\CreditOutstandingReportResource;
use App\Http\Resources\Billing\Reports\InvoiceBalanceReportResource;
use App\Http\Resources\Billing\Reports\ReceivableInvoiceReportResource;
use App\Http\Resources\Billing\Reports\RevenueReportResource;
use App\Http\Resources\Odin\StatusFilterOptionResource;
use App\Services\Billing\InvoiceService;
use App\Services\Billing\Reports\AgedInvoicesReportService;
use App\Services\Billing\Reports\CompanyOutstandingLeadsReport;
use App\Services\Billing\Reports\CreditReportService;
use App\Services\Billing\Reports\CreditMovementReportService;
use App\Services\Billing\Reports\CreditsOutstandingReportService;
use App\Services\Billing\Reports\InvoiceBalanceReportService;
use App\Services\Billing\Reports\ReceivableInvoicesReportService;
use App\Services\Billing\Reports\RevenueReportService;
use App\Services\CurrencyService;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Arr;

class InvoiceReportController extends APIController
{
    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected ReceivableInvoicesReportService $invoiceReportService,
        protected InvoiceService $invoiceService,
        protected RevenueReportService $revenueReportService,
        protected AgedInvoicesReportService $agedInvoicesReportService,
        protected InvoiceBalanceReportService $invoiceBalanceReportService,
        protected CreditMovementReportService $creditReportService,
        protected CreditsOutstandingReportService $creditsOutstandingReportService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @return AnonymousResourceCollection
     */
    public function getInvoiceStatuses(): AnonymousResourceCollection
    {
        $statuses = $this->invoiceService->getInvoiceFilters();

        return StatusFilterOptionResource::collection($statuses);
    }

    /**
     * @param GetReceivableInvoicesReportRequest $request
     * return array
     * @throws Exception
     */
    public function getReceivableInvoicesReport(GetReceivableInvoicesReportRequest $request): array
    {
        $filters = $request->validated();

        $reportGroupingValue = Arr::get($filters, GetReceivableInvoicesReportRequest::FIELD_REFERENCE, InvoiceReportsGrouping::INVOICE->value);

        [
            'query'                      => $query,
            'total_outstanding_in_cents' => $totalOutstandingInCents
        ] = $this->invoiceReportService->getReceivableInvoicesReport(
            grouping                        : InvoiceReportsGrouping::tryFrom($reportGroupingValue),
            user                            : auth()->user(),
            companyId                       : Arr::get($filters, GetReceivableInvoicesReportRequest::FIELD_COMPANY_ID),
            date                            : Arr::get($filters, GetReceivableInvoicesReportRequest::FIELD_DATE, now()->toString()),
            customerSuccessManagerUserId: Arr::get($filters, GetReceivableInvoicesReportRequest::FIELD_SUCCESS_MANAGER_USER_ID),
            accountManagerUserId            : Arr::get($filters, GetReceivableInvoicesReportRequest::FIELD_ACCOUNT_MANAGER_USER_ID),
            businessDevelopmentManagerUserId: Arr::get($filters, GetReceivableInvoicesReportRequest::FIELD_BUSINESS_DEVELOPMENT_MANAGER_USER_ID),
            invoiceStatus                   : Arr::get($filters, GetReceivableInvoicesReportRequest::FIELD_INVOICE_STATUS),
            sortBy                          : Arr::get($filters, GetReceivableInvoicesReportRequest::FIELD_SORT_BY),
            industryIds                     : Arr::get($filters, GetReceivableInvoicesReportRequest::FIELD_INDUSTRY_IDS),
        );

        return [
            'data'       => ReceivableInvoiceReportResource::format($query),
            'aggregates' => [
                'Outstanding' => CurrencyService::fromAtomicToCurrency($totalOutstandingInCents)
            ]
        ];
    }

    /**
     * @param GetRevenueReportRequest $request
     * @return array
     */
    public function getRevenueReport(GetRevenueReportRequest $request): array
    {
        $filters = $request->validated();

        [
            'query'      => $query,
            'aggregates' => $aggregates,
        ] = $this->revenueReportService->getRevenueReport(
            dateRange: Arr::get($filters, GetRevenueReportRequest::FIELD_DATE_RANGE),
            companyId: Arr::get($filters, GetRevenueReportRequest::FIELD_COMPANY_ID),
            scenarios: Arr::get($filters, GetRevenueReportRequest::FIELD_TRANSACTION_SCENARIOS),
            types    : Arr::get($filters, GetRevenueReportRequest::FIELD_TRANSACTION_TYPES),
            sortBy   : Arr::get($filters, GetRevenueReportRequest::FIELD_SORT_BY, []),
        );

        return [
            'data'       => RevenueReportResource::format($query),
            'aggregates' => $aggregates
        ];
    }


    /**
     * @param GetAgedInvoicesReportRequest $request
     * @return array
     */
    public function getAgedInvoicesReport(GetAgedInvoicesReportRequest $request): array
    {
        $filters = $request->validated();

        $dateRange = json_decode(Arr::get($filters, GetAgedInvoicesReportRequest::FIELD_DATE_RANGE, ''), true);

        $dateFrom = Arr::get($dateRange, 'from');
        $dateTo = Arr::get($dateRange, 'to');

        $query = $this->agedInvoicesReportService->getReport(
            groupedBy                       : InvoiceReportsGrouping::tryFrom(Arr::get($filters, GetAgedInvoicesReportRequest::FIELD_GROUPED_BY, InvoiceReportsGrouping::INVOICE->value)),
            user                            : auth()->user(),
            customerSuccessManagerUserId: Arr::get($filters, GetAgedInvoicesReportRequest::FIELD_SUCCESS_MANAGER_USER_ID),
            accountManagerUserId            : Arr::get($filters, GetAgedInvoicesReportRequest::FIELD_ACCOUNT_MANAGER_USER_ID),
            businessDevelopmentManagerUserId: Arr::get($filters, GetAgedInvoicesReportRequest::FIELD_BUSINESS_DEVELOPMENT_MANAGER_USER_ID),
            industryIds                     : Arr::get($filters, GetAgedInvoicesReportRequest::FIELD_INDUSTRY_ID),
            companyId                       : Arr::get($filters, GetAgedInvoicesReportRequest::FIELD_COMPANY_ID),
            dateFrom                        : $dateFrom,
            dateTo                          : $dateTo,
            sortBy                          : Arr::get($filters, GetRevenueReportRequest::FIELD_SORT_BY, []),
        );

        return AgedInvoicesReportResource::format($query);
    }

    /**
     * @param GetInvoiceBalanceReportRequest $request
     * @return array
     */
    public function getInvoiceBalanceReport(GetInvoiceBalanceReportRequest $request): array
    {
        $filters = $request->validated();

        $query = $this->invoiceBalanceReportService->getReport(
            groupedBy                       : InvoiceReportsGrouping::tryFrom(Arr::get($filters, GetInvoiceBalanceReportRequest::FIELD_GROUPED_BY, InvoiceReportsGrouping::INVOICE->value)),
            customerSuccessManagerUserId: Arr::get($filters, GetInvoiceBalanceReportRequest::FIELD_SUCCESS_MANAGER_USER_ID),
            accountManagerUserId            : Arr::get($filters, GetInvoiceBalanceReportRequest::FIELD_ACCOUNT_MANAGER_USER_ID),
            businessDevelopmentManagerUserId: Arr::get($filters, GetInvoiceBalanceReportRequest::FIELD_BUSINESS_DEVELOPMENT_MANAGER_USER_ID),
            industryIds                     : Arr::get($filters, GetInvoiceBalanceReportRequest::FIELD_INDUSTRY_ID),
            companyId                       : Arr::get($filters, GetInvoiceBalanceReportRequest::FIELD_COMPANY_ID),
            sortBy                          : Arr::get($filters, GetRevenueReportRequest::FIELD_SORT_BY, []),
        );

        return InvoiceBalanceReportResource::format($query);
    }

    /**
     * @param CreditMovementReportRequest $request
     * @return array
     */
    public function getCreditsMovementReport(CreditMovementReportRequest $request): array
    {
        $filters = $request->validated();

        $paginated = $this->creditReportService->getCreditMovementReport([
            'dates'          => Arr::get($filters, CreditMovementReportRequest::FIELD_DATES, []),
            'company_id'     => Arr::get($filters, CreditMovementReportRequest::FIELD_COMPANY_ID),
            'credit_type'    => Arr::get($filters, CreditMovementReportRequest::FIELD_CREDIT_TYPE),
            'invoice_id'     => Arr::get($filters, CreditMovementReportRequest::FIELD_INVOICE_ID),
            'action_type'    => Arr::get($filters, CreditMovementReportRequest::FIELD_ACTION_TYPE),
            'filter_by_role' => auth()->user(),
            'page'           => Arr::get($filters, CreditMovementReportRequest::FIELD_PAGE),
            'per_page'       => Arr::get($filters, CreditMovementReportRequest::FIELD_PER_PAGE),
        ]);

        $formatted = CreditMovementReportResource::format($paginated);

        $formatted['data'] = $this->creditReportService->addLastLeadDeliveredAtToReportItems($formatted['data']);

        return $formatted;
    }


    /**
     * @param CompaniesOverviewReportRequest $request
     * @return array
     * @throws BindingResolutionException
     */
    public function getCompaniesOverviewReport(CompaniesOverviewReportRequest $request): array
    {
        $filters = $request->validated();

        $paginated = CompanyOutstandingLeadsReport::query()
            ->forCompanyId(Arr::get($filters, CompaniesOverviewReportRequest::FIELD_COMPANY_ID))
            ->sortBy(Arr::get($filters, 'sort_by') ?? [])
            ->paginate(
                page   : Arr::get($filters, CompaniesOverviewReportRequest::FIELD_PAGE, 1),
                perPage: Arr::get($filters, CompaniesOverviewReportRequest::FIELD_PER_PAGE, 25),
            );

        return CompaniesOverviewReportResource::format($paginated);
    }

    /**
     * @param CreditMovementReportRequest $request
     * @return array
     */
    public function getCreditsOutstandingReport(CreditMovementReportRequest $request): array
    {
        $filters = $request->validated();

        $paginated = $this->creditsOutstandingReportService->getCreditsOutstandingReportService([
            'dates'          => Arr::get($filters, CreditMovementReportRequest::FIELD_DATES, []),
            'company_id'     => Arr::get($filters, CreditMovementReportRequest::FIELD_COMPANY_ID),
            'credit_type'    => Arr::get($filters, CreditMovementReportRequest::FIELD_CREDIT_TYPE),
            'invoice_id'     => Arr::get($filters, CreditMovementReportRequest::FIELD_INVOICE_ID),
            'action_type'    => Arr::get($filters, CreditMovementReportRequest::FIELD_ACTION_TYPE),
            'sort_by'        => Arr::get($filters, CreditMovementReportRequest::FIELD_SORT_BY),
            'filter_by_role' => auth()->user(),
            'page'           => Arr::get($filters, CreditMovementReportRequest::FIELD_PAGE),
            'per_page'       => Arr::get($filters, CreditMovementReportRequest::FIELD_PER_PAGE),
        ]);

        return CreditOutstandingReportResource::format($paginated);
    }
}
