<?php

namespace App\Http\Controllers\Billing;

use App\DTO\Billing\InvoicePayload;
use App\Enums\Billing\InvoiceEventAuthorTypes;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\Billing\CreateUpdateInvoiceRequest;
use App\Http\Requests\Billing\GetInvoiceEventsRequest;
use App\Http\Requests\Billing\GetInvoiceRequest;
use App\Http\Requests\Billing\GetInvoicesRequest;
use App\Http\Requests\Billing\GetUninvoicedProductsRequest;
use App\Http\Requests\Billing\LeadsImportRequest;
use App\Http\Resources\Billing\InvoiceEventsResource;
use App\Http\Resources\Billing\InvoiceItemDTOResource;
use App\Http\Resources\Billing\InvoiceResource;
use App\Http\Resources\Billing\ListInvoiceResource;
use App\Http\Resources\Odin\StatusFilterOptionResource;
use App\Models\Billing\Invoice;
use App\Models\Odin\Company;
use App\Services\Billing\CompanyInvoiceService;
use App\Services\Billing\InvoiceService;
use Exception;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\AnonymousResourceCollection;
use Illuminate\Support\Arr;

class InvoiceController extends APIController
{
    const string STATUS       = 'status';
    const string MESSAGE      = 'message';
    const string INVOICE_UUID = 'invoice_uuid';

    public function __construct(
        Request $request,
        JsonAPIResponseFactory $apiResponseFactory,
        protected InvoiceService $invoiceService,
        protected CompanyInvoiceService $companyInvoiceService,
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }

    /**
     * @param CreateUpdateInvoiceRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function createUpdateInvoice(CreateUpdateInvoiceRequest $request): JsonResponse
    {
        $validated = $request->validated();
        $authorId = Auth()->user()->id;
        $authorType = InvoiceEventAuthorTypes::USER;

        $payload = InvoicePayload::fromArray($validated);

        try {
            [$invoiceUuid, $message] = $this->invoiceService->createUpdateInvoice(
                invoicePayload: $payload,
                authorType    : $authorType,
                authorId      : $authorId,
            );

            return $this->formatResponse([
                self::STATUS       => true,
                self::MESSAGE      => $message,
                self::INVOICE_UUID => $invoiceUuid,
            ]);
        } catch (Exception $e) {
            logger()->error('Unable to create new Invoice : ', [$request]);
            throw $e;
        }
    }

    /**
     * @param GetInvoicesRequest $request
     * @return AnonymousResourceCollection
     */
    public function getInvoices(GetInvoicesRequest $request): AnonymousResourceCollection
    {
        $validated = $request->validated();

        $invoices = $this->invoiceService->getInvoices([
            ...$validated,
            'filter_by_role' => auth()->user(),
        ]);

        return ListInvoiceResource::collection($invoices);
    }

    /**
     * @param GetInvoiceRequest $request
     * @return InvoiceResource
     */
    public function getInvoice(GetInvoiceRequest $request): InvoiceResource
    {
        $validated = $request->validated();

        $invoice = $this->invoiceService->getInvoice(Arr::get($validated, GetInvoiceRequest::INVOICE_ID));

        return new InvoiceResource($invoice);
    }

    /**
     * @param GetInvoiceEventsRequest $request
     * @return AnonymousResourceCollection
     */
    public function getInvoiceEvents(GetInvoiceEventsRequest $request): AnonymousResourceCollection
    {
        $validated = $request->validated();

        $invoice = $this->invoiceService->getInvoice(Arr::get($validated, GetInvoiceEventsRequest::INVOICE_ID));

        $invoiceEvents = $this->invoiceService->getInvoiceEvents($invoice);

        $resource = new InvoiceEventsResource($invoiceEvents);

        return $resource->groupCollection();
    }

    /**
     * @param GetUninvoicedProductsRequest $request
     * @return JsonResponse
     */
    public function getUninvoicedProducts(GetUninvoicedProductsRequest $request): JsonResponse
    {
        $validated = $request->validated();
        /** @var Company $company */
        $company = Company::query()->findOrFail($validated[GetUninvoicedProductsRequest::COMPANY_ID]);

        $invoiceItems = $this->companyInvoiceService->getUninvoicedInvoiceItemsForCompany($company);

        return $this->formatResponse([
            'data' => InvoiceItemDTOResource::collection($invoiceItems)
        ]);

    }

    /**
     * @return JsonResponse
     */
    public function getInvoiceFilters(): JsonResponse
    {
        $filters = $this->invoiceService->getInvoiceFilters();

        return $this->formatResponse([
            'statuses' => StatusFilterOptionResource::collection(collect($filters)),
        ]);
    }

    /**
     * @param Invoice $invoice
     * @return JsonResponse
     * @throws BindingResolutionException
     */
    public function generatePdfSignedUrl(Invoice $invoice): JsonResponse
    {
        $signedUrl = $this->invoiceService->generateSignedUrl($invoice);

        return $this->formatResponse([
            'url' => $signedUrl
        ]);
    }
}
