<?php

namespace App\Http\Controllers\CompanyCampaign;

use App\Enums\Campaigns\CampaignFilterOperator;
use App\Enums\Odin\ConsumerConfigurableFieldCategory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\CompanyCampaigns\StoreCampaignFilterRequest;
use App\Http\Resources\CompanyCampaign\CampaignFilterResource;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\CompanyCampaignFilter;
use App\Models\Odin\IndustryConsumerField;
use App\Repositories\Campaigns\CampaignFilterRepository;
use App\Repositories\Odin\IndustryConsumerFieldRepository;
use Illuminate\Http\JsonResponse;

class CompanyCampaignApiController extends APIController
{
    const string RESPONSE_FILTER_OPERATORS = 'filter_operators';
    const string RESPONSE_FILTER_KEYS = 'filter_keys';

    /**
     * @param CompanyCampaign $companyCampaign
     *
     * @return JsonResponse
     */
    public function getFilters(CompanyCampaign $companyCampaign): JsonResponse
    {
        return $this->formatResponse([
            'campaign_filters' => CampaignFilterResource::collection($companyCampaign->filters)
        ]);
    }

    /**
     * @param CompanyCampaign $companyCampaign
     * @param IndustryConsumerFieldRepository $repository
     *
     * @return JsonResponse
     */
    public function getFilterOptions(CompanyCampaign $companyCampaign, IndustryConsumerFieldRepository $repository): JsonResponse
    {
        return $this->formatResponse([
            self::RESPONSE_FILTER_OPERATORS => collect(CampaignFilterOperator::cases())->map(fn(CampaignFilterOperator $operator) => [
                'id' => $operator->value,
                'name' => $operator->symbol()
            ]),
            self::RESPONSE_FILTER_KEYS => $repository->getConsumerFieldsForIndustry($companyCampaign->service->industry, [ConsumerConfigurableFieldCategory::LEAD_INFORMATION])
                ->map(fn(IndustryConsumerField $field) => [
                    'id' => $field->key,
                    'name' => $field->name
                ])
        ]);
    }

    /**
     * @param CompanyCampaign $companyCampaign
     * @param StoreCampaignFilterRequest $request
     * @param CampaignFilterRepository $repository
     *
     * @return JsonResponse
     */
    public function createFilter(CompanyCampaign $companyCampaign, StoreCampaignFilterRequest $request, CampaignFilterRepository $repository): JsonResponse
    {
        $repository->createFilter($companyCampaign, [
            CompanyCampaignFilter::FIELD_KEY => $request->validated(CompanyCampaignFilter::FIELD_KEY),
            CompanyCampaignFilter::FIELD_OPERATOR => $request->validated(CompanyCampaignFilter::FIELD_OPERATOR),
            CompanyCampaignFilter::FIELD_VALUE => $request->validated(CompanyCampaignFilter::FIELD_VALUE)
        ]);

        return $this->formatResponse();
    }

    /**
     * @param CompanyCampaign $companyCampaign
     * @param CompanyCampaignFilter $companyCampaignFilter
     * @param StoreCampaignFilterRequest $request
     * @param CampaignFilterRepository $repository
     *
     * @return JsonResponse
     */
    public function updateFilter(
        CompanyCampaign $companyCampaign,
        CompanyCampaignFilter $companyCampaignFilter,
        StoreCampaignFilterRequest $request,
        CampaignFilterRepository $repository
    ): JsonResponse
    {
        $repository->updateFilter($companyCampaignFilter, [
            CompanyCampaignFilter::FIELD_KEY => $request->validated(CompanyCampaignFilter::FIELD_KEY),
            CompanyCampaignFilter::FIELD_OPERATOR => $request->validated(CompanyCampaignFilter::FIELD_OPERATOR),
            CompanyCampaignFilter::FIELD_VALUE => $request->validated(CompanyCampaignFilter::FIELD_VALUE)
        ]);

        return $this->formatResponse();
    }

    /**
     * @param CompanyCampaign $companyCampaign
     * @param CompanyCampaignFilter $companyCampaignFilter
     *
     * @return JsonResponse
     */
    public function deleteFilter(CompanyCampaign $companyCampaign, CompanyCampaignFilter $companyCampaignFilter): JsonResponse
    {
        $companyCampaignFilter->delete();

        return $this->formatResponse();
    }
}
