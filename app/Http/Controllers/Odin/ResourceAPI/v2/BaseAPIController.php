<?php

namespace App\Http\Controllers\Odin\ResourceAPI\v2;

use App\Http\Controllers\Controller;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Services\Odin\API\OdinAuthoritativeAPIService;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * /v2 version of Odin BaseAPIController
 */
class BaseAPIController extends Controller
{
    public function __construct(
        protected Request $request,
        protected OdinAuthoritativeAPIService $service
    ) {}

    /**
     * Handles formatting the data as an accepted JsonResponse.
     *
     * @param array $data
     * @return JsonResponse
     */
    protected function formatResponse(array $data = []): JsonResponse
    {
        return response()->json(["data" => $data]);
    }

    /**
     * @param ?string $industrySlug
     * @param string $serviceSlug
     *
     * @return IndustryService
     */
    protected function getIndustryService(?string $industrySlug, string $serviceSlug): IndustryService
    {
        /** @var Industry $industry */
        $industry = $industrySlug
            ? Industry::query()->where(Industry::FIELD_SLUG, $industrySlug)->firstOrFail()
            : null;

        /** @var IndustryService $industryService */
        $industryService = $industry
            ? $industry->services()->where(IndustryService::FIELD_SLUG, $serviceSlug)->firstOrFail()
            : IndustryService::query()->where(IndustryService::FIELD_SLUG, $serviceSlug)->first();

        return $industryService;
    }
}
