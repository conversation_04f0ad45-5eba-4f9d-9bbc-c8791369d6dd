<?php

namespace App\Http\Controllers\Odin\ResourceAPI;

use \Illuminate\Validation\ValidationException;
use \Illuminate\Http\JsonResponse;

class ConsumerTrackingController extends BaseAPIController
{
    const REQUEST_CONSUMER_TRACKING = 'consumer_tracking';


    /**
     * <PERSON><PERSON> creating the Consumer Tracking model
     *
     * @return JsonResponse
     * @throws ValidationException
     */
    public function createConsumerTracking(): JsonResponse
    {
        $this->validate(
            $this->request,
            [
                self::REQUEST_CONSUMER_TRACKING => 'required|array'
            ]
        );

        $consumerTracking = $this->request->get(self::REQUEST_CONSUMER_TRACKING);

        return $this->formatResponse([
            "status" => $this->service->create($consumerTracking),
        ]);

    }

}
