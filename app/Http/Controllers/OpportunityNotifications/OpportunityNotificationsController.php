<?php

namespace App\Http\Controllers\OpportunityNotifications;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Services\OpportunityNotifications\OpportunityNotificationService;
use Illuminate\Http\Request;

class OpportunityNotificationsController extends APIController
{
    public function __construct(
        Request                                     $request,
        JsonAPIResponseFactory                      $apiResponseFactory,
        protected OpportunityNotificationService    $notificationService
    )
    {
        parent::__construct($request, $apiResponseFactory);
    }
}
