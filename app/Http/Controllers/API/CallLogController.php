<?php

namespace App\Http\Controllers\API;

use App\Models\Call;
use App\Models\Phone;
use Illuminate\Http\Request;
use Illuminate\Validation\Rule;
use Illuminate\Support\Facades\Auth;
use Illuminate\Http\JsonResponse;
use Carbon\Carbon;

class CallLogController extends APIController
{
    public function export(Request $request): JsonResponse
    {
        $request->validate([
            'filters' => 'array',
        ]);
        $filters = $request->input('filters', []);
        $filters = json_decode($filters[0], true);

        $user = Auth::user();
        $userName = $user->name;

        $allUniqueUserPhonesIds = $user->phones(true)
            ->select(Phone::TABLE . '.' . Phone::FIELD_ID)
            ->distinct()
            ->get()
            ->pluck(Phone::FIELD_ID)
            ->toArray();

        $query = Call::query();

        if (!empty($allUniqueUserPhonesIds)) {
            $query->whereIn('phone_id', $allUniqueUserPhonesIds);
        }

        if (isset($filters['communication_description'])) {
            $query->where('direction', $filters['communication_description']);
        }

        if (isset($filters['date_time'])) {
            $from = Carbon::parse($filters['date_time']['from'])->toDateTimeString();
            $to = Carbon::parse($filters['date_time']['to'])->toDateTimeString();

            $query->whereBetween('created_at', [$from, $to]);
        }

        $callLogs = $query->orderBy('created_at', 'desc')->get();

        if ($callLogs->isEmpty()) {
            return $this->formatResponse([
                'success' => false,
                'message' => 'No call logs found in the specified date range.'
            ]);
        }else {
            $startDate = null;
            $endDate = null;

            if (isset($filters['date_time'])) {
                $startDate = Carbon::parse($filters['date_time']['from'])->toDateString();
                $endDate = Carbon::parse($filters['date_time']['to'])->toDateString();
            }
            $startDate = $startDate ?? Carbon::now()->toDateString();
            $endDate = $endDate ?? Carbon::now()->toDateString();

            $csvFileName = "{$userName}_{$startDate}_{$endDate}.csv";

            $csvContent = "ID,Direction,Result,Your Phone Number,External Phone Number,Date time,Call duration\n";

            $rowNumber = 1;
            foreach ($callLogs as $callLog) {
                $callStart = $callLog->call_start;
                $callEnd = $callLog->call_end;

                $callDurationInSeconds = $callEnd === null ? '00:00:00' : date('H:i:s', strtotime($callEnd) - strtotime($callStart));
                $csvContent .= "$rowNumber,";
                $csvContent .= ucfirst($callLog->direction) .",";
                $csvContent .= ucfirst($callLog->result) .",";
                $csvContent .= "{$callLog->phone->phone},";
                $csvContent .= "{$callLog->other_number},";
                $csvContent .= "{$callLog->created_at},";
                $csvContent .= "$callDurationInSeconds";
                $csvContent .= "\n";
                $rowNumber++;
            }

            return $this->formatResponse([
                'success' => true,
                'csvFileName' => $csvFileName,
                'csvContent' => $csvContent,
            ]);
        }

    }

}
