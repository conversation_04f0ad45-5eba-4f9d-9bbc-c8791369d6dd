<?php

namespace App\Http\Controllers\API\LeadProcessing;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\LeadReportSearchRequest;
use App\Models\Legacy\EloquentQuote;
use App\Repositories\LeadProcessing\LeadProcessingReportRepository;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

/**
 * API for Failing Lead
 */
class LeadProcessingReportApiController extends APIController
{
    const START_DATE = 'start_date';
    const END_DATE   = 'end_date';

    /** @var LeadProcessingReportRepository $leadReportRepository */
    protected LeadProcessingReportRepository $leadReportRepository;

    public function __construct(
        Request                        $request,
        JsonAPIResponseFactory         $apiResponseFactory,
        LeadProcessingReportRepository $leadReportRepository,
    )
    {
        parent::__construct($request, $apiResponseFactory);
        $this->leadReportRepository = $leadReportRepository;
    }

    /**
     * @params LeadReportSearchRequest $leadReportSearchRequest
     * @return JsonResponse
     */

    public function getOverSoldLead(LeadReportSearchRequest $leadReportSearchRequest): JsonResponse
    {
        $searchParams = $leadReportSearchRequest->safe()->collect();
        $leads = $this->leadReportRepository->getAllOverSoldLead(
            $searchParams->get(self::START_DATE),
            $searchParams->get(self::END_DATE),
        );

        return $this->formatResponse([
            "status" => true,
            "leads" => $leads->paginate(EloquentQuote::LEAD_LIMIT_PER_PAGE, ['*'], 'page', $this->request->get('page')),
        ]);
    }

    /**
     * @param LeadReportSearchRequest $leadReportSearchRequest
     * @return JsonResponse
     */
    public function getCrmBouncedLeads(LeadReportSearchRequest $leadReportSearchRequest): JsonResponse
    {
        $searchParams = $leadReportSearchRequest->safe()->collect();
        $leads = $this->leadReportRepository->getAllCrmBouncedLeads(
            $searchParams->get(self::START_DATE),
            $searchParams->get(self::END_DATE),
        );

        return $this->formatResponse([
            "status" => true,
            "leads" => $leads->paginate(EloquentQuote::LEAD_LIMIT_PER_PAGE, ['*'], 'page', $this->request->get('page')),
        ]);
    }

    /**
     * @params LeadReportSearchRequest $leadReportSearchRequest
     * @return JsonResponse
     */

    public function getAllocatedLead(LeadReportSearchRequest $leadReportSearchRequest): JsonResponse
    {
        $searchParams = $leadReportSearchRequest->safe()->collect();
        $leads = $this->leadReportRepository->getAllAllocatedLead(
            $searchParams->get(self::START_DATE),
            $searchParams->get(self::END_DATE),
        );
        return $this->formatResponse([
            "status" => true,
            "leads" => $leads->paginate(EloquentQuote::LEAD_LIMIT_PER_PAGE, ['*'], 'page', $this->request->get('page')),
        ]);
    }

    /**
     * @params LeadReportSearchRequest $leadReportSearchRequest
     * @return JsonResponse
     */

    public function getDeliveredLead(LeadReportSearchRequest $leadReportSearchRequest): JsonResponse
    {
        $searchParams = $leadReportSearchRequest->safe()->collect();
        $leads = $this->leadReportRepository->getAllDeliveredButNotAllocatedOrCancelledLead(
            $searchParams->get(self::START_DATE),
            $searchParams->get(self::END_DATE),
        );

        return $this->formatResponse([
            "status" => true,
            "leads" => $leads->paginate(EloquentQuote::LEAD_LIMIT_PER_PAGE, ['*'], 'page', $this->request->get('page')),
        ]);
    }

    /**
     * @return JsonResponse
     */

    public function getNoChargedLead(): JsonResponse
    {
        $leads = $this->leadReportRepository->getNoChargedLead();
        return $this->formatResponse([
            "status" => true,
            "leads" => $leads->paginate(EloquentQuote::LEAD_LIMIT_PER_PAGE, ['*'], 'page', $this->request->get('page')),
        ]);
    }
}
