<?php

namespace App\Http\Controllers\API\LeadProcessing;

use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Models\LeadProcessor;
use App\Models\Legacy\EloquentQuote;
use App\Repositories\LeadProcessing\LeadProcessingManagementRepository;
use App\Repositories\LeadProcessing\LeadProcessingQueueRepository;
use App\Repositories\Legacy\LeadProcessingRepository;
use App\Repositories\Legacy\QuoteRepository;
use App\Services\LeadProcessing\LeadCommunicationService;
use App\Services\LeadProcessing\LeadProcessingService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;

/**
 * The base api controller relating to the Lead Processing API.
 */
abstract class LeadProcessingBaseAPIController extends APIController
{
    /** @var LeadProcessingRepository $leadProcessingRepository */
    protected LeadProcessingRepository $leadProcessingRepository;

    /** @var LeadProcessingService $leadProcessingService */
    protected LeadProcessingService $leadProcessingService;

    /** @var LeadProcessingQueueRepository $leadProcessingQueueRepository */
    protected LeadProcessingQueueRepository $leadProcessingQueueRepository;

    /** @var QuoteRepository $leadRepository */
    protected QuoteRepository $leadRepository;

    /** @var LeadProcessingManagementRepository $managementRepository */
    protected LeadProcessingManagementRepository $managementRepository;

    /**@var LeadCommunicationService $leadCommunicationService */
    protected LeadCommunicationService $leadCommunicationService;

    /**
     * Cached lead processor to limit queries.
     *
     * @var null|LeadProcessor $_cachedLeadProcessorId
     */
    private null|LeadProcessor $_cachedLeadProcessorId = null;

    public function __construct(
        Request                            $request,
        JsonAPIResponseFactory             $apiResponseFactory,
        LeadProcessingRepository           $leadProcessingRepository,
        LeadProcessingService              $leadProcessingService,
        LeadProcessingQueueRepository      $leadProcessingQueueRepository,
        QuoteRepository                    $leadRepository,
        LeadProcessingManagementRepository $managementRepository,
        LeadCommunicationService           $leadCommunicationService
    )
    {
        parent::__construct($request, $apiResponseFactory);

        $this->leadProcessingRepository      = $leadProcessingRepository;
        $this->leadProcessingService         = $leadProcessingService;
        $this->leadProcessingQueueRepository = $leadProcessingQueueRepository;
        $this->leadRepository                = $leadRepository;
        $this->managementRepository          = $managementRepository;
        $this->leadCommunicationService      = $leadCommunicationService;
    }

    /**
     * Returns the lead processor for the current request.
     *
     * @TODO: When this api moves to authentication via tokens rather than admin session privileges,
     *        this will need to be updated to facilitate that.
     *
     * @return LeadProcessor|null
     */
    protected function getLeadProcessor(): ?LeadProcessor
    {
        return $this->_cachedLeadProcessorId
            ?? $this->_cachedLeadProcessorId = $this->leadProcessingRepository->getLeadProcessorByUserId(Auth::user()->id);
    }

    /**
     * Returns whether a given lead is reserved by the current lead processor.
     *
     * @param EloquentQuote $lead
     * @return bool
     */
    protected function isLeadReservedByProcessor(EloquentQuote $lead): bool
    {
        $processor = $this->getLeadProcessor();

        return $processor && $this->leadProcessingService->isLeadReservedByProcessor($lead, $processor);
    }
}
