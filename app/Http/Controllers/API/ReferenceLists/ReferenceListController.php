<?php

namespace App\Http\Controllers\API\ReferenceLists;

use App\Enums\CompanySalesStatus;
use App\Enums\EmailTemplateType;
use App\Enums\Odin\Product;
use App\Enums\RejectionReasons;
use App\Enums\Timezone;
use App\Http\Controllers\API\APIController;
use App\Models\ContractKey;
use App\Models\Legacy\Location;
use App\Repositories\LocationRepository;
use App\Repositories\Odin\IndustryServiceRepository;
use App\Services\GlobalTypesService;
use App\Services\IndustryServicesService;
use App\Services\Legacy\ReferenceListsService;
use App\Transformers\ReferenceLists\CityTransformer;
use App\Transformers\ReferenceLists\GlobalTypeTransformer;
use App\Transformers\ReferenceLists\IndustryServiceTransformer;
use App\Services\Odin\WebsitesService;
use App\Transformers\Odin\WebsiteTransformer;
use App\Transformers\ReferenceLists\IndustryTransformer;
use App\Transformers\ReferenceLists\StateTransformer;
use App\Transformers\ReferenceLists\CountyTransformer;
use Illuminate\Http\JsonResponse;
use App\Models\Odin\Industry as OdinIndustry;
use App\Transformers\Odin\IndustryTransformer as OdinIndustryTransformer;
use Illuminate\Http\Request;

class ReferenceListController extends APIController
{
    const REQUEST_INDUSTRIES = 'industries';
    const REQUEST_DST = 'dst';

    /**
     * @param ReferenceListsService $referenceListsService
     * @param IndustryTransformer $industryTransformer
     * @return JsonResponse
     */
    public function getIndustries(
        ReferenceListsService $referenceListsService,
        IndustryTransformer   $industryTransformer
    ): JsonResponse
    {
        return $this->formatResponse([
            "status"     => true,
            "industries" => $industryTransformer->transformIndustries($referenceListsService->industries())
        ]);
    }

    /**
     * @param OdinIndustryTransformer $transformer
     * @return JsonResponse
     */
    public function getOdinIndustries(OdinIndustryTransformer $transformer): JsonResponse
    {
        return $this->formatResponse([
            'status'     => true,
            'industries' => $transformer->transform(OdinIndustry::all()),
        ]);
    }

    /**
     * @param IndustryServicesService $industryServicesService
     * @param IndustryServiceTransformer $industryServiceTransformer
     * @return JsonResponse
     */
    public function getIndustryServices(
        IndustryServicesService $industryServicesService,
        IndustryServiceTransformer $industryServiceTransformer
    ): JsonResponse
    {
        $industryServices = $industryServicesService->all($this->request->get('industry_id'));

        return $this->formatResponse([
            "status" => true,
            "industry_services" => $industryServiceTransformer->transformIndustryServices($industryServices)
        ]);
    }

    /**
     * Handles returning services against the requested set of industries.
     *
     * @param IndustryServiceRepository  $industryServiceRepository
     * @param IndustryServiceTransformer $industryServiceTransformer
     * @return JsonResponse
     */
    public function getIndustriesServices(
        IndustryServiceRepository  $industryServiceRepository,
        IndustryServiceTransformer $industryServiceTransformer
    ): JsonResponse
    {
        $industries         = $this->request->get(self::REQUEST_INDUSTRIES);
        $industriesServices = $industryServiceRepository->getIndustryServices($industries);

        return $this->formatResponse([
            "status"            => true,
            "industry_services" => $industryServiceTransformer->transformIndustryServices($industriesServices)
        ]);
    }

    /**
     * @param IndustryServicesService $industryServicesService
     * @return JsonResponse
     */
    public function allServicesByIndustry(IndustryServicesService $industryServicesService): JsonResponse
    {
        $services = $industryServicesService->allServicesByIndustry();

        return $this->formatResponse([
            'status'            => !!$services,
            'industry_services' => $services
        ]);
    }

    /**
     * @param GlobalTypesService $globalTypesService
     * @param GlobalTypeTransformer $globalTypeTransformer
     * @return JsonResponse
     */
    public function getGlobalTypes(
        GlobalTypesService $globalTypesService,
        GlobalTypeTransformer $globalTypeTransformer
    ): JsonResponse
    {
        return $this->formatResponse([
            "status" => true,
            "global_types" => $globalTypeTransformer->transformGlobalTypes($globalTypesService->all())
        ]);
    }

    /**
     * Retrieves a list of all states.
     *
     * @param LocationRepository $locationRepository
     * @param StateTransformer $stateTransformer
     * @return JsonResponse
     */
    public function getStates(
        LocationRepository $locationRepository,
        StateTransformer $stateTransformer
    ): JsonResponse
    {
        $states = $locationRepository->getstates();
        return $this->formatResponse(
            $stateTransformer->transformStates($states)
        );
    }

    /**
     * @param LocationRepository $locationRepository
     * @param CityTransformer $cityTransformer
     * @param string $stateKey
     * @return JsonResponse
     */
    public function getCitiesForState(
        LocationRepository $locationRepository,
        CityTransformer $cityTransformer,
        string $stateKey
    ): JsonResponse
    {
        $cities = $locationRepository->getCitiesInState($stateKey);

        return $this->formatResponse(
            $cityTransformer->transformCities($cities)
        );
    }

    /**
     * Retrieves a list of counties against the requested state.
     *
     * @param LocationRepository $locationRepository
     * @param CountyTransformer $countyTransformer
     * @param string $stateKey
     * @return JsonResponse
     */
    public function getCountiesForState(
        LocationRepository $locationRepository,
        CountyTransformer $countyTransformer,
        string $stateKey
    ): JsonResponse
    {
        $counties = $locationRepository->getCountiesInState($stateKey);
        return $this->formatResponse(
            $countyTransformer->transformCounties($counties)
        );
    }


    /**
     * Retrieves a list of counties belonging to a list of states.
     *
     * @param Request $request
     * @param LocationRepository $locationRepository
     * @param CountyTransformer $countyTransformer
     * @return JsonResponse
     */
    public function getCountiesByStates(
        Request $request,
        LocationRepository $locationRepository,
        CountyTransformer $countyTransformer,
    ): JsonResponse
    {
        $states = $request->get("states");
        $counties = $locationRepository->getCountiesInStatesList($states);

        return $this->formatResponse([
            "status" => true,
            "counties" => $countyTransformer->sortTransformedCounties($countyTransformer->transformCounties($counties, true)),
        ]);
    }

    /**
     * @param WebsitesService $websitesService
     * @param WebsiteTransformer $websiteTransformer
     * @return JsonResponse
     */
    public function getWebsites(
        WebsitesService $websitesService,
        WebsiteTransformer $websiteTransformer
    ): JsonResponse
    {
        return $this->formatResponse([
            "status" => true,
            "websites" => $websiteTransformer->transformAll($websitesService->all())
        ]);
    }

    /**
     * @param $product
     * @return JsonResponse
     */
    public function getRejectionReasons($product): JsonResponse
    {
        return $this->formatResponse([
            "status" => true,
            "rejection_reasons" => RejectionReasons::getSolarReasons(Product::from($product))
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function getTimezones(): JsonResponse
    {
        $isDST = Timezone::isDST();

        return $this->formatResponse([
            "status"    => true,
            "timezones" => array_flip(Timezone::displayNames($isDST)),
            "dst"       => $isDST,
        ]);
    }

    /**
     * Temporary placeholder function for Silo creation
     * TODO: implement properly when Country data exists in the DB
     * @return JsonResponse
     */
    public function getCountries(): JsonResponse
    {
        $countries = collect([
            [
                'name'  => 'USA',
                'id'    => 0,
            ]
        ]);

        return $this->formatResponse([
            'status'    => true,
            'countries' => $countries,
        ]);
    }

    /**
     * Returns the list of sales statuses.
     *
     * @return JsonResponse
     */
    public function getSaleStatuses(): JsonResponse
    {
        return $this->formatResponse([
            'status' => true,
            'statuses' => CompanySalesStatus::getAsKeyValueSelectArray(),
            'statuses_as_select_array' => CompanySalesStatus::asSelectArray(),
        ]);
    }

    public function getContractKeys(): JsonResponse
    {
        return $this->formatResponse([
            "status"        => true,
            "contract_keys" => ContractKey::all()
        ]);
    }

    public function getEmailTemplateTypes(): JsonResponse
    {
        return $this->formatResponse([
            'status'                => true,
            'email_template_types'  => EmailTemplateType::asSelectArray(),
        ]);
    }
}
