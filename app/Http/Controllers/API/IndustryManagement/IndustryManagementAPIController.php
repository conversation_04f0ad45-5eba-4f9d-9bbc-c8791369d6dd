<?php

namespace App\Http\Controllers\API\IndustryManagement;

use App\Builders\Odin\IndustryBuilder;
use App\DataModels\Odin\ConfigurableFieldDataModel;
use App\Enums\Odin\CompanyConfigurableFieldCategory;
use App\Enums\PermissionType;
use App\Factories\JsonAPIResponseFactory;
use App\Http\Controllers\API\APIController;
use App\Http\Requests\CompanyReviewRequest;
use App\Http\Requests\IndustryCompanyFieldRequest;
use App\Http\Requests\IndustryCompanyRequest;
use App\Http\Requests\IndustryConsumerFieldRequest;
use App\Http\Requests\IndustryTypeRequest;
use App\Http\Requests\IndustryWebsiteRequest;
use App\Http\Requests\StoreBulkProfitabilityAssumptionConfigurationsRequest;
use App\Http\Requests\StoreDefaultProfitabilityAssumptionConfigurationRequest;
use App\Http\Requests\StoreIndustryRequest;
use App\Http\Requests\StoreIndustryServiceRequest;
use App\Http\Requests\UpdateIndustryRequest;
use App\Http\Resources\Dashboard\GenericProfitabilityAssumptionConfigurationResource;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyIndustry;
use App\Models\Odin\CompanyReview;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryCompanyField;
use App\Models\Odin\IndustryConfiguration;
use App\Models\Odin\IndustryConsumerField;
use App\Models\Odin\IndustryService;
use App\Models\Odin\IndustryType;
use App\Models\Odin\IndustryWebsite;
use App\Models\User;
use App\Repositories\Odin\CompanyRepository;
use App\Models\Odin\Product;
use App\Models\Odin\ServiceProduct;
use App\Repositories\Odin\IndustryCompanyFieldRepository;
use App\Repositories\Odin\IndustryCompanyRepository;
use App\Repositories\Odin\IndustryConsumerFieldRepository;
use App\Repositories\Odin\IndustryRepository;
use App\Repositories\Odin\IndustryServiceRepository;
use App\Repositories\Odin\IndustryTypeRepository;
use App\Repositories\Odin\IndustryWebsiteRepository;
use App\Repositories\DefaultProfitabilityAssumptionConfigurationRepository;
use App\Services\EmailTemplates\EmailTemplateService;
use App\Transformers\Odin\CompanyReviewsTransformer;
use App\Transformers\Odin\CompanyTransformer;
use App\Transformers\Odin\IndustryCompanyFieldTransformer;
use App\Transformers\Odin\IndustryCompanyTransformer;
use App\Transformers\Odin\IndustryConsumerFieldTransformer;
use App\Transformers\Odin\IndustryServiceTransformer;
use App\Transformers\Odin\IndustryTransformer;
use App\Transformers\Odin\IndustryTypeTransformer;
use App\Transformers\Odin\IndustryWebsiteTransformer;
use App\Transformers\Odin\WebsiteTransformer;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\UnauthorizedException;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;
use App\Enums\Odin\Industry as IndustryEnum;
use Exception;

class IndustryManagementAPIController extends APIController
{
    const string PERMISSION_NAME                   = 'industry-management';

    const string REQUEST_INDUSTRY                  = 'industry';
    const string REQUEST_INDUSTRY_ID               = 'industry_id';
    const string REQUEST_INDUSTRY_WEBSITE          = 'industryWebsite';
    const string REQUEST_INDUSTRY_SERVICE          = 'industryService';
    const string REQUEST_INDUSTRY_COMPANY_FIELD    = 'industryCompanyField';
    const string REQUEST_INDUSTRY_CONSUMER_FIELD   = 'industryConsumerField';
    const string REQUEST_INDUSTRY_TYPE             = 'industryType';
    const string REQUEST_TEMPLATE_ID               = 'template_id';
    const string REQUEST_CUSTOM_FLOOR_PRICING_FLAG = 'allow_custom_floor_prices';

    const string RESPONSE_STATUS                   = 'status';

    /**
     * @param Request $request
     * @param JsonAPIResponseFactory $APIResponseFactory
     * @param IndustryRepository $industryRepository
     * @param IndustryWebsiteRepository $industryWebsiteRepository
     * @param IndustryServiceRepository $industryServiceRepository
     * @param IndustryCompanyRepository $industryCompanyRepository
     * @param IndustryCompanyFieldRepository $industryCompanyFieldRepository
     * @param IndustryConsumerFieldRepository $industryConsumerFieldRepository
     * @param IndustryTypeRepository $industryTypeRepository
     * @param DefaultProfitabilityAssumptionConfigurationRepository $profitabilityRepository
     * @param CompanyRepository $companyRepository
     */
    public function __construct(
        Request                                                         $request,
        JsonAPIResponseFactory                                          $APIResponseFactory,
        protected IndustryRepository                                    $industryRepository,
        protected IndustryWebsiteRepository                             $industryWebsiteRepository,
        protected IndustryServiceRepository                             $industryServiceRepository,
        protected IndustryCompanyRepository                             $industryCompanyRepository,
        protected IndustryCompanyFieldRepository                        $industryCompanyFieldRepository,
        protected IndustryConsumerFieldRepository                       $industryConsumerFieldRepository,
        protected IndustryTypeRepository                                $industryTypeRepository,
        protected DefaultProfitabilityAssumptionConfigurationRepository $profitabilityRepository,
        protected CompanyRepository                                     $companyRepository,
    )
    {
        parent::__construct($request, $APIResponseFactory);
    }

    /**
     * @param IndustryTransformer $transformer
     * @return JsonResponse
     */
    public function getIndustries(IndustryTransformer $transformer): JsonResponse
    {
        $industries = IndustryBuilder::query()
            ->appendIndustryConfiguration()
            ->getIndustryData()
            ->get();

        return $this->formatResponse([
            'status'     => 'true',
            'industries' => $transformer->transform($industries),
        ]);
    }

    /**
     * @param StoreIndustryRequest $request
     * @return JsonResponse
     */
    public function createIndustry(StoreIndustryRequest $request): JsonResponse
    {
        $data = $request->safe()->collect();

        return $this->formatResponse([
            'status' => $this->industryRepository->updateOrCreateIndustry(
                $data->get(Industry::FIELD_NAME),
                $data->get(Industry::FIELD_COLOR_DARK),
                $data->get(Industry::FIELD_COLOR_LIGHT)
            )
        ]);
    }

    /**
     * @param int $industry
     * @param UpdateIndustryRequest $request
     * @return JsonResponse
     */
    public function updateIndustry(int $industry, UpdateIndustryRequest $request): JsonResponse
    {
        $data = $request->safe()->collect();

        $savedIndustry = $this->industryRepository->updateOrCreateIndustry(
            $data->get(Industry::FIELD_NAME),
            $data->get(Industry::FIELD_COLOR_LIGHT),
            $data->get(Industry::FIELD_COLOR_DARK),
        $industry);

        return $this->formatResponse([
            'status' => $savedIndustry,
        ]);
    }

    /**
     * @param int $industry
     * @return JsonResponse
     */
    public function deleteIndustry(int $industry): JsonResponse
    {
        $this->performAuthorizationCheck(self::PERMISSION_NAME);

        return $this->formatResponse([
            'status' => $this->industryRepository->deleteIndustry($industry)
        ]);
    }

    /**
     * @param int $industry
     * @param IndustryWebsiteTransformer $transformer
     * @return JsonResponse
     */
    public function getIndustryWebsites(int $industry, IndustryWebsiteTransformer $transformer): JsonResponse
    {
        return $this->formatResponse([
            'status'   => 'true',
            'websites' => $transformer->transform($this->industryWebsiteRepository->getIndustryWebsites($industry))
        ]);
    }

    /**
     * @param IndustryWebsiteRequest $request
     * @return JsonResponse
     */
    public function createIndustryWebsite(IndustryWebsiteRequest $request): JsonResponse
    {
        $data = $request->safe()->collect();

        return $this->formatResponse([
            'status' => $this->industryWebsiteRepository->updateOrCreateIndustryWebsite(
                            $data->get(self::REQUEST_INDUSTRY),
                            $data->get(IndustryWebsite::FIELD_WEBSITE_ID),
                            $data->get(IndustryWebsite::FIELD_SLUG))
        ]);
    }

    /**
     * @param IndustryWebsiteRequest $request
     * @return JsonResponse
     */
    public function updateIndustryWebsite(IndustryWebsiteRequest $request): JsonResponse
    {
        $data = $request->safe()->collect();

        return $this->formatResponse([
            'status' => $this->industryWebsiteRepository->updateOrCreateIndustryWebsite(
                            $data->get(self::REQUEST_INDUSTRY),
                            $data->get(IndustryWebsite::FIELD_WEBSITE_ID),
                            $data->get(IndustryWebsite::FIELD_SLUG),
                            $data->get(self::REQUEST_INDUSTRY_WEBSITE))
        ]);
    }

    /**
     * @param int $industryWebsite
     * @return JsonResponse
     */
    public function deleteIndustryWebsite(int $industryWebsite): JsonResponse
    {
        $this->performAuthorizationCheck(self::PERMISSION_NAME);

        return $this->formatResponse([
            'status' => $this->industryWebsiteRepository->deleteIndustryWebsite($industryWebsite)
        ]);
    }

    /**
     * @param int $industry
     * @param WebsiteTransformer $transformer
     * @return JsonResponse
     */
    public function getNonAddedWebsites(int $industry, WebsiteTransformer $transformer): JsonResponse
    {
        return $this->formatResponse([
            'status'   => 'true',
            'websites' => $transformer->transformAll($this->industryWebsiteRepository->getNonAddedWebsitesAgainstIndustryId($industry))
        ]);
    }

    /**
     * @param int $industry
     * @param IndustryServiceTransformer $transformer
     * @return JsonResponse
     */
    public function getIndustryServices(int $industry, IndustryServiceTransformer $transformer): JsonResponse
    {
        return $this->formatResponse([
            'status'   => 'true',
            'services' => $transformer->transform($this->industryServiceRepository->getIndustryServices($industry))
        ]);
    }

    /**
     * @param StoreIndustryServiceRequest $request
     * @return JsonResponse
     */
    public function createIndustryService(StoreIndustryServiceRequest $request): JsonResponse
    {
        $data = $request->safe()->collect();

        return $this->formatResponse([
            'status' => $this->industryServiceRepository->updateOrCreateIndustryService(
                            industry: $data->get(self::REQUEST_INDUSTRY),
                            name: $data->get(IndustryService::FIELD_NAME),
                            slug: $data->get(IndustryService::FIELD_SLUG),
                            showOnWebsite: $data->get(IndustryService::FIELD_SHOW_ON_WEBSITE),
                            showOnRegistration: $data->get(IndustryService::FIELD_SHOW_ON_REGISTRATION),
                            showOnDashboard: $data->get(IndustryService::FIELD_SHOW_ON_DASHBOARD),
                            campaignFilterEnabled: $data->get(IndustryService::FIELD_CAMPAIGN_FILTER_ENABLED),
            )
        ]);
    }

    /**
     * @param StoreIndustryServiceRequest $request
     * @return JsonResponse
     */
    public function updateIndustryService(int $industry, int $industryService, StoreIndustryServiceRequest $request): JsonResponse
    {
        $data = $request->safe()->collect();

        return $this->formatResponse([
            'status' => $this->industryServiceRepository->updateOrCreateIndustryService(
                            industry: $data->get(self::REQUEST_INDUSTRY),
                            name: $data->get(IndustryService::FIELD_NAME),
                            slug: $data->get(IndustryService::FIELD_SLUG),
                            showOnWebsite: $data->get(IndustryService::FIELD_SHOW_ON_WEBSITE),
                            showOnRegistration: $data->get(IndustryService::FIELD_SHOW_ON_REGISTRATION),
                            showOnDashboard: $data->get(IndustryService::FIELD_SHOW_ON_DASHBOARD),
                            campaignFilterEnabled: $data->get(IndustryService::FIELD_CAMPAIGN_FILTER_ENABLED),
                            id: $industryService
            )
        ]);
    }

    /**
     * @param int $industryService
     * @return JsonResponse
     */
    public function deleteIndustryService(int $industryService): JsonResponse
    {
        $this->performAuthorizationCheck(self::PERMISSION_NAME);

        return $this->formatResponse([
            'status' => $this->industryServiceRepository->deleteIndustryService($industryService)
        ]);
    }

    /**
     * @param int $industry
     * @param IndustryCompanyTransformer $transformer
     * @return JsonResponse
     */
    public function getIndustryCompanies(int $industry, IndustryCompanyTransformer $transformer): JsonResponse
    {
        return $this->formatResponse([
            'status'   => 'true',
            'companies' => $transformer->transform($this->industryCompanyRepository->getIndustryCompanies($industry))
        ]);
    }

    /**
     * @param IndustryCompanyRequest $request
     * @return JsonResponse
     * @throws Exception
     */
    public function addIndustryCompany(IndustryCompanyRequest $request): JsonResponse
    {
        $data = $request->safe()->collect();

        $companyId = $data->get(CompanyIndustry::FIELD_COMPANY_ID);
        $company   = $this->companyRepository->findOrFail($companyId);

        $odinResponse = $this->industryCompanyRepository->addIndustryCompany(
            industry : $data->get(self::REQUEST_INDUSTRY),
            company  : $companyId
        );

        $legacyResponse = $this->industryCompanyRepository->setCompanyTypeInLegacy(
            company     : $company,
            industryIds : $company->{Company::RELATION_INDUSTRIES}->pluck(Industry::FIELD_ID)->toArray()
        );

        return $this->formatResponse([
            'status' => $odinResponse && $legacyResponse
        ]);
    }

    /**
     * @param int $industryCompany
     * @return JsonResponse
     * @throws \Exception
     */
    public function deleteIndustryCompany(int $industryCompany): JsonResponse
    {
        $this->performAuthorizationCheck(self::PERMISSION_NAME);

        /** @var CompanyIndustry|null $industryCompanyModel */
        $industryCompanyModel = $this->industryCompanyRepository->getIndustryCompanyById($industryCompany);
        if(!$industryCompanyModel) {
            throw new BadRequestException("The requested id `$industryCompany` is deemed invalid.");
        }

        $company = $this->companyRepository->findOrFail($industryCompanyModel->company_id);

        $odinResponse = $this->industryCompanyRepository->deleteIndustryCompany($industryCompany);

        //Sync company type in legacy based on the remaining industry(ies) setup
        $legacyResponse = $this->industryCompanyRepository->setCompanyTypeInLegacy(
            company     : $company,
            industryIds : $company->{Company::RELATION_INDUSTRIES}->pluck(Industry::FIELD_ID)->toArray()
        );

        return $this->formatResponse([
            'status' => $odinResponse && $legacyResponse
        ]);
    }

    /**
     * @param int $industry
     * @param CompanyTransformer $transformer
     * @return JsonResponse
     */
    public function getNonAddedCompanies(int $industry, CompanyTransformer $transformer): JsonResponse
    {
        return $this->formatResponse([
            'status'   => 'true',
            'companies' => $transformer->transformCompanies($this->industryCompanyRepository->getNonAddedCompaniesAgainstIndustryId($industry))
        ]);
    }

    /**
     * @param int $industry
     * @param IndustryCompanyFieldTransformer $transformer
     * @return JsonResponse
     */
    public function getIndustryCompanyFields(int $industry, IndustryCompanyFieldTransformer $transformer): JsonResponse
    {
        return $this->formatResponse([
            'status' => 'true',
            'fields' => $transformer->transform($this->industryCompanyFieldRepository->getIndustryCompanyFields($industry))
        ]);
    }

    /**
     * @param IndustryCompanyFieldRequest $request
     * @return JsonResponse
     */
    public function createIndustryCompanyField(IndustryCompanyFieldRequest $request): JsonResponse
    {
        $data = $request->safe()->collect();

        return $this->formatResponse([
            'status' => $this->industryCompanyFieldRepository->updateOrCreateIndustryCompanyField(
                            $data->get(self::REQUEST_INDUSTRY),
                            $data->get(IndustryCompanyField::FIELD_NAME),
                            $data->get(IndustryCompanyField::FIELD_KEY),
                            $data->get(IndustryCompanyField::FIELD_TYPE),
                            $data->get(IndustryCompanyField::FIELD_SHOW_ON_PROFILE),
                            $data->get(IndustryCompanyField::FIELD_SHOW_ON_DASHBOARD),
                            new ConfigurableFieldDataModel(collect($data->get(IndustryCompanyField::FIELD_PAYLOAD))),
                            CompanyConfigurableFieldCategory::from($data->get(IndustryCompanyField::FIELD_CATEGORY))
            )
        ]);
    }

    /**
     * @param IndustryCompanyFieldRequest $request
     * @return JsonResponse
     */
    public function updateIndustryCompanyField(IndustryCompanyFieldRequest $request): JsonResponse
    {
        $data = $request->safe()->collect();

        return $this->formatResponse([
            'status' => $this->industryCompanyFieldRepository->updateOrCreateIndustryCompanyField(
                            $data->get(self::REQUEST_INDUSTRY),
                            $data->get(IndustryCompanyField::FIELD_NAME),
                            $data->get(IndustryCompanyField::FIELD_KEY),
                            $data->get(IndustryCompanyField::FIELD_TYPE),
                            $data->get(IndustryCompanyField::FIELD_SHOW_ON_PROFILE),
                            $data->get(IndustryCompanyField::FIELD_SHOW_ON_DASHBOARD),
                            new ConfigurableFieldDataModel(collect($data->get(IndustryCompanyField::FIELD_PAYLOAD))),
                            CompanyConfigurableFieldCategory::from($data->get(IndustryCompanyField::FIELD_CATEGORY)),
                            $data->get(self::REQUEST_INDUSTRY_COMPANY_FIELD))
        ]);
    }

    /**
     * @param int $industryCompanyField
     * @return JsonResponse
     */
    public function deleteIndustryCompanyField(int $industryCompanyField): JsonResponse
    {
        $this->performAuthorizationCheck(self::PERMISSION_NAME);

        return $this->formatResponse([
            'status' => $this->industryCompanyFieldRepository->deleteIndustryCompanyField($industryCompanyField)
        ]);
    }

    /**
     * @param int $industry
     * @param IndustryConsumerFieldTransformer $transformer
     * @return JsonResponse
     */
    public function getIndustryConsumerFields(int $industry, IndustryConsumerFieldTransformer $transformer): JsonResponse
    {
        return $this->formatResponse([
            'status' => 'true',
            'fields' => $transformer->transform($this->industryConsumerFieldRepository->getIndustryConsumerFields($industry))
        ]);
    }

    /**
     * @param IndustryConsumerFieldRequest $request
     * @return JsonResponse
     */
    public function createIndustryConsumerField(IndustryConsumerFieldRequest $request): JsonResponse
    {
        $data = $request->safe()->collect();

        return $this->formatResponse([
            'status' => $this->industryConsumerFieldRepository->updateOrCreateIndustryConsumerField(
                            $data->get(self::REQUEST_INDUSTRY),
                            $data->get(IndustryConsumerField::FIELD_NAME),
                            $data->get(IndustryConsumerField::FIELD_KEY),
                            $data->get(IndustryConsumerField::FIELD_TYPE),
                            $data->get(IndustryConsumerField::FIELD_CATEGORY_ID),
                            $data->get(IndustryConsumerField::FIELD_SEND_TO_COMPANY),
                            $data->get(IndustryConsumerField::FIELD_SHOW_ON_DASHBOARD))
        ]);
    }

    /**
     * @param IndustryConsumerFieldRequest $request
     * @return JsonResponse
     */
    public function updateIndustryConsumerField(IndustryConsumerFieldRequest $request): JsonResponse
    {
        $data = $request->safe()->collect();

        return $this->formatResponse([
            'status' => $this->industryConsumerFieldRepository->updateOrCreateIndustryConsumerField(
                            $data->get(self::REQUEST_INDUSTRY),
                            $data->get(IndustryConsumerField::FIELD_NAME),
                            $data->get(IndustryConsumerField::FIELD_KEY),
                            $data->get(IndustryConsumerField::FIELD_TYPE),
                            $data->get(IndustryConsumerField::FIELD_CATEGORY_ID),
                            $data->get(IndustryConsumerField::FIELD_SEND_TO_COMPANY),
                            $data->get(IndustryConsumerField::FIELD_SHOW_ON_DASHBOARD),
                            $data->get(self::REQUEST_INDUSTRY_CONSUMER_FIELD))
        ]);
    }

    /**
     * @param int $industryConsumerField
     * @return JsonResponse
     */
    public function deleteIndustryConsumerField(int $industryConsumerField): JsonResponse
    {
        $this->performAuthorizationCheck(self::PERMISSION_NAME);

        return $this->formatResponse([
            'status' => $this->industryConsumerFieldRepository->deleteIndustryConsumerField($industryConsumerField)
        ]);
    }

    /**
     * @param int $industry
     * @param IndustryTypeTransformer $transformer
     * @return JsonResponse
     */
    public function getIndustryTypes(int $industry, IndustryTypeTransformer $transformer): JsonResponse
    {
        return $this->formatResponse([
            'status' => 'true',
            'types' => $transformer->transform($this->industryTypeRepository->getIndustryTypes($industry))
        ]);
    }

    /**
     * @param IndustryTypeRequest $request
     * @return JsonResponse
     */
    public function createIndustryType(IndustryTypeRequest $request): JsonResponse
    {
        $data = $request->safe()->collect();

        return $this->formatResponse([
            'status' => $this->industryTypeRepository->updateOrCreateIndustryType(
                            $data->get(IndustryType::FIELD_GLOBAL_TYPE_ID),
                            $data->get(self::REQUEST_INDUSTRY),
                            $data->get(IndustryType::FIELD_NAME),
                            $data->get(IndustryType::FIELD_KEY))
        ]);
    }

    /**
     * @param IndustryTypeRequest $request
     * @return JsonResponse
     */
    public function updateIndustryType(IndustryTypeRequest $request): JsonResponse
    {
        $data = $request->safe()->collect();

        return $this->formatResponse([
            'status' => $this->industryTypeRepository->updateOrCreateIndustryType(
                            $data->get(IndustryType::FIELD_GLOBAL_TYPE_ID),
                            $data->get(self::REQUEST_INDUSTRY),
                            $data->get(IndustryType::FIELD_NAME),
                            $data->get(IndustryType::FIELD_KEY),
                            $data->get(self::REQUEST_INDUSTRY_TYPE))
        ]);
    }

    /**
     * @param int $industryType
     * @return JsonResponse
     */
    public function deleteIndustryType(int $industryType): JsonResponse
    {
        $this->performAuthorizationCheck(self::PERMISSION_NAME);

        return $this->formatResponse([
            'status' => $this->industryTypeRepository->deleteIndustryType($industryType)
        ]);
    }

    /**
     * @param int $industry
     * @param CompanyReviewsTransformer $transformer
     * @return JsonResponse
     */
    public function getIndustryTypeReviews(int $industry, CompanyReviewsTransformer $transformer): JsonResponse
    {
        return $this->formatResponse([
            'status'   => true,
            'reviews'  => $transformer->transform($this->industryTypeRepository->getIndustryTypeReviews($industry))
        ]);
    }

    /**
     * @param IndustryType $industryType
     * @param CompanyReviewRequest $request
     * @return JsonResponse
     */
    public function createIndustryTypeReview(IndustryType $industryType, CompanyReviewRequest $request): JsonResponse
    {
        $data = $request->safe()->collect();

        return $this->formatResponse([
            'status' => $this->industryTypeRepository->createIndustryTypeReview(
                            $industryType->id,
                            $data->get(CompanyReview::FIELD_COMPANY_ID),
                            $data->get(CompanyReview::FIELD_REL_ID) ?? $data->get(CompanyReview::FIELD_COMPANY_ID),
                            $data->get(CompanyReview::FIELD_REL_TYPE) ?? CompanyReview::REL_TYPE_COMPANY,
                            $data->get(CompanyReview::FIELD_FIRST_NAME),
                            $data->get(CompanyReview::FIELD_LAST_NAME),
                            $data->get(CompanyReview::FIELD_EMAIL),
                            $data->get(CompanyReview::FIELD_PHONE),
                            $data->get(CompanyReview::FIELD_TITLE),
                            $data->get(CompanyReview::FIELD_BODY),
                            $data->get(CompanyReview::FIELD_OVERALL_SCORE),
                            $data->get(CompanyReview::FIELD_STATUS) ?? CompanyReview::STATUS_INITIAL,
                            $request->ip())
        ]);
    }

    /**
     * @param int $review
     * @return JsonResponse
     */
    public function deleteIndustryTypeReview(int $review): JsonResponse
    {
        $this->performAuthorizationCheck(self::PERMISSION_NAME);

        return $this->formatResponse([
            'status' => $this->industryTypeRepository->deleteIndustryTypeReview($review)
        ]);
    }

    public function getDefaultProfitabilityAssumptions(): JsonResponse
    {
        return $this->formatResponse([
            'status'                                 => true,
            'default_profitability_configurations'   => GenericProfitabilityAssumptionConfigurationResource::collection($this->profitabilityRepository->getDefaultProfitabilityAssumptions()),
        ]);
    }

    public function updateDefaultProfitabilityAssumption(StoreDefaultProfitabilityAssumptionConfigurationRequest $request): JsonResponse
    {
        $serviceId = $this->request->route('industryServiceId');
        $updated = $this->profitabilityRepository->updateOrCreateDefaultProfitabilityAssumption($serviceId, $request->toArray());

        return $this->formatResponse([
            'status'                              => !!$updated,
            'default_profitability_configuration' => new GenericProfitabilityAssumptionConfigurationResource($updated)
        ]);
    }

    public function updateAllDefaultProfitabilityAssumptions(StoreBulkProfitabilityAssumptionConfigurationsRequest $request): JsonResponse
    {
        $updated = $this->profitabilityRepository->updateAllDefaultProfitabilityConfigurations($request->collect());

        return $this->formatResponse([
            'status'                                => !!$updated,
            'default_profitability_configurations'  => GenericProfitabilityAssumptionConfigurationResource::collection($updated),
        ]);
    }

    public function deleteDefaultProfitabilityAssumption(): JsonResponse
    {
        $serviceId = $this->request->route('industryServiceId');

        return $this->formatResponse([
            'status'    => $this->profitabilityRepository->deleteDefaultProfitabilityAssumption($serviceId)
        ]);
    }

    /**
     * @param Industry $industry
     * @param EmailTemplateService $emailTemplateService
     *
     * @return JsonResponse
     */
    public function updateDeliveryEmailTemplate(Industry $industry, EmailTemplateService $emailTemplateService): JsonResponse
    {
        $templateId = $this->request->get(self::REQUEST_TEMPLATE_ID);

        if ($templateId === null)
            $industry->delivery_email_template_id = null;
        else
            $industry->delivery_email_template_id = $emailTemplateService->getTemplateById($templateId)->id;

        return $this->formatResponse([
            'status'    => $industry->save()
        ]);
    }

    /**
     * @param EmailTemplateService $emailTemplateService
     *
     * @return JsonResponse
     */
    public function updateDefaultLeadEmailTemplate(EmailTemplateService $emailTemplateService): JsonResponse
    {
        $emailTemplate = $emailTemplateService->getTemplateById($this->request->get(self::REQUEST_TEMPLATE_ID));

        return $this->formatResponse([
            'status'    => $emailTemplateService->updateDefaultLeadEmailTemplate($emailTemplate)
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function enableFutureCampaigns(): JsonResponse
    {
        if ($this->userCanUpdateConfiguration()) {
            /** @var Industry $industry */
            $industry = Industry::query()
                ->findOrFail($this->request->get(self::REQUEST_INDUSTRY_ID));
            $industryConfiguration = $industry->industryConfiguration()->firstOrCreate();
            $industryConfiguration->future_campaigns_active = true;
            $success = $industryConfiguration->save();
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS   => $success ?? false,
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function disableFutureCampaigns(): JsonResponse
    {
        if ($this->userCanUpdateConfiguration()) {
            /** @var Industry $industry */
            $industry = Industry::query()
                ->findOrFail($this->request->get(self::REQUEST_INDUSTRY_ID));
            $industry->industryConfiguration->future_campaigns_active = false;
            $success = $industry->industryConfiguration->save();
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS   => $success ?? false,
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function toggleCustomFloorPricingFlag(): JsonResponse
    {
        if ($this->userCanUpdateConfiguration()) {
            $data = $this->request->validate([
                self::REQUEST_INDUSTRY_ID               => 'numeric|required',
                self::REQUEST_CUSTOM_FLOOR_PRICING_FLAG => 'boolean|required',
            ]);

            $success = $this->industryRepository->setCustomFloorPriceFlag($data[self::REQUEST_INDUSTRY_ID], $data[self::REQUEST_CUSTOM_FLOOR_PRICING_FLAG]);
        }

        return $this->formatResponse([
            self::RESPONSE_STATUS => $success ?? false,
        ]);
    }

    /**
     * @return JsonResponse
     */
    public function updateReviewsFlag(): JsonResponse
    {
        $this->userCanUpdateConfiguration();

        $payload = $this->request->validate([
            IndustryConfiguration::FIELD_INDUSTRY_ID             => 'numeric|required',
            IndustryConfiguration::FIELD_CONSUMER_REVIEWS_ACTIVE => 'boolean|required',
        ]);

        $config = Industry::query()
            ->findOrFail($payload[IndustryConfiguration::FIELD_INDUSTRY_ID])
            ?->industryConfiguration;
        if ($config)
            $config->consumer_reviews_active = $payload[IndustryConfiguration::FIELD_CONSUMER_REVIEWS_ACTIVE];

        return $this->formatResponse([
            self::RESPONSE_STATUS => $config?->save(),
        ]);
    }

    /**
     * @return bool
     */
    protected function userCanUpdateConfiguration(): bool
    {
        /** @var User $user */
        $user = Auth::user();
        if (!$user->hasPermissionTo(PermissionType::INDUSTRY_CONFIGURATION->value))
            throw new UnauthorizedException("The current user does not have the required Industry Configuration permission.");

        else return true;
    }

    /**
     * @param int $industryId
     * @return bool
     */
    protected function industryCanBeUpgraded(int $industryId): bool
    {
        return CompanyCampaign::query()
            ->join(IndustryService::TABLE, IndustryService::TABLE .'.'. IndustryService::FIELD_ID, '=', CompanyCampaign::FIELD_SERVICE_ID)
            ->where(IndustryService::FIELD_INDUSTRY_ID, $industryId)
            ->count() > 0;
    }
}
