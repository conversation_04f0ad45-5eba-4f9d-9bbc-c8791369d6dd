<?php

namespace App\Http\Controllers\API\IndustryManagement;

use App\Http\Controllers\API\APIController;
use App\Http\Requests\Odin\StoreProductRequest;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ServiceProduct;
use App\Models\User;
use App\Repositories\Odin\ProductRepository;
use App\Transformers\Odin\IndustryServiceTransformer;
use App\Transformers\Odin\ProductTransformer;
use Illuminate\Http\JsonResponse;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\UnauthorizedException;
use Symfony\Component\HttpFoundation\Exception\BadRequestException;

class ProductController extends APIController
{
    /**
     * @param ProductTransformer $transformer
     *
     * @return JsonResponse
     */
    public function index(ProductTransformer $transformer): JsonResponse
    {
        return $this->formatResponse([
            'products' => $transformer->transformAll(Product::query()->latest()->get())
        ]);
    }

    /**
     * @param ProductRepository $repository
     * @param StoreProductRequest $request
     * @param ProductTransformer $transformer
     *
     * @return JsonResponse
     */
    public function createProduct(ProductRepository $repository, StoreProductRequest $request, ProductTransformer $transformer): JsonResponse
    {
        return $this->formatResponse([
            'product' => $transformer->transformProduct($repository->createOrUpdateProduct($request->get(Product::FIELD_NAME)))
        ]);
    }

    /**
     * @param Product $product
     * @param ProductRepository $repository
     * @param StoreProductRequest $request
     * @param ProductTransformer $transformer
     *
     * @return JsonResponse
     */
    public function updateProduct(Product $product, ProductRepository $repository, StoreProductRequest $request, ProductTransformer $transformer): JsonResponse
    {
        return $this->formatResponse([
            'product' => $transformer->transformProduct($repository->createOrUpdateProduct($request->get(Product::FIELD_NAME), $product->id))
        ]);
    }

    /**
     * @param Product $product
     * @param ProductRepository $repository
     *
     * @return JsonResponse
     */
    public function deleteProduct(Product $product, ProductRepository $repository): JsonResponse
    {
        $this->authorizeRequest();

        return $this->formatResponse([
            'status' => $repository->deleteProduct($product)
        ]);
    }

    /**
     * @param Product $product
     * @param IndustryServiceTransformer $transformer
     *
     * @return JsonResponse
     */
    public function getServices(Product $product, IndustryServiceTransformer $transformer): JsonResponse
    {
        return $this->formatResponse([
            'services' => $transformer->transform($product->services)
        ]);
    }

    /**
     * @param Product $product
     * @param IndustryService $industryService
     *
     * @return JsonResponse
     */
    public function addService(Product $product, IndustryService $industryService): JsonResponse
    {
        $this->authorizeRequest();

        if ($product->services()
            ->where(ServiceProduct::FIELD_PRODUCT_ID, $product->id)
            ->where(ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $industryService->id)
            ->first())
            throw new BadRequestException('The service has already been added');

        $product->services()->attach($industryService);

        return $this->formatResponse([
            'status' =>true
        ]);
    }

    /**
     * @param Product $product
     * @param IndustryService $industryService
     *
     * @return JsonResponse
     */
    public function deleteService(Product $product, IndustryService $industryService): JsonResponse
    {
        $this->authorizeRequest();
        $product->services()->detach($industryService);

        return $this->formatResponse([
            'status' =>true
        ]);
    }

    /**
     * @return void
     */
    protected function authorizeRequest(): void
    {
        /** @var User $user */
        $user = Auth::user();

        if (!$user->hasPermissionTo('industry-management')) throw new UnauthorizedException('User does not have permission');
    }
}
