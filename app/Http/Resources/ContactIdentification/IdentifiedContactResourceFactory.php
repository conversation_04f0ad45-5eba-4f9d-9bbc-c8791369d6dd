<?php

namespace App\Http\Resources\ContactIdentification;

use App\Models\Odin\CompanyUser;
use App\Models\Odin\Consumer;
use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Http\Resources\Json\JsonResource;

class IdentifiedContactResourceFactory
{
    static function make(Model $model): JsonResource
    {
        return match ($model::class) {
            Consumer::class     => new ConsumerContactResource($model),
            User::class         => new UserContactResource($model),
            CompanyUser::class  => new CompanyUserContactResource($model),
            default             => new JsonResource($model) // TODO - Default resource with common fields
        };
    }
}
