<?php

namespace App\Http\Resources\CompanyCampaign;

use App\Models\Campaigns\CompanyCampaignFilter;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin CompanyCampaignFilter
 */
class CampaignFilterResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            CompanyCampaignFilter::FIELD_ID => $this->id,
            CompanyCampaignFilter::FIELD_KEY => $this->key,
            CompanyCampaignFilter::FIELD_OPERATOR => [
                'value' => $this->operator->value,
                'label' => $this->operator->symbol()
            ],
            CompanyCampaignFilter::FIELD_VALUE => $this->value,
        ];
    }
}
