<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Collection;

class ConsolidatedCompanyContractResource extends JsonResource
{
    const string FIELD_A20_CONTRACTS    = 'a20';
    const string FIELD_LEGACY_CONTRACTS = 'legacy';

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return $this->formatAndMergeCommunicationsAndEvents(
            a20Contracts   : $this->resource->get(self::FIELD_A20_CONTRACTS, collect()),
            legacyContracts: $this->resource->get(self::FIELD_LEGACY_CONTRACTS, collect())
        )->sortBy('date')->values()->toArray();
    }

    /**
     * @param Collection $a20Contracts
     * @param Collection $legacyContracts
     * @return Collection
     */
    protected function formatAndMergeCommunicationsAndEvents(
        Collection $a20Contracts,
        Collection $legacyContracts,
    ): Collection
    {
        $a20 = CompanyContractResource::collection($a20Contracts)->collection;
        $legacy = LegacyCompanyContractResource::collection($legacyContracts)->collection;

        return $a20->merge($legacy)->flatten();
    }
}
