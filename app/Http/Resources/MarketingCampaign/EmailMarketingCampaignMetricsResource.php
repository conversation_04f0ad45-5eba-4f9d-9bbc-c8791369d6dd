<?php

namespace App\Http\Resources\MarketingCampaign;

use App\DTO\MarketingCampaign\EmailCampaignMetrics;
use App\Models\MarketingCampaign;
use Illuminate\Http\Client\Request;

/**
 * @mixin MarketingCampaign
 */
class EmailMarketingCampaignMetricsResource extends BaseMarketingCampaignMetricsResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        $metrics = EmailCampaignMetrics::fromMarketingCampaign($this->resource);

        return [
            'opens'         => $metrics->getOpens(),
            'unique_opens'  => $metrics->getUniqueOpens(),
            'open_rate'     => round($metrics->getOpenRate(),2),
            'clicks'        => $metrics->getClicks(),
            'unique_clicks' => $metrics->getUniqueClicks(),
            'click_rate'    => round($metrics->getClickRate(),2),
            ...parent::toArray($request)
        ];
    }
}
