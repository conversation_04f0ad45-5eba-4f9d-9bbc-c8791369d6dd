<?php

namespace App\Http\Resources\MarketingCampaign;

use App\Enums\MarketingCampaigns\MarketingCampaignCallbackType;
use App\Http\Resources\Odin\ListOptionResource;
use App\Models\MarketingCampaign;
use Illuminate\Support\Str;

/**
 * @mixin MarketingCampaignCallbackType
 */
class MarketingCampaignCallbackListOptionResource extends ListOptionResource
{
    public function getId(): string
    {
        return $this->value;
    }

    public function getName(): string
    {
        return Str::headline($this->value);
    }

    public function getPayload(): array
    {
        return [
            'inputs' => $this->getInputs()
        ];
    }
}
