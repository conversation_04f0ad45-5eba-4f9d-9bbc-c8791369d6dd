<?php

namespace App\Http\Resources\MarketingCampaign;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\Company;
use Illuminate\Http\Request;

/**
 * @mixin CompanyCampaign|Company
 */
class ExternalAvailableCompaniesResource extends BaseJsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return match (get_class($this->resource)) {
            CompanyCampaign::class => [
                'company_id' => $this->company_id,
                'company_name' => $this->company->name,
                'campaign_id' => $this->id,
            ],
            Company::class  => [
                'company_id' => $this->id,
                'company_name' => $this->name,
                'campaign_id' => null,
            ]
        };
    }
}
