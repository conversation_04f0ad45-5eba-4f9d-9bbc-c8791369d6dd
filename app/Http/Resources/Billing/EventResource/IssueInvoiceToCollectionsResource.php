<?php

namespace App\Http\Resources\Billing\EventResource;

use App\Http\Resources\Billing\AuthorableEventResource;
use Illuminate\Support\Arr;
use Spatie\EventSourcing\StoredEvents\StoredEvent;

/**
 * @mixin StoredEvent
 */
class IssueInvoiceToCollectionsResource extends AuthorableEventResource
{
    const string EVENT_PROPERTY_RECOVERY_STATUS  = 'recovery_status';
    const string EVENT_PROPERTY_AMOUNT_COLLECTED = 'amount_collected';
    const string EVENT_PROPERTY_SENT_DATE        = 'sent_date';

    /**
     * Transform the resource into an array.
     *
     * @return array
     */
    public function getEventData(): array
    {
        $eventProperties = $this->event_properties;

        return [
            self::EVENT_PROPERTY_RECOVERY_STATUS  => Arr::get($eventProperties, 'recoveryStatus'),
            self::EVENT_PROPERTY_AMOUNT_COLLECTED => Arr::get($eventProperties, 'amountCollected'),
            self::EVENT_PROPERTY_SENT_DATE        => Arr::get($eventProperties, 'sentDate'),
        ];
    }
}
