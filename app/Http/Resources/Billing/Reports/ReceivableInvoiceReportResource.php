<?php

namespace App\Http\Resources\Billing\Reports;

use App\Enums\Billing\InvoiceReportsGrouping;
use App\Enums\Billing\InvoiceStates;
use App\Helpers\CarbonHelper;
use App\Http\Requests\Billing\Reports\GetReceivableInvoicesReportRequest;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Billing\InvoiceSnapshot;
use App\Models\Odin\Company;
use App\Services\CurrencyService;

/**
 * @mixin InvoiceSnapshot
 */
class ReceivableInvoiceReportResource extends BaseJsonResource
{
    const string FIELD_ID                 = 'id';
    const string FIELD_INVOICE_ID         = 'invoice_id';
    const string FIELD_INDUSTRY_ID        = 'industry_id';
    const string FIELD_COMPANY_ID         = 'company_id';
    const string FIELD_ACCOUNT_MANAGER_ID = 'account_manager_id';
    const string FIELD_SUCCESS_MANAGER_ID = 'success_manager_id';
    const string FIELD_STATUS             = 'status';
    const string FIELD_TRANSITION_DATE    = 'transition_date';
    const string FIELD_TOTAL_OUTSTANDING  = 'total_outstanding';

    public function toArray($request): array
    {
        /** @var CurrencyService $currencyService */
        $currencyService = app()->make(CurrencyService::class);

        $reference = $request->get(GetReceivableInvoicesReportRequest::FIELD_REFERENCE, InvoiceReportsGrouping::INVOICE->value);

        return [
            self::FIELD_ID                 => $this->{InvoiceSnapshot::FIELD_ID},
            'success_manager'              => [
                'id'   => $this->company_customer_success_manager_user_id,
                'name' => $this->company_customer_success_manager_user_name,
            ],
            'business_development_manager' => [
                'id'   => $this->company_business_development_manager_user_id,
                'name' => $this->company_business_development_manager_user_name,
            ],
            'account_manager'              => [
                'id'   => $this->company_account_manager_user_id,
                'name' => $this->company_account_manager_user_name,
            ],
            'company'                      => [
                'id'   => $this->{InvoiceSnapshot::RELATION_COMPANY}->{Company::FIELD_ID},
                'name' => $this->{InvoiceSnapshot::RELATION_COMPANY}->{Company::FIELD_NAME},
            ],
            self::FIELD_INVOICE_ID         => $reference === InvoiceReportsGrouping::INVOICE->value ? $this->{InvoiceSnapshot::FIELD_INVOICE_ID} : 'N/A',
            self::FIELD_STATUS             => $reference === InvoiceReportsGrouping::INVOICE->value ? InvoiceStates::tryFrom($this->{InvoiceSnapshot::FIELD_STATUS})->getTitle() : 'N/A',
            'total_outstanding'            => $currencyService->formatToCurrency($this->{InvoiceSnapshot::FIELD_TOTAL_OUTSTANDING} / 100),
            'total_paid'                   => $currencyService->formatToCurrency($this->{InvoiceSnapshot::FIELD_TOTAL_PAID} / 100),
            'total_refunded'               => $currencyService->formatToCurrency($this->{InvoiceSnapshot::FIELD_TOTAL_REFUNDED} / 100),
            'total_value'                  => $currencyService->formatToCurrency($this->{InvoiceSnapshot::FIELD_TOTAL_VALUE} / 100),
            'created_at'                   => $reference === InvoiceReportsGrouping::INVOICE->value
                ? CarbonHelper::parseWithTimezone($this->{InvoiceSnapshot::FIELD_DATE})->toFormat(CarbonHelper::FORMAT_DATETIME_WITH_TIMEZONE)
                : 'N/A',
        ];
    }
}
