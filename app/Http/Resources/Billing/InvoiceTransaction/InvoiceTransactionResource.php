<?php

namespace App\Http\Resources\Billing\InvoiceTransaction;

use App\Helpers\CarbonHelper;
use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Billing\InvoiceTransaction;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Number;

/**
 * @mixin InvoiceTransaction
 */
class InvoiceTransactionResource extends BaseJsonResource
{
    const string ID                    = 'id';
    const string INVOICE_UUID          = 'invoice_uuid';
    const string INVOICE_ID            = 'invoice_id';
    const string EXTERNAL_REFERENCE    = 'external_reference';
    const string AMOUNT                = 'amount';
    const string TRANSACTION_TYPE      = 'transaction_type';
    const string PAYLOAD               = 'payload';
    const string TRANSACTION_TYPE_ID   = 'id';
    const string TRANSACTION_TYPE_NAME = 'name';


    public function __construct($resource)
    {
        parent::__construct($resource);
    }

    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array
     */
    public function toArray($request): array
    {
        return [
            self::ID                 => $this->id,
            self::INVOICE_UUID       => $this->invoice_uuid,
            self::INVOICE_ID         => $this->invoice?->id,
            self::EXTERNAL_REFERENCE => $this->external_reference,
            'transaction_date'       => CarbonHelper::parseWithTimezone($this->date)->toFormat(),
            'amount'                 => $this->amount,
            'amount_in_dollars'      => Number::currency($this->amount / 100, $this->currency),
            'created_at'             => CarbonHelper::parseWithTimezone($this->created_at)->toFormat(),
            self::TRANSACTION_TYPE   => [
                self::TRANSACTION_TYPE_ID   => $this->type->value,
                self::TRANSACTION_TYPE_NAME => $this->getConsolidatedTitle()
            ],
            self::PAYLOAD            => $this->payload,
        ];
    }
}
