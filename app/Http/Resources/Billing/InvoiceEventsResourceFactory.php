<?php

namespace App\Http\Resources\Billing;

use App\Events\Billing\StoredEvents\Credit\CreditAdded;
use App\Events\Billing\StoredEvents\ActionApprovalRequest\ActionApprovalRequested;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeDisputeClosed;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeDisputeCreated;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeRequest;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeRequestAttempted;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeRequestFailed;
use App\Events\Billing\StoredEvents\Invoice\Charge\InvoiceChargeRequestSuccess;
use App\Events\Billing\StoredEvents\Invoice\Collections\IssueInvoiceToCollections;
use App\Events\Billing\StoredEvents\Invoice\Collections\UpdateInvoiceCollections;
use App\Events\Billing\StoredEvents\Invoice\InvoiceInitialized;
use App\Events\Billing\StoredEvents\Invoice\InvoiceItemTaxApplied;
use App\Events\Billing\StoredEvents\Invoice\InvoiceStatusUpdated;
use App\Events\Billing\StoredEvents\Invoice\InvoiceUpdated;
use App\Events\Billing\StoredEvents\Invoice\Pdf\CreateInvoicePdf;
use App\Events\Billing\StoredEvents\Invoice\RequestInvoiceTax;
use App\Events\Billing\StoredEvents\ActionApprovalRequest\ActionApprovalReviewed;
use App\Events\Billing\StoredEvents\InvoiceItem\InvoiceItemAdded;
use App\Events\Billing\StoredEvents\InvoiceItem\InvoiceItemDeleted;
use App\Events\Billing\StoredEvents\InvoiceItem\InvoiceItemUpdated;
use App\Http\Resources\Billing\EventResource\CreditAddedEventResource;
use App\Http\Resources\Billing\EventResource\InvoiceActionRequestEventResource;
use App\Http\Resources\Billing\EventResource\InvoiceActionRequestReviewedEventResource;
use App\Http\Resources\Billing\EventResource\InvoiceDisputeClosedResource;
use App\Http\Resources\Billing\EventResource\InvoiceDisputeCreatedResource;
use App\Http\Resources\Billing\EventResource\IssueInvoiceToCollectionsResource;
use App\Http\Resources\Billing\EventResource\UpdateInvoiceCollectionsResource;
use Illuminate\Http\Resources\Json\JsonResource;
use Spatie\EventSourcing\StoredEvents\Models\EloquentStoredEvent;

class InvoiceEventsResourceFactory
{
    static function make(EloquentStoredEvent $storedEvent): ?JsonResource
    {
        return match ($storedEvent->event_class) {
            InvoiceInitialized::class            => new InvoiceInitializedResource($storedEvent),
            InvoiceItemAdded::class              => new InvoiceItemAddedResource($storedEvent),
            RequestInvoiceTax::class             => new RequestInvoiceTaxResource($storedEvent),
            CreateInvoicePdf::class              => new CreateInvoicePdfResource($storedEvent),
            InvoiceItemTaxApplied::class         => new InvoiceItemTaxAppliedResource($storedEvent),
            InvoiceStatusUpdated::class          => new InvoiceStatusUpdatedResource($storedEvent),
            InvoiceChargeRequest::class          => new InvoiceChargeRequestResource($storedEvent),
            InvoiceChargeRequestAttempted::class => new InvoiceChargeRequestAttemptedResource($storedEvent),
            InvoiceChargeRequestSuccess::class   => new InvoiceChargeRequestSuccessResource($storedEvent),
            InvoiceChargeRequestFailed::class    => new InvoiceChargeRequestFailedResource($storedEvent),
            InvoiceUpdated::class                => new InvoiceUpdatedResource($storedEvent),
            InvoiceItemUpdated::class            => new InvoiceItemUpdatedResource($storedEvent),
            InvoiceItemDeleted::class            => new InvoiceItemDeletedResource($storedEvent),
            ActionApprovalRequested::class       => new InvoiceActionRequestEventResource($storedEvent),
            ActionApprovalReviewed::class        => new InvoiceActionRequestReviewedEventResource($storedEvent),
            CreditAdded::class                   => new CreditAddedEventResource($storedEvent),
            IssueInvoiceToCollections::class     => new IssueInvoiceToCollectionsResource($storedEvent),
            UpdateInvoiceCollections::class      => new UpdateInvoiceCollectionsResource($storedEvent),
            InvoiceChargeDisputeCreated::class   => new InvoiceDisputeCreatedResource($storedEvent),
            InvoiceChargeDisputeClosed::class    => new InvoiceDisputeClosedResource($storedEvent),
            default                              => null
        };
    }
}
