<?php

namespace App\Http\Resources\Sales;

use App\Models\RoundRobinParticipant;
use App\Models\User;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin User
 * @mixin RoundRobinParticipant
 */
class AccountAssignmentParticipantResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray(Request $request): array|Arrayable|JsonSerializable
    {
        return [
            'user' => [
                'id' => $this->id,
                'name' => $this->name,
            ],
            'active' => !!$this->round_robin_id,
        ];
    }
}