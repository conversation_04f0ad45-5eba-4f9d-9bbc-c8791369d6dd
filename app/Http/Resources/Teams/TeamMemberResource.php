<?php

namespace App\Http\Resources\Teams;

use App\Models\Teams\TeamMember;
use App\Models\User;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class TeamMemberResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray($request): array
    {
        $user = $this->{TeamMember::RELATION_USER};
        $reportsTo = $this->{TeamMember::RELATION_REPORTS_TO}();

        return [
            'id'            => $this->{TeamMember::FIELD_ID},
            'title'         => $this->{TeamMember::FIELD_TITLE},
            'user_id'       => $user?->{User::FIELD_ID},
            'user_name'     => $user?->{User::FIELD_NAME},
            'reports_to'    => [
                'id'    => $reportsTo?->{User::FIELD_ID},
                'name'  => $reportsTo?->{User::FIELD_NAME},
            ],
        ];
    }
}
