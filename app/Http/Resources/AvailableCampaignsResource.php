<?php

namespace App\Http\Resources;

use App\Enums\ProductAssignment\BudgetProductConfigurationEnum;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\ComputedRejectionStatistic;
use App\Services\Campaigns\Modules\Budget\BudgetUsageService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

/**
 * @mixin CompanyCampaign
 */
class AvailableCampaignsResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'company_id' => $this->company_id,
            'company_name' => $this->company->name,
            'campaign_id' => $this->id,
            'campaign_name' => $this->name,
            'product' => $this->product->name,
            'verified_budget_usage' => $this->getVerifiedBudgetUsage(),
            'rejection_statistics' => $this->getRejections(),
            'has_active_filters' => $this->hasActiveFilters(),
        ];
    }

    /**
     * @return float
     */
    protected function getVerifiedBudgetUsage(): float
    {
        /** @var Budget $verifiedBudget */
        $verifiedBudget = $this->budgetContainer->budgets()
            ->where(Budget::FIELD_PRODUCT_CONFIGURATION, BudgetProductConfigurationEnum::LEAD_VERIFIED)
            ->first();

        if (!$verifiedBudget) {
            return 0.00;
        }

        /** @var BudgetUsageService $budgetUsageService */
        $budgetUsageService = app(BudgetUsageService::class);

        return round(
            $budgetUsageService->getCurrentUsagePercent($verifiedBudget),
            2
        );
    }

    /**
     * @return array
     */
    protected function getRejections(): array
    {
        return $this->company->rejectionStatistics()
            ->where(ComputedRejectionStatistic::FIELD_PRODUCT_ID, $this->product_id)
            ->first()
            ->only([
                ComputedRejectionStatistic::FIELD_CRM_REJECTION_PERCENTAGE,
                ComputedRejectionStatistic::FIELD_MANUAL_REJECTION_PERCENTAGE,
                ComputedRejectionStatistic::FIELD_OVERALL_REJECTION_PERCENTAGE
            ]);
    }
}
