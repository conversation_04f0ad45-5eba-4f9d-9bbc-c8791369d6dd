<?php

namespace App\Http\Resources\Odin;

use App\Repositories\Mailbox\MailboxUserTokenRepository;
use App\Services\ImpersonateService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Session;

/**
 * @mixin \App\Models\User
 */
class UserResource extends JsonResource
{
    const string FIELD_ID                    = "id";
    const string FIELD_NAME                  = "name";
    const string FIELD_EMAIL                 = "email";
    const string FIELD_LEGACY_USER_ID        = "legacy_user_id";
    const string FIELD_SLACK_USERNAME        = "slack_username";
    const string FIELD_ROLES                 = "roles";
    const string FIELD_PHONE                 = "phone";
    const string FIELD_VERIFIED_2FA          = "verified_2fa";
    const string FIELD_FORCE_TWO_FACTOR_AUTH = "force_two_factor_auth";
    const string FIELD_TIMEZONE              = "timezone";
    const string FIELD_CREATED_AT            = 'created_at';
    const string FIELD_UPDATED_AT            = 'updated_at';
    const string FIELD_CREATED_BY_NAME       = 'created_by_name';
    const string FIELD_UPDATED_BY_NAME       = 'updated_by_name';
    const string FIELD_USES_MAILBOX          = 'uses_mailbox';
    const string FIELD_IMPERSONATING         = 'impersonating';
    const string FIELD_MEETING_URL           = 'meeting_url';
    const string FIELD_HAS_ENABLED_MAILBOX   = 'has_enabled_mailbox';

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     *
     * @return array
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     */
    public function toArray($request): array
    {
        /** @var MailboxUserTokenRepository $mailboxUserTokenRepository */
        $mailboxUserTokenRepository = app()->make(MailboxUserTokenRepository::class);
        return [
            self::FIELD_ID                    => $this->id,
            self::FIELD_NAME                  => $this->name,
            self::FIELD_EMAIL                 => $this->email,
            self::FIELD_LEGACY_USER_ID        => $this->legacy_user_id,
            self::FIELD_SLACK_USERNAME        => $this->slack_username,
            self::FIELD_ROLES                 => $this->getRoleNames(),
            self::FIELD_PHONE                 => new BasicPhoneResource($this->primaryPhone()),
            self::FIELD_VERIFIED_2FA          => $this->verified_2fa,
            self::FIELD_FORCE_TWO_FACTOR_AUTH => $this->force_two_factor_auth,
            self::FIELD_USES_MAILBOX          => $this->uses_mailbox,
            self::FIELD_TIMEZONE              => $this->timezone,
            self::FIELD_CREATED_AT            => $this?->created_at,
            self::FIELD_UPDATED_AT            => $this?->updated_at,
            self::FIELD_CREATED_BY_NAME       => $this->createdBy?->name,
            self::FIELD_UPDATED_BY_NAME       => $this->updatedBy?->name,
            self::FIELD_IMPERSONATING         => Session::has(ImpersonateService::SESSION_KEY),
            self::FIELD_MEETING_URL           => $this?->meeting_url,
            self::FIELD_HAS_ENABLED_MAILBOX   => !!$mailboxUserTokenRepository->getLatestUserEmailToken($this->resource)
        ];
    }

    private function getRoleNames()
    {
        return $this->roles->map(fn($role) => $role->name);
    }
}
