<?php

namespace App\Http\Resources\Calendar;

use App\Http\Resources\Odin\BaseJsonResource;
use App\Models\Calendar\Demo;
use Illuminate\Support\Str;

/**
 * @mixin Demo
 */
class DemoResource extends BaseJsonResource
{
    public function toArray($request): array
    {
        return [
            'id'                => $this->{Demo::FIELD_ID},
            'calendar_event_id' => $this->{Demo::FIELD_CALENDAR_EVENT_ID},
            'company'           => [
                'name' => $this->company?->name ?? '-',
                'id'   => $this->company?->id,
            ],
            'status'            => Str::headline($this->demo_status ?? $this->status ?? 'unknown'),
            'note'              => $this->{Demo::FIELD_NOTE},
            'user_id'           => $this->{Demo::FIELD_USER_ID},
            'event'             => new CalendarEventResource($this->calendarEvent),
            'user'              => [
                'id'   => $this->{Demo::FIELD_USER_ID},
                'name' => $this->user?->name,
            ],
            'sdr'               => [
                'id'   => $this->company?->salesDevelopmentRepresentative?->id,
                'name' => $this->company?->salesDevelopmentRepresentative?->name ?? '-',
            ]
        ];
    }
}
