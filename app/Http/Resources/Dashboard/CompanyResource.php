<?php

namespace App\Http\Resources\Dashboard;

use App\Enums\CompanyConsolidatedStatus;
use App\Enums\Odin\GlobalConfigurableFields;
use App\Models\Legacy\EloquentCompany;
use App\Models\Odin\Company;
use App\Models\Odin\CompanyConfiguration;
use App\Models\Odin\Industry;
use App\Models\Odin\IndustryConfiguration;
use App\Models\Odin\IndustryService;
use App\Models\Odin\Product;
use App\Models\Odin\ServiceProduct;
use App\Models\User;
use App\Repositories\Odin\CompanyRepository;
use App\Services\OutreachCadence\TimeZoneHelperService;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use JsonSerializable;

/**
 * @mixin Company
 */
class CompanyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param Request $request
     * @return array|Arrayable|JsonSerializable
     */
    public function toArray($request)
    {
        $businessContact = $this->currentBusinessContact();

        $productMap = Product::query()
            ->select('id', 'name')
            ->get()
            ->mapWithKeys(fn(Product $product) => [$product->id => $product->name]);

        return [
            'id'                                => $this->id,
            'name'                              => $this->name,
            'services'                          => $this->services
                ->filter(fn(IndustryService $industryService) => !!$industryService->show_on_dashboard)
                ->map(fn(IndustryService $service) => [
                    "id"            => $service->id,
                    "name"          => $service->name,
                    "slug"          => $service->slug,
                    "industry"      => strtolower($service->industry->name),
                    "industry_slug" => $service->industry->slug,
                    "industry_id"   => $service->industry_id,
                    "products"      => $service->serviceProducts()
                        ?->pluck(ServiceProduct::FIELD_PRODUCT_ID)
                        ->unique()
                        ->map(fn(int $productId) => isset($productMap[$productId]) ? $productMap[$productId] : null)
                        ->filter(),
                ])->groupBy('industry_slug'),
            'industries'                        => $this->industries->map(fn(Industry $industry) => $industry->slug),
            'industry_configurations'           => $this->industries->mapWithKeys(fn(Industry $industry) => [
                strtolower($industry->{Industry::FIELD_NAME}) => [
                    IndustryConfiguration::FIELD_FUTURE_CAMPAIGNS_ACTIVE => $industry->{Industry::RELATION_INDUSTRY_CONFIGURATION}?->{IndustryConfiguration::FIELD_FUTURE_CAMPAIGNS_ACTIVE} ?? false,
                    IndustryConfiguration::FIELD_CONSUMER_REVIEWS_ACTIVE => $industry->industryConfiguration?->consumer_reviews_active ?? false,
                ]
            ]),
            'status'                            => CompanyConsolidatedStatus::label($this->consolidated_status),
            'has_valid_payment_method'          => true, // TODO: this
            'active_customer'                   => true, // TODO: this
            'admin_locked'                      => $this->admin_locked,
            'admin_approved'                    => $this->admin_approved,
            'enabled_watchdog_compliance_links' => true,
            'business_contact'                  => $businessContact ? new BusinessContactResource($businessContact) : null,
            'lead_purchasing'                   => $this->consolidated_status === CompanyConsolidatedStatus::COMPANY_CONSOLIDATED_STATUS_CAN_RECEIVE_LEADS,
            'logo_url'                          => $this->link_to_logo,
            'description'                       => $this->data->payload[GlobalConfigurableFields::DESCRIPTION->value] ?? "",
            'website_url'                       => $this->website,
            'missed_leads_enabled'              => $this->{Company::RELATION_CONFIGURATION}?->{CompanyConfiguration::FIELD_MISSED_PRODUCTS_ACTIVE} ?? false,
            'appointments_active'               => $this->configuration?->appointments_active ?? false,
            //TODO: remove this when dashboard reviews update is live, use industry_config instead
            'reviews_enabled'                   => false,
            'unrestricted_zip_code_targeting'   => $this->{Company::RELATION_CONFIGURATION}?->{CompanyConfiguration::FIELD_UNRESTRICTED_ZIP_CODE_TARGETING} ?? false,
            'billing_version'                   => $this->getBillingVersion(),
            'bypass_contract_signing'           => $this->bypass_contract_signing,
            'yearsInBusiness'                   => $this->data?->payload['year_started_business'] ?? null,
            'campaign_alert_enabled'            => $this->configuration?->campaign_alert_enabled ?? false,
            'timezone'                          => $this->getCompanyTimezone(),
        ];
    }

    /**
     * @return array
     */
    private function getCompanyTimezone(): array
    {
        $timezone = TimeZoneHelperService::getCompanyTimezone($this->resource);

        return [
            'name'   => $timezone->getDisplayName(true),
            'offset' => $timezone->value,
        ];
    }
}
