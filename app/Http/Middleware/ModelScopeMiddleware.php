<?php

namespace App\Http\Middleware;

use App\Models\Billing\Invoice;
use App\Models\Scopes\ExcludeDeletedScope;
use App\Models\Scopes\ExcludeDraftScope;
use App\Models\Scopes\ExcludeWrittenOffScope;
use Closure;
use Illuminate\Http\Request;

class ModelScopeMiddleware
{
    public function handle(Request $request, Closure $next)
    {
        Invoice::addGlobalScopes([
            new ExcludeDeletedScope(),
            new ExcludeDraftScope(),
            new ExcludeWrittenOffScope()
        ]);
        return $next($request);
    }

}
