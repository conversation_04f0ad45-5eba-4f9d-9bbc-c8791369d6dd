<?php

namespace App\Http\Middleware;

use App\Services\Communication\TwilioCommunicationService;
use Closure;
use Illuminate\Http\Request;
use Illuminate\Validation\UnauthorizedException;

class AuthorizeTwilioWebhook
{
    const TWILIO_HEADER = 'X-Twilio-Signature';

    /**
     * Handle an incoming request.
     *
     * @param \Illuminate\Http\Request $request
     * @param \Closure(\Illuminate\Http\Request): (\Illuminate\Http\Response|\Illuminate\Http\RedirectResponse)  $next
     * @return \Illuminate\Http\Response|\Illuminate\Http\RedirectResponse
     */
    public function handle(Request $request, Closure $next)
    {
        /** @var TwilioCommunicationService $service */
        $service = app()->make(TwilioCommunicationService::class);
        if (!$service->validateIncomingWebhook($request->header(self::TWILIO_HEADER, ""), $_POST)) {
            throw new UnauthorizedException();
        }

        return $next($request);
    }
}
