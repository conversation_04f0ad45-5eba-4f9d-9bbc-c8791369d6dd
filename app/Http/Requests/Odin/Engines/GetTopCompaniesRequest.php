<?php

namespace App\Http\Requests\Odin\Engines;

use App\Enums\Odin\Product;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class GetTopCompaniesRequest extends FormRequest
{
    const string REQUEST_INDUSTRY_SERVICE = 'industry_service';
    const string REQUEST_ZIP_CODE = 'zip_code';
    const string REQUEST_PRODUCT = 'product';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            self::REQUEST_INDUSTRY_SERVICE => 'required|exists:industry_services,slug',
            self::REQUEST_ZIP_CODE => 'required|digits:5',
            self::REQUEST_PRODUCT => [Rule::enum(Product::class)]
        ];
    }
}
