<?php

namespace App\Http\Requests\Odin\API;

use Illuminate\Foundation\Http\FormRequest;

class ComponentDataRequest extends FormRequest
{
    const FIELD_ID                      = 'id';
    const FIELD_COMPONENT_DATA_REQUESTS = 'component_data_requests';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }


    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::FIELD_COMPONENT_DATA_REQUESTS => 'array'
        ];
    }
}
