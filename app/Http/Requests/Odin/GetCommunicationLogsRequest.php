<?php

namespace App\Http\Requests\Odin;

use App\Models\Odin\Address;
use App\Models\Odin\CompanyLocation;
use App\Rules\Phone;
use Illuminate\Foundation\Http\FormRequest;

class GetCommunicationLogsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    public function validator($factory)
    {
        return $factory->make(
            $this->sanitize(), $this->container->call([$this, 'rules']), $this->messages()
        );
    }


    public function sanitize(): array
    {
        $dateTime = $this->input('date_time');

        $this->merge([
            'date_time' => is_string($dateTime) ? json_decode($this->input('date_time'), true) : $dateTime
        ]);
        return $this->all();
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $paginationRules = [
            'page'                     => 'nullable|numeric|min:1',
            'perPage'                  => 'nullable|numeric|min:1|max:100',
        ];

        return [
            ...$paginationRules,

            'communication_description' => 'nullable|string',
            'user_phone'                => 'nullable|string',
            'external_phone'            => 'nullable|string',
            'type'                      => 'nullable|string',

            'communication_type'        => 'nullable|array',

            'date_time.from'            => 'nullable|string',
            'date_time.to'              => 'nullable|string',
        ];
    }
}
