<?php

namespace App\Http\Requests\Advertising;

use App\Enums\Advertising\AdvertisingPlatform;
use Exception;
use Illuminate\Foundation\Http\FormRequest;

class HandleTokenAuthRedirectRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize()
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     * @throws Exception
     */
    public function rules()
    {
        if(!in_array($this->route()->parameter('platform'), array_keys(AdvertisingPlatform::all()), true)) {
            throw new Exception("Invalid platform");
        }

        return [
            //
        ];
    }
}
