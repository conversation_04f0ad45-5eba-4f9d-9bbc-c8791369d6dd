<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class GetContactUrlRequest extends FormRequest
{
    const string RELATION_TYPE = 'relation_type';
    const string RELATION_ID = 'relation_id';
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            self::RELATION_TYPE => ['nullable', 'sometimes', 'string'],
            self::RELATION_ID => ['nullable', 'sometimes', 'integer'],
        ];
    }

}