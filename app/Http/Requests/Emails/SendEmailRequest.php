<?php

namespace App\Http\Requests\Emails;

use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;

class SendEmailRequest extends FormRequest
{
    const string RECIPIENT_ID   = 'emailRecipientId';
    const string EMAIL          = 'email';
    const string SUBJECT        = 'subject';
    const string CONTENT        = 'content';
    const string CC             = 'cc';
    const string BCC            = 'bcc';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            self::EMAIL         => 'required|email',
            self::RECIPIENT_ID  => 'required',
            self::SUBJECT       => 'required',
            self::CONTENT       => 'required',
            self::CC            => 'sometimes|array',
            self::BCC           => 'sometimes|array',
            self::CC . ".*"           => 'sometimes|email',
            self::BCC . ".*"           => 'sometimes|email',
        ];
    }
}
