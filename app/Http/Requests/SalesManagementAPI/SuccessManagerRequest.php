<?php

namespace App\Http\Requests\SalesManagementAPI;

use App\Http\Controllers\API\Sales\Management\SalesManagementAPIController;
use App\Models\SuccessManager;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class SuccessManagerRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(SalesManagementAPIController::MODULE_PERMISSION);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $successManager = $this->route()->parameter('successManager');
        $unique         = Rule::unique(SuccessManager::TABLE)->whereNull(SuccessManager::FIELD_DELETED_AT);

        return
            [
                SuccessManager::FIELD_USER_ID => [
                    'integer',
                    'required',
                    Rule::exists(User::TABLE, User::FIELD_ID),
                    ($successManager instanceof SuccessManager) ? $unique->ignore($successManager->{SuccessManager::FIELD_ID}) : $unique
                ],
                SuccessManager::FIELD_TYPE => [
                    'integer',
                    'required',
                    Rule::in(SuccessManager::TYPES)
                ],
                SuccessManager::FIELD_INCLUDE_IN_SALES_ROUND_ROBIN => [
                    'required',
                    'boolean'
                ]
            ];
    }

}
