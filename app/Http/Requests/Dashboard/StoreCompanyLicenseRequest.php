<?php

namespace App\Http\Requests\Dashboard;

use App\Models\Odin\Industry;
use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class StoreCompanyLicenseRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     * @return bool
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Mutate data before passing to validation
     *
     * @return void
     */
    public function prepareForValidation(): void
    {
        $this->merge([
            'issued_at'     => $this->issued_at ? Carbon::createFromTimestampMs($this->issued_at) : null,
            'expires_at'    => $this->expires_at ? Carbon::createFromTimestampMs($this->expires_at) : null,
            'industry_id'   => $this->industry && !$this->industry_id ? Industry::query()->where(Industry::FIELD_NAME, $this->industry)->first()?->id : null,
        ]);
    }

    /**
     * Get the validation rules that apply to the request.
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        return [
            'id'                => ['numeric', 'nullable', Rule::requiredIf(request()->isMethod('patch'))],
            'name'              => 'string|max:128',
            'type'              => 'string|max:64|nullable',
            'license_number'    => 'string|max:32|nullable',
            'country'           => 'string|max:2|nullable',
            'state'             => 'string|max:3|nullable',
            'issued_at'         => 'date|nullable',
            'expires_at'        => 'date|nullable',
            'url'               => 'string|nullable|max:128',
            'industry'          => 'string|nullable|max:64',
            'industry_id'       => 'numeric|nullable'
        ];
    }
}
