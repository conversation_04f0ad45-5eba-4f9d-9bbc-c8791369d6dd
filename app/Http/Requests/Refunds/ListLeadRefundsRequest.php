<?php

namespace App\Http\Requests\Refunds;

use App\Enums\LeadRefundStatus;
use App\Enums\RoleType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class ListLeadRefundsRequest extends FormRequest
{
    const string FIELD_PAGE              = 'page';
    const string FIELD_PER_PAGE          = 'per_page';
    const string FIELD_COMPANY_ID        = 'company_id';
    const string FIELD_REVIEWED_BY       = 'reviewed_by';
    const string FIELD_REQUESTED_BY      = 'requested_by';
    const string FIELD_STATUS            = 'status';
    const string FIELD_LEAD_ID_LEGACY_ID = 'lead_id_legacy_id';


    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasAnyRole([
            RoleType::LEAD_REFUNDS_REVIEWER->value,
            RoleType::LEAD_REFUNDS_VIEWER->value,
            RoleType::LEAD_REFUNDS_REQUESTER->value,
        ]);
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            self::FIELD_PAGE     => 'sometimes|numeric',
            self::FIELD_PER_PAGE => 'sometimes|numeric|max:100',

            self::FIELD_LEAD_ID_LEGACY_ID => 'sometimes|numeric',
            self::FIELD_COMPANY_ID        => 'sometimes|numeric',
            self::FIELD_REVIEWED_BY       => 'sometimes|numeric',
            self::FIELD_REQUESTED_BY      => 'sometimes|numeric',
            self::FIELD_STATUS            => 'sometimes|' . Rule::in(LeadRefundStatus::cases()),
        ];
    }
}
