<?php

namespace App\Http\Requests\Refunds;

use App\Enums\RoleType;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class GetLeadRefundDataRequest extends FormRequest
{
    const string FIELD_PRODUCT_ASSIGNMENT_IDS = 'product_assignment_ids';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasAnyRole([
            RoleType::LEAD_REFUNDS_REVIEWER->value,
            RoleType::LEAD_REFUNDS_VIEWER->value,
            RoleType::LEAD_REFUNDS_REQUESTER->value,
        ]);
    }

    /**
     * @return array
     */
    public function rules(): array
    {
        return [
            self::FIELD_PRODUCT_ASSIGNMENT_IDS => 'required|array',
        ];
    }
}
