<?php

namespace App\Http\Requests\EmailTemplates;

use App\Enums\EmailTemplateType;
use App\Enums\PermissionType;
use App\Models\EmailTemplate;
use App\Models\Odin\Industry;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class EmailTemplatesShortcodesRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->can(PermissionType::EMAIL_TEMPLATE->value);
    }

    public function rules(): array
    {
        return [
            EmailTemplate::FIELD_TYPE           => [
                'integer',
                'nullable',
                Rule::in(EmailTemplateType::allTypes())
            ],
            EmailTemplate::FIELD_INDUSTRY_ID    => [
                'integer',
                'nullable',
                Rule::exists(Industry::TABLE, Industry::FIELD_ID),
            ],
        ];
    }

}