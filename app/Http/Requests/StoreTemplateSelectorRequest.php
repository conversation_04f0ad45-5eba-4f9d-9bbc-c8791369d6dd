<?php

namespace App\Http\Requests;

use App\Enums\PermissionType;
use App\Enums\TemplateManagement\TemplatePurposeKey;
use App\Enums\TemplateManagement\TemplateRelation;
use App\Models\Odin\Industry;
use App\Models\TemplateSelector;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreTemplateSelectorRequest extends FormRequest
{
    const string TEMPLATE_TYPE = 'template_type';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::SMS_TEMPLATE_EDIT->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            TemplateSelector::FIELD_PURPOSE_KEY => ['required', Rule::enum(TemplatePurposeKey::class)],
            TemplateSelector::FIELD_INDUSTRY_ID => ['nullable', 'exists:' . Industry::TABLE . ',' . Industry::FIELD_ID],
            TemplateSelector::FIELD_TEMPLATE_ID => ['required', 'integer'],
            self::TEMPLATE_TYPE => ['required', 'string']
        ];
    }
}
