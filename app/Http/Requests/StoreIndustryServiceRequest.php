<?php

namespace App\Http\Requests;

use App\Models\Odin\Industry;
use App\Models\Odin\IndustryService;
use App\Models\User;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreIndustryServiceRequest extends FormRequest
{
    const PERMISSION_NAME           = 'industry-management';
    const REQUEST_INDUSTRY_ID       = 'industry';

    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();
        return $user->hasPermissionTo(self::PERMISSION_NAME);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, mixed>
     */
    public function rules(): array
    {
        $industryServiceId = $this->route('industryService');

        $industryId = $this->request->get(self::REQUEST_INDUSTRY_ID);

        $namesInThisIndustry = Industry::query()->find($industryId)
            ?->{Industry::RELATION_SERVICES}
            ?->where(IndustryService::FIELD_ID, '!=', $industryServiceId)
            ?->pluck(Industry::FIELD_NAME);

        $slugs = IndustryService::query()
            ->select(IndustryService::FIELD_SLUG)
            ->where(IndustryService::FIELD_ID, '!=', $industryServiceId)
            ->get()
            ->pluck(Industry::FIELD_SLUG);

        return [
            IndustryService::FIELD_NAME                    => ["required", "string", "max:255", Rule::notIn($namesInThisIndustry)],
            IndustryService::FIELD_SLUG                    => ["required", "string", "alpha_dash", "max:255", Rule::notIn($slugs)],
            IndustryService::FIELD_SHOW_ON_WEBSITE         => ["required", "boolean"],
            IndustryService::FIELD_SHOW_ON_REGISTRATION    => ["required", "boolean"],
            IndustryService::FIELD_SHOW_ON_DASHBOARD       => ["required", "boolean"],
            IndustryService::FIELD_CAMPAIGN_FILTER_ENABLED => ["required", "boolean"],
            self::REQUEST_INDUSTRY_ID                      => ["required", "numeric"],
        ];
    }

    /**
     * Get the error messages for the defined validation rules.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            IndustryService::FIELD_NAME.".not_in"     => 'Service must have a unique Name within this Industry.',
            IndustryService::FIELD_SLUG.".alpha_dash" => 'Service Slug should be skewer-case',
            IndustryService::FIELD_SLUG.".unique"     => 'Service Slug is already in use - must be unique across all industries',
        ];
    }


}
