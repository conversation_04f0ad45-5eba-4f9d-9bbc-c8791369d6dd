<?php

namespace App\Http\Requests\CompanyCampaigns;

use App\Enums\Campaigns\CampaignFilterOperator;
use App\Enums\PermissionType;
use App\Models\Campaigns\CompanyCampaignFilter;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class StoreCampaignFilterRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return Auth::user()->hasPermissionTo(PermissionType::CAMPAIGNS_MANAGE_FILTERS->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule|array|string>
     */
    public function rules(): array
    {
        return [
            CompanyCampaignFilter::FIELD_KEY => 'required|string',
            CompanyCampaignFilter::FIELD_OPERATOR => ['required', Rule::enum(CampaignFilterOperator::class)],
            CompanyCampaignFilter::FIELD_VALUE => 'required'
        ];
    }
}
