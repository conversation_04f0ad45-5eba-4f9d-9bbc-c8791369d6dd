<?php

namespace App\Http\Requests\Billing\InvoiceTemplate;

use App\Enums\Billing\InvoiceTemplateComponent;
use App\Enums\PermissionType;
use App\Models\User;
use App\Services\Billing\InvoiceTemplateRenderer\BaseComponent;
use Exception;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;

class SaveInvoiceTemplateRequest extends FormRequest
{
    const string FIELD_NAME       = 'name';
    const string FIELD_MODEL_TYPE = 'model_type';
    const string FIELD_MODEL_ID   = 'model_id';
    const string FIELD_IS_GLOBAL  = 'is_global';
    const string FIELD_PROPS      = 'props';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::BILLING_INVOICE_TEMPLATES_SAVE->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule>
     * @throws Exception
     */
    public function rules(): array
    {
        return [
            self::FIELD_NAME       => 'required|string',
            self::FIELD_IS_GLOBAL  => 'nullable|boolean',
            self::FIELD_MODEL_TYPE => 'nullable|string',
            self::FIELD_MODEL_ID   => 'nullable|numeric',
            ...$this->loadPropsRules(),
        ];
    }

    /**
     * @return string[]
     * @throws Exception
     */
    private function loadPropsRules(): array
    {
        // TODO - Load the components dynamically
        /** @var BaseComponent $componentClass */
        $componentClass = InvoiceTemplateComponent::INVOICE_FOUNDATION->getClass();

        $rules = $componentClass::getValidationRules();

        $propsRules = [
            self::FIELD_PROPS => 'sometimes',
        ];

        foreach ($rules as $key => $rule) {
            $propsRules[self::FIELD_PROPS . '.' . $key] = $rule;
        }

        return $propsRules;
    }
}
