<?php

namespace App\Http\Requests\Billing\BillingProfilePolicy;

use App\Enums\Billing\BillingPolicyActionType;
use App\Enums\Billing\BillingPolicyEventType;
use App\Enums\PermissionType;
use App\Models\User;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;

class SaveBillingProfilePolicyRequest extends FormRequest
{
    const string FIELD_EVENT  = 'event';
    const string FIELD_ACTION = 'action';

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        /** @var User $user */
        $user = Auth::user();

        return $user->hasPermissionTo(PermissionType::BILLING_PROFILE_POLICIES_SAVE->value);
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, ValidationRule>
     */
    public function rules(): array
    {
        return [
            self::FIELD_EVENT  => 'required|string|' . Rule::in(BillingPolicyEventType::all()),
            self::FIELD_ACTION => 'required|string|' . Rule::in(BillingPolicyActionType::all())
        ];
    }
}
