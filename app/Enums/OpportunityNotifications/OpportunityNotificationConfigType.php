<?php

namespace App\Enums\OpportunityNotifications;

use App\Enums\EmailTemplateType;
use App\Models\MissedProducts\OpportunityNotificationConfig;

enum OpportunityNotificationConfigType: int
{
    case CUSTOM           = 0;
    case BDM_COMPANIES    = 1;
    case CAMPAIGN_SUMMARY = 2;

    /**
     * @return string
     */
    public function getTitle(): string
    {
        return match ($this) {
            self::BDM_COMPANIES    => 'BDM Dashboard',
            self::CAMPAIGN_SUMMARY => 'Campaigns Summary',
            default                => 'Custom'
        };
    }

    /**
     * @return string
     */
    public function getDescription(): string
    {
        return match ($this) {
            self::BDM_COMPANIES    => 'This configuration controls how BDMs can dispatch missed product emails from their dashboard. No emails are sent automatically by this configuration',
            self::CAMPAIGN_SUMMARY => 'These configurations dispatch emails automatically on the assigned days to any qualifying company. The company must appear in the company search preset selected, and satisfy the other criteria in the configuration.',
            default                => 'Custom configurations are currently not processed.',
        };
    }

    /**
     * @return EmailTemplateType
     */
    public function getEmailTemplate(): EmailTemplateType
    {
        return match ($this) {
            self::BDM_COMPANIES    => EmailTemplateType::STATUS_MISSED_PRODUCTS_BDM,
            self::CAMPAIGN_SUMMARY => EmailTemplateType::STATUS_MISSED_PRODUCTS_SUMMARY,
            default                => EmailTemplateType::STATUS_OTHER
        };
    }

    /**
     * @return array
     */
    public function getDisabledProperties(): array
    {
        return match ($this) {
            self::BDM_COMPANIES    => [
                OpportunityNotificationConfig::FIELD_FILTER_PRESET_ID,
                OpportunityNotificationConfig::FIELD_RULE_ID,
                OpportunityNotificationConfig::FIELD_SEND_TIME,
                OpportunityNotificationConfig::FIELD_ATTEMPT_ON_DAYS,
                OpportunityNotificationConfig::FIELD_CAMPAIGN_THRESHOLD,
                OpportunityNotificationConfig::FIELD_ACTIVE,
                OpportunityNotificationConfig::FIELD_EXPIRES_AT,
                OpportunityNotificationConfig::FIELD_MAXIMUM_DAYS_LAST_LEAD,
            ],
            self::CAMPAIGN_SUMMARY => [OpportunityNotificationConfig::FIELD_MAXIMUM_PROMO_PRODUCTS],
            default                => [],
        };
    }

    /**
     * @return array
     */
    public static function getFrontendData(): array
    {
        return array_reduce(self::cases(), function(array $output, OpportunityNotificationConfigType $type) {
            $output[$type->name] = [
                'id'          => $type->value,
                'title'       => $type->getTitle(),
                'description' => $type->getDescription(),
                'template'    => $type->getEmailTemplate()->name,
                'disabled'    => $type->getDisabledProperties(),
            ];
            return $output;
        }, []);
    }

}