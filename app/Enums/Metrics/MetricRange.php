<?php

namespace App\Enums\Metrics;

enum MetricRange: string
{
    case TODAY = 'today';
    case THIS_WEEK = 'this_week';
    case THIS_MONTH = 'this_month';
    case THIS_QUARTER = 'this_quarter';
    case THIS_YEAR = 'this_year';

    public function previous()
    {
        return match ($this) {
            self::TODAY => [
                now()->subDay()->startOfDay()->toDateTimeString(),
                now()->subDay()->endOfDay()->toDateTimeString(),
            ],
            self::THIS_WEEK => [
                now()->subWeek()->startOfWeek()->toDateTimeString(),
                now()->subWeek()->endOfWeek()->toDateTimeString(),
            ],
            self::THIS_MONTH => [
                now()->subMonth()->startOfMonth()->toDateTimeString(),
                now()->subMonth()->endOfMonth()->toDateTimeString(),
            ],
            self::THIS_QUARTER => [
                now()->subQuarter()->startOfQuarter()->toDateTimeString(),
                now()->subQuarter()->endOfQuarter()->toDateTimeString(),
            ],
            self::THIS_YEAR => [
                now()->subYear()->startOfYear()->toDateTimeString(),
                now()->subYear()->endOfYear()->toDateTimeString(),
            ]
        };
    }

    public function current()
    {
        return match ($this) {
            self::TODAY => [
                now()->startOfDay()->toDateTimeString(),
                now()->endOfDay()->toDateTimeString(),
            ],
            self::THIS_WEEK => [
                now()->startOfWeek()->toDateTimeString(),
                now()->endOfWeek()->toDateTimeString(),
            ],
            self::THIS_MONTH => [
                now()->startOfMonth()->toDateTimeString(),
                now()->endOfMonth()->toDateTimeString(),
            ],
            self::THIS_QUARTER => [
                now()->startOfQuarter()->toDateTimeString(),
                now()->endOfQuarter()->toDateTimeString(),
            ],
            self::THIS_YEAR => [
                now()->startOfYear()->toDateTimeString(),
                now()->endOfYear()->toDateTimeString(),
            ]
        };
    }
}
