<?php

namespace App\Enums;

enum Timezone: int
{
    case EASTERN_DST = -4;
    case EASTERN = -5;
    case CENTRAL = -6;
    case MOUNTAIN = -7;
    case PACIFIC = -8;
    case ALASKA = -9;
    case HAWAII = -10;

    /**
     * @param int $offset
     * @param Timezone $default
     * @return Timezone
     */
    public static function tryFromWithDST(int $offset, Timezone $default = Timezone::MOUNTAIN): Timezone
    {
        if (self::isDST())
            $offset++;

        $timezone = Timezone::tryFrom($offset);

        return $timezone ?? $default;
    }

    /**
     * Returns the class for a given action type.
     *
     * @param bool $checkDst
     * @return string
     */
    public function getDisplayName(bool $checkDst = false): string
    {
        $dst = $checkDst && self::isDST();

        return $dst
            ? match ($this) {
                self::EASTERN_DST => "Eastern",
                self::EASTERN     => "Central",
                self::CENTRAL     => "Mountain",
                self::MOUNTAIN    => "Pacific",
                self::PACIFIC     => "Alaska",
                self::ALASKA      => "Hawaii DST",
                self::HAWAII      => "Hawaii Standard",
            }
            : match ($this) {
                self::EASTERN     => "Eastern",
                self::CENTRAL     => "Central",
                self::MOUNTAIN    => "Mountain",
                self::PACIFIC     => "Pacific",
                self::ALASKA      => "Alaska",
                self::HAWAII      => "Hawaii",
                self::EASTERN_DST => 'Eastern Dst',
            };
    }

    /**
     * @return string[]
     */
    public static function displayNames(bool $isDst = false): array
    {
        if($isDst) {
            return [
                self::EASTERN_DST->value => "Eastern",
                self::EASTERN->value => "Central",
                self::CENTRAL->value => "Mountain",
                self::MOUNTAIN->value => "Pacific",
                self::PACIFIC->value => "Alaska",
                self::ALASKA->value => "Hawaii DST",
                self::HAWAII->value => "Hawaii Standard"
            ];
        }
        else {
            return [
                self::EASTERN->value => "Eastern",
                self::CENTRAL->value => "Central",
                self::MOUNTAIN->value => "Mountain",
                self::PACIFIC->value => "Pacific",
                self::ALASKA->value => "Alaska",
                self::HAWAII->value => "Hawaii"
            ];
        }
    }

    /**
     * @param bool $dst
     * @return array
     */
    public static function displayAbbreviations(bool $dst): array
    {
        if($dst) {
            return [
                self::EASTERN_DST->value => "EDT",
                self::EASTERN->value => "CDT",
                self::CENTRAL->value => "MDT",
                self::MOUNTAIN->value => "PDT",
                self::PACIFIC->value => "AKDT",
                self::ALASKA->value => "HDT",
                self::HAWAII->value => "HST"
            ];
        }
        else {
            return [
                self::EASTERN_DST->value => "EDT",
                self::EASTERN->value => "EST",
                self::CENTRAL->value => "CST",
                self::MOUNTAIN->value => "MST",
                self::PACIFIC->value => "PST",
                self::ALASKA->value => "AKST",
                self::HAWAII->value => "HST"
            ];
        }
    }

    public function toLocalFormat(): string
    {
        $name = self::getDisplayName();

        return "$name Time (UTC " . ($this->value >= 0 ? '+' : '-') . sprintf("%02d:00", abs($this->value)) . ")";
    }

    /**
     * @return bool
     */
    public static function isDST(): bool
    {
        $currentTimezone    = date_default_timezone_get();
        $timezoneToCheckDST = 'America/Los_Angeles';

        date_default_timezone_set($timezoneToCheckDST);
        $isActiveDST = date("I");
        date_default_timezone_set($currentTimezone);

        return $isActiveDST;
    }
}
