<?php

namespace App\Enums\ContactIdentification;

use App\Models\Odin\CompanyUser;
use App\Models\Odin\Consumer;
use App\Models\User;
use App\Services\ContactIdentification\ContactIdentifiers\CompanyUserContactIdentifier;
use App\Services\ContactIdentification\ContactIdentifiers\ConsumerContactIdentifier;
use App\Services\ContactIdentification\ContactIdentifiers\UserContactIdentifier;

enum ContactType: string
{
    case CONSUMER         = 'consumer';
    case COMPANY_USER     = 'company_user';
    case USER             = 'user';

    /**
     * @return array
     */
    public static function values(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * @return string
     */
    public function getIdentifierClass(): string
    {
        return match ($this) {
            self::CONSUMER          =>  ConsumerContactIdentifier::class,
            self::COMPANY_USER      =>  CompanyUserContactIdentifier::class,
            self::USER              =>  UserContactIdentifier::class,
        };
    }

    public function getModelClass(): string
    {
        return match ($this) {
            self::CONSUMER          =>  Consumer::class,
            self::COMPANY_USER      =>  CompanyUser::class,
            self::USER              =>  User::class,
        };
    }

    public static function fromModelClass(string $modelClass): self
    {
        return match ($modelClass) {
            Consumer::class         => self::CONSUMER,
            CompanyUser::class      => self::COMPANY_USER,
            User::class             => self::USER,
            default                 => throw new \Exception('Contact type not found for model class ' . $modelClass)
        };
    }
}
