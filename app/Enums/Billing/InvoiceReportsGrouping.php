<?php

namespace App\Enums\Billing;

use App\Models\Billing\InvoiceSnapshot;

enum InvoiceReportsGrouping: string
{
    case INVOICE = 'invoice';
    case COMPANY = 'company';

    /**
     * @return string|null
     */
    public function getGroupColumn(): ?string
    {
        return match ($this) {
            self::INVOICE => InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_INVOICE_ID,
            self::COMPANY => InvoiceSnapshot::TABLE . '.' . InvoiceSnapshot::FIELD_COMPANY_ID,
            default       => null
        };
    }
}
