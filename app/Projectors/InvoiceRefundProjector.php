<?php

namespace App\Projectors;

use App\DTO\Billing\Refund\InvoiceRefundDTO;
use App\Enums\Billing\Refunds\RefundReason;
use App\Enums\InvoiceRefundStatus;
use App\Events\Billing\StoredEvents\Invoice\Refund\InvoiceRefundRequestSuccess;
use App\Events\Billing\StoredEvents\Invoice\Refund\RequestRefund;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoiceRefundCharge;
use App\Repositories\Billing\InvoiceRefund\InvoiceRefundChargeRepository;
use App\Repositories\Billing\InvoiceRefund\InvoiceRefundItemsRepository;
use App\Repositories\Billing\InvoiceRefund\InvoiceRefundRepository;
use Spatie\EventSourcing\EventHandlers\Projectors\Projector;

class InvoiceRefundProjector extends Projector
{
    public function __construct(
        protected InvoiceRefundRepository       $repository,
        protected InvoiceRefundChargeRepository $refundChargeRepository,
        protected InvoiceRefundItemsRepository  $refundItemsRepository
    )
    {
    }

    /**
     * @param RequestRefund $event
     * @return void
     */
    public function onRequestRefund(RequestRefund $event): void
    {
        /** @var Invoice $invoice */
        $invoice = Invoice::findByUuid($event->invoiceUuid);

        $invoiceRefundDTO = InvoiceRefundDTO::fromArray($event->invoiceRefundObject);

        $invoiceRefund = $this->repository->createRefund(
            uuid     : $invoiceRefundDTO->getUuid(),
            invoiceId: $invoice->id,
            total    : $invoiceRefundDTO->getTotal(),
            status   : $invoiceRefundDTO->getStatus() ?? InvoiceRefundStatus::PENDING,
            reason   : $invoiceRefundDTO->getReason() ?? RefundReason::OTHER->value,
        );

        $invoiceRefundItems = $invoiceRefundDTO->getInvoiceRefundItemsCollection();

        foreach ($invoiceRefundItems as $refundItem) {
            $this->refundItemsRepository->createRefundItem(
                invoiceRefundId: $invoiceRefund->id,
                value          : $refundItem->getValue(),
                invoiceItemId  : $refundItem->getInvoiceItemId(),
            );
        }

        $invoiceRefundCharges = $invoiceRefundDTO->getInvoiceRefundChargesCollection();

        foreach ($invoiceRefundCharges as $refundCharge) {
            $this->refundChargeRepository->createRefundCharge(
                uuid               : $refundCharge->getUuid(),
                amount             : $refundCharge->getAmount(),
                requestStatus      : $refundCharge->getRequestStatus() ?? InvoiceRefundStatus::PENDING,
                invoiceRefundId    : $invoiceRefund->id,
                refundedPaymentId  : $refundCharge->getRefundedPaymentId()
            );
        }
    }

    /**
     * @param InvoiceRefundRequestSuccess $event
     * @return void
     */
    public function onInvoiceRefundRequestSuccess(InvoiceRefundRequestSuccess $event): void
    {
        /** @var InvoiceRefundCharge $refund */
        $refund = InvoiceRefundCharge::query()
            ->where(InvoiceRefundCharge::FIELD_UUID, $event->refundUuid)
            ->where(InvoiceRefundCharge::FIELD_REQUEST_STATUS, InvoiceRefundStatus::PENDING)
            ->first();

        $refund->update([
            InvoiceRefundCharge::FIELD_REQUEST_STATUS => InvoiceRefundStatus::REQUESTED
        ]);

        $refund->save();

        $invoiceRefund = $refund->{InvoiceRefundCharge::RELATION_INVOICE_REFUND};

        $this->repository->updateInvoiceRefundStatus($invoiceRefund);

    }
}
