<?php

namespace App\Exceptions;

use App\Contracts\NotifiableExceptionInterface;
use App\Enums\NotificationLinkType;
use App\Enums\NotificationType;
use App\Enums\RoleType;
use App\Models\Notification;
use App\Models\Role;
use Exception;
use http\Exception\InvalidArgumentException;

/**
 * Custom Exception for generating Notifications for a given role
 */
class LeadProcessingException extends \RuntimeException implements NotifiableExceptionInterface
{
    /**
     * @param string $subject
     * @param string $message
     * @param string|null $link
     * @param NotificationLinkType|null $linkType
     * @param array|null $payload
     */
    public function __construct(
        protected string                $subject = "Lead Processing Exception Occurred",
        protected                       $message = "",
        protected ?string               $link = null,
        protected ?NotificationLinkType $linkType = null,
        protected ?array                $payload = null
    )
    {
        parent::__construct($message);
    }

    /**
     * @inheritDoc
     */
    public function getRoleId(): ?int
    {
        return Role::query()->where(Role::FIELD_NAME, '=', RoleType::ALERT_PROCESSOR->value)->first()?->{Role::FIELD_ID};
    }

    /**
     * @inheritDoc
     */
    public function getFromId(): int
    {
        return Notification::FROM_SYSTEM;
    }

    /**
     * @inheritDoc
     */
    public function getSubject(): string
    {
        return $this->subject;
    }

    /**
     * @inheritDoc
     */
    public function getBody(): string
    {
        return $this->message;
    }

    /**
     * @inheritDoc
     */
    public function getType(): NotificationType
    {
        return NotificationType::LEAD;
    }

    /**
     * @inheritDoc
     */
    public function getLink(): ?string
    {
        return $this->link;
    }

    /**
     * @inheritDoc
     */
    public function getLinkType(): ?NotificationLinkType
    {
        return $this->linkType;
    }

    /**
     * @inheritDoc
     */
    public function getPayload(): ?array
    {
        return $this->payload;
    }

}
