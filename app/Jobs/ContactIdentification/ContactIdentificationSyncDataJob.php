<?php

namespace App\Jobs\ContactIdentification;

use App\Enums\ContactIdentification\IdentificationStatus;
use App\Enums\ContactIdentification\SearchableFieldType;
use App\Models\ContactIdentification\IdentifiedContact;
use App\Models\ContactIdentification\PossibleContact;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class ContactIdentificationSyncDataJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    const OPERATION_DELETE  = 'delete';
    const OPERATION_SAVE    = 'save';

    /**
     * Create a new job instance.
     *
     * @return void
     */
    public function __construct(
        protected string $operation,
        protected Model $model,
        protected ?string $identifiedField = null,
        protected ?SearchableFieldType $searchableFieldType = null,
        protected ?string $newValue = null
    )
    {
        $this->queue = 'contact_identification_sync_data';
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws \Exception
     */
    public function handle(): void
    {
        switch ($this->operation) {
            case self::OPERATION_DELETE:
                $this->handleDelete();
                break;
            case self::OPERATION_SAVE:
                $this->handleSave();
                break;
            default:
                throw new \Exception('Unknown operation ' . $this->operation);
        }
    }

    /**
     * @return void
     */
    private function handleDelete(): void
    {
        /** @var PossibleContact[] $possibleContacts */
        $possibleContacts = PossibleContact::query()
            ->where(PossibleContact::FIELD_RELATION_ID, $this->model->id)
            ->where(PossibleContact::FIELD_RELATION_TYPE, $this->model::class)
            ->get();

        foreach ($possibleContacts as $possibleContact) {
            /** @var PossibleContact $identifiedContact */
            $identifiedContact = $possibleContact->{PossibleContact::RELATION_IDENTIFIED_CONTACT};

            if ($identifiedContact->{IdentifiedContact::FIELD_NOMINATED_CONTACT_ID} === $possibleContact->id) {
                $identifiedContact->update([
                    IdentifiedContact::FIELD_NOMINATED_CONTACT_ID => null
                ]);

                $identifiedContact->refresh();
            }

            $possibleContact->delete();

            ContactIdentificationIdentifyJob::dispatchSync($identifiedContact);
        }

    }


    /**
     * @return void
     */
    private function handleSave(): void
    {
        if (empty($this->newValue)) {
            $this->handleDelete();
        } else {
            /** @var PossibleContact $possibleContact */
            $possibleContact = PossibleContact::withTrashed()->where(
                [
                    PossibleContact::FIELD_RELATION_ID   => $this->model->id,
                    PossibleContact::FIELD_RELATION_TYPE => $this->model::class
                ]
            )->first();

            $identifiedContact = IdentifiedContact::query()
                ->where(IdentifiedContact::FIELD_IDENTIFIER_VALUE, $this->newValue)
                ->where(IdentifiedContact::FIELD_IDENTIFIER_FIELD_TYPE, $this->searchableFieldType->value)
                ->first();

            if (!$identifiedContact) {
                return;
            }

            if (!$possibleContact) {
                $possibleContact = PossibleContact::query()->create(
                    [
                        PossibleContact::FIELD_RELATION_ID           => $this->model->id,
                        PossibleContact::FIELD_RELATION_TYPE         => $this->model::class,
                        PossibleContact::FIELD_IDENTIFIED_CONTACT_ID => $identifiedContact->id,
                    ]
                );
            }

            if ($possibleContact->{PossibleContact::FIELD_DELETED_AT}) {
                $possibleContact->restore();
            }


            ContactIdentificationIdentifyJob::dispatchSync($identifiedContact);
        }
    }
}
