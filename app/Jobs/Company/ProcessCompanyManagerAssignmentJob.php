<?php

namespace App\Jobs\Company;

use App\Services\Companies\CompanyManagerAssignmentService;
use App\Services\QueueHelperService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Throwable;

class ProcessCompanyManagerAssignmentJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        $this->onQueue(QueueHelperService::QUEUE_NAME_LONG_RUNNING);
    }

    /**
     * Execute the job.
     *
     * @param CompanyManagerAssignmentService $service
     *
     * @throws Throwable
     */
    public function handle(CompanyManagerAssignmentService $service): void
    {
        $service->handleBdm();
    }
}
