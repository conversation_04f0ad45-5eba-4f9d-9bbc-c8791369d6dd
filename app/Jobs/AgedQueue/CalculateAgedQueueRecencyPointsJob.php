<?php

namespace App\Jobs\AgedQueue;

use App\Services\AgedQueueService;
use App\Services\QueueHelperService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class CalculateAgedQueueRecencyPointsJob implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        $this->onQueue(QueueHelperService::QUEUE_NAME_LONG_RUNNING);
    }

    /**
     * Execute the job.
     */
    public function handle(AgedQueueService $agedQueueService): void
    {
        $agedQueueService->calculateRecencyPoints();
    }
}
