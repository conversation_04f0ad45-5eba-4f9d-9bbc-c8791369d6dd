<?php

namespace App\Jobs\MarketingCampaign;

use App\Contracts\Services\MarketingCampaign\InternalEmailMarketingService;
use App\DTO\EmailService\OutgoingEmailDTO;
use App\Models\EmailTemplate;
use App\Services\QueueHelperService;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;

class SendTestMarketingEmail implements ShouldQueue
{
    use Queueable;

    public function __construct(
        protected OutgoingEmailDTO $target,
        protected EmailTemplate $emailTemplate,
        protected string $fromName,
        protected string $fromEmail,
    )
    {
        $this->onQueue(QueueHelperService::QUEUE_NAME_MARKETING);
    }

    public function handle(InternalEmailMarketingService $emailMarketingService): void
    {
        $emailMarketingService->sendTestEmail(
            target: $this->target,
            template: $this->emailTemplate,
            fromName: $this->fromName,
            fromEmail: $this->fromEmail,
        );
    }

}
