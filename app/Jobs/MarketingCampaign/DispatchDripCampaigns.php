<?php

namespace App\Jobs\MarketingCampaign;

use App\Enums\Log\LogLevel;
use App\Enums\MarketingCampaigns\MarketingCampaignStatus;
use App\Enums\MarketingCampaigns\MarketingCampaignType;
use App\Enums\MarketingCampaigns\MarketingLogType;
use App\Models\MarketingCampaign;
use App\Services\MarketingCampaign\MarketingLogService;
use App\Services\QueueHelperService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class DispatchDripCampaigns implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct()
    {
        $this->onQueue(QueueHelperService::QUEUE_NAME_MARKETING);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $dripCampaigns = MarketingCampaign::query()
            ->whereIn(MarketingCampaign::FIELD_TYPE, [MarketingCampaignType::DRIP_EMAIL, MarketingCampaignType::DRIP_SMS])
            ->whereIn(MarketingCampaign::FIELD_STATUS, [MarketingCampaignStatus::ACTIVE, MarketingCampaignStatus::DRAFT, MarketingCampaignStatus::SENT])
            ->where(MarketingCampaign::FIELD_SENT_AT, '<=', now())
            ->get();

        /**
         * @var MarketingCampaign $dripCampaign
         */
        foreach ($dripCampaigns as $dripCampaign) {
            MarketingLogService::log(
                message: "Dispatching Drip Campaign #{$dripCampaign->id}",
                namespace: MarketingLogType::MARKETING_DRIP_CAMPAIGN,
                level: LogLevel::INFO,
                context: [
                    'marketing_campaign_id' => $dripCampaign->id,
                ],
                relations: [$dripCampaign]
            );

            ProcessDripCampaign::dispatch($dripCampaign->id);
        };
    }
}
