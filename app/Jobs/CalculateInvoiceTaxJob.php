<?php

namespace App\Jobs;

use App\Aggregates\InvoiceAggregateRoot;
use App\Contracts\Services\TaxServiceApiContract;
use App\DTO\Billing\AddressData;
use App\DTO\Billing\InvoiceItemDTO;
use App\DTO\Billing\InvoiceTaxData;
use App\Services\Billing\InvoiceService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Throwable;

class CalculateInvoiceTaxJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * @param AddressData $addressData
     * @param array<InvoiceItemDTO> $invoiceItemDTOArray
     * @param string $invoiceUuid
     * @param int|null $userId
     */
    public function __construct(
        public AddressData $addressData,
        public array $invoiceItemDTOArray,
        public string $invoiceUuid,
        public ?int $userId = null
    )
    {
    }

    /**
     * Execute the job.
     */
    public function handle(TaxServiceApiContract $taxServiceContract, InvoiceService $invoiceService): void
    {
        /** @var InvoiceTaxData $invoiceTaxData */
        $invoiceTaxData = $taxServiceContract->getInvoiceTax($this->addressData, $this->invoiceItemDTOArray, $this->invoiceUuid);
        $invoiceService->applyTaxToInvoice($invoiceTaxData, $this->userId);
    }

    /**
     * @param Throwable|null $exception
     * @return void
     */
    public function failed(?Throwable $exception): void
    {
        logger()->channel('billing')->error("Calculate Invoice Tax Request Failed: " . $exception->getMessage());
        InvoiceAggregateRoot::retrieve($this->invoiceUuid)
            ->calculateInvoiceTaxFailed(
                invoiceUuid: $this->invoiceUuid,
                authorId   : $this->userId,
                message    : $exception->getMessage()
            );
    }
}
