<?php

namespace App\Jobs\Affiliate;

use App\Enums\Affiliate\PayoutStrategyTypeEnum;
use App\Jobs\UpsertInitialAffiliatePayout;
use App\Models\Affiliates\Affiliate;
use App\Models\Affiliates\PayoutStrategy;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\ConsumerProductAffiliateRecord;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class GenerateInitialAffiliatePayouts implements ShouldQueue
{
    use Queueable;
    const int CHUNK_SIZE = 10000;

    /**
     * Create a new job instance.
     */
    public function __construct(
        protected int $affiliateId
    )
    {
        $this->onQueue(config('queue.named_queues.long_running'));
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $affiliate = Affiliate::query()->findOrFail($this->affiliateId);

        $strategy = $affiliate->strategy;

        if (empty($strategy)) {
            logger()->info("creating default strategy for affiliate id: $this->affiliateId");

            $strategy = PayoutStrategy::query()->create([
                PayoutStrategy::FIELD_AFFILIATE_ID => $this->affiliateId,
                PayoutStrategy::FIELD_TYPE => PayoutStrategyTypeEnum::REVENUE_PERCENTAGE,
                PayoutStrategy::FIELD_VALUE => PayoutStrategy::DEFAULT_REVENUE_PERCENTAGE,
                PayoutStrategy::FIELD_ACTIVE_FROM => now(),
                PayoutStrategy::FIELD_CREATED_AT => now(),
                PayoutStrategy::FIELD_UPDATED_AT => now(),
            ]);
        }

        ConsumerProduct::query()
            ->select(ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID)
            ->join(ConsumerProductAffiliateRecord::TABLE,
            ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_CONSUMER_PRODUCT_AFFILIATE_RECORD_ID,
            '=',
            ConsumerProductAFfiliateRecord::TABLE .'.'. ConsumerProductAffiliateRecord::FIELD_ID,
        )->where(ConsumerProductAffiliateRecord::TABLE .'.'.ConsumerProductAffiliateRecord::FIELD_AFFILIATE_ID, $this->affiliateId)
            ->chunk(self::CHUNK_SIZE,
                function ($consumerProducts) use ($strategy) {
                $count = $consumerProducts->count();
                logger()->info("dispatching job to recalculate $count consumer product payouts");

                UpsertInitialAffiliatePayout::dispatch(
                    $strategy,
                    $consumerProducts->pluck('id')->toArray()
                );
            });
    }
}
