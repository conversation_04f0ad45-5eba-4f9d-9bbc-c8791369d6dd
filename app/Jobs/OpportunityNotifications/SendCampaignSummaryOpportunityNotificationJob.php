<?php

namespace App\Jobs\OpportunityNotifications;

use App\Services\OpportunityNotifications\OpportunityNotificationService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldBeUnique;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SendCampaignSummaryOpportunityNotificationJob implements ShouldQueue, ShouldBeUnique
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    const int DEFAULT_NOTIFICATION_COOLDOWN_IN_DAYS = 3;

    /**
     * Create a new job instance.
     * @return void
     */
    public function __construct(
        protected array $recipientContacts,
        protected int $companyId,
        protected int $configId,
        protected ?bool $sendTest = false,
    )
    {
        //TODO: previous queue does not exist - this will need to be updated
        $this->onQueue('default');
    }

    /**
     * Only one job should ever be queued for a company
     * @return string
     */
    public function uniqueId(): string
    {
        return $this->companyId;
    }

    /**
     * Execute the job.
     * @param OpportunityNotificationService $opportunityNotificationService
     * @return void
     */
    public function handle(OpportunityNotificationService $opportunityNotificationService): void
    {
        if ($this->recipientContacts)
            $opportunityNotificationService->sendNotification($this->recipientContacts, $this->companyId, $this->configId, $this->sendTest);
    }
}
