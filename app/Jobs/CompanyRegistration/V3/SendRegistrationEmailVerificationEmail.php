<?php

namespace App\Jobs\CompanyRegistration\V3;

use App\Mail\CompanyRegistration\V3\RegistrationEmailVerificationEmail;
use App\Models\Odin\Website;
use App\Models\Prospects\NewBuyerProspect;
use App\Services\CompanyRegistration\CompanyRegistrationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;
use Illuminate\Support\Facades\Mail;

class SendRegistrationEmailVerificationEmail implements ShouldQueue
{
    use Queueable;

    /**
     * Create a new job instance.
     */
    public function __construct(protected NewBuyerProspect $newBuyerProspect) {}

    /**
     * Execute the job.
     *
     * @param CompanyRegistrationService $registrationService
     */
    public function handle(CompanyRegistrationService $registrationService): void
    {
        $url = $this->newBuyerProspect->source_data[NewBuyerProspect::SOURCE_KEY_REGISTRATION_URL];
        $domain = $registrationService->getDomain($url);

        Mail::to($this->newBuyerProspect->source_data[NewBuyerProspect::SOURCE_KEY_EMAIL])
            ->send(new RegistrationEmailVerificationEmail(
                fromEmail: "noreply@$domain",
                data: [
                    'verificationLink' => "$url?token={$this->newBuyerProspect->reference}",
                    'domain' => $domain,
                    'firstName' => $this->newBuyerProspect->source_data[NewBuyerProspect::SOURCE_KEY_FIRST_NAME]
                ],
                fromName: $this->getSiteNameFromDomain($domain)
            ));
    }

    /**
     * @param string $domain
     *
     * @return string|null
     */
    protected function getSiteNameFromDomain(string $domain): ?string
    {
        return Website::query()->whereLike(Website::FIELD_URL, "%$domain%")->first()?->{Website::FIELD_NAME};
    }
}
