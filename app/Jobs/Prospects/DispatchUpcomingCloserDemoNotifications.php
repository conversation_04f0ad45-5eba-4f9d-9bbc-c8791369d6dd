<?php

namespace App\Jobs\Prospects;

use App\Models\Prospects\CloserDemo;
use App\Services\Prospects\CloserDemoNotificationService;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Queue\Queueable;

class DispatchUpcomingCloserDemoNotifications implements ShouldQueue
{
    use Queueable;

    public function __construct() {}

    public function handle(): void
    {
        $demos = CloserDemoNotificationService::getUpcomingDemos();
        foreach ($demos as $demo) {
            $delay = CloserDemoNotificationService::getDelay($demo);
            SendDirectSlackMessage::dispatch(CloserDemoNotificationService::getUpcomingDemoMessage($demo), $demo->user_id)->delay($delay);
            $demo->update([CloserDemo::FIELD_UPCOMING_NOTIFICATION_SENT => true]);
        }
    }
}
