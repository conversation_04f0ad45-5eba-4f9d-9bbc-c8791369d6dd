<?php

namespace App\Jobs;

use App\Enums\DynamicPriorityTypes;
use App\Models\Legacy\EloquentAddress;
use App\Models\Legacy\EloquentQuote;
use App\Models\Sales\Task;
use App\Repositories\Legacy\QuoteRepository;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Collection;

class DynamicPriorityJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $tries = 1; // already scheduled hourly - retry serves no purpose

    public function __construct()
    {
        $this->queue = 'long_running_single_attempt';
    }

    /**
     * Execute the job.
     *
     * @return void
     * @throws BindingResolutionException
     */
    public function handle(): void
    {
        //process priority for the tasks that are less than a week old and dynamic priority enabled
        Task::query()
            ->select([
                Task::FIELD_ID,
                Task::FIELD_DYNAMIC_PRIORITY,
                Task::FIELD_COMPLETED,
                Task::CREATED_AT,
                Task::FIELD_RUNNING_WORKFLOW_ID,
                Task::FIELD_DYNAMIC_PRIORITY_TYPE,
                Task::FIELD_PRIORITY
            ])
            ->where(Task::FIELD_DYNAMIC_PRIORITY, true)
            ->where(Task::FIELD_COMPLETED, false)
            ->where(Task::CREATED_AT, '>', now()->subWeek())
            ->with(Task::RELATION_RUNNING_WORKFLOW)
            ->chunk(100, function (Collection $tasks) {

                $allLeadIds = collect();
                foreach ($tasks as $task) {
                    $allLeadIds[] = $task->runningWorkflow?->payload->event->get('lead_id');
                }
                $allLeadIds = $allLeadIds->filter()->toArray();
                $quoteRepository = app(QuoteRepository::class);
                $allLeads = $quoteRepository->findByIds($allLeadIds);
                $allLeads->load(EloquentQuote::RELATION_ADDRESS, EloquentQuote::RELATION_ADDRESS.'.'.EloquentAddress::RELATION_MAP_LOCATIONS);
                $allLeadZipCodes = $allLeads->pluck(EloquentQuote::RELATION_ADDRESS.'.'.EloquentAddress::ZIP_CODE)->filter()->toArray();
                $unsoldLeadsByCounty = $quoteRepository->getUnsoldLeadCountByCounties($allLeadZipCodes, now()->subDays(config('app.dynamic_priority_calculation.unsold_days')));

                foreach ($tasks as $task) {
                    $leadId = $task->runningWorkflow?->payload->event->get('lead_id');
                    $lead = $leadId ? $allLeads->where(EloquentQuote::ID, $leadId)->first() : null;
                    $unsoldLeadCount = 0;
                    if($lead){
                        $stateCountyKey = $lead->address->location()->state_abbr.':'.$lead->address->location()->county_key;
                        if($lead->solar_lead){
                            $unsoldLeadCount = $unsoldLeadsByCounty[$stateCountyKey]['solar'];
                        }elseif($lead->roofing_lead){
                            $unsoldLeadCount = $unsoldLeadsByCounty[$stateCountyKey]['roofing'];
                        }
                    }
                    $this->processDynamicPriority($task, $lead, $unsoldLeadCount);
                }
            });
    }

    /**
     * @param Task $task
     * @param EloquentQuote|null $lead
     * @param int $unsoldLeadCount
     * @return void
     * @throws BindingResolutionException
     */
    protected function processDynamicPriority(Task $task, ?EloquentQuote $lead, int $unsoldLeadCount): void
    {
        if (!$task->dynamic_priority_type) return;

        $dynamicType = DynamicPriorityTypes::tryFrom($task->dynamic_priority_type);

        if (!$dynamicType) return;

        $priority = $dynamicType->getService()->calculate($task, $lead, $unsoldLeadCount);

        if ($priority !== null) {
            $task->priority = $priority;
            $task->save();
        }
    }
}
