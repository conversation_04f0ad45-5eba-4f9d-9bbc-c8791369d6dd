<?php

namespace App\Console\Commands;

use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\EloquentQuoteLog;
use App\Models\Odin\CompanyUser;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ProductRejection;
use App\Services\DatabaseHelperService;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Throwable;

class FixImproperlyMigratedProductRejections extends Command
{

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'fix:improperly-migrated-product-rejections';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Fixes a one off when missing product assignments were migrated';

    /**
     * Execute the console command.
     *
     * @return int
     * @throws Throwable
     */
    public function handle()
    {
        $productAssignmentIds = $this->getProductAssignmentIds();

        $this->deleteBadProductRejections();

        $this->migrateRejections($productAssignmentIds);

        return 0;
    }

    /**
     * @return array
     */
    private function getProductAssignmentIds(): array
    {
        return ProductRejection::query()
            ->select(ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID)
            ->whereNull(ProductRejection::CREATED_AT)
            ->distinct()
            ->pluck(ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID)
            ->toArray();
    }

    /**
     * @return void
     */
    private function deleteBadProductRejections(): void
    {
        $this->info('Deleting bad records');

        ProductRejection::query()
            ->select(ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID)
            ->whereNull(ProductRejection::CREATED_AT)
            ->forceDelete();
    }


    /**
     * @param array $productAssignmentIds
     * @return void
     */
    private function migrateRejections(array $productAssignmentIds): void
    {
        $this->info('Migrating records');

        $bar = $this->output->createProgressBar(count($productAssignmentIds));
        $bar->start();

        ProductAssignment::query()
            ->select([
                ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_ID,
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::CHARGE_STATUS,
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::REJECT_NOTES,
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::QUOTE_ID,
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::TIMESTAMP_REJECTED_AT,
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::COMPANY_ID . ' as legacy_company_id'
            ])
            ->join(
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE,
                DatabaseHelperService::readOnlyDatabase().'.'.EloquentQuoteCompany::TABLE.'.'.EloquentQuoteCompany::ID,
                '=',
                ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_LEGACY_ID
            )
            ->whereIntegerInRaw(ProductAssignment::FIELD_ID, $productAssignmentIds)
            ->chunk(500, function($productAssignments) use ($bar) {
                $data = [];

                foreach($productAssignments as $productAssignment) {
                    if ($productAssignment->{EloquentQuoteCompany::CHARGE_STATUS} === EloquentQuoteCompany::VALUE_CHARGE_STATUS_REJECTED) {
                        $data[] = [
                            ProductRejection::FIELD_PRODUCT_ASSIGNMENT_ID => $productAssignment->{ProductAssignment::FIELD_ID},
                            ProductRejection::FIELD_REASON                => $productAssignment->{EloquentQuoteCompany::REJECT_NOTES},
                            ProductRejection::CREATED_AT                  => Carbon::createFromTimestamp($productAssignment->{EloquentQuoteCompany::TIMESTAMP_REJECTED_AT}),
                            ProductRejection::UPDATED_AT                  => Carbon::createFromTimestamp($productAssignment->{EloquentQuoteCompany::TIMESTAMP_REJECTED_AT}),
                            ProductRejection::FIELD_COMPANY_USER_ID       => $this->retrieveCompanyUserId(
                                $productAssignment->{EloquentQuoteCompany::QUOTE_ID},
                                $productAssignment->{'legacy_company_id'}
                            )
                        ];
                    }

                    $bar->advance();
                }

                ProductRejection::insert($data);
            });

        $bar->finish();
    }

    /**
     * @param int $quoteId
     * @param int $legacyCompanyId
     * @return int
     */
    protected function retrieveCompanyUserId(int $quoteId, int $legacyCompanyId): int
    {
        $rejectLog = EloquentQuoteLog::query()
            ->where(EloquentQuoteLog::QUOTE_ID, $quoteId)
            ->where(EloquentQuoteLog::DATA, 'LIKE', "%($legacyCompanyId) marked as rejected%")
            ->orderBy(EloquentQuoteLog::TIMESTAMP_ADDED, 'DESC')
            ->first();

        if($rejectLog) {
            return CompanyUser::query()
                ->where(CompanyUser::FIELD_LEGACY_ID, $rejectLog->{EloquentQuoteLog::ADDED_BY_USER_ID} ?? 0)
                ->where(CompanyUser::FIELD_IS_CONTACT, '!=', true)
                ->first()?->{CompanyUser::FIELD_ID} ?? 0;
        }

        return 0;
    }
}
