<?php

namespace App\Console\Commands;

use App\Jobs\Billing\CreateInvoicePdfJob;
use App\Models\Billing\Invoice;
use Exception;
use Illuminate\Console\Command;

class RegenerateInvoicePdf extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'regenerate-invoice-pdf {--invoice-id=}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Regenerate pdf for a given invoice.';

    /**
     * @return void
     */
    public function handle(): void
    {
        $invoiceIdOption = $this->option('invoice-id');

        if (empty($invoiceIdOption)) {
            $this->error('Invoice id is required.');
            return;
        }

        $invoiceIds = collect(explode(',', $invoiceIdOption))
            ->map(fn(string $item) => trim($item))
            ->filter()
            ->unique();

        foreach ($invoiceIds as $idx => $invoiceId) {
            $this->info("Dispatching regenerate pdf job for invoice id $invoiceId");

            try {
                $invoice = Invoice::query()->findOrFail($invoiceId);

                $delayInSeconds = $idx * 1000;

                $this->info("Delay $delayInSeconds");

                CreateInvoicePdfJob::dispatch(
                    $invoice,
                    false,
                    false
                )->delay(now()->addSeconds($idx));
            } catch (Exception $exception) {
                $this->error($exception);
            }
        }

        $this->info('Jobs dispatched');
    }
}
