<?php

namespace App\Console\Commands;

use App\Enums\Billing\InvoicePaymentChargeStatus;
use App\Enums\Billing\InvoicePaymentStatus;
use App\Enums\Billing\InvoiceTransactionType;
use App\Models\Billing\BillingProfile;
use App\Models\Billing\Invoice;
use App\Models\Billing\InvoicePayment;
use App\Models\Billing\InvoicePaymentCharge;
use App\Models\Billing\InvoiceTransaction;
use App\Repositories\Billing\CompanyPaymentMethodRepository;
use Illuminate\Console\Command;
use Illuminate\Support\Str;

class MigrateBillingProfilesToCompanyPaymentMethods extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'migrate-billing-profiles-to-company-payment-methods';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Migrate billing profiles to company payment methods';

    /**
     * Create a new command instance.
     */
    public function __construct(
        protected CompanyPaymentMethodRepository $companyPaymentMethodRepository,
    )
    {
        parent::__construct();
    }

    /**
     * @return void
     */
    public function handle(): void
    {
        $billingProfiles = BillingProfile::query()->get();

        foreach ($billingProfiles as $billingProfile) {
            $paymentMethod = $this->companyPaymentMethodRepository->create(
                companyId                      : $billingProfile->{BillingProfile::FIELD_COMPANY_ID},
                type                           : $billingProfile->{BillingProfile::FIELD_PAYMENT_METHOD},
                addedByType                    : $billingProfile->{BillingProfile::FIELD_CREATED_BY_ID} ? \App\Models\Odin\CompanyUser::class : 'system',
                isDefault                      : $billingProfile->{BillingProfile::FIELD_DEFAULT},
                addedById                      : $billingProfile->{BillingProfile::FIELD_CREATED_BY_ID},
                paymentGatewayPaymentMethodCode: $billingProfile->{BillingProfile::FIELD_PAYMENT_GATEWAY_PAYMENT_METHOD_CODE},
                paymentGatewayClientCode       : $billingProfile->{BillingProfile::FIELD_PAYMENT_GATEWAY_CLIENT_CODE},
            );

            $invoiceUuids = Invoice::query()
                ->where(Invoice::FIELD_BILLING_PROFILE_ID, $billingProfile->id)
                ->get()
                ->pluck(Invoice::FIELD_UUID);

            $invoiceTransactions = InvoiceTransaction::query()
                ->where(InvoiceTransaction::FIELD_TYPE, InvoiceTransactionType::PAYMENT->value)
                ->whereIn(InvoiceTransaction::FIELD_INVOICE_UUID, $invoiceUuids)
                ->get();

            foreach ($invoiceTransactions as $transaction) {
                $invoice = Invoice::findByUuid($transaction->{InvoiceTransaction::FIELD_INVOICE_UUID});

                if (!$invoice) {
                    $this->info('Invoice not found for transaction id ' . $transaction->id);
                    continue;
                }

                $invoicePayment = InvoicePayment::query()->create([
                    InvoicePayment::FIELD_UUID                            => Str::uuid()->toString(),
                    InvoicePayment::FIELD_INVOICE_ID                      => $invoice->id,
                    InvoicePayment::FIELD_TOTAL                           => $transaction->{InvoiceTransaction::FIELD_AMOUNT},
                    InvoicePayment::FIELD_STATUS                          => InvoicePaymentStatus::CHARGED->value,
                    InvoicePayment::FIELD_AUTHOR_ID                       => '',
                    InvoicePayment::FIELD_AUTHOR_TYPE                     => 'system',
                    InvoicePayment::FIELD_CREATED_AT                      => $transaction->created_at,
                    InvoicePayment::FIELD_UPDATED_AT                      => $transaction->created_at,
                    InvoicePayment::FIELD_CHARGED_AT                      => $transaction->created_at,
                    InvoicePayment::FIELD_REQUESTED_AT                    => $transaction->created_at,
                    InvoicePayment::FIELD_NEXT_ATTEMPT_AT                 => null,
                    InvoicePayment::FIELD_MAX_ATTEMPTS_PER_PAYMENT_METHOD => 3,
                    InvoicePayment::FIELD_ATTEMPT_NUMBER                  => 1,
                ]);

                InvoicePaymentCharge::query()->create([
                    InvoicePaymentCharge::FIELD_UUID               => Str::uuid()->toString(),
                    InvoicePaymentCharge::FIELD_INVOICE_PAYMENT_ID => $invoicePayment->id,
                    InvoicePaymentCharge::FIELD_PAYMENT_METHOD_ID  => $paymentMethod->id,
                    InvoicePaymentCharge::FIELD_TRANSACTION_UUID   => $transaction->{InvoiceTransaction::FIELD_UUID},
                    InvoicePaymentCharge::FIELD_TOTAL              => $transaction->{InvoiceTransaction::FIELD_AMOUNT},
                    InvoicePaymentCharge::FIELD_STATUS             => InvoicePaymentChargeStatus::REQUESTED->value,
                    InvoicePaymentCharge::FIELD_ERROR_MESSAGE      => null,
                    InvoicePaymentCharge::FIELD_CREATED_AT         => $transaction->created_at,
                    InvoicePaymentCharge::FIELD_UPDATED_AT         => $transaction->created_at,
                ]);
            }
        }
    }
}
