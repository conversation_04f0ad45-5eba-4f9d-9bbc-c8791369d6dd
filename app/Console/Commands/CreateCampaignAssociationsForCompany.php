<?php

namespace App\Console\Commands;

use App\Models\Legacy\LeadCampaign;
use App\Models\Odin\Product;
use App\Models\Odin\ProductCampaign;
use App\Models\Odin\Company;
use App\Models\Odin\IndustryService;
use Illuminate\Console\Command;

class CreateCampaignAssociationsForCompany extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'create:company-campaign-associations {company}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Creates associations for existing campaigns for one Company.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        // Single company only - use create:campaign-associations to process all Companies

        /** @var Company $company */
        $company = Company::query()->findOrFail($this->argument('company'));

        /** @var IndustryService $service */
        $service = $company->services()->firstOrFail();

        $campaigns = $company->legacyCompany->campaigns()->where(LeadCampaign::DELETED_AT, null)->get();

        /** @var Product $product */
        $product = Product::query()->where(Product::FIELD_NAME, \App\Enums\Odin\Product::LEAD)->firstOrFail();

        /** @var LeadCampaign $campaign */
        foreach($campaigns as $campaign) {
            ProductCampaign::query()->updateOrCreate(
                [ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID => $campaign->id],
                [
                    ProductCampaign::FIELD_PARENT_LEGACY_LEAD_CAMPAIGN_ID => $campaign->id,
                    ProductCampaign::FIELD_INDUSTRY_SERVICE_ID => $service->id,
                    ProductCampaign::FIELD_PRODUCT_ID => $product->id,
                    ProductCampaign::FIELD_COMPANY_ID => $company->id
                ]
            );
        }

        return 0;
    }
}
