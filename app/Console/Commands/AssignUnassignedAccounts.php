<?php

namespace App\Console\Commands;

use App\Enums\RoundRobinType;
use App\Models\AccountManagerClient;
use App\Models\Legacy\EloquentCompany;
use App\Repositories\CompanyAccountManagerRepository;
use App\Services\DatabaseHelperService;
use App\Services\RoundRobins\RoundRobinService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;

class AssignUnassignedAccounts extends Command
{
    const STATUSES = [
        EloquentCompany::STATUS_ACTIVE,
        EloquentCompany::STATUS_PRESALES,
        EloquentCompany::STATUS_REGISTERING,
        EloquentCompany::STATUS_PENDING,
    ];

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'assign:unassigned-accounts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handles assigning un-assigned accounts.';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $count = $this->getBaseQuery()->count();
        $answer = $this->ask("{$count} companies will be assigned an account manager.\n\n*** Warning! This command script uses legacy ID's and may update the wrong companies. ***\nContinue?", "n");

        if (in_array(strtolower($answer), ["y", "yes"]))
            $this->dispatchAllocation($count);
        else
            $this->line("Cancelling allocation. {$answer}");

        return 0;
    }

    protected function dispatchAllocation(int $count = 0): void
    {
        /** @var RoundRobinService $roundRobinService */
        $roundRobinService = app()->make(RoundRobinService::class);
        /** @var CompanyAccountManagerRepository $repository */
        $repository = app()->make(CompanyAccountManagerRepository::class);

        $this->line("Starting allocation.");
        $progress = $this->output->createProgressBar($count);
        $progress->start();

        $this->getBaseQuery()->chunk(100, function ($companies) use (&$progress, $roundRobinService, $repository) {
            /** @var EloquentCompany $company */
            foreach ($companies as $company)
                $repository->assignAccountManager(
                    $company->companyid,
                    $roundRobinService->executeRoundRobin(
                        RoundRobinType::ACCOUNT_MANAGER,
                        true
                    )
                );

            $progress->advance(count($companies));
        });

        $progress->finish();
    }

    /**
     * Returns the base query for returning companies to assign.
     *
     * @return Builder
     */
    protected function getBaseQuery(): Builder
    {
        return EloquentCompany::query()
            ->whereIn(EloquentCompany::TABLE.'.'.EloquentCompany::STATUS, self::STATUSES)
            ->leftJoin(
                DatabaseHelperService::database() . '.' . AccountManagerClient::TABLE,
                DatabaseHelperService::database() . '.' . AccountManagerClient::TABLE . '.' . AccountManagerClient::FIELD_COMPANY_REFERENCE,
                "=",
                EloquentCompany::TABLE . '.' . EloquentCompany::REFERENCE
            )
            ->where(DatabaseHelperService::database() . '.' . AccountManagerClient::TABLE.'.'.AccountManagerClient::FIELD_ID, null)
            ->where(fn(Builder $query) => $query->where(EloquentCompany::ACCOUNT_MANAGER_ID, 0)
                ->orWhere(EloquentCompany::ACCOUNT_MANAGER_ID, null));
    }
}
