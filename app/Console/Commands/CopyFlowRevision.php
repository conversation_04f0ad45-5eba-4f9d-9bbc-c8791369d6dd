<?php

namespace App\Console\Commands;

use App\Models\Firestore\Flows\Revision;
use App\Repositories\Flows\v2\RevisionRepository;
use App\Repositories\Flows\RevisionRepository as LegacyRevisionRepository;
use App\Services\GoogleFirestoreService;
use Google\Cloud\Firestore\DocumentReference;
use Google\Cloud\Firestore\DocumentSnapshot;
use Illuminate\Console\Command;
use Ramsey\Uuid\Uuid;

class CopyFlowRevision extends Command
{
    /**
     * Copy a Flow Revision between environments
     *  - production environment can be source, but not target of copy
     *  - supply from=<environment> and to=<environment>
     *  - supply --flow=<flowId>
     *  - supply --revision=<revisionId>
     *  - optionally supply
     *    --legacy to copy from and to a legacy Revision
     *    --overwrite to allow overwriting the document if it exists in the target
     *
     * For copying v1 (Legacy) Revisions:
     *  - must supply --revision=<revisionId>
     *
     * @var string
     */
    protected $signature = 'copy-flow-revision {--from=} {--to=} {--flow=} {--revision=} {--legacy} {--overwrite}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Copy a Revision between environments';

    protected GoogleFirestoreService $firestoreService;

    protected DocumentReference $sourceEnvironment;

    protected DocumentReference $targetEnvironment;

    protected string $flowId;

    protected string $revisionId;

    protected bool $allowOverwrite;

    /**
     * Execute the console command.
     *
     * @return void
     */
    public function handle(): void
    {
        $this->firestoreService = app()->make(GoogleFirestoreService::class);

        $legacyMode = $this->option('legacy');
        $allowOverwrite = $this->option('overwrite');
        $source = $this->option('from');
        $target = $this->option('to');
        $flowId = $this->option('flow');
        $revisionId = $this->option('revision');

        if (!$source || !$target || $source === $target) {
            $this->error("A 'from' and 'to' environment must be specified, and cannot be the same");
            return;
        }
        if (!$flowId || !$revisionId) {
            $this->error("A Flow ID and Revision ID must be specified.");
            return;
        }
        if ($target === 'production') {
            $this->error("This script cannot be used to target the Production environment");
            return;
        }

        $environments = $this->firestoreService->db->collection("environments")->documents();
        $validEnvironmentNames = [];
        foreach($environments as $document) {
            $validEnvironmentNames[] = $document->id();
        }

        if (!in_array($source, $validEnvironmentNames) || !in_array($target, $validEnvironmentNames)) {
            $this->error("From and To must exist in [" . implode(", ", $validEnvironmentNames) . "]");
            return;
        }
        $this->sourceEnvironment = $this->firestoreService->db->collection("environments")->document($source);
        $this->targetEnvironment = $this->firestoreService->db->collection("environments")->document($target);
        $this->flowId = $flowId;
        $this->revisionId = $revisionId;
        $this->allowOverwrite = $allowOverwrite;

        $legacyMode
            ? $this->handleLegacyCopy()
            : $this->handleV2Copy();
    }

    public function handleLegacyCopy()
    {
        $this->checkFlowDocumentExists(LegacyRevisionRepository::COLLECTION_FLOWS);

        $sourceRevisionMetaPath = $this->getSourceCollectionPath(LegacyRevisionRepository::COLLECTION_FLOWS . "/$this->flowId/" . LegacyRevisionRepository::COLLECTION_REVISION_META);
        $sourceRevisionPath = $this->getSourceCollectionPath(LegacyRevisionRepository::COLLECTION_FLOWS . "/$this->flowId/" . LegacyRevisionRepository::COLLECTION_REVISIONS);
        $targetRevisionMetaPath = $this->getTargetCollectionPath(LegacyRevisionRepository::COLLECTION_FLOWS . "/$this->flowId/" . LegacyRevisionRepository::COLLECTION_REVISION_META);
        $targetRevisionPath = $this->getTargetCollectionPath(LegacyRevisionRepository::COLLECTION_FLOWS . "/$this->flowId/" . LegacyRevisionRepository::COLLECTION_REVISIONS);

        $sourceRevisionMeta = $this->getDocumentFromCollection($sourceRevisionMetaPath, $this->revisionId)->snapshot();
        $sourceRevision = $this->getDocumentFromCollection($sourceRevisionPath, $this->revisionId)->snapshot();

        if (!$sourceRevisionMeta->exists() || !$sourceRevision->exists()) {
            $this->error("The requested Revision could not be found $this->flowId/$this->revisionId");
            return;
        }

        $sourceRevisionData = $sourceRevision->data();
        $sourceRevisionMetaData = $sourceRevisionMeta->data();
        $existingId = $this->checkRevisionExists($targetRevisionMetaPath, $sourceRevisionMetaData);

        if ($existingId && !$this->allowOverwrite) {
            $this->error("The requested Revision already exists on the target. Use the --overwrite flag if you wish to overwrite.");
            return;
        }

        $targetUuid = $existingId ?? Uuid::uuid4()->toString();

        $targetRevisionMetaReference = $this->getDocumentFromCollection($targetRevisionMetaPath, $targetUuid);
        $targetRevisionReference = $this->getDocumentFromCollection($targetRevisionPath, $targetUuid);

        $targetRevisionMetaReference->set($this->updateRevision($sourceRevisionMetaData, $targetUuid));
        $targetRevisionReference->set($this->updateRevision($sourceRevisionData, $targetUuid));

        $this->info("\nCompleted Copying\n");
    }

    public function handleV2Copy()
    {
        $this->checkFlowDocumentExists(RevisionRepository::COLLECTION_FLOWS);

        $sourceWorkingRevisionPath = $this->getSourceCollectionPath(RevisionRepository::COLLECTION_FLOWS . "/$this->flowId/" . RevisionRepository::COLLECTION_WORKING_REVISIONS);
        $sourcePublishedRevisionPath = $this->getSourceCollectionPath(RevisionRepository::COLLECTION_FLOWS . "/$this->flowId/" . RevisionRepository::COLLECTION_PUBLISHED_REVISIONS);

        $sourceRevision = $this->getDocumentFromCollection($sourceWorkingRevisionPath, $this->revisionId)->snapshot();
        $isWorkingRevision = $sourceRevision->exists();

        if (!$isWorkingRevision) {
            $sourceRevision = $this->getDocumentFromCollection($sourcePublishedRevisionPath, $this->revisionId)->snapshot();
        }

        if (!$sourceRevision->exists()) {
            $this->error("The requested Revision could not be found $this->flowId/$this->revisionId");
            return;
        }

        $targetRevisionPath = $isWorkingRevision
            ? $this->getTargetCollectionPath(RevisionRepository::COLLECTION_FLOWS . "/$this->flowId/" . RevisionRepository::COLLECTION_WORKING_REVISIONS)
            : $this->getTargetCollectionPath(RevisionRepository::COLLECTION_FLOWS . "/$this->flowId/" . RevisionRepository::COLLECTION_PUBLISHED_REVISIONS);

        $sourceRevisionData = $sourceRevision->data();
        $existingId = $this->checkRevisionExists($targetRevisionPath, $sourceRevisionData, $isWorkingRevision);

        if ($existingId && !$this->allowOverwrite) {
            $this->error("The requested Revision already exists on the target. Use the --overwrite flag if you wish to overwrite.");
            return;
        }

        $targetUuid = $existingId ?? "v2_" . Uuid::uuid4()->toString();
        $targetRevisionReference = $this->getDocumentFromCollection($targetRevisionPath, $targetUuid);

        $targetRevisionReference->set($this->updateRevision($sourceRevisionData, $targetUuid));

        $sourceRevisionId = $sourceRevision->id();
        $targetRevisionId = $targetRevisionReference->id();

        $sourcePath = $isWorkingRevision
            ? $sourceWorkingRevisionPath
            : $sourcePublishedRevisionPath;

        $this->cloneCollection(
            "$sourcePath" . "/$sourceRevisionId/" . RevisionRepository::COLLECTION_SLIDES,
            "$targetRevisionPath" . "/$targetRevisionId/" . RevisionRepository::COLLECTION_SLIDES,
        );

        $this->info("\nCopying completed.\n");
    }

    protected function checkRevisionExists(string $targetPath, array &$sourceRevision, bool $workingRevision = false): ?string
    {
        $existingRevision = $workingRevision
            ? $this->firestoreService->db->collection($targetPath)
                ->where(Revision::NAME, '==', $sourceRevision[Revision::NAME])
                ->documents()
            : $this->firestoreService->db->collection($targetPath)
                ->where(Revision::VERSION, '==', $sourceRevision[Revision::VERSION])
                ->where(Revision::NAME, '==', $sourceRevision[Revision::NAME])
                ->documents();

        $existingDocumentId = null;
        foreach($existingRevision as $document) {
            if ($document->exists()) {
                $existingDocumentId = $document->id();
            }
        }

        return $existingDocumentId;
    }

    protected function updateRevision(array &$documentData, string $newId): array
    {
        $documentData[Revision::ENVIRONMENT] = $this->targetEnvironment->path();
        $documentData[Revision::REVISION_ID] = $newId;
        $documentData[Revision::PARENT_REVISION] = $this->targetEnvironment->path() . "/" . $documentData[Revision::REVISION_ID];

        return $documentData;
    }

    protected function checkFlowDocumentExists(string $flowCollectionName): void {
        $targetFlowPath = $this->getTargetCollectionPath($flowCollectionName);
        $flowReference = $this->getDocumentFromCollection($targetFlowPath, $this->flowId);

        if ($flowReference->snapshot()->exists()) return;
        else {
            $sourceFlowPath = $this->getSourceCollectionPath($flowCollectionName);
            $sourceFlow = $this->getDocumentFromCollection($sourceFlowPath, $this->flowId);
            if (!$sourceFlow->snapshot()->exists()) {
                $this->error("The requested Flow ID was not found");
                return;
            }

            // Remove Prod version as Document ID will not exist on target environment
            $data = $sourceFlow->snapshot()->data();
            $data['versions']['production'] = null;

            $flowReference->set($data);
        }
    }

    protected function cloneCollection(string $sourceCollection, string $targetCollection): void
    {
        $documents = $this->firestoreService->db->collection($sourceCollection)
            ->documents();

        $batch = $this->firestoreService->db->batch();

        /** @var DocumentSnapshot $document */
        foreach($documents as $document) {
            $target = $this->firestoreService->db->collection($targetCollection)->document($document->id());
            $batch->set($target, $document->data());
        }
        $batch->commit();
    }

    protected function getSourceCollectionPath(string $relativePath): string
    {
        return $this->sourceEnvironment->path() . "/$relativePath";
    }

    protected function getTargetCollectionPath(string $relativePath): string
    {
        return $this->targetEnvironment->path() . "/$relativePath";
    }

    protected function getDocumentFromCollection(string $collectionPath, string $documentId): DocumentReference
    {
        return $this->firestoreService->db->collection($collectionPath)->document($documentId);
    }
}
