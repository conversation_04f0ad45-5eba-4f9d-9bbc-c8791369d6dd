<?php

namespace App\Console\Commands;

use App\Models\Legacy\EloquentCompany;
use App\Models\Odin\Company;
use App\Services\Odin\CompanySyncServices;
use Illuminate\Console\Command;
use Illuminate\Contracts\Container\BindingResolutionException;

class SyncCompanyContacts extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'sync:legacy-company-contacts';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Sync legacy company contacts with A2 company contacts';

    /**
     * Execute the console command.
     *
     * @return void
     * @throws BindingResolutionException
     */
    public function handle(): void
    {
        $companies = $this->ask('legacy company Ids to sync (comma seperated)');

        $companies = explode(',', $companies);

        if (count($companies) === 0) return;

        foreach ($companies as $company) {
            $this->info("Syncing company contacts for legacy company ID: {$company}");

            $status = $this->syncCompanyContacts(trim($company));

            if ($status !== true) $this->alert($status);
        }
    }

    /**
     * @param int $companyId
     *
     * @return bool|string
     * @throws BindingResolutionException
     */
    public function syncCompanyContacts(int $companyId): bool|string
    {
        /** @var EloquentCompany $legacyCompany */
        $legacyCompany = EloquentCompany::query()->find($companyId);

        /** @var Company $company */
        $company = Company::query()->where(Company::FIELD_LEGACY_ID, $companyId)->first();

        if (!$legacyCompany) return "Legacy company does not exist for legacy company ID: {$companyId}";
        if (!$company) return "Admin2 company does not exist for legacy company ID: {$companyId}";

        /** @var CompanySyncServices $companySyncService */
        $companySyncService = app()->make(CompanySyncServices::class);

        $companySyncService->syncLegacyCompanyContact($legacyCompany, $company);

        /** @var EloquentCompany $legacyCompany */
        $legacyCompany = EloquentCompany::query()->find($company->id);

        /** @var Company $company */
        $company = Company::query()->where(Company::FIELD_LEGACY_ID, $legacyCompany?->companyid)->first();

        if ($legacyCompany && $company) $companySyncService->syncLegacyCompanyContact($legacyCompany, $company);

        return true;
    }
}
