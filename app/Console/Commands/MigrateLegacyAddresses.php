<?php

namespace App\Console\Commands;

use App\Jobs\LegacyMigrations\MigrateLegacyAddressesJob;
use App\Models\Legacy\EloquentAddress;
use App\Models\Odin\Address;
use App\Services\BatchHelperService;
use App\Services\QueueHelperService;
use App\Services\DatabaseHelperService;
use Illuminate\Bus\Batch;
use Illuminate\Console\Command;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\DB;
use Throwable;

class MigrateLegacyAddresses extends Command
{
    const BATCH_SIZE = 'batch-size';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'legacy-migrate:addresses {--batch-size=3000 : The number of batches/jobs to create} {--ids=* : Pass in specific ids to migrate }';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Handles the migration of legacy addresses to their new schema.';

    /**
     * Execute the console command.
     *
     * @return int
     * @throws Throwable
     */
    public function handle()
    {
        $batchSize = (int) $this->option(self::BATCH_SIZE);

        $ids = $this->option('ids');
        if (count($ids)) {
                MigrateLegacyAddressesJob::dispatchSync(collect($ids));
            return count($ids);
        }

        $this->line("Migrating addresses");

        // Performance
        DB::disableQueryLog();

        $addressIdCol = EloquentAddress::ID;
        $countCol = 'count';

        $total = EloquentAddress::query()
                    ->leftJoin(DatabaseHelperService::database().'.'.Address::TABLE, function($join) {
                        $join->on(
                            DatabaseHelperService::database().'.'.Address::TABLE.'.'.Address::FIELD_LEGACY_ID,
                            '=',
                            DatabaseHelperService::readOnlyDatabase().'.'.EloquentAddress::TABLE.'.'.EloquentAddress::ID
                        );
                    })
                    ->whereNull(Address::TABLE.'.'.Address::FIELD_LEGACY_ID)
                    ->selectRaw("COUNT($addressIdCol) AS $countCol")
                    ->first()
                    ->{$countCol};

        if($total > 0) {
            $this->line("$total addresses total");

            $this->line("Creating migration jobs");

            EloquentAddress::query()
                ->leftJoin(DatabaseHelperService::database().'.'.Address::TABLE, function($join) {
                    $join->on(
                        DatabaseHelperService::database().'.'.Address::TABLE.'.'.Address::FIELD_LEGACY_ID,
                        '=',
                        DatabaseHelperService::readOnlyDatabase().'.'.EloquentAddress::TABLE.'.'.EloquentAddress::ID
                    );
                })
                ->whereNull(Address::TABLE.'.'.Address::FIELD_LEGACY_ID)
                ->select(EloquentAddress::ID)
                ->chunk($batchSize, function($addressIds) {
                    MigrateLegacyAddressesJob::dispatch($addressIds)
                        ->onConnection(QueueHelperService::QUEUE_CONNECTION)
                        ->onQueue(QueueHelperService::QUEUE_NAME_LEGACY_MIGRATION);
                });
        }
        else {
            $this->line("No addresses to migrate");
        }

        return 0;
    }
}
