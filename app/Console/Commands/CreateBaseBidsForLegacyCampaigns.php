<?php

namespace App\Console\Commands;

use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\LeadCampaignLocation;
use App\Models\Legacy\LeadCategory;
use App\Repositories\LocationRepository;
use Carbon\Carbon;
use Exception;
use Illuminate\Console\Command;
use App\Models\Legacy\LeadPrice;
use Illuminate\Support\Collection;
use App\Models\Legacy\Location;
use Illuminate\Support\Facades\DB;

class CreateBaseBidsForLegacyCampaigns extends Command
{
    const string OPTION_STATE = 'state';
    const string OPTION_DAYS = 'days';

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'legacy:create-base-bids-for-legacy-campaigns {state} {--days=30}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Creates bids for companies with purchasing campaigns';

    /** @var Collection<LeadPrice> */
    protected Collection $floorPrices;

    protected Location $stateLocation;

    /**
     * Execute the console command.
     */
    public function handle(): void
    {
        /** @var Location|null $stateLocation */
        $stateLocation = Location::query()
            ->where(Location::STATE_ABBREVIATION, $this->argument(self::OPTION_STATE))
            ->orWhere(Location::STATE, $this->argument(self::OPTION_STATE))
            ->where(Location::TYPE, Location::TYPE_STATE)
            ->first();

        if (!$stateLocation) {
            $this->error('Invalid State');
            return;
        }

        $this->stateLocation = $stateLocation;

        $this->info("Getting Started for State: {$this->argument(self::OPTION_STATE)}");

        $allCampaigns = $this->getCountyLocationsOfSolarCampaignsForState($stateLocation->state_key);
        $allCampaigns = $this->filterOutCompaniesNotPurchasedLeads($allCampaigns);
        $allCampaigns = $this->filterOutCompaniesWithIndividualPrice($allCampaigns);

        $this->info("Total companies with campaign in state {$this->argument(self::OPTION_STATE)}: {$allCampaigns->count()}");

        $this->fetchFloorPrices($stateLocation);
        $this->processAllCampaigns($allCampaigns);
    }

    /**
     * @param Collection $campaigns
     *
     * @return Collection
     */
    protected function filterOutCompaniesNotPurchasedLeads(Collection $campaigns): Collection
    {
        $companies = EloquentQuoteCompany::query()
            ->select(EloquentQuoteCompany::COMPANY_ID)
            ->where(EloquentQuoteCompany::DELIVERED, true)
            ->where(EloquentQuoteCompany::TIMESTAMP_DELIVERED, '>=', Carbon::now()->subDays($this->option(self::OPTION_DAYS))->timestamp)
            ->distinct()
            ->get()
            ->pluck(EloquentQuoteCompany::COMPANY_ID)
            ->toArray();

        return $campaigns->filter(fn($campaigns, int $company_id) => in_array($company_id, $companies));
    }

    /**
     * @param Collection $campaigns
     *
     * @return Collection
     */
    protected function filterOutCompaniesWithIndividualPrice(Collection $campaigns): Collection
    {
        $companies = LeadPrice::query()
            ->select(LeadPrice::COMPANY_ID)
            ->where(LeadPrice::PRICE_TYPE, LeadPrice::PRICE_TYPE_FLOOR)
            ->where(LeadPrice::COMPANY_ID, '!=', LeadPrice::DEFAULT_COMPANY)
            ->distinct()
            ->get()
            ->pluck(LeadPrice::COMPANY_ID)
            ->toArray();

        return $campaigns->filter(fn($campaigns, int $company_id) => !in_array($company_id, $companies));
    }

    /**
     * @param Location $sateLocation
     *
     * @return void
     */
    protected function fetchFloorPrices(Location $sateLocation): void
    {
        $this->floorPrices = LeadPrice::query()
            ->join(
                Location::TABLE,
                Location::TABLE . '.' . Location::ID,
                LeadPrice::TABLE . '.' . LeadPrice::LOCATION_ID
            )
            ->where(LeadPrice::TABLE . '.' . LeadPrice::COMPANY_ID, LeadPrice::DEFAULT_COMPANY)
            ->where(LeadPrice::TABLE . '.' . LeadPrice::PRICE_TYPE, LeadPrice::PRICE_TYPE_FLOOR)
            ->where(LeadPrice::TABLE . '.' . LeadPrice::LEAD_INDUSTRY, LeadPrice::LEAD_INDUSTRY_SOLAR)
            ->where(LeadPrice::TABLE . '.' . LeadPrice::LEAD_CATEGORY_ID, LeadCategory::RESIDENTIAL)
            ->where(Location::TABLE . '.' . Location::STATE_KEY, $sateLocation->state_key)
            ->get();
    }

    /**
     * @param Collection $allCampaigns
     *
     * @return void
     */
    protected function processAllCampaigns(Collection $allCampaigns): void
    {
        $allCampaigns->each(function (Collection $campaigns, int $companyId) {
            if ($campaigns->isEmpty()) {
                return;
            }

            $this->info("Creating bids for company: {$campaigns->first()->company_name} ($companyId)");

            $this->createBids($campaigns);
        });
    }

    /**
     * @param Collection $campaigns
     *
     * @return void
     */
    protected function createBids(Collection $campaigns): void
    {
        $bidPricesForCampaign = $this->getBidPricesForCampaign(
            $campaigns->pluck('campaign_id')->unique()->toArray()
        );

        $bids = collect();

        foreach ($campaigns as $campaign) {
            foreach ($this->getLeadTypes() as $leadType) {
                foreach ($this->getLeadSaleTypes() as $leadSaleType) {
                    if (!$this->hasBids(
                        bidPrices: $bidPricesForCampaign,
                        campaignId: $campaign->campaign_id,
                        leadTypeId: $leadType,
                        leadSaleTypeId: $leadSaleType,
                        locationId: $campaign->county_location_id)
                    ) {
                        $bids->push([
                            LeadPrice::COMPANY_ID => $campaign->company_id,
                            LeadPrice::LEAD_CATEGORY_ID => LeadCategory::RESIDENTIAL,
                            LeadPrice::LEAD_TYPE_ID => $leadType,
                            LeadPrice::LEAD_SALES_TYPE_ID => $leadSaleType,
                            LeadPrice::LOCATION_ID => $campaign->county_location_id,
                            LeadPrice::PRICE => $this->getPrice(
                                leadTypeId: $leadType,
                                leadSaleTypeId: $leadSaleType,
                                locationId: $campaign->county_location_id
                            ),
                            LeadPrice::PRICE_TYPE => LeadPrice::PRICE_TYPE_BID,
                            LeadPrice::LEAD_CAMPAIGN_ID => $campaign->campaign_id,
                            LeadPrice::LEAD_INDUSTRY => LeadPrice::LEAD_INDUSTRY_SOLAR,
                        ]);
                    }
                }
            }
        }

        if ($bids->isEmpty()) {
            return;
        }

        DB::beginTransaction();

        try {
            foreach ($bids->chunk(100) as $chunk) {
                LeadPrice::query()->insert($chunk->toArray());
            }

            $this->info("Bids created for campaigns: {$campaigns->pluck('campaign_id')->unique()->join(', ')}");
        } catch (Exception $e) {
            $campaign = $campaigns->first();
            $this->error("Failed to add bid price for company: $campaign?->company_name ($campaign->company_id)");
            $this->error("Error: {$e->getMessage()}");
            DB::rollBack();

            return;
        }

        DB::commit();
    }

    /**
     * @param int $leadTypeId
     * @param int $leadSaleTypeId
     * @param int $locationId
     *
     * @return float
     */
    protected function getPrice(int $leadTypeId, int $leadSaleTypeId, int $locationId): float
    {
        /** @var LeadPrice $statePrice */
        $statePrice = $this->floorPrices->filter(fn(LeadPrice $leadPrice) => $leadPrice->lead_type_id === $leadTypeId
            && $leadPrice->lead_sales_type_id === $leadSaleTypeId
            && $leadPrice->location_id === $this->stateLocation->id
            && $leadPrice->lead_category_id === LeadCategory::RESIDENTIAL
        )->first();

        /** @var LeadPrice|null $countyPrice */
        $countyPrice = $this->floorPrices->filter(fn(LeadPrice $leadPrice) => $leadPrice->lead_type_id === $leadTypeId
            && $leadPrice->lead_sales_type_id === $leadSaleTypeId
            && $leadPrice->location_id === $locationId
            && $leadPrice->lead_category_id === LeadCategory::RESIDENTIAL
        )->first();

        return $countyPrice ? $countyPrice->price : $statePrice->price;
    }

    /**
     * @param Collection<LeadPrice> $bidPrices
     * @param int $campaignId
     * @param int $leadTypeId
     * @param int $leadSaleTypeId
     * @param int $locationId
     *
     * @return bool
     */
    protected function hasBids(
        Collection $bidPrices,
        int $campaignId,
        int $leadTypeId,
        int $leadSaleTypeId,
        int $locationId
    ): bool
    {
        return !! $bidPrices->first(fn (LeadPrice $leadPrice) => $leadPrice->lead_campaign_id === $campaignId
            && $leadPrice->lead_type_id === $leadTypeId
            && $leadPrice->lead_sales_type_id === $leadSaleTypeId
            && $leadPrice->location_id === $locationId
            && $leadPrice->lead_category_id === LeadCategory::RESIDENTIAL
        );
    }

    /**
     * @return int[]
     */
    protected function getLeadTypes(): array
    {
        return[LeadPrice::LEAD_TYPE_STANDARD, LeadPrice::LEAD_TYPE_PREMIUM];
    }

    /**
     * @return int[]
     */
    protected function getLeadSaleTypes(): array
    {
        return [
            LeadPrice::SALES_TYPE_EXCLUSIVE,
            LeadPrice::SALES_TYPE_DUO,
            LeadPrice::SALES_TYPE_TRIO,
            LeadPrice::SALES_TYPE_QUAD
        ];
    }

    /**
     * @param array $campaignIds
     *
     * @return Collection
     */
    protected function getBidPricesForCampaign(array $campaignIds): Collection
    {
        return LeadPrice::query()
            ->where(LeadPrice::PRICE_TYPE, LeadPrice::PRICE_TYPE_BID)
            ->whereIn(LeadPrice::LEAD_CAMPAIGN_ID, $campaignIds)
            ->get();
    }

    /**
     * @param string $stateKey
     *
     * @return Collection
     */
    protected function getCountyLocationsOfSolarCampaignsForState(string $stateKey): Collection
    {
        return LeadCampaign::query()
            ->select([
                LeadCampaign::TABLE . '.' . LeadCampaign::COMPANY_ID,
                EloquentCompany::TABLE . '.' . EloquentCompany::COMPANY_NAME . ' AS company_name',
                LeadCampaign::TABLE . '.' . LeadCampaign::ID . ' AS campaign_id',
                Location::TABLE . '.' . Location::ID . ' AS county_location_id'
            ])
            ->join(
                EloquentCompany::TABLE,
                EloquentCompany::TABLE . '.' . EloquentCompany::COMPANY_ID,
                LeadCampaign::TABLE . '.' . LeadCampaign::COMPANY_ID
            )
            ->join(
                LeadCampaignLocation::TABLE,
                LeadCampaignLocation::TABLE . '.' . LeadCampaignLocation::LEAD_CAMPAIGN_ID,
                LeadCampaign::TABLE . '.' . LeadCampaign::ID
            )
            ->join(
                Location::TABLE,
                Location::TABLE . '.' . Location::ID,
                LeadCampaignLocation::TABLE . '.' . LeadCampaignLocation::LOCATION_ID
            )
            ->whereNull(LeadCampaign::TABLE . '.' . LeadCampaign::DELETED_AT)
            ->where(LeadCampaign::TABLE . '.' . LeadCampaign::IS_MANAGED_BY_A2, false)
            ->where(EloquentCompany::TABLE . '.' . EloquentCompany::TYPE, EloquentCompany::TYPE_INSTALLER)
            ->where(EloquentCompany::TABLE . '.' . EloquentCompany::STATUS, EloquentCompany::STATUS_ACTIVE)
            ->where(Location::TABLE . '.' . Location::TYPE, Location::TYPE_COUNTY)
            ->where(Location::TABLE . '.' . Location::STATE_KEY, $stateKey)
            ->get()
            ->groupBy('company_id');

    }
}
