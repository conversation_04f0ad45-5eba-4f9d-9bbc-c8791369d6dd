<?php

namespace App\Console\Commands\ConsumerProjects;

use App\Campaigns\Modules\Bidding\ProductBiddingModule;
use App\Campaigns\Modules\Budget\BaseBudgetContainerModule;
use App\Campaigns\Modules\DeliveryModule;
use App\Campaigns\Modules\LocationModule;
use App\Enums\Campaigns\CampaignStatus;
use App\Enums\Campaigns\CampaignType;
use App\Enums\Campaigns\Modules\Budget\BudgetType;
use App\Enums\ProductAssignment\BudgetProductConfigurationEnum;
use App\Mediators\CampaignMediator;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Legacy\EloquentCompany;
use App\Models\Legacy\LeadCampaign;
use App\Models\Legacy\LeadCampaignLocation;
use App\Models\Legacy\Location;
use App\Models\Odin\IndustryService;
use Illuminate\Console\Command;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Ramsey\Uuid\Uuid;

class GenerateCampaign extends Command
{
    const CAMPAIGNS_TO_GENERATE = 1000;

    protected ?array $serviceIds = null;

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'consumer-projects:generate-campaign';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Temporary command to generate campaign(s), before seeders';

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        /**
         * This command is intended as an investigation into how we want to generate campaigns, ultimately will be
         * replaced with seeders.
         */

        $this->info("Finding example campaigns");
        $solarCampaigns = LeadCampaign::query()
            ->where(LeadCampaign::STATUS, LeadCampaign::STATUS_ACTIVE)
            ->whereHas(
                LeadCampaign::RELATION_COMPANY,
                fn(Builder $query) => $query->where(EloquentCompany::STATUS, EloquentCompany::STATUS_ACTIVE)->where(EloquentCompany::TYPE, EloquentCompany::TYPE_INSTALLER)
            )
            ->limit(floor(self::CAMPAIGNS_TO_GENERATE / 3))
            ->inRandomOrder()
            ->get();

        $roofingCampaigns = LeadCampaign::query()
            ->where(LeadCampaign::STATUS, LeadCampaign::STATUS_ACTIVE)
            ->whereHas(
                LeadCampaign::RELATION_COMPANY,
                fn(Builder $query) => $query->where(EloquentCompany::STATUS, EloquentCompany::STATUS_ACTIVE)->where(EloquentCompany::TYPE, EloquentCompany::TYPE_ROOFER)
            )
            ->limit(floor(self::CAMPAIGNS_TO_GENERATE / 3))
            ->inRandomOrder()
            ->get();

        $multiCampaigns = LeadCampaign::query()
            ->where(LeadCampaign::STATUS, LeadCampaign::STATUS_ACTIVE)
            ->whereHas(
                LeadCampaign::RELATION_COMPANY,
                fn(Builder $query) => $query->where(EloquentCompany::STATUS, EloquentCompany::STATUS_ACTIVE)->where(EloquentCompany::TYPE, EloquentCompany::TYPE_MULTI)
            )
            ->limit(self::CAMPAIGNS_TO_GENERATE - ($solarCampaigns->count() + $roofingCampaigns->count()))
            ->inRandomOrder()
            ->get();

        $campaigns = collect()->merge($solarCampaigns)->merge($roofingCampaigns)->merge($multiCampaigns);

        $this->info("Found " . $campaigns->count() . " campaigns.");
        $this->info("Starting generation");
        $bar = $this->output->createProgressBar($campaigns->count());
        $errors = 0;
        $errorMessages = [];

        foreach($campaigns as $campaign) {
            try {
                $this->generateCampaignFromLegacyCampaign($campaign);
            } catch (\Exception $e) {
                $errors++;
                $errorMessages[] = $e->getMessage();
                throw $e;
                break;
            }

            $bar->advance();
        }

        $bar->finish();
        $this->info("");
        $this->info("Finished generating, " . $errors . " errors");
        if($errors > 0)
            $this->error("Example error: " . $errorMessages[0]);


        return Command::SUCCESS;
    }

    protected function generateCampaignFromLegacyCampaign(LeadCampaign $legacyCampaign): void
    {
        /** @var CampaignMediator $mediator */
        $mediator = app(CampaignMediator::class);

        $campaign = new CompanyCampaign();
        $campaign->company_id = $legacyCampaign->company_id; // This should really be the a2 id, but w/e it's for testing
        $campaign->product_id = 1;
        $campaign->service_id = $this->getServiceId($legacyCampaign);
        $campaign->type = $legacyCampaign->company->type === EloquentCompany::TYPE_INSTALLER ? CampaignType::SOLAR_LEAD_CAMPAIGN : CampaignType::LEAD_CAMPAIGN;
        $campaign->status = CampaignStatus::ACTIVE;
        $campaign->name = $legacyCampaign->name;
        $campaign->reference = Uuid::uuid4();
        $campaign->save();

        $mediator->save($campaign, $this->preparePayload($legacyCampaign));
    }

    protected function preparePayload(LeadCampaign $legacyCampaign): Collection
    {
        return collect([
            LocationModule::PAYLOAD_PARENT_KEY => [
                LocationModule::PAYLOAD_ZIP_CODES => [...$legacyCampaign->leadCampaignZipLocations->pluck(LeadCampaignLocation::RELATION_LOCATION.'.'.Location::ZIP_CODE)->toArray(), "92337"]
            ],
            BaseBudgetContainerModule::PAYLOAD_PARENT_KEY => [
                BaseBudgetContainerModule::PAYLOAD_BUDGETS => [
                    [
                        Budget::FIELD_DISPLAY_NAME          => 'Verified',
                        Budget::FIELD_KEY                   => 'verified',
                        Budget::FIELD_STATUS                => Budget::STATUS_ENABLED,
                        Budget::FIELD_TYPE                  => BudgetType::NO_LIMIT,
                        Budget::FIELD_VALUE                 => 0,
                        Budget::FIELD_PRODUCT_CONFIGURATION => BudgetProductConfigurationEnum::LEAD_VERIFIED,
                    ],
                    [
                        Budget::FIELD_DISPLAY_NAME          => 'Unverified',
                        Budget::FIELD_KEY                   => 'unverified',
                        Budget::FIELD_STATUS                => Budget::STATUS_DISABLED,
                        Budget::FIELD_TYPE                  => BudgetType::NO_LIMIT,
                        Budget::FIELD_VALUE                 => 0,
                        Budget::FIELD_PRODUCT_CONFIGURATION => BudgetProductConfigurationEnum::LEAD_UNVERIFIED
                    ],
                    [
                        Budget::FIELD_DISPLAY_NAME          => 'Email Only',
                        Budget::FIELD_KEY                   => 'email_only',
                        Budget::FIELD_STATUS                => Budget::STATUS_DISABLED,
                        Budget::FIELD_TYPE                  => BudgetType::NO_LIMIT,
                        Budget::FIELD_VALUE                 => 0,
                        Budget::FIELD_PRODUCT_CONFIGURATION => BudgetProductConfigurationEnum::LEAD_EMAIL_ONLY
                    ]
                ]
            ],
            ProductBiddingModule::PAYLOAD_PARENT_KEY => [],
            DeliveryModule::PAYLOAD_PARENT_KEY => [
                DeliveryModule::PAYLOAD_CONTACT_DELIVERIES => [],
                DeliveryModule::PAYLOAD_CRM_DELIVERIES => []
            ]
        ]);
    }

    protected function getServiceId(LeadCampaign $leadCampaign): int
    {
        return match($leadCampaign->company->type) {
            //EloquentCompany::TYPE_INSTALLER => 1,
            //EloquentCompany::TYPE_ROOFER => 6,
            default => $this->getServiceIds()[rand(0, count($this->getServiceIds()) - 1)]
        };
    }

    protected function getServiceIds(): array
    {
        if($this->serviceIds !== null)
            return $this->serviceIds;

        return $this->serviceIds = IndustryService::query()->where(IndustryService::FIELD_INDUSTRY_ID , '>', 2)
            ->get()
            ->pluck(IndustryService::FIELD_ID)
            ->toArray();
    }
}
