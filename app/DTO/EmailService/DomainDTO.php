<?php

namespace App\DTO\EmailService;

use App\DTO\DTOContract;
use App\Enums\Emails\DomainStatus;
use Carbon\Carbon;
use Illuminate\Support\Arr;

class DomainDTO implements DTOContract
{
    const string NAME       = 'name';
    const string STATUS     = 'status';
    const string ID         = 'id';
    const string CREATED_AT = 'created_at';
    const string REGION     = 'region';


    public function __construct(
        protected string        $name,
        protected ?DomainStatus $status = null,
        protected ?string       $id = null,
        protected ?Carbon       $createdAt = null,
        protected ?string       $region = null,
    )
    {
    }

    public function getName(): string
    {
        return $this->name;
    }

    public function getStatus(): ?DomainStatus
    {
        return $this->status;
    }

    public function getId(): ?string
    {
        return $this->id;
    }

    public function getCreatedAt(): ?Carbon
    {
        return $this->createdAt;
    }

    public function getRegion(): ?string
    {
        return $this->region;
    }

    public function toArray(): array
    {
        return [
            self::NAME       => $this->name,
            self::STATUS     => $this->status?->value,
            self::ID         => $this->id,
            self::CREATED_AT => $this->createdAt->toIso8601String(),
            self::REGION     => $this->region,
        ];
    }

    public static function fromArray(array $array): DTOContract
    {
        return new self(
            name     : Arr::get($array, self::NAME),
            status   : DomainStatus::tryFrom(Arr::get($array, self::STATUS)),
            id       : Arr::get($array, self::ID),
            createdAt: Carbon::parse(Arr::get($array, self::CREATED_AT)),
            region   : Arr::get($array, self::REGION),
        );
    }
}