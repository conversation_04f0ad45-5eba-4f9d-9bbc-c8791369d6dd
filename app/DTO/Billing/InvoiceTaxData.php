<?php

namespace App\DTO\Billing;

use App\DTO\DTOContract;
use App\Enums\Billing\BillingPolicyActionType;
use App\Enums\Billing\BillingPolicyEventType;
use Illuminate\Support\Arr;

class InvoiceTaxData implements DTOContract
{
    const string INVOICE_UUID          = 'invoiceUuid';
    const string TAX_RATE              = 'taxRate';
    const string REGION                = 'region';
    const string COUNTRY               = 'country';
    const string TAX_TYPE              = 'taxType';
    const string TAXABLE_AMOUNT        = 'taxableAmount';
    const string TOTAL_TAXED           = 'totalTaxed';
    const string INVOICE_ITEM_TAX_DATA = 'invoiceItemTaxData';

    /**
     * @param string|null $invoiceUuid
     * @param float|null $taxRate
     * @param string|null $region
     * @param string|null $country
     * @param string|null $taxType
     * @param float|null $taxableAmount
     * @param float|null $totalTaxed
     * @param array<InvoiceItemTaxData> $invoiceItemTaxData
     */
    public function __construct(
        protected ?string $invoiceUuid = null,
        protected ?float  $taxRate = null,
        protected ?string $region = null,
        protected ?string $country = null,
        protected ?string $taxType = null,
        protected ?float  $taxableAmount = null,
        protected ?float  $totalTaxed = null,
        protected array   $invoiceItemTaxData = []
    )
    {
    }

    public function getInvoiceUuid(): ?string
    {
        return $this->invoiceUuid;
    }

    public function getTaxRate(): ?float
    {
        return $this->taxRate;
    }

    public function getRegion(): ?string
    {
        return $this->region;
    }

    public function getCountry(): ?string
    {
        return $this->country;
    }

    public function getTaxType(): ?string
    {
        return $this->taxType;
    }

    public function getTaxableAmount(): ?float
    {
        return $this->taxableAmount;
    }

    public function getTotalTaxed(): ?float
    {
        return $this->totalTaxed;
    }

    public function getInvoiceItemTaxData(): array
    {
        return $this->invoiceItemTaxData;
    }

    public function toArray(): array
    {
        return [
            self::INVOICE_UUID          => $this->invoiceUuid,
            self::TAX_RATE              => $this->taxRate,
            self::REGION                => $this->region,
            self::COUNTRY               => $this->country,
            self::TAX_TYPE              => $this->taxType,
            self::TAXABLE_AMOUNT        => $this->taxableAmount,
            self::TOTAL_TAXED           => $this->totalTaxed,
            self::INVOICE_ITEM_TAX_DATA => $this->invoiceItemTaxData,
        ];
    }

    public static function fromArray(array $array): self
    {
        return new self(
            Arr::get($array, self::INVOICE_UUID),
            Arr::get($array, self::TAX_RATE),
            Arr::get($array, self::REGION),
            Arr::get($array, self::COUNTRY),
            Arr::get($array, self::TAX_TYPE),
            Arr::get($array, self::TAXABLE_AMOUNT),
            Arr::get($array, self::TOTAL_TAXED),
            Arr::get($array, self::INVOICE_ITEM_TAX_DATA),
        );
    }
}
