<?php

namespace App\DTO\Billing\InvoicePdf;

use App\DTO\DTOContract;
use App\Services\CurrencyService;
use Illuminate\Contracts\Container\BindingResolutionException;
use Illuminate\Support\Arr;

class SummaryItem implements DTOContract
{
    const string FIELD_TITLE    = 'title';
    const string FIELD_CURRENCY = 'value';

    public function __construct(
        protected ?string $title = null,
        protected ?Money  $value = null,
    )
    {
    }

    /**
     * @return array
     * @throws BindingResolutionException
     */
    public function toArray(): array
    {
        return [
            self::FIELD_TITLE    => $this->title,
            self::FIELD_CURRENCY => $this->value->formatToCurrency(),
        ];
    }

    /**
     * @param array $array
     * @return DTOContract
     */
    public static function fromArray(array $array): DTOContract
    {
        return new self(
            title: Arr::get($array, self::FIELD_TITLE),
            value: Arr::get($array, self::FIELD_CURRENCY),
        );
    }
}
