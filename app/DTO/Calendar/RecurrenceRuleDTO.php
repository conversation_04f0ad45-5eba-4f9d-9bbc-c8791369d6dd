<?php

namespace App\DTO\Calendar;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;


class RecurrenceRuleDTO implements DTOContract
{
    const string FIELD_FREQUENCY    = 'frequency';
    const string FIELD_INTERVAL     = 'interval';
    const string FIELD_BY_DAY       = 'by_day';
    const string FIELD_BY_MONTH_DAY = 'by_month_day';
    const string FIELD_BY_MONTH     = 'by_month';
    const string FIELD_UNTIL        = 'until';
    const string FIELD_COUNT        = 'count';


    public function __construct(
        public ?string $frequency = null,
        public ?int $interval = 1,
        public ?array $byDay = [],
        public ?array $byMonthDay = [],
        public ?array $byMonth = [],
        public ?string $until = null,
        public ?int $count = null,
    )
    {

    }

    public function getFrequency(): ?string
    {
        return $this->frequency;
    }

    public function setFrequency(?string $frequency): void
    {
        $this->frequency = $frequency;
    }

    public function getInterval(): ?int
    {
        return $this->interval;
    }

    public function setInterval(?int $interval): void
    {
        $this->interval = $interval;
    }

    public function getByDay(): ?array
    {
        return $this->byDay;
    }

    public function setByDay(?array $byDay): void
    {
        $this->byDay = $byDay;
    }

    public function getByMonthDay(): ?array
    {
        return $this->byMonthDay;
    }

    public function setByMonthDay(?array $byMonthDay): void
    {
        $this->byMonthDay = $byMonthDay;
    }

    public function getByMonth(): ?array
    {
        return $this->byMonth;
    }

    public function setByMonth(?array $byMonth): void
    {
        $this->byMonth = $byMonth;
    }

    public function getUntil(): ?string
    {
        return $this->until;
    }

    public function setUntil(?string $until): void
    {
        $this->until = $until;
    }

    public function getCount(): ?int
    {
        return $this->count;
    }

    public function setCount(?int $count): void
    {
        $this->count = $count;
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_FREQUENCY    => $this->frequency,
            self::FIELD_INTERVAL     => $this->interval,
            self::FIELD_BY_DAY       => $this->byDay,
            self::FIELD_BY_MONTH_DAY => $this->byMonthDay,
            self::FIELD_BY_MONTH     => $this->byMonth,
            self::FIELD_UNTIL        => $this->until,
            self::FIELD_COUNT        => $this->count,
        ];
    }

    /**
     * @param array $array
     * @return DTOContract
     */
    public static function fromArray(array $array): DTOContract
    {
        return new self(
            frequency : Arr::get($array, self::FIELD_FREQUENCY),
            interval  : Arr::get($array, self::FIELD_INTERVAL),
            byDay     : Arr::get($array, self::FIELD_BY_DAY),
            byMonthDay: Arr::get($array, self::FIELD_BY_MONTH_DAY),
            byMonth   : Arr::get($array, self::FIELD_BY_MONTH),
            until     : Arr::get($array, self::FIELD_UNTIL),
            count     : Arr::get($array, self::FIELD_COUNT),
        );
    }
}

