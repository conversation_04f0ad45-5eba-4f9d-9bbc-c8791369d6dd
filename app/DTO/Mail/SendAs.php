<?php

namespace App\DTO\Mail;

use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class SendAs implements DTOContract
{
    const string FIELD_DISPLAY_NAME  = 'display_name';
    const string FIELD_SIGNATURE     = 'signature';
    const string FIELD_SEND_AS_EMAIL = 'send_as_email';
    const string FIELD_IS_DEFAULT    = 'is_default';
    const string FIELD_IS_PRIMARY    = 'is_primary';

    public function __construct(
        protected ?string $displayName = null,
        protected ?string $signature = null,
        protected ?string $sendAsEmail = null,
        protected ?bool $isDefault = null,
        protected ?bool $isPrimary = null,
    )
    {

    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return [
            self::FIELD_DISPLAY_NAME  => $this->displayName,
            self::FIELD_SIGNATURE     => $this->signature,
            self::FIELD_SEND_AS_EMAIL => $this->sendAsEmail,
            self::FIELD_IS_DEFAULT    => $this->isDefault,
            self::FIELD_IS_PRIMARY    => $this->isPrimary,
        ];
    }

    /**
     * @param array $array
     * @return SendAs
     */
    public static function fromArray(array $array): SendAs
    {
        return new self(
            displayName: Arr::get($array, self::FIELD_DISPLAY_NAME),
            signature  : Arr::get($array, self::FIELD_SIGNATURE),
            sendAsEmail: Arr::get($array, self::FIELD_SEND_AS_EMAIL),
            isDefault  : Arr::get($array, self::FIELD_IS_DEFAULT),
            isPrimary  : Arr::get($array, self::FIELD_IS_PRIMARY),
        );
    }

    /**
     * @return string|null
     */
    public function getDisplayName(): ?string
    {
        return $this->displayName;
    }

    /**
     * @return string|null
     */
    public function getSignature(): ?string
    {
        return $this->signature;
    }

    /**
     * @return string|null
     */
    public function getSendAsEmail(): ?string
    {
        return $this->sendAsEmail;
    }

    /**
     * @return bool|null
     */
    public function getIsDefault(): ?bool
    {
        return $this->isDefault;
    }

    /**
     * @return bool|null
     */
    public function getIsPrimary(): ?bool
    {
        return $this->isPrimary;
    }
}
