<?php

namespace App\DTO\Mail;


use App\DTO\DTOContract;
use Illuminate\Support\Arr;

class GetEmailsParam implements DTOContract
{
    const FIELD_MAX_RESULTS = 'max_results';

    public function __construct(
        protected ?int $maxResults = null
    )
    {

    }

    /**
     * @return int|null
     */
    public function getMaxResults(): ?int
    {
        return $this->maxResults;
    }

    /**
     * @param int|null $maxResults
     */
    public function setMaxResults(?int $maxResults): void
    {
        $this->maxResults = $maxResults;
    }

    public function toArray(): array
    {
        return [
            self::FIELD_MAX_RESULTS => $this->maxResults
        ];
    }

    public static function fromArray(array $array): GetEmailsParam
    {
        return new GetEmailsParam(
            Arr::get($array, self::FIELD_MAX_RESULTS),
        );
    }
}
