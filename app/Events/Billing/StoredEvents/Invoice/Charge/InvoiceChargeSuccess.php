<?php

namespace App\Events\Billing\StoredEvents\Invoice\Charge;

use Spatie\EventSourcing\StoredEvents\ShouldBeStored;

class InvoiceChargeSuccess extends ShouldBeStored
{
    public function __construct(
        public string $externalTransactionId,
        public string $invoiceUuid,
        public float $amount,
        public string $currency,
        public string $source,
        public string $type,
        public string $date,
        public ?string $scenario = null,
        public ?string $scope = null,
        public ?string $invoicePaymentChargeUuid = null,
        public ?string $externalPaymentMethodId = null,
    )
    {

    }
}
