<?php

namespace App\Events\Billing\StoredEvents\Invoice\Charge;

use App\Enums\Billing\BillingPolicyActionType;
use App\Enums\Billing\BillingPolicyEventType;

class InvoicePaymentDue extends BillingWorkflowStoredEvent
{
    public function __construct(
        public string $invoiceUuid,
        public string $paymentMethod,
        public int $total,
        public string $dueDate,
        public string $issuedDate,
    )
    {
    }


    /**
     * @return string
     */
    static function getId(): string
    {
        return BillingPolicyEventType::INVOICE_PAYMENT_DUE->value;
    }

    /**
     * @return string
     */
    static function getName(): string
    {
        return 'Invoice Payment Due';
    }

    /**
     * @return BillingPolicyActionType[]
     */
    static function getPossibleActions(): array
    {
        return [
            BillingPolicyActionType::SUSPEND_COMPANY,
            BillingPolicyActionType::SEND_EMAIL_NOTIFICATION
        ];
    }
}
