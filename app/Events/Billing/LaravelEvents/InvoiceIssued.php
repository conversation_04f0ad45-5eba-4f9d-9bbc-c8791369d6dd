<?php

namespace App\Events\Billing\LaravelEvents;

use App\Enums\Billing\BillingPolicyActionType;
use App\Enums\Billing\BillingPolicyEventType;
use App\Events\Billing\BillingWorkflowEventContract;
use Illuminate\Foundation\Events\Dispatchable;

class InvoiceIssued implements BillingWorkflowEventContract
{
    use Dispatchable;

    /**
     * @param string $invoiceUuid
     */
    public function __construct(
        public string $invoiceUuid
    )
    {

    }

    /**
     * @return string
     */
    static function getId(): string
    {
        return BillingPolicyEventType::INVOICE_ISSUED->value;
    }

    /**
     * @return string
     */
    static function getName(): string
    {
        return 'Invoice Issued';
    }

    static function getDescription(): ?string
    {
        return null;
    }

    static function getPossibleActions(): array
    {
        return [
            BillingPolicyActionType::SEND_EMAIL_NOTIFICATION
        ];
    }
}
