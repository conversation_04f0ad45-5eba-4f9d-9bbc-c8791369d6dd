<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

/**
 * @property int $id
 * @property string $platform
 * @property string $platform_account_id
 * @property string $platform_campaign_id
 * @property string $location_id
 * @property boolean $targeted
 *
 * @property-read MorphTo $platformLocations
 */
class LockedAdvertisingCampaignLocation extends Model
{
    const TABLE = 'locked_advertising_campaign_locations';

    const FIELD_ID = 'id';
    const FIELD_PLATFORM = 'platform';
    const FIELD_PLATFORM_ACCOUNT_ID = 'platform_account_id';
    const FIELD_PLATFORM_CAMPAIGN_ID = 'platform_campaign_id';
    const FIELD_LOCATION_ID = 'location_id';
    const FIELD_TARGETED = 'targeted';

    const RELATION_PLATFORM_LOCATION = 'platformLocation';

    const TARGETED_INCLUDE = 1;
    const TARGETED_EXCLUDE = 0;
    const TARGETED_NONE = -1;

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];

    /**
     * @return MorphTo
     */
    public function platformLocation(): MorphTo
    {
        return $this->morphTo(
            __FUNCTION__,
            self::FIELD_PLATFORM,
            self::FIELD_LOCATION_ID,
            'location_id'
        );
    }
}
