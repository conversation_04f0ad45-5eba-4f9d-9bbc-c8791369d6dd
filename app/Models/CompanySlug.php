<?php

namespace App\Models;

use App\Models\Odin\Company;
use App\Services\CompanySlugService;
use Database\Factories\Odin\CompanySlugFactory;
use Illuminate\Database\Eloquent\Factories\Factory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Carbon;

/**
 * @property int id
 * @property int company_id
 * @property string slug
 * @property int|null redirect_company_slug_id
 * @property Carbon created_at
 * @property Carbon updated_at
 * @property-read Company company
 * @property-read CompanySlug $redirectCompanySlug
 */
class CompanySlug extends BaseModel
{
    use HasFactory;

    const TABLE                                 = 'company_slugs';

    const FIELD_ID                              = 'id';
    const FIELD_COMPANY_ID                      = 'company_id';
    const FIELD_SLUG                            = 'slug';
    const FIELD_REDIRECT_COMPANY_SLUG_ID        = 'redirect_company_slug_id';
    const FIELD_CREATED_AT                      = 'created_at';
    const FIELD_UPDATED_AT                      = 'updated_at';

    const RELATION_COMPANY                      = 'company';
    const RELATION_REDIRECT_COMPANY_SLUG        = 'redirect_company_slug';

    protected $table                            = self::TABLE;

    protected $fillable                         = [
        self::FIELD_COMPANY_ID,
        self::FIELD_SLUG,
        self::FIELD_REDIRECT_COMPANY_SLUG_ID,
    ];

    public static function boot(): void
    {
        parent::boot();

        /** @var CompanySlugService $companySlugService */
        $companySlugService = app(CompanySlugService::class);

        static::creating(
            function (self $model) use ($companySlugService) {
                $model->{self::FIELD_SLUG} = $companySlugService->setSlug($model->{self::FIELD_SLUG}, true);
            }
        );

        static::created(
            function (self $model) use ($companySlugService) {
                $companySlugService->redirectPreviousSlug($model);
            }
        );
    }

    /**
     * Create a new factory instance for the model.
     *
     * @return Factory
     */
    protected static function newFactory(): Factory
    {
        return CompanySlugFactory::new();
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID);
    }

    public function redirectCompanySlug(): HasOne
    {
        return $this->hasOne(CompanySlug::class, self::FIELD_REDIRECT_COMPANY_SLUG_ID);
    }
}
