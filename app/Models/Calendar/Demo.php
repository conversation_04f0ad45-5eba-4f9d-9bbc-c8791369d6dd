<?php

namespace App\Models\Calendar;

use App\Events\Calendar\DemoCreated;
use App\Models\BaseModel;
use App\Models\Odin\Company;
use App\Models\User;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Collection;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * @property int $id
 *
 * @property int $calendar_event_id
 * @property string $status
 * @property string $note
 * @property int $user_id
 * @property int $company_id
 * @property-read ?Company $company
 *
 * @property-read Collection<CalendarEventAttendee> $attendees
 * @property-read User $user
 * @property-read CalendarEvent $calendarEvent
 */
class Demo extends BaseModel
{
    use SoftDeletes, LogsActivity, HasFactory;

    const string TABLE = 'demos';

    const string FIELD_ID = 'id';

    const string FIELD_CALENDAR_EVENT_ID = 'calendar_event_id';
    const string FIELD_STATUS            = 'status';
    const string FIELD_NOTE              = 'note';
    const string FIELD_USER_ID           = 'user_id';
    const string FIELD_COMPANY_ID        = 'company_id';

    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';
    const string FIELD_DELETED_AT = 'deleted_at';

    const string RELATION_CALENDAR_EVENT = 'calendarEvent';
    const string RELATION_COMPANY        = 'company';

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $table = self::TABLE;

    protected $dispatchesEvents = [
        'created' => DemoCreated::class,
    ];

    /**
     * @return BelongsTo
     */
    public function calendarEvent(): BelongsTo
    {
        return $this->belongsTo(CalendarEvent::class, self::FIELD_CALENDAR_EVENT_ID, CalendarEvent::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, self::FIELD_USER_ID, User::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }

    public function attendees(): HasMany
    {
        return $this->hasMany(CalendarEventAttendee::class, self::FIELD_CALENDAR_EVENT_ID, CalendarEventAttendee::FIELD_CALENDAR_EVENT_ID);
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->useLogName(self::TABLE)
            ->logAll()
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
}
