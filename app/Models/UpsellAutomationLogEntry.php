<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;

/**
 * Class UpsellAutomationLogEntry
 *
 * @package App\Models
 *
 * @property integer $id
 * @property string $log_id
 * @property string $type
 * @property integer $consumer_product_id
 * @property float $old_revenue
 * @property float $new_revenue
 * @property float $difference
 * @property array $data
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class UpsellAutomationLogEntry extends Model
{
    const string TABLE = 'upsell_automation_log_entries';

    const string FIELD_ID                   = 'id';
    const string FIELD_LOG_ID               = 'log_id';
    const string FIELD_TYPE                 = 'type';
    const string FIELD_STATUS               = 'status';
    const string FIELD_ENTRY                = 'entry';
    const string FIELD_CONSUMER_PRODUCT_ID  = 'consumer_product_id';
    const string FIELD_OLD_REVENUE          = 'old_revenue';
    const string FIELD_NEW_REVENUE          = 'new_revenue';
    const string FIELD_DIFFERENCE           = 'difference';
    const string FIELD_DATA                 = 'data';

    const string FIELD_CREATED_AT           = 'created_at';
    const string FIELD_UPDATED_AT           = 'updated_at';

    const string TYPE_INFO                  = 'info';
    const string TYPE_CONSUMER_PRODUCT      = 'cp_log';

    const string STATUS_UPSOLD              = 'upsold';
    const string STATUS_TEST_UPSOLD         = 'test_upsold';
    const string STATUS_NONE_AVAILABLE      = 'none_available';
    const string STATUS_SKIPPED             = 'skipped';
    const string STATUS_REV_DECREASE        = 'rev_decrease';
    const string STATUS_ERROR               = 'error';


    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_DATA => 'array',
    ];
}
