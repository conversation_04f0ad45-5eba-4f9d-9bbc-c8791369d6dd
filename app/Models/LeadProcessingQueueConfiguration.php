<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Support\Collection;

/**
 * @property int                       $id
 * @property string                    $name
 * @property string                    $primary_status
 * @property boolean                   $last_round
 * @property Carbon                    $created_at
 * @property Carbon                    $updated_at
 *
 * @property-read Collection|LeadProcessingTeam[] $teams
 */
class LeadProcessingQueueConfiguration extends BaseModel
{
    const string TABLE = 'lead_processing_queue_configurations';

    const string FIELD_ID             = 'id';
    const string FIELD_NAME           = 'name';
    const string FIELD_PRIMARY_STATUS = 'primary_status';
    const string FIELD_LAST_ROUND     = 'last_round';

    const string STATUS_INITIAL            = 'initial';
    const string STATUS_PENDING_REVIEW     = 'pending_review';
    const string STATUS_UNDER_REVIEW       = 'under_review';
    const string STATUS_AGED               = 'aged';
    const string STATUS_AGED_NO_LIMIT_ONLY = 'aged_no_limit_only';
    const string STATUS_AFFILIATE          = 'affiliate';

    const string RELATION_TEAMS = 'teams';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * Defines the relationship with the lead processing teams.
     *
     * @return HasMany
     */
    public function teams(): HasMany
    {
        return $this->hasMany(LeadProcessingTeam::class, LeadProcessingTeam::FIELD_PRIMARY_QUEUE_CONFIGURATION_ID, self::FIELD_ID);
    }

    /**
     * @return MorphMany
     */
    public function alerts(): MorphMany
    {
        return $this->morphMany(Alert::class, 'alertable');
    }
}
