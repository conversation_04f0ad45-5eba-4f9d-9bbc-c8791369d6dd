<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $name
 *
 * @property-read Action $action
 * @property-read User $user
 */
class ActionTag extends Model
{
    const FIELD_ID = 'id';
    const FIELD_ACTION_ID = 'action_id';
    const FIELD_USER_ID = 'user_id';

    const RELATION_USER = 'user';
    const RELATION_ACTION = 'action';

    const TABLE = 'action_tags';

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function action(): BelongsTo
    {
        return $this->belongsTo(Action::class, Action::FIELD_ID, self::FIELD_ACTION_ID);
    }

    /**
     * @return HasOne
     */
    public function user(): HasOne
    {
        return $this->hasOne(User::class, User::FIELD_ID, self::FIELD_USER_ID);
    }

}
