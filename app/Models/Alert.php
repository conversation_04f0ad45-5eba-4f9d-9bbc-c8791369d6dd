<?php

namespace App\Models;

use App\Enums\Alert\AlertType;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Arr;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property string $alertable_type
 * @property int $alertable_id
 * @property AlertType $type
 * @property bool $active
 * @property Carbon|null $last_alerted_at
 * @property array|null $payload
 *
 * @property-read Collection<AlertRecipient> $recipients
 */
class Alert extends Model
{
    const string TABLE = 'alerts';

    const string FIELD_ID = 'id';
    const string FIELD_ALERTABLE_TYPE = 'alertable_type';
    const string FIELD_ALERTABLE_ID = 'alertable_id';
    const string FIELD_TYPE = 'type';
    const string FIELD_ACTIVE = 'active';
    const string FIELD_LAST_ALERTED_AT = 'last_alerted_at';
    const string FIELD_PAYLOAD = 'payload';

    const string PAYLOAD_KEY_CAMPAIGN_USAGE_THRESHOLD = 'campaign_usage_threshold';

    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_PAYLOAD => 'array',
        self::FIELD_TYPE => AlertType::class,
        self::FIELD_LAST_ALERTED_AT => 'datetime'
    ];

    /**
     * @param string $key
     * @param mixed|null $default
     *
     * @return mixed
     */
    public function getPayloadByKey(string $key, mixed $default = null): mixed
    {
        if (!$this->payload) {
            return $default;
        }

        return Arr::get($this->payload, $key, $default);
    }

    /**
     * @return HasMany
     */
    public function recipients(): HasMany
    {
        return $this->hasMany(AlertRecipient::class);
    }
}
