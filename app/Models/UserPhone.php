<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property int $phone_id
 * @property int $user_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read User $user
 * @property-read Phone $phone
 */
class UserPhone extends BaseModel
{
    use SoftDeletes, HasFactory;

    const TABLE = 'user_phones';

    const FIELD_ID             = 'id';
    const FIELD_PHONE_ID       = 'phone_id';
    const FIELD_USER_ID        = 'user_id';
    const FIELD_DELETED_AT     = 'deleted_at';
    const FIELD_CREATED_AT     = 'created_at';


    const RELATION_USER  = 'user';
    const RELATION_PHONE = 'phone';

    protected $table   = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    public function user(): HasOne
    {
        return $this->hasOne(User::class, User::FIELD_ID, self::FIELD_USER_ID);
    }

    public function phone(): HasOne
    {
        return $this->hasOne(Phone::class, Phone::FIELD_ID, self::FIELD_PHONE_ID);
    }
}
