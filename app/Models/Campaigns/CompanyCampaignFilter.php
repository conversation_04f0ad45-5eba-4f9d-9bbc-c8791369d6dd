<?php

namespace App\Models\Campaigns;

use App\Enums\Campaigns\CampaignFilterOperator;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * @property int $id
 * @property int $company_campaign_id
 * @property string $key
 * @property CampaignFilterOperator $operator
 * @property string $value
 *
 * @property-read CompanyCampaign $companyCampaign
 */
class CompanyCampaignFilter extends Model
{
    use LogsActivity;

    const string TABLE = 'company_campaign_filters';

    const string FIELD_ID = 'id';
    const string FIELD_COMPANY_CAMPAIGN_ID = 'company_campaign_id';
    const string FIELD_KEY = 'key';
    const string FIELD_OPERATOR = 'operator';
    const string FIELD_VALUE = 'value';

    protected $table = self::TABLE;
    protected $guarded = [self::FIELD_ID];
    protected $casts =[
        self::FIELD_OPERATOR => CampaignFilterOperator::class
    ];

    /**
     * @return BelongsTo
     */
    public function companyCampaign(): BelongsTo
    {
        return $this->belongsTo(CompanyCampaign::class);
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([
                self::FIELD_KEY,
                self::FIELD_OPERATOR,
                self::FIELD_VALUE,
            ])
            ->useLogName('company_campaign_filter')
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
}
