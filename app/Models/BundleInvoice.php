<?php

namespace App\Models;

use App\Enums\BundleInvoiceStatus;
use App\Models\Billing\Invoice;
use App\Models\Legacy\EloquentInvoice;
use App\Models\Odin\Company;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Database\Eloquent\Collection;

/**
 * Class BundleInvoice
 *
 * @package App\Models
 *
 * @property integer $id
 * @property integer $bundle_id
 * @property integer $company_id
 * @property string $name
 * @property string $note
 * @property float $cost
 * @property float $credit
 * @property BundleInvoiceStatus|int $status
 * @property Carbon $issued_at
 * @property Carbon $approved_at
 * @property Carbon $denied_at
 * @property Carbon $paid_at
 * @property Carbon $cancelled_at
 * @property string $issued_by
 * @property string $approved_by
 * @property string $denied_by
 * @property string $cancelled_by
 * @property Carbon $deleted_at
 * @property int|null $payable_invoice_id
 * @property-read Bundle $bundle
 * @property-read Company $company
 * @property-read Collection|null $history
 * @property-read User|null $issuedBy
 * @property-read User|null $approvedBy
 * @property-read User|null $deniedBy
 * @property-read User|null $cancelledBy
 */
class BundleInvoice extends Model
{
    use HasFactory, SoftDeletes;

    const TABLE = 'bundle_invoices';
    const FIELD_ID = 'id';
    const FIELD_BUNDLE_ID = 'bundle_id';
    const FIELD_COMPANY_ID = 'company_id';
    const FIELD_NOTE = 'note';
    const FIELD_COST = 'cost';
    const FIELD_CREDIT = 'credit';
    const FIELD_ISSUED_AT = 'issued_at';
    const FIELD_APPROVED_AT = 'approved_at';
    const FIELD_DENIED_AT = 'denied_at';
    const FIELD_PAID_AT = 'paid_at';
    const FIELD_CANCELLED_AT = 'cancelled_at';
    const FIELD_ISSUED_BY = 'issued_by';
    const FIELD_APPROVED_BY = 'approved_by';
    const FIELD_DENIED_BY = 'denied_by';
    const FIELD_CANCELLED_BY = 'cancelled_by';
    const FIELD_DELETED_AT = 'deleted_at';
    const FIELD_FAILED_AT = 'failed_at';
    const FIELD_FAIL_REASON = 'fail_reason';
    const FIELD_STATUS = 'status';
    const FIELD_BILLING_VERSION = 'billing_version';

    const FIELD_PAYABLE_INVOICE_ID = 'payable_invoice_id';
    const FIELD_PAYABLE_INVOICE_URL = 'payable_invoice_url';
    const string FIELD_PAYLOAD = 'payload';

    const TRANSITION_ISSUE = 'issue';
    const TRANSITION_PAID = 'paid';
    const TRANSITION_APPROVE = 'approve';
    const TRANSITION_DENY = 'deny';
    const TRANSITION_CANCEL = 'cancel';
    const TRANSITION_PENDING = 'pending';
    const TRANSITION_FAIL = 'fail';
    const RELATION_BUNDLE = 'bundle';
    const RELATION_HISTORY = 'history';
    const RELATION_COMPANY = 'company';
    const RELATION_ISSUED_BY = 'issuedBy';
    const RELATION_APPROVED_BY = 'approvedBy';
    const RELATION_DENIED_BY = 'deniedBy';
    const RELATION_CANCELLED_BY = 'cancelledBy';
    const RELATION_LEGACY_INVOICE = 'legacyInvoice';

    protected $casts = [
        self::FIELD_STATUS  => BundleInvoiceStatus::class,
        self::FIELD_PAYLOAD => 'array'
    ];

    protected $guarded = [ self::FIELD_ID ];

    /**
     * @return BelongsTo
     */
    public function bundle(): BelongsTo
    {
        return $this->belongsTo(Bundle::class, self::FIELD_BUNDLE_ID, Bundle::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function history(): HasMany
    {
        return $this->hasMany(BundleInvoiceHistory::class, BundleInvoiceHistory::FIELD_BUNDLE_INVOICE_ID);
    }

    /**
     * @return HasOne
     */
    public function company(): HasOne
    {
        return $this->hasOne(Company::class, Company::FIELD_ID, self::FIELD_COMPANY_ID);
    }

    /**
     * @return HasOne
     */
    public function issuedBy(): HasOne
    {
        return $this->hasOne(User::class, User::FIELD_ID, self::FIELD_ISSUED_BY);
    }


    /**
     * @return HasOne
     */
    public function legacyInvoice(): HasOne
    {
        return $this->hasOne(EloquentInvoice::class, EloquentInvoice::INVOICE_ID, self::FIELD_PAYABLE_INVOICE_ID);
    }

    /**
     * @return HasOne
     */
    public function invoice(): HasOne
    {
        return $this->hasOne(Invoice::class, Invoice::FIELD_ID, self::FIELD_PAYABLE_INVOICE_ID);
    }

    /**
     * @return HasOne
     */
    public function approvedBy(): HasOne
    {
        return $this->hasOne(User::class, User::FIELD_ID, self::FIELD_APPROVED_BY);
    }


    /**
     * @return HasOne
     */
    public function deniedBy(): HasOne
    {
        return $this->hasOne(User::class, User::FIELD_ID, self::FIELD_DENIED_BY);
    }

    /**
     * @return HasOne
     */
    public function cancelledBy(): HasOne
    {
        return $this->hasOne(User::class, User::FIELD_ID, self::FIELD_CANCELLED_BY);
    }

    /**
     * Set the invoice cost and credit to the parent bundle's respective values
     *
     * @return void
     */
    protected static function booted(): void
    {
        parent::booted();
        static::created(function (BundleInvoice $invoice) {
            $invoice->{self::FIELD_COST} = $invoice->bundle->cost;
            $invoice->{self::FIELD_CREDIT} = $invoice->bundle->credit;
            $invoice->save();
        });
    }
}
