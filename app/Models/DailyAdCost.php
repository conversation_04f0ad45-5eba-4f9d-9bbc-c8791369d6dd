<?php

namespace App\Models;

use App\Enums\Advertising\Advertiser;
use App\Models\Legacy\Location;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class DailyAdCost
 *
 * @property int $id
 * @property int $industry_id
 * @property int $industry_service_id
 * @property int $location_id
 * @property int $advertiser
 * @property int $platform
 * @property Carbon $date
 * @property float $cost
 * @property int $advertising_account_id
 * @property int $daily_ad_cost_account_id
 * @property array $data
 *
 * @property-read Location $location
 * @property-read AdvertisingAccount $advertisingAccount
 * @property-read DailyAdCostAccount $dailyAdCostAccount
 */
class DailyAdCost extends BaseModel
{
    const string TABLE = 'daily_ad_costs';

    const string FIELD_ID          = 'id';
    const string FIELD_INDUSTRY_ID = 'industry_id';
    const string FIELD_INDUSTRY_SERVICE_ID = 'industry_service_id';
    const string FIELD_LOCATION_ID = 'location_id';
    const string FIELD_ADVERTISER  = 'advertiser';
    const string FIELD_PLATFORM    = 'platform';
    const string FIELD_DATE        = 'date';
    const string FIELD_COST        = 'cost';
    const string FIELD_DATA        = 'data';
    const string FIELD_ADVERTISING_ACCOUNT_ID = 'advertising_account_id';
    const string FIELD_DAILY_AD_COST_ACCOUNT_ID = 'daily_ad_cost_account_id';

    const string RELATION_LOCATION = 'location';
    const string RELATION_ADVERTISING_ACCOUNT = 'advertisingAccount';
    const string RELATION_DAILY_AD_COST_ACCOUNT = 'dailyAdCostAccount';

    // JSON Data payload keys
    const string DATA_GOOGLE_ACCOUNT_ID     = 'google_acct_id';
    const string DATA_MICROSOFT_ACCOUNT_ID  = 'microsoft_acct_id';
    const string DATA_META_ACCOUNT_ID       = 'meta_acct_id';
    const string DATA_COST_SHARE            = 'cost_share';

    protected $casts = [
        self::FIELD_ADVERTISER => Advertiser::class,
        self::FIELD_COST       => 'decimal:2',
        self::FIELD_DATA       => 'array',
    ];

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class, self::FIELD_LOCATION_ID, Location::ID);
    }

    /**
     * @return BelongsTo
     */
    public function advertisingAccount(): BelongsTo
    {
        return $this->belongsTo(AdvertisingAccount::class, self::FIELD_ADVERTISING_ACCOUNT_ID, AdvertisingAccount::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function dailyAdCostAccount(): BelongsTo
    {
        return $this->belongsTo(DailyAdCostAccount::class, self::FIELD_DAILY_AD_COST_ACCOUNT_ID, DailyAdCostAccount::FIELD_ID);
    }
}
