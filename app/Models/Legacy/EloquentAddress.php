<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;
use Database\Factories\EloquentAddressFactory;

/**
 * Class EloquentAddress
 *
 * @property int $addressid
 * @property string $address1
 * @property string $address2
 * @property string $city
 * @property string $state
 * @property string $zipcode
 * @property string $phone
 * @property string $country
 * @property string $longitude
 * @property string $latitude
 *
 * @property-read Collection $mapLocations
 */
class EloquentAddress extends LegacyModel
{
    use HasFactory;

    const TABLE = 'tbladdress';

    const ID         = self::ADDRESS_ID;
    const ADDRESS_ID = 'addressid';
    const ADDRESS1               = 'address1';
    const ADDRESS2               = 'address2';
    const CITY                   = 'city';
    const STATE                  = 'state';
    const STATE_ABBR             = 'state';
    const ZIP_CODE               = 'zipcode';
    const COUNTRY                = 'country';
    const PHONE                  = 'phone';
    const FAX                    = 'fax';
    const LONGITUDE              = 'longitude';
    const LATITUDE               = 'latitude';

    const RELATION_MAP_LOCATIONS = 'mapLocations';

    protected $table      = self::TABLE;
    protected $primaryKey = self::ID;
    public    $timestamps = false;

    protected $guarded = [self::ID];

    /**
     * @return EloquentAddressFactory
     */
    protected static function newFactory(): EloquentAddressFactory
    {
        return EloquentAddressFactory::new();
    }

    /**
     * @return string
     */
    function getFullAddress(): string
    {
        return $this->getFullStreetAddress() . ", " . $this->city . " " . $this->state . ", " . $this->zipcode;
    }

    /**
     * Gets Concatenation of address1 and address2
     * @return string
     */
    public function getFullStreetAddress(): string
    {
        $addressString = $this->address1;
        if (!empty($this->address2)) {
            $addressString .= ", " . $this->address2;
        }
        return $addressString;
    }

    /**
     * @return HasMany
     */
    public function mapLocations(): HasMany
    {
        return $this->hasMany(Location::class, Location::ZIP_CODE, self::ZIP_CODE);
    }

    /**
     * @return Location|null
     */
    public function location(): ?Location
    {
        return $this->mapLocations->where(Location::TYPE, Location::TYPE_ZIP_CODE)->first();
    }

    /**
     * Returns phone number without white-space, non-numeric chars, or leading "+1"
     * @return string
     */
    public function trimmedPhoneNumber(): string
    {
        $phone = $this->phone;
        $phone=preg_replace("/[^0-9]/", "", $phone);
        if (substr($phone,0,1)=="1" && strlen($phone)>10) {
            $phone=substr($phone,1,strlen($phone));
        }
        return $phone;
    }
}
