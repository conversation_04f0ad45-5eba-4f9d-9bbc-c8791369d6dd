<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class LeadCampaignCrmIntegration
 *
 * @package App
 *
 * @property int $id
 * @property int $lead_campaign_id
 * @property int $crm_integration_id
 *
 */
class LeadCampaignCrmIntegration extends LegacyModel
{
    const TABLE = 'tbl_lead_campaign_crm_integrations';
    protected $table = self::TABLE;

    const ID = 'id';
    const LEAD_CAMPAIGN_ID = 'lead_campaign_id';
    const CRM_INTEGRATION_ID = 'crm_integration_id';

    const RELATION_LEAD_CAMPAIGN = 'leadCampaign';
    const RELATION_CRM_INTEGRATION = 'crmIntegration';

    protected $guarded = [self::ID];

    public $timestamps = false;

    /**
     * @return BelongsTo
     */
    public function leadCampaign()
    {
        return $this->belongsTo(
            LeadCampaign::class,
            self::LEAD_CAMPAIGN_ID,
            LeadCampaign::ID
        );
    }

    /**
     * @return BelongsTo
     */
    public function crmIntegration()
    {
        return $this->belongsTo(
            CrmIntegration::class,
            self::CRM_INTEGRATION_ID,
            CrmIntegration::ID
        );
    }
}
