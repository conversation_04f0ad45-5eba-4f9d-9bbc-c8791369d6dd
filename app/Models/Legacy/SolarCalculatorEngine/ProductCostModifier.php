<?php

namespace App\Models\Legacy\SolarCalculatorEngine;

use App\Models\Legacy\LegacyModel;

/**
 * Class ProductCostModifier
 * @package App
 * @property string $reference
 * @property string $manufacturer_reference
 * @property string $product_type
 * @property float $multiplier
 */
class ProductCostModifier extends LegacyModel
{
    const TABLE = 'product_cost_modifiers';

    const FIELD_REFERENCE              = 'reference';
    const FIELD_MANUFACTURER_REFERENCE = 'manufacturer_reference';
    const FIELD_PRODUCT_TYPE           = 'product_type';
    const FIELD_MULTIPLIER             = 'multiplier';

    const PRODUCT_TYPE_PANELS    = 'panels';
    const PRODUCT_TYPE_INVERTERS = 'inverters';
    const PRODUCT_TYPE_BATTERIES = 'batteries';

    public $primaryKey = self::FIELD_REFERENCE;
}
