<?php

namespace App\Models\Legacy;

/**
 * Class LeadCategory
 *
 * @property string $name
 */
class LeadCategory extends LegacyModel
{
    const TABLE = 'tbl_lead_categories';
    const RESIDENTIAL = 1;
    const COMMERCIAL = 2;

    const ID = 'id';
    const NAME = 'name';
    const KEY_VALUE = 'key_value';
    const ORDINAL_POSITION = 'ordinal_position';
    const STATUS = 'status';
    const LEGACY_ALIAS = 'legacy_alias';

    const KEY_VALUE_RESIDENTIAL = "RESIDENTIAL";
    const KEY_VALUE_COMMERCIAL = "COMMERCIAL";
    const KEY_VALUE_INDUSTRIAL = "INDUSTRIAL";

    const STATUS_ENABLE = 1;
    const STATUS_DISABLE = 0;

    const CATEGORY_NAME_RESIDENTIAL = 'Residential';
    const CATEGORY_NAME_COMMERCIAL = 'Commercial';

    protected $table      = self::TABLE;
    public    $timestamps = false;
}
