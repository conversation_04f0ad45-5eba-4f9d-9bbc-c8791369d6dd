<?php

namespace App\Models\Legacy;

use Carbon\Carbon;

/**
 * Class LeadPurchasingLog
 *
 * @property int $id
 * @property int $lead_campaign_id
 * @property int $company_id
 * @property int $user_id
 * @property string $event
 * @property string $action
 * @property int|Carbon $pause_lead_expiry
 * @property string $pause_lead_reason
 * @property Carbon|int $created_at
 * @property Carbon|int $updated_at
 */
class LeadPurchasingLog extends LegacyModel
{
    const ID = 'id';
    const LEAD_CAMPAIGN_ID = 'lead_campaign_id';
    const COMPANY_ID = 'company_id';
    const USER_ID = 'user_id';
    const UPDATED_AT = 'updated_at';
    const CREATED_AT = 'created_at';
    const EVENT = 'event';
    const ACTION  = 'action';
    const PAUSE_LEAD_EXPIRY = "pause_lead_expiry";
    const PAUSE_LEAD_REASON = "pause_lead_reason";

    const VALUE_EVENT_FROM_ADMIN = 1;
    const VALUE_EVENT_FROM_CLIENT = 2;
    const VALUE_EVENT_FROM_AUTO = 3;
    const VALUE_EVENT_FROM_EMAIL = 4;

    const VALUE_EVENT_ADMIN_PAUSE_LEAD = 'admin_pause_lead';
    const VALUE_EVENT_ADMIN_RESUME_LEAD = 'admin_resume_lead';
    const VALUE_EVENT_ADMIN_LOCK_LEAD = 'admin_lock_lead';
    const VALUE_EVENT_CLIENT_PAUSE_LEAD = 'client_pause_lead';
    const VALUE_EVENT_CLIENT_RESUME_LEAD = 'client_resume_lead';
    const VALUE_EVENT_AUTO_RESUME_LEAD = 'auto_resume_lead';
    const VALUE_EVENT_EMAIL_RESUME_LEAD = 'email_resume_lead';
    const VALUE_EVENT_ADMIN_ACTIVE_CAMPAIGN = 'admin_active_campaign';
    const VALUE_EVENT_ADMIN_INACTIVE_CAMPAIGN = 'admin_inactive_campaign';
    const VALUE_EVENT_CLIENT_ACTIVE_CAMPAIGN = 'client_active_campaign';
    const VALUE_EVENT_CLIENT_INACTIVE_CAMPAIGN = 'client_inactive_campaign';
    const VALUE_EVENT_AUTO_ACTIVE_CAMPAIGN = 'auto_active_campaign';

    const VALUE_ACTION_PAUSE = 'PAUSE';
    const VALUE_ACTION_RESUME = 'RESUME';
    const VALUE_ACTION_LOCK = 'LOCK';
    const VALUE_ACTION_ACTIVE_CAMPAIGN = 'ACTIVE_CAMPAIGN';
    const VALUE_ACTION_INACTIVE_CAMPAIGN = 'INACTIVE_CAMPAIGN';

    const RELATION_COMPANY = 'company';
    const RELATION_LEAD_CAMPAIGN = 'leadCampaign';
    const RELATION_USER = 'user';

    const TABLE = 'lead_purchasing_log';

    protected $table = self::TABLE;

    protected $guarded = [self::ID];
}
