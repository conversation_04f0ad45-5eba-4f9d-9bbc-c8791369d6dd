<?php

namespace App\Models\Legacy;

use Carbon\Carbon;

/**
 * Class OverBudgetLog
 *
 * @package App
 *
 * @property int $id
 * @property int $lead_campaign_id
 * @property int $company_id
 * @property float $spent
 * @property int|Carbon $spent_from
 * @property float $usage_percent
 * @property string $budget_context
 * @property string $budget_type
 * @property float $budget
 * @property string|array $content
 * @property Carbon|int $created_at
 * @property Carbon|int $updated_at
 */
class OverBudgetLog extends LegacyModel
{
    const ID = 'id';
    const LEAD_CAMPAIGN_ID = 'lead_campaign_id';
    const COMPANY_ID = 'company_id';
    const SPENT = 'spent';
    const SPENT_FROM = 'spent_from';
    const USAGE_PERCENT = 'usage_percent';
    const BUDGET = 'budget';
    const BUDGET_CONTEXT = 'budget_context';
    const BUDGET_TYPE = 'budget_type';

    const UPDATED_AT = 'updated_at';
    const CREATED_AT = 'created_at';

    const VALUE_BUDGET_CONTEXT_CAMPAIGN = 'Campaign';
    const VALUE_BUDGET_TYPE_SPEND = 'Daily Spend';
    const VALUE_BUDGET_TYPE_LEADS = 'Daily Leads';

    const RELATION_COMPANY = 'company';
    const RELATION_LEAD_CAMPAIGN = 'leadCampaign';

    const TABLE = 'over_budget_log';

    protected $table = self::TABLE;

    protected $guarded = [self::ID];
}
