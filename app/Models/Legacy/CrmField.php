<?php

namespace App\Models\Legacy;

use App\Collections\Collection;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class CrmField
 *
 * @package App
 *
 * @property int $id
 * @property int $crm_integration_field_id
 * @property string $name
 * @property string $display_name
 * @property string $default_field_value
 * @property int $mandatory
 * @property int $status
 * @property int $ordinal_position
 *
 * @property-read CrmIntegrationField[]|Collection $crm_integration_fields
 */
class CrmField extends LegacyModel
{
    const TABLE = 'tbl_crm_fields';
    protected $table = self::TABLE;

    const ID = 'id';
    const CRM_INTEGRATION_FIELD_ID = 'crm_integration_field_id';
    const NAME = 'name';
    const DISPLAY_NAME = 'display_name';
    const DEFAULT_FIELD_VALUE = 'default_field_value';
    const MANDATORY = 'mandatory';
    const STATUS = 'status';
    const ORDINAL_POSITION = 'ordinal_position';

    protected $guarded = [self::ID];

    public $timestamps = false;

    const IS_MANDATORY = 1;

    /**
     * @return BelongsTo
     */
    public function crmIntegrationFields()
    {
        return $this->belongsTo(CrmIntegrationField::class, self::CRM_INTEGRATION_FIELD_ID, CrmIntegrationField::ID);
    }

    /**
     * @param array $data
     * @return CrmField
     */
    public static function createInstanceFromData($data = array())
    {
        $self = new CrmField();

        $self->id = 0;
        $self->name = $data['name'];
        $self->display_name = $data['display_name'];
        $self->mandatory = $data['mandatory'] ? 1 : 0;
        $self->ordinal_position = $data['ordinal_position'];
        $self->status = $data['status'] ? 1 : 0;
        $self->default_field_value = $data['default_field_value'] ?? "";
        $self->crm_integration_field_id = $data['crm_integration_field_id'] ?? 0;

        return $self;
    }
}
