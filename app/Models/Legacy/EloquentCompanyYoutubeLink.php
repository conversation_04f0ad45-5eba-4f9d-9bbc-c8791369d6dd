<?php

namespace App\Models\Legacy;

/**
 * @property int $id
 * @property int $company_id
 * @property int $location_id
 */
class EloquentCompanyYoutubeLink extends LegacyModel
{
    const TABLE = 'company_youtube_links';

    const ID          = 'id';
    const COMPANY_ID  = 'company_id';
    const LINK        = 'link';

    public $timestamps = false;

    protected $table = self::TABLE;

    protected $fillable = [
        self::ID,
        self::COMPANY_ID,
        self::LINK,
    ];
}
