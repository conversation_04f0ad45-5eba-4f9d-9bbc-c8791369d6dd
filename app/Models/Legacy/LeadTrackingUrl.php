<?php

namespace App\Models\Legacy;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class LeadTrackingUrl
 * @package App
 * @property int $id
 * @property int $lead_id
 * @property string|null $url_start
 * @property string|null $url_convert
 * @property string|null $source
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property string|null $start_domain
 * @property string|null $convert_domain
 * @property EloquentQuote|null $lead
 */
class LeadTrackingUrl extends LegacyModel
{
    use HasFactory;

    const ID             = 'id';
    const LEAD_ID        = 'lead_id';
    const URL_START      = 'url_start';
    const URL_CONVERT    = 'url_convert';
    const SOURCE         = 'source';
    const CREATED_AT     = 'created_at';
    const UPDATED_AT     = 'updated_at';
    const START_DOMAIN   = 'start_domain';
    const CONVERT_DOMAIN = 'convert_domain';

    const MAX_URL_LENGTH    = 1024;
    const MAX_SOURCE_LENGTH = 255;

    const TABLE = 'lead_tracking_urls';

    const ORIGIN_TO_DOMAIN_MAP
        = [
            'sr' => ['solarreviews.com', 'd3tw7nt15cf0n7.cloudfront.net'],
            'se' => [
                'solar-estimate.org', 'd3skrlqoqnneot.cloudfront.net',
                'solarestimate.com', 'd1kz20opbqrs5y.cloudfront.net',
            ],
            'cmb' => ['cutmybill.com'],
            'ws' => ['mysolar.com', 'mysolarinstaller.com'],
            'spr' => ['solarpowerrocks.com', 'd3jz7auj2pm6bs.cloudfront.net'],
            'sn' => ['sunnumber.com'],
        ];

    protected $table = 'lead_tracking_urls';

    protected $fillable
        = [
            self::URL_START,
            self::URL_CONVERT,
            self::SOURCE,
        ];

    /**
     * @return BelongsTo
     */
    public function lead(): BelongsTo
    {
        return $this->belongsTo(EloquentQuote::class, EloquentQuote::ID, self::LEAD_ID);
    }

    /**
     * Generate domain from tracking urls and save in related domain fields
     *
     * @return $this
     */
    public function fillDomains(): self
    {
        $startDomain   = $this->getDomainFromUrl($this->url_start);
        $convertDomain = $this->getDomainFromUrl($this->url_convert);

        $this->start_domain   = $startDomain;
        $this->convert_domain = $convertDomain ? $convertDomain : $startDomain;

        return $this;
    }

    /**
     * Get domain name from url
     *
     * @param $url
     *
     * @return string|null
     */
    function getDomainFromUrl($url): ?string
    {
        if (empty($url)) {
            return null;
        }

        return parse_url($url, PHP_URL_HOST);
    }
}
