<?php

namespace App\Models\Legacy;

use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * Class Crm
 *
 * @package App
 *
 * @property int $id
 * @property string $name
 * @property string $display_name
 * @property int $ordinal_position
 * @property int $status
 *
 * @property-read CrmIntegrationField[] $crmIntegrationFields
 *
 * @method Builder active(Builder $query)
 */
class Crm extends LegacyModel
{
    const TABLE = 'tbl_crms';
    protected $table = self::TABLE;

    const ID = 'id';
    const NAME = 'name';
    const DISPLAY_NAME = 'display_name';
    const ORDINAL_POSITION = 'ordinal_position';
    const STATUS = 'status';

    const PIPEDRIVE_NAME = 'pipedrive';
    const STANDARD_WEBFORM = 'standard_webform';

    protected $guarded = [self::ID];

    public $timestamps = false;

    const ACTIVE_STATUS = 1;
    const INACTIVE_STATUS = 0;

    const REJECT_FOR_CONTROL_PANEL = ['nrg', 'testcrm', 'comcast_sunrun_custom'];

    /**
     * @return HasMany
     */
    public function crmIntegrationFields()
    {
        return $this->hasMany(CrmIntegrationField::class, CrmIntegrationField::CRM_ID, self::ID);
    }

    /**
     * @param Builder $query
     * @return Builder
     */
    public function scopeActive(Builder $query)
    {
        return $query->where(self::STATUS, self::ACTIVE_STATUS);
    }

    /**
     * @return bool
     */
    public function isPipedrive()
    {
        return $this->name == self::PIPEDRIVE_NAME;
    }

    /**
     * @return bool
     */
    public function isStandardWebform()
    {
        return $this->name == self::STANDARD_WEBFORM;
    }
}
