<?php

namespace App\Models;

use App\Models\Legacy\EloquentQuote;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * Class LeadProcessingPendingReview
 *
 * @property int                $id
 * @property int                $lead_processor_id
 * @property int                $lead_id
 * @property string             $reason
 * @property int                $consumer_product_id
 *
 * @property-read LeadProcessor $leadProcessor
 * @property-read EloquentQuote $lead
 */
class LeadProcessingPendingReview extends BaseModel
{
    const TABLE = 'lead_processing_pending_reviews';

    const FIELD_ID      = 'id';
    const FIELD_LEAD_ID = 'lead_id';
    const FIELD_USER_ID = 'lead_processor_id';
    const FIELD_REASON  = 'reason';
    const FIELD_CONSUMER_PRODUCT_ID = 'consumer_product_id';

    const RELATION_LEAD_PROCESSOR = 'leadProcessor';
    const RELATION_LEAD           = 'lead';
    const RELATION_USER           = 'user';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function leadProcessor(): BelongsTo
    {
        return $this->belongsTo(LeadProcessor::class, self::FIELD_USER_ID, LeadProcessor::FIELD_ID)->withTrashed();
    }

    /**
     * @return BelongsTo
     */
    public function lead(): BelongsTo
    {
        return $this->belongsTo(EloquentQuote::class, self::FIELD_LEAD_ID, EloquentQuote::ID);
    }
}
