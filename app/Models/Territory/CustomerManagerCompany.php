<?php

namespace App\Models\Territory;

use App\Models\BaseModel;
use App\Models\Odin\Company;
use App\Traits\HasActiveStateByDate;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property integer id,
 * @property integer customer_success_manager_id
 * @property integer company_id
 * @property Carbon active_from
 * @property Carbon active_to
 * @property array campaign_event
 * @property Carbon created_at
 * @property Carbon updated_at
 *
 * @property-read CustomerSuccessManager $customerSuccessManager
 * @property-read Company $company
 */
class CustomerManagerCompany extends BaseModel
{
    use HasFactory, HasActiveStateByDate;

    const string TABLE                             = 'customer_manager_companies';
    const string FIELD_ID                          = 'id';
    const string FIELD_CUSTOMER_SUCCESS_MANAGER_ID = 'customer_success_manager_id';
    const string FIELD_COMPANY_ID                  = 'company_id';
    const string FIELD_ACTIVE_FROM                 = 'active_from';
    const string FIELD_ACTIVE_TO                   = 'active_to';
    const string FIELD_CAMPAIGN_EVENT              = 'campaign_event';
    const string FIELD_CREATED_AT                  = 'created_at';
    const string FIELD_UPDATED_AT                  = 'updated_at';
    const string RELATION_CUSTOMER_SUCCESS_MANAGER = 'customerSuccessManager';
    const string RELATION_COMPANY                  = 'company';

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];
    protected $casts   = [
        self::FIELD_CAMPAIGN_EVENT => 'array',
        self::FIELD_ACTIVE_TO      => 'date',
        self::FIELD_ACTIVE_FROM    => 'date',
    ];

    /**
     * @return BelongsTo
     */
    public function customerSuccessManager(): BelongsTo
    {
        return $this->belongsTo(CustomerSuccessManager::class, self::FIELD_CUSTOMER_SUCCESS_MANAGER_ID, CustomerSuccessManager::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }
}
