<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @property int $id
 * @property string $name
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class ConsumerConfigurableFieldCategory extends BaseModel
{
    use SoftDeletes, HasFactory;

    const TABLE = 'consumer_configurable_field_categories';

    const FIELD_ID = 'id';
    const FIELD_NAME = 'name';
    const FIELD_SLUG = 'slug';
    const FIELD_CREATED_AT = 'created_at';
    const FIELD_UPDATED_AT = 'updated_at';

    protected $table = self::TABLE;

    protected $fillable = [
        self::FIELD_NAME,
        self::FIELD_SLUG,
    ];

    /**
     * @return HasMany
     */
    public function industryConsumerFields(): HasMany
    {
        return $this->hasMany(IndustryConsumerField::class, IndustryConsumerField::FIELD_CATEGORY_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function serviceConsumerFields(): HasMany
    {
        return $this->hasMany(ServiceConsumerField::class, ServiceConsumerField::FIELD_CATEGORY_ID, self::FIELD_ID);
    }
}
