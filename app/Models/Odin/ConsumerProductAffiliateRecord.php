<?php

namespace App\Models\Odin;

use App\Models\Affiliates\Affiliate;
use App\Models\Affiliates\Campaign;
use App\Models\BaseModel;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property int $affiliate_id
 * @property int $campaign_id
 * @property string $track_name
 * @property string $track_code
 * @property Collection<ConsumerProduct> $consumerProducts
 * @property-read Affiliate $affiliate
 */
class ConsumerProductAffiliateRecord extends BaseModel
{
    use HasFactory;

    const TABLE = 'consumer_product_affiliate_records';

    const FIELD_ID           = 'id';
    const FIELD_AFFILIATE_ID = 'affiliate_id';
    const FIELD_CAMPAIGN_ID  = 'campaign_id';
    const FIELD_TRACK_NAME   = 'track_name';
    const FIELD_TRACK_CODE   = 'track_code';

    const RELATION_CONSUMER_PRODUCTS = 'consumerProducts';
    const RELATION_AFFILIATE = 'affiliate';
    const RELATION_AFFILIATE_CAMPAIGN = 'affiliateCampaign';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    /**
     * @return HasMany
     */
    public function consumerProducts(): HasMany
    {
        return $this->hasMany(ConsumerProduct::class, ConsumerProduct::RELATION_CONSUMER_PRODUCT_AFFILIATE_RECORD, self::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function affiliate(): BelongsTo
    {
        return $this->belongsTo(Affiliate::class, self::FIELD_AFFILIATE_ID, Affiliate::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function affiliateCampaign(): BelongsTo
    {
        return $this->belongsTo(Campaign::class, self::FIELD_CAMPAIGN_ID, Campaign::FIELD_ID);
    }
}
