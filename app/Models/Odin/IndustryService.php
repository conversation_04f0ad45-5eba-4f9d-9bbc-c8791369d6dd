<?php

namespace App\Models\Odin;

use App\Models\BaseModel;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\MissedProducts\MissedProduct;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Support\Collection;

/**
 * @property int $id
 * @property int $industry_id
 * @property string $name
 * @property string $slug
 * @property boolean $show_on_website
 * @property boolean $show_on_registration
 * @property boolean $show_on_dashboard
 * @property boolean $campaign_filter_enabled
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Industry $industry
 * @property-read Company[] $companies
 * @property-read Collection<ServiceCompanyField> $companyFields
 * @property-read ServiceConsumerField[] $consumerFields
 * @property-read ServiceCompanyReviewField[] $companyReviewFields
 * @property-read Collection<Product> $products
 * @property-read CompanyService[] $companyServices
 * @property-read Collection<ServiceProduct> $serviceProducts
 * @property-read Collection<CompanyCampaign> $companyCampaigns
 */
class IndustryService extends BaseModel
{
    use HasFactory;
    const TABLE = 'industry_services';

    const FIELD_ID              = 'id';
    const FIELD_INDUSTRY_ID     = 'industry_id';
    const FIELD_NAME            = 'name';
    const FIELD_SLUG            = 'slug';
    const string FIELD_SHOW_ON_WEBSITE      = 'show_on_website';
    const string FIELD_SHOW_ON_REGISTRATION = 'show_on_registration';
    const string FIELD_SHOW_ON_DASHBOARD    = 'show_on_dashboard';
    const string FIELD_CAMPAIGN_FILTER_ENABLED = 'campaign_filter_enabled';

    const RELATION_INDUSTRY              = 'industry';
    const RELATION_COMPANIES             = 'companies';
    const RELATION_COMPANY_FIELDS        = 'companyFields';
    const RELATION_CONSUMER_FIELDS       = 'consumerFields';
    const RELATION_COMPANY_REVIEW_FIELDS = 'companyReviewFields';
    const RELATION_PRODUCTS              = 'products';
    const RELATION_COMPANY_SERVICES      = 'companyServices';
    const RELATION_SERVICE_PRODUCTS      = 'serviceProducts';
    const RELATION_MISSED_PRODUCTS      = 'missedProducts';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $fillable = [
        self::FIELD_ID,
        self::FIELD_INDUSTRY_ID,
        self::FIELD_NAME,
        self::FIELD_SLUG,
        self::FIELD_SHOW_ON_WEBSITE,
        self::FIELD_SHOW_ON_REGISTRATION,
        self::FIELD_SHOW_ON_DASHBOARD,
        self::FIELD_CAMPAIGN_FILTER_ENABLED,
        self::CREATED_AT,
        self::UPDATED_AT
    ];

    protected $casts = [
        self::FIELD_CAMPAIGN_FILTER_ENABLED => 'boolean'
    ];

    /**
     * @return BelongsTo
     */
    public function industry(): BelongsTo
    {
        return $this->belongsTo(Industry::class, self::FIELD_INDUSTRY_ID, Industry::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function companyFields(): HasMany
    {
        return $this->hasMany(ServiceCompanyField::class, ServiceCompanyField::FIELD_INDUSTRY_SERVICE_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function consumerFields(): HasMany
    {
        return $this->hasMany(ServiceConsumerField::class, ServiceConsumerField::FIELD_INDUSTRY_SERVICE_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function companyReviewFields(): HasMany
    {
        return $this->hasMany(ServiceCompanyReviewField::class, ServiceCompanyReviewField::FIELD_INDUSTRY_SERVICE_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function companyServices(): HasMany
    {
        return $this->hasMany(CompanyService::class, CompanyService::FIELD_INDUSTRY_SERVICE_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function serviceProducts(): HasMany
    {
        return $this->hasMany(ServiceProduct::class, ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, self::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function missedProducts(): HasMany
    {
        return $this->hasMany(MissedProduct::class, MissedProduct::FIELD_INDUSTRY_SERVICE_ID, self::FIELD_ID);
    }

    /**
     * @return BelongsToMany
     */
    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class, ServiceProduct::TABLE, ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, ServiceProduct::FIELD_PRODUCT_ID);
    }

    public function companyCampaigns(): HasMany
    {
        return $this->hasMany(CompanyCampaign::class, 'service_id');
    }

    // todo: companies
}
