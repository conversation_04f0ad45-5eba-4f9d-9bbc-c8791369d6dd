<?php

namespace App\Models\Odin;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $industry_id
 * @property bool $future_campaigns_active
 * @property bool $allow_custom_floor_prices
 * @property bool $consumer_reviews_active
 *
 * @property-read Industry $industry
 */
class IndustryConfiguration extends Model
{
    use HasFactory;

    const TABLE = 'industry_configurations';

    const FIELD_ID                        = 'id';
    const FIELD_INDUSTRY_ID               = 'industry_id';
    const FIELD_FUTURE_CAMPAIGNS_ACTIVE   = 'future_campaigns_active';
    const FIELD_ALLOW_CUSTOM_FLOOR_PRICES = 'allow_custom_floor_prices';
    const FIELD_CONSUMER_REVIEWS_ACTIVE   = 'consumer_reviews_active';

    const RELATION_INDUSTRY = 'industry';

    protected $table = self::TABLE;
    protected $fillable = [];

    public function industry(): BelongsTo
    {
        return $this->belongsTo(Industry::class, self::FIELD_INDUSTRY_ID, Industry::FIELD_ID);
    }
}
