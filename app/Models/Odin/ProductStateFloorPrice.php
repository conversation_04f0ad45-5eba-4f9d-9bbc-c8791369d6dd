<?php

namespace App\Models\Odin;

use App\Models\FloorPriceActivityLog;
use App\Models\Legacy\Location;
use App\Models\SaleType;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

/**
 * @property int $id
 * @property int $state_location_id
 * @property int $service_product_id
 * @property int $sale_type_id
 * @property int $quality_tier_id
 * @property int $property_type_id
 * @property float $price
 *
 * @property-read ServiceProduct $serviceProduct
 * @property-read Location $location
 * @property-read QualityTier $qualityTier
 * @property-read SaleType $saleType
 * @property-read PropertyType $propertyType
 * @property-read Location $stateLocation
 */
class ProductStateFloorPrice extends Model
{
    use HasFactory, LogsActivity;

    const string TABLE = 'product_state_floor_prices';

    const string FIELD_ID = 'id';
    const string FIELD_STATE_LOCATION_ID = 'state_location_id';
    const string FIELD_SERVICE_PRODUCT_ID = 'service_product_id';
    const string FIELD_SALE_TYPE_ID = 'sale_type_id';
    const string FIELD_QUALITY_TIER_ID = 'quality_tier_id';
    const string FIELD_PROPERTY_TYPE_ID = 'property_type_id';
    const string FIELD_PRICE = 'price';

    const string RELATION_SERVICE_PRODUCT = 'serviceProduct';
    const string RELATION_LOCATION = 'location';
    const string RELATION_QUALITY_TIER = 'qualityTier';
    const string RELATION_SALE_TYPE = 'saleType';
    const string RELATION_PROPERTY_TYPE = 'propertyType';
    const string RELATION_STATE_LOCATION = 'stateLocation';

    const string ACTIVITY_LOG_TABLE = FloorPriceActivityLog::TABLE;

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];

    /**
     * @return BelongsTo
     */
    public function serviceProduct(): BelongsTo
    {
        return $this->belongsTo(ServiceProduct::class, self::FIELD_SERVICE_PRODUCT_ID, ServiceProduct::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function location(): BelongsTo
    {
        return $this->belongsTo(Location::class, self::FIELD_STATE_LOCATION_ID, Location::ID);
    }

    /**
     * @return BelongsTo
     */
    public function stateLocation(): BelongsTo
    {
        return $this->belongsTo(Location::class, self::FIELD_STATE_LOCATION_ID, Location::ID);
    }

    /**
     * @return BelongsTo
     */
    public function qualityTier(): BelongsTo
    {
        return $this->belongsTo(QualityTier::class, self::FIELD_QUALITY_TIER_ID, QualityTier::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function saleType(): BelongsTo
    {
        return $this->belongsTo(SaleType::class, self::FIELD_SALE_TYPE_ID, SaleType::FIELD_ID);
    }

    /**
     * @return BelongsTo
     */
    public function propertyType(): BelongsTo
    {
        return $this->belongsTo(PropertyType::class, self::FIELD_PROPERTY_TYPE_ID, PropertyType::FIELD_ID);
    }

    /**
     * @return LogOptions
     */
    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logOnly([self::FIELD_PRICE])
            ->useLogName('state_floor_price')
            ->logOnlyDirty()
            ->dontSubmitEmptyLogs();
    }
}
