<?php

namespace App\Models\Odin;

use App\Database\Casts\AsConfigurableFieldPayload;
use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $industry_id
 * @property string $name
 * @property string $key
 * @property int $type
 * @property string $payload
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read Industry $industry
 */
class IndustryCompanyReviewField extends BaseModel
{
    const TABLE = 'industry_company_review_fields';

    const FIELD_ID          = 'id';
    const FIELD_INDUSTRY_ID = 'industry_id';
    const FIELD_NAME        = 'name';
    const FIELD_KEY         = 'key';
    const FIELD_TYPE        = 'type';
    const FIELD_PAYLOAD     = 'payload';

    const RELATION_INDUSTRY = 'industry';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_PAYLOAD => AsConfigurableFieldPayload::class
    ];

    // todo: define type values

    /**
     * @return BelongsTo
     */
    public function industry(): BelongsTo
    {
        return $this->belongsTo(Industry::class, self::FIELD_INDUSTRY_ID, Industry::FIELD_ID);
    }
}
