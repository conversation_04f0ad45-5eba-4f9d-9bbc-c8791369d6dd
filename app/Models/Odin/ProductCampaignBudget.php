<?php

namespace App\Models\Odin;

use App\Enums\Odin\BudgetCategory;
use App\Enums\Odin\QualityTier;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id
 * @property int $product_campaign_id
 * @property QualityTier $quality_tier
 * @property BudgetCategory $category
 * @property string $value_type
 * @property float $value
 * @property boolean $status
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $budget_start_timestamp
 *
 * @property-read string $formatted_budget
 */
class ProductCampaignBudget extends Model
{
    use HasFactory;

    const TABLE = 'product_campaign_budgets';

    const FIELD_ID = 'id';
    const FIELD_PRODUCT_CAMPAIGN_ID = 'product_campaign_id';
    const FIELD_QUALITY_TIER = 'quality_tier';
    const FIELD_CATEGORY = 'category';
    const FIELD_VALUE = 'value';
    const FIELD_VALUE_TYPE = 'value_type';
    const FIELD_STATUS = 'status';
    const FIELD_BUDGET_START_TIMESTAMP = 'budget_start_timestamp';
    const FIELD_MAX_BUDGET_USAGE = 'max_budget_usage';
    const FIELD_CREATED_AT = 'created_at';
    const FIELD_UPDATED_AT = 'updated_at';

    const RELATION_PRODUCT_ASSIGNMENTS = 'productAssignments';
    const RELATION_PRODUCT_CAMPAIGN = 'productCampaign';

    const VALUE_TYPE_NO_LIMIT        = 'no_limit';
    const VALUE_TYPE_AVG_DAILY_LEADS = 'avg_daily_leads';
    const VALUE_TYPE_AVG_DAILY_SPEND = 'avg_daily_spend';
    const VALUE_TYPE_AVG_DAILY_APPOINTMENTS = 'avg_daily_appointments';

    const DEFAULT_MAX_BUDGET_USAGE = 115;

    protected $table = self::TABLE;
    protected $guarded = [
        self::FIELD_ID
    ];

    protected $casts = [
        self::FIELD_CATEGORY => BudgetCategory::class,
        self::FIELD_QUALITY_TIER => QualityTier::class,
        self::FIELD_BUDGET_START_TIMESTAMP => 'datetime'
    ];

    /**
     * @return HasMany
     */
    public function productAssignments(): HasMany
    {
        return $this->hasMany(ProductAssignment::class, self::FIELD_ID, ProductAssignment::FIELD_PRODUCT_CAMPAIGN_BUDGET_ID);
    }

    /**
     * @return BelongsTo
     */
    public function productCampaign(): BelongsTo
    {
        return $this->belongsTo(ProductCampaign::class, self::FIELD_PRODUCT_CAMPAIGN_ID, ProductCampaign::FIELD_ID);
    }

    /**
     * @return Attribute
     */
    protected function formattedBudget(): Attribute
    {
        $formattedBudget = match($this->{self::FIELD_VALUE_TYPE}) {
            self::VALUE_TYPE_NO_LIMIT => 'Unlimited',
            self::VALUE_TYPE_AVG_DAILY_LEADS => "{$this->{self::FIELD_VALUE}} Leads",
            self::VALUE_TYPE_AVG_DAILY_SPEND => "\${$this->{self::FIELD_VALUE}}",
            self::VALUE_TYPE_AVG_DAILY_APPOINTMENTS => "{$this->{self::FIELD_VALUE}} Appointments",
            default                                 => "",
        };

        return Attribute::make(
            get: fn($value, $attributes) => $formattedBudget,
        );
    }
}
