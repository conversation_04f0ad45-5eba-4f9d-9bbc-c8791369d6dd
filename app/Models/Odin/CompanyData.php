<?php

namespace App\Models\Odin;

use App\Enums\Odin\GlobalConfigurableFields;
use App\Models\BaseModel;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * @property int $id
 * @property int $company_id
 * @property array $payload
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read bool|null $qr_top_500_company
 * @property-read Company $company
 */
class CompanyData extends BaseModel
{

    use HasFactory;

    const string TABLE = 'company_data';

    const string FIELD_ID         = 'id';
    const string FIELD_COMPANY_ID = 'company_id';
    const string FIELD_PAYLOAD    = 'payload';

    const string RELATION_COMPANY = 'company';

    protected $table = self::TABLE;

    protected $guarded = [self::FIELD_ID];

    protected $casts = [
        self::FIELD_PAYLOAD => 'array'
    ];

    /**
     * @return BelongsTo
     */
    public function company(): BelongsTo
    {
        return $this->belongsTo(Company::class, self::FIELD_COMPANY_ID, Company::FIELD_ID);
    }

    /**
     * @param GlobalConfigurableFields $field
     * @return mixed
     */
    public function getDatumByGlobalConfigField(GlobalConfigurableFields $field): mixed
    {
        return key_exists($field->value, $this->payload) ? $this->payload[$field->value] : null;
    }
}
