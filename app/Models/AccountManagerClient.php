<?php

namespace App\Models;

use App\Models\Odin\Company;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * Class AccountManagerClient
 *
 * @package App\Models
 *
 * @property int $id
 * @property int $account_manager_id
 * @property string $company_reference
 * @property float $total_spend
 * @property int $status
 * @property Carbon $created_at
 * @property Carbon $updated_at
 *
 * @property-read AccountManager $accountManager
 */
class AccountManagerClient extends BaseModel
{
    use SoftDeletes;

    const TABLE = 'account_manager_clients';

    const FIELD_ID                 = 'id';
    const FIELD_ACCOUNT_MANAGER_ID = 'account_manager_id';
    const FIELD_COMPANY_REFERENCE  = 'company_reference';
    const FIELD_TOTAL_SPEND        = 'total_spend';
    const FIELD_STATUS             = 'status';
    const FIELD_CREATED_AT         = 'created_at';
    const FIELD_UPDATED_AT         = 'updated_at';
    const FIELD_DELETED_AT         = 'deleted_at';

    const RELATION_ACCOUNT_MANAGER = 'accountManager';
    const RELATION_CLIENT = 'client';

    // todo: confirm status options
    const STATUS_INACTIVE = 0;
    const STATUS_ACTIVE   = 1;

    const STATUSES = [
        self::STATUS_INACTIVE,
        self::STATUS_ACTIVE,
    ];

    protected $table   = self::TABLE;
    protected $guarded = [self::FIELD_ID];

    /**
     * @return BelongsTo
     */
    public function accountManager(): BelongsTo
    {
        return $this->belongsTo(AccountManager::class, self::FIELD_ACCOUNT_MANAGER_ID, AccountManager::FIELD_ID);
    }

    /**
     * @return HasOne
     */
    public function client(): HasOne
    {
        return $this->hasOne(Company::class, Company::FIELD_REFERENCE, self::FIELD_COMPANY_REFERENCE);
    }
}
