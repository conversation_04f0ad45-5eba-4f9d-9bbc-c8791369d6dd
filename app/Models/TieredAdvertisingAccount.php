<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;

class TieredAdvertisingAccount extends Model
{
    use SoftDeletes;

    const string TABLE = 'tiered_advertising_accounts';

    const string FIELD_ID = 'id';
    const string FIELD_INDUSTRY_ID = 'industry_id';
    const string FIELD_PLATFORM = 'platform';
    const string FIELD_PLATFORM_ACCOUNT_ID = 'platform_account_id';
    const string FIELD_NAME = 'name';
    const string FIELD_DATA = 'data';

    const string TEMP_NAME = 'Name TBD When Enabled';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $casts = [
        self::FIELD_DATA => 'array',
    ];
}
