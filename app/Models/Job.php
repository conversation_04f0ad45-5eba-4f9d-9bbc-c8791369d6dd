<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

/**
 * @property int $id
 * @property int $attempts
 * @property Carbon $available_at
 */
class Job extends Model
{
    use HasFactory;

    const string TABLE = 'jobs';

    const string FIELD_ID = 'id';
    const string FIELD_QUEUE = 'queue';
    const string FIELD_PAYLOAD = 'payload';
    const string FIELD_ATTEMPTS = 'attempts';
    const string FIELD_RESERVED_AT = 'reserved_at';
    const string FIELD_AVAILABLE_AT = 'available_at';
    const string FIELD_CREATED_AT = 'created_at';

    protected $table = self::TABLE;

    protected $casts = [
        self::FIELD_AVAILABLE_AT => 'datetime'
    ];
}
