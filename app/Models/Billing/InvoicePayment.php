<?php

namespace App\Models\Billing;

use App\Enums\Billing\InvoicePaymentStatus;
use App\Models\BaseModel;
use App\Traits\Uuid;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * @property int $id
 * @property string $uuid
 * @property int $invoice_id
 * @property int $total
 * @property InvoicePaymentStatus $status
 * @property string $error_message
 * @property int $author_id
 * @property string $author_type
 * @property Carbon $created_at
 * @property Carbon $updated_at
 * @property Carbon $charged_at
 * @property Carbon $requested_at
 * @property Carbon $next_attempt_at
 * @property int $max_attempts_per_payment_method
 * @property int $attempt_number
 */
class InvoicePayment extends BaseModel
{
    use HasFactory, Uuid;

    const string TABLE = 'invoice_payments';

    const string FIELD_ID                              = 'id';
    const string FIELD_UUID                            = 'uuid';
    const string FIELD_INVOICE_ID                      = 'invoice_id';
    const string FIELD_TOTAL                           = 'total';
    const string FIELD_STATUS                          = 'status';
    const string FIELD_ERROR_MESSAGE                   = 'error_message';
    const string FIELD_AUTHOR_ID                       = 'author_id';
    const string FIELD_AUTHOR_TYPE                     = 'author_type';
    const string FIELD_CREATED_AT                      = 'created_at';
    const string FIELD_UPDATED_AT                      = 'updated_at';
    const string FIELD_CHARGED_AT                      = 'charged_at';
    const string FIELD_REQUESTED_AT                    = 'requested_at';
    const string FIELD_NEXT_ATTEMPT_AT                 = 'next_attempt_at';
    const string FIELD_MAX_ATTEMPTS_PER_PAYMENT_METHOD = 'max_attempts_per_payment_method';
    const string FIELD_ATTEMPT_NUMBER                  = 'attempt_number';

    const string RELATION_INVOICE = 'invoice';
    const string RELATION_CHARGES = 'charges';

    protected $guarded = [
        self::FIELD_ID
    ];

    protected $casts = [
        self::FIELD_STATUS => InvoicePaymentStatus::class
    ];

    /**
     * @return BelongsTo
     */
    public function invoice(): BelongsTo
    {
        return $this->belongsTo(Invoice::class, self::FIELD_INVOICE_ID, Invoice::FIELD_ID);
    }

    /**
     * @return HasMany
     */
    public function charges(): HasMany
    {
        return $this->hasMany(InvoicePaymentCharge::class, InvoicePaymentCharge::FIELD_INVOICE_PAYMENT_ID, self::FIELD_ID);
    }

    /**
     * @return bool
     */
    public function isCancelled(): bool
    {
        return $this->{InvoicePayment::FIELD_STATUS} === InvoicePaymentStatus::CANCELED;
    }

    /**
     * @return bool
     */
    public function isChargeable(): bool
    {
        return !in_array($this->status, [
            InvoicePaymentStatus::REQUESTED->value,
            InvoicePaymentStatus::CHARGED->value,
            InvoicePaymentStatus::CANCELED->value,
        ]);
    }
}
