<?php

namespace App\Models\PingPostAffiliates;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Carbon;

/**
 * @property int $id
 * @property string $name
 * @property string $type
 * @property string $status
 * @property array $request_rules
 * @property array $industry_map
 * @property array $data
 * @property int $affiliate_id
 * @property Carbon $created_at
 * @property Carbon $updated_at
 */
class PingPostAffiliate extends Model
{
    use SoftDeletes;

    const string TABLE = 'ping_post_affiliates';

    const string FIELD_ID = 'id';
    const string FIELD_NAME = 'name';
    const string FIELD_TYPE = 'type';
    const string FIELD_STATUS = 'status';
    const string FIELD_REQUEST_RULES = 'request_rules';
    const string FIELD_KEY_MAP = 'key_map';
    const string FIELD_DEFAULT_KEY_VALUES = 'default_key_values';
    const string FIELD_KEY_VALUE_CONVERSIONS = 'key_value_conversions';
    const string FIELD_DATA = 'data';
    const string FIELD_AFFILIATE_ID = 'affiliate_id';
    const string FIELD_CREATED_AT = 'created_at';
    const string FIELD_UPDATED_AT = 'updated_at';
    const string FIELD_DELETED_AT = 'deleted_at';

    const string TYPE_POST = 'post';
    const string TYPE_PING_POST = 'ping_post';

    const string STATUS_ACTIVE = 'active';
    const string STATUS_INACTIVE = 'inactive';

    protected $table = self::TABLE;

    protected $guarded = [
        self::FIELD_ID,
    ];

    protected $casts = [
        self::FIELD_REQUEST_RULES => 'array',
        self::FIELD_KEY_MAP => 'array',
        self::FIELD_DEFAULT_KEY_VALUES => 'array',
        self::FIELD_KEY_VALUE_CONVERSIONS => 'array',
        self::FIELD_DATA => 'array',
    ];
}
