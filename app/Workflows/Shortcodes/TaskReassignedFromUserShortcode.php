<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\User;
use App\Workflows\WorkflowPayload;

class TaskReassignedFromUserShortcode extends WorkflowPipelineShortcode
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "task-reassigned-user";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        return $payload->has('task_reassigned_from_user_id') ?
            (User::query()->find($payload->get('task_reassigned_from_user_id'))?->name ?? "Unknown User") : "Unknown User";
    }

    public function getLabel(): string
    {
        return "Task Reassigned User";
    }

}
