<?php

namespace App\Workflows\Shortcodes;

use App\Models\Legacy\EloquentQuote;
use App\Workflows\WorkflowPayload;
use App\Abstracts\Workflows\WorkflowPipelineShortcode;

class LeadIdShortcode extends WorkflowPipelineShortcode
{
    /**
     * @return string
     */
    public function getKey(): string
    {
        return 'lead-id';
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return 'Lead ID';
    }

    /**
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        return $payload->event->get('lead_id');
    }
}
