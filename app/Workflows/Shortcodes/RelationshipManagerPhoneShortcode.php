<?php

namespace App\Workflows\Shortcodes;

use App\Workflows\WorkflowPayload;

class RelationshipManagerPhoneShortcode extends BaseRelationshipManagerShortcode
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return 'relationship-manager-phone';
    }

    /**
     * @inheritDoc
     */
    public function getLabel(): string
    {
        return "Relationship Manager Phone";
    }

    /**
     * @inheritDoc
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        return $this->getRelationshipManager($payload->event->get('company_reference', ''))
            ?->primaryPhone()
            ?->phone
            ?? 'Unknown Phone Number';
    }
}