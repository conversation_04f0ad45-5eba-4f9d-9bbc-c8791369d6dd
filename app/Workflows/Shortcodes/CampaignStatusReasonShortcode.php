<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Workflows\WorkflowPayload;

class CampaignStatusReasonShortcode extends WorkflowPipelineShortcode
{

    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return 'status-reason';
    }

    /**
     * @inheritDoc
     */
    public function getLabel(): string
    {
        return 'Campaign Status Reason';
    }

    /**
     * @inheritDoc
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        return $payload->event->get('status_reason') ?? 'Unknown';
    }
}
