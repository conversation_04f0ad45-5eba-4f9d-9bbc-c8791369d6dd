<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Odin\ProductAssignment;
use App\Workflows\WorkflowPayload;

class LeadsReceivedShortcode extends WorkflowPipelineShortcode
{
    /**
     * @inheritDoc
     */
    public function getKey(): string
    {
        return "leads-received";
    }

    /**
     * Returns the correct value to replace.
     *
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $companyId = $payload->event->get('company_id');

        return ProductAssignment::query()
            ->where(ProductAssignment::FIELD_COMPANY_ID, $companyId)
            ->where(ProductAssignment::FIELD_DELIVERED, true)
            ->groupBy(ProductAssignment::FIELD_CONSUMER_PRODUCT_ID)
            ->count() ?? 0;
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return "Number of Leads Received";
    }
}
