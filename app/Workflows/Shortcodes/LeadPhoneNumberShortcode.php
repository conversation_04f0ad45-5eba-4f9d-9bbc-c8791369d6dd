<?php

namespace App\Workflows\Shortcodes;

use App\Abstracts\Workflows\WorkflowPipelineShortcode;
use App\Models\Odin\Consumer;
use App\Workflows\WorkflowPayload;

class LeadPhoneNumberShortcode extends WorkflowPipelineShortcode
{
    /**
     * @return string
     */
    public function getKey(): string
    {
        return 'lead-phone-number';
    }

    /**
     * @return string
     */
    public function getLabel(): string
    {
        return 'Lead Phone Number';
    }

    /**
     * @param WorkflowPayload $payload
     * @return string
     */
    protected function getValue(WorkflowPayload $payload): string
    {
        $leadId = $payload->event->get('lead_id');

        /** @var Consumer|null $consumer */
        $consumer = Consumer::query()->where(Consumer::FIELD_LEGACY_ID, $leadId)->first();

        return $consumer && !empty($consumer->{Consumer::FIELD_PHONE})
            ? $consumer->{Consumer::FIELD_PHONE}
            : 'Unknown ' . $this->getLabel();
    }
}
