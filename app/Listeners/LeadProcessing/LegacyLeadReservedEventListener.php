<?php

namespace App\Listeners\LeadProcessing;

use App\Events\LeadProcessing\LeadReservedEvent;
use App\Services\Legacy\APIConsumer;
use Illuminate\Contracts\Queue\ShouldQueue;
use Symfony\Component\String\Exception\RuntimeException;

class LegacyLeadReservedEventListener implements ShouldQueue
{
    const SYSTEM_RESERVED_VALUE               = -1;
    const API_BASE_ENDPOINT                   = '/repositories/lead-processing';
    const API_RESERVE_LEAD_TO_SYSTEM_ENDPOINT = self::API_BASE_ENDPOINT . '/reserve-lead-to-system';
    const API_RESERVE_LEAD_ENDPOINT           = self::API_BASE_ENDPOINT . '/reserve-lead';

    /**
     * Handles updating the reserved lead status in the legacy system.
     *
     * @param LeadReservedEvent $event
     * @return void
     * @throws \Illuminate\Contracts\Container\BindingResolutionException
     * @throws \Exception
     */
    public function handle(LeadReservedEvent $event): void
    {
        /** @var APIConsumer $consumer */
        $consumer = app()->make(APIConsumer::class);

        if ($event->processorId === self::SYSTEM_RESERVED_VALUE)
            $this->reserveToSystem($event->leadReference, $consumer);
        else {
            $legacyId = $event->getLeadProcessor()?->user?->legacy_user_id;
            if(!$legacyId) throw new RuntimeException("The legacy ID is missing.");

            $this->reserveToProcessor($event->leadReference, $legacyId, $consumer);
        }
    }

    /**
     * @throws \Exception
     */
    protected function reserveToSystem(string $leadReference, APIConsumer $consumer): void
    {
        $consumer->patch(self::API_RESERVE_LEAD_TO_SYSTEM_ENDPOINT,
            compact("leadReference")
        );
    }

    /**
     * @throws \Exception
     */
    protected function reserveToProcessor(string $leadReference, int $legacyUserId, APIConsumer $consumer): void
    {
        $consumer->patch(self::API_RESERVE_LEAD_ENDPOINT,
            compact("leadReference", "legacyUserId")
        );
    }
}
