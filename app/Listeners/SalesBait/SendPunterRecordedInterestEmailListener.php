<?php

namespace App\Listeners\SalesBait;

use App\Events\SalesBait\PunterRecordedInterest;
use App\Models\SalesBaitLead;
use App\Models\SalesBaitRegisteredInterest;
use App\Repositories\Legacy\CompanyRepository;
use App\Services\Delivery\Email;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Mail;

class SendPunterRecordedInterestEmailListener implements ShouldQueue
{
    /**
     * Create the event listener.
     *
     * @return void
     */
    public function __construct(protected CompanyRepository $companyRepository) {}

    /**
     * Handle the event.
     *
     * @param PunterRecordedInterest $event
     * @return void
     */
    public function handle(PunterRecordedInterest $event): void
    {
        $salesBait = $event->getSalesBait();
        $interest  = $event->getSalesBaitInterest();

        if ($salesBait == null || $interest == null)
            return;

        foreach ($this->getRecipients($salesBait) as $recipient) {

            Email::send($recipient, new \App\Mail\PunterRecordedInterest(
                $salesBait->lead->getLeadIndustry(),
                $salesBait->company,
                $salesBait->lead_id,
                $this->getName($interest),
                $this->getPhone($interest),
                $this->getEmail($interest)
            ));
        }
    }

    /**
     * Returns the recipients for this email.
     *
     * @return array
     */
    protected function getRecipients(SalesBaitLead $salesBaitLead): array
    {
        $to = [$salesBaitLead->lead->getLeadIndustry()->key == "roofing" ? "<EMAIL>" : "<EMAIL>"];

        $successManagersAndAccountManagers = $this->companyRepository->getAllAccountAndCustomerSuccessManagers()->pluck('email');

        return [...$to, ...$successManagersAndAccountManagers->toArray()];
    }

    /**
     * Returns the name for sales bait.
     *
     * @param SalesBaitRegisteredInterest $interest
     * @return string
     */
    protected function getName(SalesBaitRegisteredInterest $interest): string
    {
        return $interest->relation_type == SalesBaitRegisteredInterest::TYPE_USER ?
            ($interest->legacyUser->firstname . " " . $interest->legacyUser->lastname) :
            ($interest->legacyContact->firstname . " " . $interest->legacyContact->lastname);
    }

    /**
     * Returns the email for sales bait.
     *
     * @param SalesBaitRegisteredInterest $interest
     * @return string
     */
    protected function getEmail(SalesBaitRegisteredInterest $interest): string
    {
        return $interest->relation_type == SalesBaitRegisteredInterest::TYPE_USER ?
            ($interest->legacyUser->email) :
            ($interest->legacyContact->email);
    }

    /**
     * Returns the phone for sales bait.
     *
     * @param SalesBaitRegisteredInterest $interest
     * @return string
     */
    protected function getPhone(SalesBaitRegisteredInterest $interest): string
    {
        return $interest->relation_type == SalesBaitRegisteredInterest::TYPE_USER ?
            ($interest->legacyUser->phone) :
            ($interest->legacyContact->phone);
    }
}
