<?php

namespace App\Builders;

use App\DTO\Mail\ListEmailQueryDTO;
use App\Enums\ContactIdentification\ContactType;
use App\Enums\Mailbox\EmailCategory;
use App\Models\ContactIdentification\IdentifiedContact;
use App\Models\ContactIdentification\PossibleContact;
use App\Models\Mailbox\MailboxEmail;
use App\Models\Mailbox\MailboxEmailLabel;
use App\Models\Mailbox\MailboxEmailRecipient;
use App\Models\Mailbox\MailboxUserEmail;
use App\Models\Mailbox\MailboxUserToken;
use App\Models\Odin\CompanyUser;
use App\Models\User;
use App\Services\Mailbox\Mail\MailProviderFactory;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;

class MailboxUserEmailBuilder extends BaseBuilder
{
    protected ?User $forUser = null;
    protected ?string $forCategory = null;
    protected ?string $emailUuid = null;
    protected ?bool $starred = null;
    protected ?bool $archived = null;
    protected ?string $textQuery = null;
    protected ?int $forRelatedCompanyId = null;

    /**
     * @param Builder $query
     * @return void
     * @throws \Exception
     */
    private function applyTextQuery(Builder $query): void
    {
        $mailProvider = MailProviderFactory::make();

        /** @var MailboxUserToken $userToken */
        $userToken = MailboxUserToken::query()->where(MailboxUserToken::FIELD_USER_ID, $this->forUser->{User::FIELD_ID})->latest()->first();

        $emailListingQueryDTO = new ListEmailQueryDTO(
            rawQuery: $this->textQuery
        );

        [$emailIds] = $mailProvider
            ->getEmailIds(
                mailboxUserToken: $userToken,
                perPage         : 50,
                query           : $emailListingQueryDTO
            );

        $query->whereIn(MailboxUserEmail::FIELD_EXTERNAL_ID, $emailIds);
    }

    private function applyCategoryConditions(Builder $query): void
    {
        $category = $this->forCategory;

        if (in_array($category, EmailCategory::getValues()->toArray())) {
            match ($category) {
                EmailCategory::SENT->value      => $query->where(MailboxUserEmail::TABLE . '.' . MailboxUserEmail::FIELD_IS_SENT, true),
                EmailCategory::STARRED->value   => $query->where(MailboxUserEmail::TABLE .'.'. MailboxUserEmail::FIELD_IS_STARRED, true),
                EmailCategory::IMPORTANT->value => $query->where(MailboxUserEmail::TABLE .'.'. MailboxUserEmail::FIELD_IS_IMPORTANT, true),
                EmailCategory::ARCHIVED->value  => $query->where(MailboxUserEmail::TABLE .'.'. MailboxUserEmail::FIELD_IS_ARCHIVED, true),
                EmailCategory::ALL_MAIL->value  => $query,
                EmailCategory::INBOX->value, 'default' => $query->where(MailboxUserEmail::TABLE . '.' . MailboxUserEmail::FIELD_IS_INBOX, true),
            };
        } else {
            $query->whereHas(MailboxUserEmail::RELATION_LABEL, function ($query) use ($category) {
                $query->where(MailboxEmailLabel::FIELD_LABEL_ID, $category);
            });
        }

    }

    private function applyRelatedCompanyIdQuery(Builder $query): void
    {
        $companyUserIds = CompanyUser::query()
            ->select(CompanyUser::FIELD_ID)
            ->where(CompanyUser::FIELD_COMPANY_ID, $this->forRelatedCompanyId)
            ->get()
            ->pluck(CompanyUser::FIELD_ID);

        $query->where(function (Builder $query) use ($companyUserIds) {
            $query->whereHas(MailboxUserEmail::RELATION_EMAIL
                . '.' . MailboxEmail::RELATION_IDENTIFIED_CONTACT
                . '.' . IdentifiedContact::RELATION_NOMINATED_CONTACT,
                function (Builder $query) use ($companyUserIds) {
                    $query->whereIn(PossibleContact::FIELD_RELATION_ID, $companyUserIds)
                        ->where(PossibleContact::FIELD_RELATION_TYPE, ContactType::COMPANY_USER->getModelClass());
                })->orWhereHas(MailboxUserEmail::RELATION_EMAIL
                . '.' . MailboxEmail::RELATION_RECIPIENTS
                . '.' . MailboxEmailRecipient::RELATION_IDENTIFIED_CONTACT
                . '.' . IdentifiedContact::RELATION_NOMINATED_CONTACT,
                    function (Builder $query) use ($companyUserIds) {
                        $query->whereIn(PossibleContact::FIELD_RELATION_ID, $companyUserIds)
                            ->where(PossibleContact::FIELD_RELATION_TYPE, ContactType::COMPANY_USER->getModelClass());
                    });
        });
    }

    /**
     * @throws \Exception
     */
    public function getQuery(): Builder
    {
        $query = MailboxUserEmail::query()
            ->select(
                DB::raw('MAX(' . MailboxUserEmail::TABLE . '.' . MailboxUserEmail::FIELD_SENT_AT . ') as ' . MailboxUserEmail::FIELD_SENT_AT),
                MailboxUserEmail::TABLE . '.' . MailboxUserEmail::FIELD_EXTERNAL_THREAD_ID
            )
            ->when($this->forUser, function (Builder $query) {
                $query->where(MailboxUserEmail::FIELD_USER_ID, $this->forUser->id);
            })
            ->join(MailboxEmail::TABLE, MailboxEmail::TABLE . '.' . MailboxEmail::FIELD_ID, '=', MailboxUserEmail::TABLE . '.' . MailboxUserEmail::FIELD_EMAIL_ID)
            ->groupBy(MailboxUserEmail::TABLE . '.' . MailboxUserEmail::FIELD_EXTERNAL_THREAD_ID);

        if (isset($this->forRelatedCompanyId)) {
            $this->applyRelatedCompanyIdQuery($query);
        }

        if (!empty($this->textQuery)) {
            $this->applyTextQuery($query);
        }

        if (!empty($this->forCategory)) {
            $this->applyCategoryConditions($query);
        }

        if (!empty($this->emailUuid)) {
            $query->where(MailboxUserEmail::TABLE . '.' . MailboxUserEmail::FIELD_UUID, $this->emailUuid);
        }

        return MailboxUserEmail::query()
            ->joinSub($query, 'sub', function ($join) {
                $join->on(MailboxUserEmail::TABLE . '.' . MailboxUserEmail::FIELD_EXTERNAL_THREAD_ID, '=', 'sub.' . MailboxUserEmail::FIELD_EXTERNAL_THREAD_ID)
                    ->on(MailboxUserEmail::TABLE . '.' . MailboxUserEmail::FIELD_SENT_AT, '=', 'sub.' . MailboxUserEmail::FIELD_SENT_AT);
            })
            ->orderByDesc(MailboxUserEmail::TABLE . '.' . MailboxUserEmail::FIELD_SENT_AT);
    }

    /**
     * @param string|null $forCategory
     * @return MailboxUserEmailBuilder
     */
    public function setForCategory(?string $forCategory): MailboxUserEmailBuilder
    {
        $this->forCategory = $forCategory;
        return $this;
    }

    /**
     * @param User|null $forUser
     * @return MailboxUserEmailBuilder
     */
    public function setForUser(?User $forUser): MailboxUserEmailBuilder
    {
        $this->forUser = $forUser;
        return $this;
    }

    /**
     * @param bool|null $starred
     * @return MailboxUserEmailBuilder
     */
    public function setStarred(?bool $starred): MailboxUserEmailBuilder
    {
        $this->starred = $starred;
        return $this;
    }

    /**
     * @param bool|null $archived
     * @return MailboxUserEmailBuilder
     */
    public function setArchived(?bool $archived): MailboxUserEmailBuilder
    {
        $this->archived = $archived;
        return $this;
    }

    /**
     * @param string|null $query
     * @return $this
     */
    public function setTextQuery(?string $query = null): static
    {
        $this->textQuery = $query;
        return $this;
    }

    /**
     * @param string|null $emailUuid
     * @return MailboxUserEmailBuilder
     */
    public function setForEmailUuid(?string $emailUuid): static
    {
        $this->emailUuid = $emailUuid;
        return $this;
    }

    /**
     * @return MailboxUserEmailBuilder
     */
    public static function query(): MailboxUserEmailBuilder
    {
        return new MailboxUserEmailBuilder();
    }

    /**
     * @param int|null $id
     * @return MailboxUserEmailBuilder
     */
    public function forRelatedCompanyId(?int $id = null): MailboxUserEmailBuilder
    {
        $this->forRelatedCompanyId = $id;
        return $this;
    }
}
