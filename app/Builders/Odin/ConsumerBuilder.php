<?php

namespace App\Builders\Odin;

use App\Enums\CompanyCampaignSource;
use App\Models\Campaigns\Modules\Budget\Budget;
use App\Models\Campaigns\Modules\Budget\BudgetContainer;
use App\Models\LeadRefund;
use App\Models\LeadRefundItem;
use App\Models\Legacy\EloquentQuote;
use App\Models\Legacy\EloquentQuoteCompany;
use App\Models\Legacy\LeadCampaignSalesTypeConfiguration;
use App\Models\Odin\Address;
use App\Models\Odin\Consumer;
use App\Models\Odin\ConsumerProduct;
use App\Models\Odin\Product;
use App\Models\Odin\ProductAssignment;
use App\Models\Odin\ServiceProduct;
use App\Services\DatabaseHelperService;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;

class ConsumerBuilder
{
    /**
     * @param int|null $companyId
     * @param int|null $status
     * @param int|null $service
     * @param int|null $leadId
     * @param string|null $name
     * @param mixed|null $state
     * @param int|null $fromDate
     * @param int|null $toDate
     * @param int|null $productId
     * @param string|null $leadRefundStatus
     * @param int|null $companyCampaignId
     * @param string|null $multiCampaignSource
     * @param bool|null $orderByDelivery
     */
    public function __construct(
        protected ?int    $companyId = null,
        protected ?int    $status = null,
        protected ?int    $service = null,
        protected ?int    $leadId = null,
        protected ?string $name = null,
        protected mixed   $state = null,
        protected ?int    $fromDate = null,
        protected ?int    $toDate = null,
        protected ?int    $productId = null,
        protected ?string $leadRefundStatus = null,
        protected ?int    $companyCampaignId = null,
        protected ?string $multiCampaignSource = null,
        protected ?bool   $orderByDelivery = null,
    ){}

    /**
     * Creates an instance of industry builder.
     *
     * @return static
     */
    public static function query(): self
    {
        return new self();
    }

    /**
     * Allows querying via company ID - Note this is the Odin ID
     *
     * @param int|null $id
     * @return $this
     */
    public function forCompanyId(?int $id = null): self
    {
        $this->companyId = $id;

        return $this;
    }

    public function forCompanyCampaign(?int $id = null): self
    {
        $this->companyCampaignId = $id;

        return $this;
    }

    /**
     * Allows querying for Consumer Product Statuses
     *
     * @param int|null $status
     * @return $this
     */
    public function forStatus(?int $status = null): self
    {
        $this->status = $status;

        return $this;
    }

    /**
     * @param int|null $service
     * @return $this
     */
    public function forService(?int $service = null): self
    {
        $this->service = $service;

        return $this;
    }

    /**
     * Allows querying via lead ID
     *
     * @param int|null $id
     * @return $this
     */
    public function forLeadId(?int $id = null): self
    {
        $this->leadId = $id;

        return $this;
    }

    /**
     * Query for name - includes first_name, last_name and email
     *
     * @param string|null $name
     * @return $this
     */
    public function forName(?string $name = null): self
    {
        $this->name = $name;

        return $this;
    }

    /**
     * Query for state and zipcode
     *
     * @param int|null $state
     * @return $this
     */
    public function forState(mixed $state = null): self
    {
        $this->state = $state;

        return $this;
    }

    /**
     * @param int|null $fromDate
     * @return $this
     */
    public function forFromDate(?int $fromDate = null): self
    {
        $this->fromDate = $fromDate;

        return $this;
    }

    /**
     * @param int|null $toDate
     * @return $this
     */
    public function forToDate(?int $toDate = null): self
    {
        $this->toDate = $toDate;

        return $this;
    }

    /**
     * @param int|null $id
     * @return $this
     */
    public function forProductId(?int $id = null): self
    {
        $this->productId = $id;

        return $this;
    }

    /**
     * @param string|null $leadRefundStatus
     * @return $this
     */
    public function forLeadRefundStatus(?string $leadRefundStatus = null): self
    {
        $this->leadRefundStatus = $leadRefundStatus;

        return $this;
    }

    /**
     * @param bool $orderByDelivery
     * @return $this
     */
    public function orderByDelivery(?bool $orderByDelivery = false): self
    {
        $this->orderByDelivery = $orderByDelivery;

        return $this;
    }

    /**
     * Returns a final collection by running the query builder.
     *
     * @return Collection
     */
    public function get(): Collection
    {
        return $this->getQuery()->get();
    }

    /**
     * @param Builder $query
     * @return void
     */
    protected function filterByCompanyCampaign(Builder $query): void
    {
        $query->whereHas(Consumer::RELATION_CONSUMER_PRODUCT, function (Builder $query) {
                $query->whereHas(ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT, function (Builder $query) {
                    $query->whereHas(ProductAssignment::RELATION_BUDGET, function (Builder $query) {
                        $query->whereHas(Budget::RELATION_BUDGET_CONTAINER, function (Builder $query) {
                            $query->where(BudgetContainer::FIELD_CAMPAIGN_ID, $this->companyCampaignId);
                        });
                    });
                });
            });
    }

    /**
     * Returns a query for this instance.
     *
     * @return Builder
     */
    public function getQuery(): Builder
    {
        $query = Consumer::query()->select(
            Consumer::TABLE.'.'.Consumer::FIELD_ID,
            Consumer::TABLE.'.'.Consumer::FIELD_LEGACY_ID,
            Consumer::TABLE.'.'.Consumer::FIELD_CREATED_AT,
            Consumer::TABLE.'.'.Consumer::FIELD_FIRST_NAME,
            Consumer::TABLE.'.'.Consumer::FIELD_LAST_NAME,
            ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_ID . ' as product_assignment_id',
            ConsumerProduct::TABLE .'.'. ConsumerProduct::FIELD_ID .' as consumer_product_id'
        );

        //eager load these relationships
        $query = $query->with([
            Consumer::RELATION_CONSUMER_PRODUCT,
            Consumer::RELATION_CONSUMER_PRODUCT.'.'.ConsumerProduct::RELATION_PRODUCT_ASSIGNMENT,
            Consumer::RELATION_LEGACY_LEAD.'.'.EloquentQuote::RELATION_CRM_DELIVERY_LOGS
        ]);

        $query = $query
            ->join(ConsumerProduct::TABLE, ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_CONSUMER_ID, '=', Consumer::TABLE.'.'.Consumer::FIELD_ID)
            ->join(ProductAssignment::TABLE, ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_CONSUMER_PRODUCT_ID,'=', ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ID);

        if($this->companyId !== null) {
            $query = $query->where(ProductAssignment::TABLE.'.'.ProductAssignment::FIELD_COMPANY_ID, $this->companyId);
        }
        if($this->status || $this->status === ConsumerProduct::STATUS_INITIAL) {
            $query->where(ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_STATUS, $this->status);
        }
        if($this->leadId) {
            $leadId = $this->leadId;
            // allowing search for legacy and odin
            $query->where(function($query) use ($leadId) {
                $query->where(Consumer::TABLE .'.'. Consumer::FIELD_ID, $leadId)
                    ->orWhere(Consumer::TABLE .'.'. Consumer::FIELD_LEGACY_ID, $leadId)
                    ->orWhere(ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_ID, $leadId);
            });
        }
        if($this->name) {
            $name = $this->name;
            $query->where(function($query) use ($name) {
                $query->where(Consumer::TABLE.'.'.Consumer::FIELD_FIRST_NAME, 'like', '%'.$name.'%')
                    ->orWhere(Consumer::TABLE.'.'.Consumer::FIELD_LAST_NAME, 'like', '%'.$name.'%')
                    ->orwhere(Consumer::TABLE.'.'.Consumer::FIELD_EMAIL,'like',  '%'.$name.'%');
            });
        }
        if($this->state) {
            $query = $query->join(Address::TABLE, Address::TABLE.'.'.Address::FIELD_ID, '=',ConsumerProduct::TABLE.'.'.ConsumerProduct::FIELD_ADDRESS_ID);
            $state = $this->state;
            $query->where(function($query) use ($state) {
                $query->where(Address::TABLE.'.'.Address::FIELD_STATE, $state)
                    ->orWhere(Address::TABLE.'.'.Address::FIELD_ZIP_CODE, $state);
            });
        }
        if($this->toDate && $this->fromDate) {
            $query->where(Consumer::TABLE .'.'.Consumer::FIELD_CREATED_AT, '>', Carbon::createFromTimestamp($this->fromDate)->format("Y-m-d H:i:s"))
                ->where(Consumer::TABLE .'.'.Consumer::FIELD_CREATED_AT, '<', Carbon::createFromTimestamp($this->toDate)->format("Y-m-d H:i:s"));
        }

        if($this->productId || $this->service) {
            $query->join(
                ServiceProduct::TABLE . ' AS sp2',
                'sp2.' . ServiceProduct::FIELD_ID,
                '=',
                ConsumerProduct::TABLE . '.' . ConsumerProduct::FIELD_SERVICE_PRODUCT_ID
            );

            if ($this->productId) {
                $query->where('sp2.'.ServiceProduct::FIELD_PRODUCT_ID, $this->productId);
            }

            if ($this->service) {
                $query->where('sp2.'.ServiceProduct::FIELD_INDUSTRY_SERVICE_ID, $this->service);
            }
        }

        if ($this->leadRefundStatus) {
            $query->whereExists(function ($query) {
               $query
                   ->from(LeadRefund::TABLE)
                   ->where(LeadRefund::TABLE . '.' . LeadRefund::FIELD_STATUS, $this->leadRefundStatus)
                   ->whereColumn(LeadRefund::TABLE . '.' . LeadRefund::FIELD_COMPANY_ID, ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_COMPANY_ID)
                   ->join(LeadRefundItem::TABLE, function ($query){
                       $query->on(LeadRefundItem::TABLE . '.' . LeadRefundItem::FIELD_PRODUCT_ASSIGNMENT_ID, ProductAssignment::TABLE . '.' . ProductAssignment::FIELD_ID)
                           ->on(LeadRefund::TABLE . '.' . LeadRefund::FIELD_ID, LeadRefundItem::TABLE . '.' . LeadRefundItem::FIELD_LEAD_REFUND_ID);
                   });
            });
        }

        if (!empty($this->companyCampaignId)) {
            $this->filterByCompanyCampaign($query);
        }

        if ($this->orderByDelivery) {
            $query->orderBy(ProductAssignment::TABLE.'.'.ProductAssignment::CREATED_AT, 'DESC');
        } else {
            $query->orderBy(Consumer::FIELD_CREATED_AT, 'DESC');
        }

        return $query;
    }
}
