<?php

namespace App\Mail\LeadRefunds;

use Carbon\Carbon;
use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Number;

class LeadRefundRequested extends Mailable
{
    use Queueable, SerializesModels;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(
        public string $recipientName,
        public string $company,
        public string $total,
        public string $requesterName,
        public string $requestDate,
    )
    {

    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        return $this->subject("A lead refund for the company " . $this->company . " has been requested")
            ->markdown('emails.lead-refunds.refund-requested')
            ->with([
                "recipientName"  => $this->recipientName,
                "company"        => $this->company,
                "total"          => $this->total,
                "requesterName"  => $this->requesterName,
                "requestDate"    => Carbon::parse($this->requestDate)->format('j F Y - g:iA (e)'),
                "leadRefundsUrl" => route('lead-refunds')
            ]);
    }
}
