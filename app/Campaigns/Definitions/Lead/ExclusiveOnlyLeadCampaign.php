<?php

namespace App\Campaigns\Definitions\Lead;

use App\Campaigns\Definitions\BaseCampaign;
use App\Campaigns\Modules\Bidding\ProductBiddingModule;
use App\Campaigns\Modules\Budget\ExclusiveOnlyLeadBudgetContainerModule;
use App\Campaigns\Modules\DashboardModule;
use App\Campaigns\Modules\DeliveryModule;
use App\Campaigns\Modules\Filter\CampaignFilterModule;
use App\Campaigns\Modules\Legacy\LegacyModule;
use App\Campaigns\Modules\LocationModule;
use App\Campaigns\Modules\RejectionModule;
use Illuminate\Support\Collection;

class ExclusiveOnlyLeadCampaign extends BaseCampaign
{
    /**
     * @inheritDoc
     */
    public function modules(): Collection
    {
        return collect([
            app(LocationModule::class),
            app(RejectionModule::class),
            app(CampaignFilterModule::class),
            app(ExclusiveOnlyLeadBudgetContainerModule::class),
            app(ProductBiddingModule::class),
            app(DeliveryModule::class),
            app(DashboardModule::class),
            app(LegacyModule::class),
        ]);
    }
}
