<?php

namespace App\Campaigns\Modules\Filter;

use App\Campaigns\Modules\BaseModule;
use App\DataModels\Campaigns\ConsumerProject;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\CompanyCampaignFilter;
use Illuminate\Database\Eloquent\Model;

class CampaignFilterModule extends BaseModule
{

    /**
     * @inheritDoc
     */
    #[\Override] protected function getModel(CompanyCampaign $campaign): Model
    {
        return $campaign;
    }

    /**
     * @inheritDoc
     */
    public function filter(CompanyCampaign $campaign, ConsumerProject $project): bool
    {
        $status = $this->applyFilters($campaign, $project);

        if (!$status && $project->getTracker()) {
            $project->getTracker()->addFilteredCampaign($campaign->id, 'Campaign Filter');
        }

        return $status;
    }

    /**
     * @param CompanyCampaign $campaign
     * @param ConsumerProject $project
     *
     * @return bool
     */
    protected function applyFilters(CompanyCampaign $campaign, ConsumerProject $project): bool
    {
        if (!$campaign->service->campaign_filter_enabled) {
            return true;
        }

        if ($campaign->filters->isEmpty()) {
            return true;
        }

        foreach ($campaign->filters as $filter) {
            if (!$this->compareFilter($filter, $project)) {
                return false;
            }
        }

        return true;
    }

    /**
     * @param CompanyCampaignFilter $filter
     * @param ConsumerProject $consumerProject
     *
     * @return bool
     */
    protected function compareFilter(CompanyCampaignFilter $filter, ConsumerProject $consumerProject): bool
    {
        $expected = $filter->value;
        $actual = $consumerProject->leadConsumerProduct()->consumerProductData?->payload[$filter->key] ?? null;

        if (!$actual) {
            return false;
        }

        if (is_numeric($expected) && !is_numeric($actual)) {
            return false;
        }

        if (is_numeric($expected)) {
            //Convert to numeric and compare
            return $filter->operator->compare(
                actual: $actual + 0,
                expected: $expected + 0
            );
        }

        return $filter->operator->compare(
            actual: strtolower(trim($actual)),
            expected: strtolower(trim($expected))
        );
    }
}
