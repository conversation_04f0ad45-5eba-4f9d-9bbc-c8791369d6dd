<?php

namespace App\Campaigns\Delivery\CRM\ZohoOAuth;

use App\Campaigns\Delivery\CRM\BaseCRMTestDeliverer;
use App\Campaigns\Delivery\CRM\CRMTestDeliveryResponse;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ConsumerProduct;

class ZohoOAuthCRMTestDeliverer extends BaseCRMTestDeliverer
{
    /**
     * @inheritDoc
     */
    #[\Override] protected function getDefaultSystemFields(): array
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    #[\Override] protected function getDefaultAdditionalFields(): array
    {
        return [];
    }

    /**
     * @inheritDoc
     */
    #[\Override] public function deliver(ConsumerProduct $product, CompanyCampaign $campaign, ?array $payload = null): bool
    {
        return false;
    }

    /**
     * @inheritDoc
     */
    #[\Override] public function deliverTestLead(): CRMTestDeliveryResponse
    {
        $deliveryService = new ZohoOAuthDeliveryService($this->crm);
        $campaign = $this->crm->module->campaign;
        $url = $this->getFieldValue($campaign, ZohoOAuthCRMDeliverer::TOKEN_RESPONSE_API_DOMAIN);
        $lead = $this->parseManyFieldValuesForFakeData($this->getManyFieldGroups($campaign, [self::ADDITIONAL_FIELDS_KEY, self::CUSTOM_FIELDS_KEY]), $campaign);

        $delivery = $deliveryService->setOAuthFields(
                $this->getFieldValue($campaign, ZohoOAuthCRMDeliverer::TOKEN_REQUEST_CLIENT_ID) ?? "",
                $this->getFieldValue($campaign, ZohoOAuthCRMDeliverer::TOKEN_REQUEST_CLIENT_SECRET) ?? "",
                $this->getFieldValue($campaign, ZohoOAuthCRMDeliverer::GRANT_TOKEN_RESPONSE_REFRESH_TOKEN) ?? "",
                $this->getFieldValue($campaign, ZohoOAuthCRMDeliverer::TOKEN_RESPONSE_ACCESS_TOKEN),
            )->setUrl($url)
            ->setLead($lead);

        $response = $delivery->deliver();

        return new CRMTestDeliveryResponse(
            url: $delivery->getUrl(),
            requestBody: $lead,
            headers: $delivery->getHeaders(),
            success: $response->success,
            responseBody: $response->payload
        );
    }
}
