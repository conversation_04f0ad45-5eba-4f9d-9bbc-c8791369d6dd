<?php

namespace App\Campaigns\Delivery\CRM\SolarNexus;

use App\Campaigns\Delivery\CRM\BaseCRMDeliveryService;
use App\Campaigns\Delivery\CRM\CRMDeliveryResponse;
use Illuminate\Http\Client\ConnectionException;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;


class SolarNexusLegacyDeliveryService extends BaseCRMDeliveryService
{
    const string BASE_URL          = 'https://app.solarnexus.com/api3';
    const string LEADS_API         = '/projects';
    const string HEADER_VALUE_AUTH = 'SolarNexus apikey=';
    const string HEADER_KEY_AUTH   = 'Authorization';
    const string PAYLOAD_KEY       = 'project_iep';

    const string TEST_KEY          = 'solar-nexus-json';

    const string FIELD_ASSIGNED_TO = 'assigned_to';
    const string FIELD_DATE        = 'date';
    const string ALL_CUSTOM_FIELDS = 'custom_fields';

    protected string $apiKey;
    protected array $body = [];
    protected array $headers = [];
    protected ?string $url = null;

    /**
     * @inheritDoc
     * @throws ConnectionException
     */
    public function deliver(): CRMDeliveryResponse
    {
        if (!$this->apiKey)
            return new CRMDeliveryResponse(false, ['error' => 'Bad SolarNexus CRM configuration, no API key supplied.']);
        if (!$this->url)
            $this->url = self::BASE_URL . self::LEADS_API;

        $this->headers[self::HEADER_KEY_AUTH] = $this->apiKey;
        $response = Http::withHeaders($this->headers)
            ->withBody(json_encode($this->body), 'application/json')
            ->post($this->url);

        return new CRMDeliveryResponse($response->successful(), ['body' => $response->body()]);
    }

    /**
     * @inheritDoc
     */
    public function debugDelivery(): CRMDeliveryResponse
    {
        $this->url = $this->getDebugUrl(self::TEST_KEY);
        $this->setTestHeaders($this->headers);
        $this->body['api_key'] = Str::mask($this->apiKey, '*', 5);
        $this->apiKey = config('services.crm_delivery.test_secret');

        return $this->deliver();
    }

    /**
     * @param array|null $headers
     * @return $this
     */
    public function setHeaders(?array $headers): self
    {
        $this->headers = $headers ?: $this->headers;

        return $this;
    }

    /**
     * @param string|null $apiKey
     * @return $this
     */
    public function setApiKey(?string $apiKey): self
    {
        $this->apiKey = self::HEADER_VALUE_AUTH . ($apiKey ?? "");

        return $this;
    }

    /**
     * @param array $additionalFields
     * @param array $customFields
     * @return $this
     */
    public function setBody(array $additionalFields, array $customFields): self
    {
        $this->body = [
            self::PAYLOAD_KEY => $this->generateXmlString($additionalFields, $customFields),
        ];

        return $this;
    }

    /**
     * @return array
     */
    public function getBody(): array
    {
        return [...$this->body];
    }

    /**
     * @return array[]
     */
    public function getHeaders(): array
    {
        return [...$this->headers];
    }

    /**
     * Fill out an XML template for this delivery method
     *
     * @param array $additionalFields
     * @param array $customFields
     * @return string
     */
    private function generateXmlString(array $additionalFields, array $customFields): string
    {
        $xmlTemplate = $this->getXmlTemplate();
        $customFieldString = implode("\n", $customFields);

        return $this->runXmlReplacer($xmlTemplate, $additionalFields, $customFieldString);
    }

    /**
     * @param string $template
     * @param array $fields
     * @param string $customFieldString
     * @return string
     */
    private function runXmlReplacer(string $template, array $fields, string $customFieldString): string
    {
        $replaced = preg_replace_callback("/\[([0-z-]+)]/", function($match) use ($fields, $customFieldString) {
            return $match[1] === self::ALL_CUSTOM_FIELDS
                ? $customFieldString
                : $fields[$match[1]] ?? $match[0];
        }, $template);

        return str_replace("&", "&amp;", $replaced);
    }

    /**
     * XML template copied from tbltext table in legacy and cleaned up
     * @return string
     */
    private function getXmlTemplate(): string
    {
        return "<?xml version=\"1.0\" encoding=\"UTF-8\"?>
            <project>
                <name>[". SolarNexusDeliveryService::FIELD_FIRST_NAME ."] [". SolarNexusDeliveryService::FIELD_LAST_NAME ."] [". SolarNexusDeliveryService::FIELD_LEAD_ID ."]</name>
                <description>PV Lead</description>
                <projectClassification>RETROFIT</projectClassification>
                <applicationReferenceIds>
                    <applicationReferenceId>
                        <idValue>[". SolarNexusDeliveryService::FIELD_LEAD_ID ."]</idValue>
                        <idSource>[". SolarNexusDeliveryService::FIELD_LEAD_SOURCE ."]</idSource>
                    </applicationReferenceId>
                </applicationReferenceIds>
                <assignedTo>
                    <id>[". self::FIELD_ASSIGNED_TO ."]</id>
                </assignedTo>
                <participants>
                    <participant id=\"participant1\">
                        <participantType>INDIVIDUAL</participantType>
                        <taxStatus>INDIVIDUAL</taxStatus>
                        <contacts>
                            <contact>
                                <firstName>[". SolarNexusDeliveryService::FIELD_FIRST_NAME ."]</firstName>
                                <lastName>[". SolarNexusDeliveryService::FIELD_LAST_NAME ."]</lastName>
                                <phoneNumbers>
                                    <phoneNumber>
                                        <number>[". SolarNexusDeliveryService::FIELD_PHONE ."]</number>
                                        <ext></ext>
                                        <primary>TRUE</primary>
                                        <phoneNumberUse>MOBILE</phoneNumberUse>
                                    </phoneNumber>
                                </phoneNumbers>
                                <emailAddresses>
                                    <emailAddress>
                                        <address>[". SolarNexusDeliveryService::FIELD_EMAIL ."]</address>
                                        <primary>TRUE</primary>
                                        <emailAddressUse>HOME</emailAddressUse>
                                    </emailAddress>
                                </emailAddresses>
                            </contact>
                        </contacts>
                        <addresses></addresses>
                        <roles>
                            <role>CUSTOMER</role>
                        </roles>
                    </participant>
                </participants>
                <dateInitiated>[". self::FIELD_DATE ."]</dateInitiated>
                <projectTimeframe>not given</projectTimeframe>
                <leadSource>[". SolarNexusDeliveryService::FIELD_LEAD_SOURCE ."]</leadSource>
                <financePreference>purchase</financePreference>
                <goal>not given</goal>
                <utilityServices>
                    <utilityService>
                        <energy>ELECTRICITY</energy>
                        <utilityName>[". SolarNexusDeliveryService::FIELD_UTILITY_NAME ."]</utilityName>
                        <energyConsumption periodStart=\"\">
                            <dataSource>ESTIMATED</dataSource>
                            <energyCosts>
                                <energyCost periodName=\"MONTHLY\">[". SolarNexusDeliveryService::FIELD_ELECTRIC_SPEND ."]</energyCost>
                            </energyCosts>
                        </energyConsumption>
                    </utilityService>
                </utilityServices>
                <site>
                    <location>
                        <addressUse>PROJECT</addressUse>
                        <line1>[". SolarNexusDeliveryService::FIELD_ADDRESS_1 ."]</line1>
                        <city>[". SolarNexusDeliveryService::FIELD_CITY ."]</city>
                        <state>[". SolarNexusDeliveryService::FIELD_STATE_ABBR ."]</state>
                        <zipCode>[". SolarNexusDeliveryService::FIELD_ZIP_CODE ."]</zipCode>
                    </location>
                    <realEstateClassification>RESIDENTIAL</realEstateClassification>
                    <notes>
                        [". SolarNexusDeliveryService::FIELD_COMBINED_COMMENTS ."]\n[". self::ALL_CUSTOM_FIELDS ."]
                    </notes>
                </site>
            </project>";
    }
}
