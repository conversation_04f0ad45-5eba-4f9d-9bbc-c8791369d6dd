<?php

namespace App\Campaigns\Delivery\Contacts\Strategies\SMS;

use App\Campaigns\Delivery\CRM\CRMFieldReplacerService;
use App\Models\Campaigns\CompanyCampaign;
use App\Models\Odin\ConsumerProduct;
use Illuminate\Contracts\Container\BindingResolutionException;

class SMSTemplateDataModel
{
    public function __construct(
        protected string $body
    ) {}

    /**
     * Returns the default sms template.
     *
     * @TODO: Remove this when SMS templates are data-driven.
     *
     * @return self
     */
    public static function getDefaultTemplate(): SMSTemplateDataModel
    {
        return new SMSTemplateDataModel(
            <<<TEMPLATE
Campaign: [campaign_name]
Lead Sale Type: [lead_sale_type]
[brand] Lead ID: [lead_id] [full_name]
Email: [email]
Phone: [phone]
Address: [full_address]
Industry: [lead_industry]
Service: [lead_service]
TEMPLATE
        );
    }

    public static function getSolarTemplate(): SMSTemplateDataModel
    {
        return new SMSTemplateDataModel(
            <<<TEMPLATE
Campaign: [campaign_name]
Lead Sale Type: [lead_sale_type]
[brand] Lead ID: [lead_id] [full_name]
Email: [email]
Phone: [phone]
Address: [full_address]
Industry: [lead_industry]
Service: [lead_service]
Shading: [shade]
TEMPLATE
        );
    }


    /**
     * Returns the body of this template, and replaces the shortcodes with their respective values.
     *
     * @param ConsumerProduct $product
     * @param CompanyCampaign $campaign
     * @return string
     * @throws BindingResolutionException
     */
    public function getBody(ConsumerProduct $product, CompanyCampaign $campaign): string
    {
        /** @var CRMFieldReplacerService $replacerService */
        $replacerService = app()->make(CRMFieldReplacerService::class);

        return $replacerService->replaceField($product, $campaign, $this->body);
    }
}
