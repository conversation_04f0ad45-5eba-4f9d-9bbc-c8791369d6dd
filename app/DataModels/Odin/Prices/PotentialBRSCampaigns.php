<?php

namespace App\DataModels\Odin\Prices;

use App\Enums\Odin\Product;
use App\Enums\Odin\PropertyType;
use App\Enums\Odin\QualityTier;
use App\Enums\Odin\StateAbbreviation;
use App\Models\ComputedRejectionStatistic;
use ArrayIterator;
use Exception;
use Illuminate\Contracts\Support\Arrayable;
use Illuminate\Support\Collection;
use IteratorAggregate;

/**
 * @property-read Product $product
 * @property-read string $industry
 * @property-read PropertyType $propertyType
 * @property-read StateAbbreviation $stateAbbreviation
 * @property-read QualityTier $qualityTier
 * @property-read string $countyKey
 * @property-read int $serviceProductId
 */
class PotentialBRSCampaigns implements IteratorAggregate, Arrayable
{
    const ALLOWED_KEYS = [
        'serviceProductId',
        'product',
        'industry',
        'propertyType',
        'stateAbbreviation',
        'qualityTier',
        'countyKey'
    ];

    const COMPANY_ID = 'company_id';
    const SCHEDULE_ID = 'schedule_id';

    /**
     * @var Collection $campaigns
     */
    private Collection $campaigns;

    public function __construct(
        private readonly int               $serviceProductId,
        private readonly Product           $product,
        private readonly string            $industry,
        private readonly PropertyType      $propertyType,
        private readonly QualityTier       $qualityTier,
        private readonly StateAbbreviation $stateAbbreviation,
        private readonly string            $countyKey
    ) {
        $this->campaigns = collect();
    }

    /**
     * @param int $saleTypeId
     * @param int $campaignId
     * @param int $companyId
     * @param float $rejectionPercentageImpactingEligibility
     * @param float $rejectionPercentageImpactingBid
     * @return bool
     * @throws Exception
     */
    public function addCampaign(
        int $saleTypeId,
        int $campaignId,
        int $companyId,
        float $rejectionPercentageImpactingEligibility,
        float $rejectionPercentageImpactingBid
    ): bool
    {
        if($rejectionPercentageImpactingEligibility < 0 || $rejectionPercentageImpactingBid < 0) {
            throw new Exception("Rejection percentage can't be negative");
        }

        if(empty($this->campaigns->get($saleTypeId))) {
            $this->campaigns->put($saleTypeId, collect());
        }

        $this->campaigns->get($saleTypeId)->put($campaignId, collect([
            self::COMPANY_ID                                                       => $companyId,
            ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY => $rejectionPercentageImpactingEligibility,
            ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID         => $rejectionPercentageImpactingBid
        ]));

        return true;
    }

    /**
     * @param Collection $campaignIds
     * @return $this
     */
    public function removeCampaigns(
        Collection $campaignIds
    ): self
    {
        foreach($this->campaigns as $saleTypeId => $campaigns) {
            $remainingCampaigns = $campaigns->except($campaignIds);

            if($remainingCampaigns->isEmpty()) {
                $this->campaigns->forget($saleTypeId);
            }
            else {
                $this->campaigns->put($saleTypeId, $remainingCampaigns);
            }
        }

        return $this;
    }

    /**
     * @param int $campaignId
     * @param int $saleTypeId
     * @param int $scheduleId
     * @return bool
     */
    public function setCampaignSchedule(
        int $campaignId,
        int $saleTypeId,
        int $scheduleId
    ): bool
    {
        if(empty($this->campaigns->get($saleTypeId))) {
            return false;
        }
        else if(empty($this->campaigns->get($saleTypeId)->get($campaignId))) {
            return false;
        }

        $this->campaigns->get($saleTypeId)->get($campaignId)->put(self::SCHEDULE_ID, $scheduleId);

        return true;
    }

    /**
     * @param string $name
     * @return mixed
     * @throws Exception
     */
    public function __get(string $name): mixed
    {
        if(!in_array($name, self::ALLOWED_KEYS, true)) {
            throw new Exception("Property $name does not exist");
        }

        return $this->{$name};
    }

    /**
     * @return array
     */
    public function toArray(): array
    {
        return $this->campaigns->toArray();
    }

    /**
     * @return ArrayIterator
     */
    public function getIterator(): ArrayIterator
    {
        return $this->campaigns->getIterator();
    }

    /**
     * @return bool
     */
    public function isEmpty(): bool
    {
        return $this->campaigns->isEmpty();
    }

    /**
     * @return bool
     */
    public function isNotEmpty(): bool
    {
        return $this->campaigns->isNotEmpty();
    }

    /**
     * @return Collection
     */
    public function getCampaignIds(): Collection
    {
        $campaignIds = collect();
        foreach($this->campaigns as $saleTypeId => $campaigns) {
            $campaignIds->put($saleTypeId, $campaigns->keys());
        }

        return $campaignIds;
    }

    /**
     * @return Collection
     */
    public function getCompanyIds(): Collection
    {
        $companyIds = collect();
        foreach($this->campaigns as $saleTypeId => $campaigns) {
            $companyIds = $companyIds->merge($campaigns->pluck(self::COMPANY_ID));
        }

        return $companyIds->unique();
    }

    /**
     * @param array $companyIds
     * @return Collection
     */
    public function getCampaignIdsForCompanies(array $companyIds): Collection
    {
        $campaignIds = collect();
        foreach($this->campaigns as $saleTypeId => $campaigns) {
            $campaignIds->put($saleTypeId, $campaigns->whereIn(self::COMPANY_ID, $companyIds)->keys());
        }

        return $campaignIds;
    }

    /**
     * @return $this
     * @throws Exception
     */
    public function copy(): self
    {
        $copy = new self(
            $this->serviceProductId,
            $this->product,
            $this->industry,
            $this->propertyType,
            $this->qualityTier,
            $this->stateAbbreviation,
            $this->countyKey
        );

        foreach($this->toArray() as $saleTypeId => $campaigns) {
            foreach($campaigns as $campaignId => $campaignInfo) {
                $copy->addCampaign(
                    $saleTypeId,
                    $campaignId,
                    $campaignInfo[self::COMPANY_ID],
                    $campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_ELIGIBILITY],
                    $campaignInfo[ComputedRejectionStatistic::REJECTION_PERCENTAGE_IMPACTING_BID]
                );
            }
        }

        return $copy;
    }
}
