<?php

namespace App\DataModels\Odin\Prices;

use App\Abstracts\ProductPricing\PriceTableDataModelAbstract;
use App\Enums\Odin\Product;
use App\Enums\Odin\PropertyType;
use App\Enums\Odin\QualityTier as QualityTierEnum;
use App\Models\Odin\Industry;
use App\Models\Odin\QualityTier as QualityTierModel;
use App\Enums\Odin\SaleTypes;
use App\Models\Legacy\LeadPrice;
use App\Models\Legacy\Location;
use App\Models\SaleType;
use App\Repositories\ProductPriceRepository;
use Illuminate\Support\Collection;

class BidPriceTable extends PriceTableDataModelAbstract
{
    /**
     * @param Product $product
     * @param string $industry
     * @param PropertyType $propertyType
     * @param bool $includeCounties
     * @param array|null $exclusiveStateList
     * @param array|null $exclusiveCountyKeys
     */
    public function __construct(
        Product $product = Product::LEAD,
        string $industry = Industry::INDUSTRY_SOLAR,
        PropertyType $propertyType = PropertyType::RESIDENTIAL,
        bool $includeCounties = true,
        ?array $exclusiveStateList = null,
        ?array $exclusiveCountyKeys = null
    )
    {
        parent::__construct(
            $product,
            $industry,
            $propertyType,
            $includeCounties,
            $exclusiveStateList,
            $exclusiveCountyKeys
        );
    }

    /** @inheritDoc */
    public function ingestData(Collection $prices): bool
    {
        foreach($prices as $price) {
            $salesType = SaleTypes::tryFrom($price->{ProductPriceRepository::COLUMN_SALE_TYPE_NAME});
            $qualityTier = QualityTierEnum::tryFrom($price->{ProductPriceRepository::COLUMN_QUALITY_TIER_NAME});
            if(!$qualityTier || !$salesType) {
                continue;
            }

            if(
                $price->{Location::TYPE} === Location::TYPE_STATE
                && (empty($this->exclusiveStateList) || in_array($price->{Location::STATE_ABBREVIATION}, $this->exclusiveStateList, true))
            ) {
                $this->addSalesTypeToState($qualityTier, $price->{Location::STATE_ABBREVIATION}, $salesType, $price->product_campaign_id, $price->price);
            }
            else if(
                $this->includeCounties
                && $price->{Location::TYPE} === Location::TYPE_COUNTY
                && (empty($this->exclusiveCountyKeys) || in_array($price->{Location::COUNTY_KEY}, $this->exclusiveCountyKeys, true))
            ) {
                $this->addSalesTypeToCounty($qualityTier, $price->{Location::STATE_ABBREVIATION}, $price->{Location::COUNTY_KEY}, $salesType, $price->product_campaign_id, $price->price);
            }
        }

        return true;
    }

    /** @inheritDoc */
    public function ingestLegacyData(Collection $legacyPrices): bool
    {
        /** @var LeadPrice $price */
        foreach ($legacyPrices as $price) {
            // skip unhandled legacy tier or sales type
            $qualityTier = $this->getQualityTierFromLegacyPrice($price);
            $salesType = $this->getSalesTypeFromLegacyPrice($price);
            if(!$qualityTier || !$salesType) {
                continue;
            }

            $validState = empty($this->exclusiveStateList) || in_array($price->{Location::STATE_ABBREVIATION}, $this->exclusiveStateList);

            if(
                $price->{Location::TYPE} === Location::TYPE_STATE
                && $validState
            ) {
                $this->addSalesTypeToState($qualityTier, $price->{Location::STATE_ABBREVIATION}, $salesType, $price->{LeadPrice::LEAD_CAMPAIGN_ID}, $price->{LeadPrice::PRICE});
            }
            else if(
                $this->includeCounties
                && $price->{Location::TYPE} === Location::TYPE_COUNTY
                && (empty($this->exclusiveCountyKeys) || in_array($price->{Location::COUNTY_KEY}, $this->exclusiveCountyKeys, true))
            ) {
                $this->addSalesTypeToCounty($qualityTier, $price->{Location::STATE_ABBREVIATION}, $price->{Location::COUNTY_KEY}, $salesType, $price->{LeadPrice::LEAD_CAMPAIGN_ID}, $price->{LeadPrice::PRICE});
            }
        }

        return true;
    }

    /**
     * @inheritDoc
     */
    public function getSalesTypeStructure(): array
    {
        $salesTypes = match ($this->product) {
            Product::LEAD => self::LEAD_SALES_TYPES,
            Product::APPOINTMENT => self::APPOINTMENT_SALES_TYPES
        };

        $structure = [];
        foreach($salesTypes as $salesType) {
            $structure[$salesType->value] = [];
        }

        return $structure;
    }

    /**
     * @param QualityTierEnum $qualityTier
     * @param string $stateAbbr
     * @param SaleTypes $saleType
     * @param int $campaignId
     * @param float $bidPrice
     * @return void
     */
    private function addSalesTypeToState(QualityTierEnum $qualityTier, string $stateAbbr, SaleTypes $saleType, int $campaignId, float $bidPrice): void
    {
        $this->priceData[self::ARRAY_KEY_QUALITY_TIERS][$qualityTier->value][self::ARRAY_KEY_STATES][$stateAbbr][self::ARRAY_KEY_SALES_TYPES][$saleType->value][$campaignId] = [self::ARRAY_KEY_PRICE => $bidPrice];
    }

    /**
     * @param QualityTierEnum $qualityTier
     * @param string $stateAbbr
     * @param string $countyKey
     * @param SaleTypes $saleType
     * @param int $campaignId
     * @param float $bidPrice
     * @return void
     */
    private function addSalesTypeToCounty(QualityTierEnum $qualityTier, string $stateAbbr, string $countyKey, SaleTypes $saleType, int $campaignId, float $bidPrice): void
    {
        $this->priceData[self::ARRAY_KEY_QUALITY_TIERS][$qualityTier->value][self::ARRAY_KEY_STATES][$stateAbbr][self::ARRAY_KEY_COUNTIES][$countyKey][self::ARRAY_KEY_SALES_TYPES][$saleType->value][$campaignId] = [self::ARRAY_KEY_PRICE => $bidPrice];
    }

    public function getPrice(string $qualityTier, string $salesType, string $stateAbbr, ?string $countyKey = null): ?float
    {
        // TODO: Implement getPrice() method.
    }
}
