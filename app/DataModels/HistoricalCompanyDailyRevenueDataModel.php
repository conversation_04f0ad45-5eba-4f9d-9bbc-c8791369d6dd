<?php

namespace App\DataModels;

use Carbon\Carbon;
use Carbon\CarbonInterface;
use Carbon\CarbonPeriod;
use Exception;
use Illuminate\Contracts\Support\Jsonable;
use Illuminate\Support\Collection;

class HistoricalCompanyDailyRevenueDataModel implements Jsonable
{
    const DATE_FORMAT = 'Y-m-d';

    private Collection $data;

    public function __construct()
    {
        $this->data = collect();
    }

    public function setDayRevenue(Carbon|CarbonInterface $date, float $revenue): bool
    {
        if($revenue < 0) {
            throw new Exception("\$revenue must be zero or greater");
        }

        $this->data->put($date->format(self::DATE_FORMAT), round($revenue, 2));

        return true;
    }

    public function getDayRevenue(Carbon|CarbonInterface $date): ?float
    {
        return $this->data->get($date->format(self::DATE_FORMAT));
    }

    public function getRevenueBetween(Carbon|CarbonInterface $start, Carbon|CarbonInterface $end): Collection
    {
        $period = CarbonPeriod::since($start, true)->until($end, true)->days(1);

        $dates = [];
        foreach($period as $date) {
            $dates[] = $date->format(self::DATE_FORMAT);
        }

        return $this->data->only($dates);
    }

    public function toArray(): array
    {
        return $this->data->toArray();
    }

    /**
     * @inheritDoc
     */
    public function toJson($options = 0)
    {
        return json_encode($this->toArray(), $options);
    }

    public static function fromJson(string $json): HistoricalCompanyDailyRevenueDataModel
    {
        $arrayData = json_decode($json, true);

        $hcdrdm = new HistoricalCompanyDailyRevenueDataModel();

        foreach($arrayData as $date => $revenue) {
            $hcdrdm->setDayRevenue(Carbon::createFromFormat(self::DATE_FORMAT, $date), $revenue);
        }

        return $hcdrdm;
    }
}
