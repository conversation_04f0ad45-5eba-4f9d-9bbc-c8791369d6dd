<?php

namespace App\Company\DeleteCompany\Validate;

use App\Models\BundleInvoice;

class NoBundleInvoices implements DeleteValidatorContract
{
    function validate(int $companyId): bool
    {
        return BundleInvoice::query()->where(BundleInvoice::FIELD_COMPANY_ID, $companyId)->doesntExist();
    }

    function title(): string
    {
        return 'Company has no bundle invoices';
    }

    function failReason(): string
    {
        return 'Company has bundle invoices';
    }
}
