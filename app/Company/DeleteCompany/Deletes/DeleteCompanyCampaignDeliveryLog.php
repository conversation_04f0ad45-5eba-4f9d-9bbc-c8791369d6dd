<?php

namespace App\Company\DeleteCompany\Deletes;

use App\Models\Campaigns\CompanyCampaign;
use App\Models\Campaigns\Modules\Delivery\CompanyCampaignDeliveryLog;
use Illuminate\Database\Eloquent\Builder;

class DeleteCompanyCampaignDeliveryLog extends DeleteContract
{
    function query(int $companyId): Builder
    {
        return CompanyCampaignDeliveryLog::query()
            ->whereHas(CompanyCampaignDeliveryLog::RELATION_CAMPAIGN, function(Builder $query) use ($companyId) {
                $query->where(CompanyCampaign::FIELD_COMPANY_ID, $companyId);
            });
    }

    function modelClass(): string
    {
        return CompanyCampaignDeliveryLog::class;
    }
}
