<?php

namespace App\Notifications;

use Illuminate\Notifications\Messages\MailMessage;

/**
 * Generate a password reset email for a CompanyUser
 * Can add further customisation for different services if required e.g. branding/images
 *  or convert to Email Templates if more control over content is needed
 */
class CompanyUserPasswordReset extends PasswordReset
{
    const string FIXR_RC_LOGO = 'images/FIXR_RC_Logo.png';

    protected string $url;
    protected string $messageSubject;
    protected string $messageSalutation;
    protected string $fromEmail;
    protected string $fromName;

    public function __construct($token)
    {
        parent::__construct($token);

        $this->setUrl(config('app.url'));
        $this->setMailProperties(
            subject   : 'Reset Password Notification',
            fromEmail : config('mail.from.address'),
            fromName  : config('app.name'),
        );
    }

    /**
     * @param string $domain
     * @param string|null $route
     * @return self
     */
    public function setUrl(string $domain, ?string $route = "reset-password"): self
    {
        $domain = preg_replace("/[\/\s]*$/", "", $domain);
        $route = preg_replace("/^[\s\/]*/", "", $route);
        $this->url = "$domain/$route";

        return $this;
    }

    /**
     * @param string|null $subject
     * @param string|null $salutation
     * @param string|null $fromEmail
     * @param string|null $fromName
     * @return self
     */
    public function setMailProperties(?string $subject = null, ?string $salutation = null, ?string $fromEmail = null, ?string $fromName = null): self
    {
        if ($subject)
            $this->messageSubject = $subject;
        if ($salutation)
            $this->messageSalutation = $salutation;
        if ($fromEmail)
            $this->fromEmail = $fromEmail;
        if ($fromName)
            $this->fromName = $fromName;

        return $this;
    }

    /**
     * @inheritDoc
     */
    protected function buildMailMessage($url): MailMessage
    {
        $expiry = config('auth.passwords.company_users.expire', 60);

        return (new MailMessage)
            ->subject($this->messageSubject)
            ->line('You are receiving this email because we received a password reset request for your account.')
            ->action('Reset Password', $url)
            ->line("This password reset link will expire in $expiry minutes.")
            ->line('If you did not request a password reset, no further action is required.')
            ->salutation($this->messageSalutation)
            ->line("Regards,")
            ->line($this->getLogo())
            ->from($this->fromEmail, $this->fromName);
    }

    /**
     * @return string
     */
    protected function getLogo(): string
    {
        return '![image FIXR_RC_LOGO](' . asset(self::FIXR_RC_LOGO) . ')';
    }

    /**
     * @inheritDoc
     */
    protected function resetUrl($notifiable): string
    {
        $query = http_build_query([
            'token' => $this->token,
            'email' => $notifiable->getEmailForPasswordReset(),
        ]);

        return $this->url . "?$query";
    }
}
